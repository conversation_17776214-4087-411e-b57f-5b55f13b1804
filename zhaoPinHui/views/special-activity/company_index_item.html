<?php foreach ($list as $c) {?>
<a class="item animation-transition <?=(($c['applyBtnData']['btnIsShow'] == 1 && $c['applyBtnData']['btnType'] == 2) ? 'is-apply' : '')?> <?=($c['isTop'] == 1 ? 'is-top': '') ?> <?=($activityId == 0 ? '':'is-single') ?>" href="<?=($c['targetUrl'] ?? '')?>" title="<?=($c['shortName'] ? $c['shortName'] : $c['fullName']) ?>" target="_blank" data-announcement-id="<?=$c['announcementId']?>">
    <div class="diamond"></div>
    <div class="top">
        <div class="logo">
            <img src="<?=$c['logoUrl']?>" alt="<?=($c['shortName'] ? $c['shortName'] : $c['fullName']) ?>" />
        </div>
        <div class="grow">
            <div class="name"><?=($c['shortName'] ? $c['shortName'] : $c['fullName']) ?></div>

            <div class="type"><?=$c['cardTag']?></div>
        </div>
    </div>
    <?php if(!empty($c['cardTag2'])):?>
    <div class="middle"><span>招 </span><?=$c['cardTag2']?></div>
    <?php endif?>

    <!-- 亮点：praise
     公告：site
     空：announcement
     专业：major -->
<!--    1: 公告亮点；2:需求学科；3:公告标题；4:敬请关注-->
    <?php if(($c['cardTag3']['type'] == 1)):?>
    <div class="bottom praise"><?=$c['cardTag3']['value']?></div>
    <?php endif?>
    <?php if(($c['cardTag3']['type'] == 2)):?>
    <div class="bottom major"><?=$c['cardTag3']['value']?></div>
    <?php endif?>
    <?php if(($c['cardTag3']['type'] == 3)):?>
    <div class="bottom site"><?=$c['cardTag3']['value']?></div>
    <?php endif?>
    <?php if(($c['cardTag3']['type'] == 4)):?>
    <div class="bottom announcement"><?=$c['cardTag3']['value']?></div>
    <?php endif?>
    <!-- 立即报名： apply
    已报名：applied -->
    <?php if(($c['applyBtnData']['btnIsShow'] == 1)):?>
    <div class="aside">
        <button class="btn <?=($c['applyBtnData']['btnType'] == 2 ? 'applied' : 'apply')?>" data-link="<?=$c['applyBtnData']['btnLink']?>" <?=($c['applyBtnData']['btnIsClick'] == 1 ? '' : 'disabled')?>>
        <?=$c['applyBtnData']['btnText']?>
        </button>
    </div>
    <?php endif?>
</a>
<?php } ?>
