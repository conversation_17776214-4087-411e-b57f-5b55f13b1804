<link rel="stylesheet" href="/static/css/activitySpecial.css"/>

<!-- 默认模板：is-default -->
<div class="main-container <?=$detail['templateId'] == 1 ? 'is-default' :''?>">
    <!-- 自定义图片 style="background: url(图片路径) no-repeat center top / auto 100%" -->
    <div class="banner-wrapper">
        <div class="banner-box">
            <img src="<?=$detail['imagePcBannerUrl']?>" alt="">
        </div>

        <div class="detail-wrapper view-content">
            <?php if($detail['status'] == 1):?>
            <div class="status await">待举办</div>
            <?php endif?>
            <?php if($detail['status'] == 2):?>
            <div class="status await-start">即将开始</div>
            <?php endif?>
            <?php if($detail['status'] == 3):?>
            <div class="status start">进行中</div>
            <?php endif?>
            <?php if($detail['status'] == 4):?>
            <div class="status ended">已结束</div>
            <?php endif?>
            <div class="detail">
                <?php if(count($detail['tagList']) > 0):?>
                <div class="tag">
                    <?php foreach($detail['tagList'] as $k=>$tagInfo):?>
                    <span class="item <?=($tagInfo['type'] == 2 || $tagInfo['type'] == 3) ? '': 'offline'?>"><?=$tagInfo['value']?></span>
                    <?php endforeach;?>
                </div>
                <?php endif?>

                <h1 class="title"><?=$detail['name']?></h1>
                <?php if($detail['eventOrganization']):?>
                <div class="detail-item organization">
                    <div class="label">活动组织：</div>
                    <div class="value" title="<?=$detail['eventOrganization'];?>">
                        <?=$detail['eventOrganization'];?>
                    </div>
                </div>
                <?php endif?>

                <div class="detail-item time">
                    <div class="label">活动时间：</div>
                    <div class="value"><?=$detail['activityDateText']?></div>
                </div>
                <div class="detail-item address">
                    <div class="label">
                        <?php if($detail['toHoldType'] == 1):?>
                        活动平台：
                        <? else: ?>
                        活动地点：
                        <?php endif?>
                        </div>
                    <div class="value"><?=$detail['addressText']?></div>
                </div>
                <?php if($detail['type']):?>
                <div class="detail-item type">
                    <div class="label">活动系列：</div>
                    <div class="value">
                        <?=$detail['typeText']?>
                    </div>
                </div>
                <?php endif?>

                <?php if($detail['participationBenefit']):?>
                <div class="welfare has-detail">
                    <div class="value"><?=$detail['participationBenefit']?></div>
                    <?php if($detail['participationBenefitDetail']):?>
                    <div class="arrow open-dialog-btn" data-target="#welfareDialog">点击查看更多</div>
                    <?php endif?>
                </div>
                <?php endif?>

            </div>

            <aside class="aside">
                <div class="update-status">持续更新中</div>
                <div class="item">
                    <div class="amount"><?=($detail['realParticipationActivityAmount'])?></div>
                    <span>活动场次</span>
                </div>
                <div class="item">
                    <div class="amount"><?=($detail['realParticipationCompanyAmount'] > 10 ? $detail['realParticipationCompanyAmount'] : "更新中")?></div>
                    <span>参会单位</span>
                </div>
            </aside>
        </div>
    </div>

    <div class="tabs-wrapper">
        <div class="tabs-header-wrapper view-content" id="page-change-fixed">
            <div class="tabs-header-content">
                <div class="tabs-header">
                    <div class="tabs-nav tabs-switch-item <?=($detail['realParticipationActivityAmount'] >= 2 ? '' : 'active' )?>" open-pane="detail-pane">活动详情</div>
                    <?php if($detail['realParticipationActivityAmount'] >= 2):?>
                    <div class="tabs-nav tabs-switch-item active" open-pane="session-pane">场次安排</div>
                    <?php endif?>

                    <?php if($detail['realParticipationCompanyAmount'] >= 2):?>
                    <div class="tabs-nav tabs-switch-item" open-pane="company-pane">参会单位</div>
                    <?php endif?>
                    <?php if($detail['participationMethod']):?>
                    <div class="tabs-nav tabs-switch-item" open-pane="apply-way-pane">参会方式</div>
                    <?php endif?>
                    <?php if($detail['retrospection']):?>
                    <div class="tabs-nav tabs-switch-item" open-pane="review-pane">往届回顾</div>
                    <?php endif?>

                    <div class="share-mini-code-container">
                        <div class="share-mini-code-trigger">微信扫码分享</div>

                        <div class="share-mini-code-popup">
                            <div class="share-mini-code">
                                <img src="<?=$shareLink?>" alt="" class="share-mini-code-img" />
                            </div>
                            <div class="share-mini-code-title">微信扫一扫</div>
                            <div class="share-mini-code-tips">分享给你的朋友吧</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="tabs-content view-content">
            <!-- 活动详情 -->
            <div class="tabs-pane detail-pane <?=($detail['realParticipationActivityAmount'] >= 2 ? '' : 'show' )?>">
                <?=$detail['activityDetail']?>
            </div>

            <div class="tabs-pane session-pane <?=($detail['realParticipationActivityAmount'] >= 2 ? 'show' : '' )?>">
                <?php if(count($relationActivity['recommendActivityList']) > 0):?>
                <div class="common-wrapper hot-session-wrapper">
                    <div class="common-title-content">
                        <div class="common-title">
                            <span>热门场次</span>
                        </div>
                    </div>

                    <div class="common-content">
                        <div class="swiper-container hot-session-swiper">
                            <div class="swiper-wrapper">
                                <?php foreach($relationActivity['recommendActivityList'] as $activityInfo):?>
                                    <a class="swiper-slide hot-list" href="<?=$activityInfo['activityDetailLink']?>" target="_blank">
                                    <div class="cover-content">
                                        <div class="name">
                                            <?php if($activityInfo['activityShort']):?>
                                            <?=$activityInfo['activityShort']?>
                                            <?php else: ?>
                                            <?=$activityInfo['name']?>
                                            <?php endif?>
                                        </div>
                                        <img class="cover" src="<?=$activityInfo['mainImgFileUrl']?>" alt="<?=($activityInfo['activityShort'] ? $activityInfo['activityShort'] : $activityInfo['name'])?>" />
                                        <div class="view"><?=$activityInfo['showClick']?></div>
                                    </div>
                                    <div class="detail">
                                        <div class="date"><?=$activityInfo['dateText']?></div>
                                        <div class="address"><?=$activityInfo['addressText']?></div>
                                        <button class="apply <?=($activityInfo['applyBtnData']['btnType'] == 2 ? 'gray-status' : '')?>" data-link="<?=$activityInfo['applyBtnData']['btnLink']?>"><?=$activityInfo['applyBtnData']['btnText']?></button>
                                    </div>
                                </a>
                                <?php endforeach;?>
                            </div>
                            <?php if(count($relationActivity['recommendActivityList'])>3):?>
                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                            <?php endif?>
                        </div>
                    </div>
                </div>
                <?php endif?>

                <div class="common-wrapper session-wrapper show">
                    <div class="common-title-content">
                        <div class="common-title">
                            <span>全部场次</span>
                        </div>
                    </div>

                    <div class="common-content">
                        <?php foreach($relationActivity['activitySchedule'] as $key=> $activitySchedule):?>

                        <div class="session-order-wrapper <?=($key=='historyActivityList' ? 'review-session-wrapper' : '')?>">
                            <?php if($key=='historyActivityList' && $activitySchedule):?>
                            <div class="tips">以下场次已结束啦，还有更多精彩场次等着你，快来一起参与吧！</div>
                            <?php endif?>
                            <!-- 月份维度 -->
                            <?php foreach($activitySchedule as $m):?>
                            <?php if($m['activityAmount'] > 0):?>
                            <div class="month-list">
                                <div class="month-title-content">
                                    <div class="aside-circle">
                                        <span class="circle"></span>
                                    </div>

                                    <div class="month-title">
                                        <?php if($m['monthStatus'] == 4):?>
                                        <div class="month">时间待定</div>
                                        <?php else: ?>
                                        <div class="month"><?=$m['month']?>月</div>
                                        <div class="year">/<?=$m['year']?></div>
                                        <?php endif?>
<!--                                        1:历史月；2:当前月；3:未来月;4:时间待定-->
                                        <?php if($m['monthStatus'] == 1 && $m['statusMap']['inProgress'] >0):?>
                                            <div class="amount">
                                                共计
                                                <span class="number"><?=$m['activityAmount']?></span>
                                                场；
                                                <span class="number"><?=$m['statusMap']['inProgress']?></span>
                                                场进行中
                                            </div>
                                        <?php endif?>

                                        <?php if($m['monthStatus'] == 2 ):?>
                                        <?php if($m['statusMap']['toBeHeld'] != 0):?>
                                        <div class="amount">
                                            共计
                                            <span class="number"><?=$m['statusMap']['toBeHeld']?></span>
                                            场待举办
                                        </div>
                                        <?php elseif($m['statusMap']['inProgress'] != 0):?>
                                        <div class="amount">
                                            共计
                                            <span class="number"><?=$m['activityAmount']?></span>
                                            场；
                                            <span class="number"><?=$m['statusMap']['inProgress']?></span>
                                            场进行中
                                        </div>
                                        <?php endif?>
                                        <?php endif?>

                                        <?php if($m['monthStatus'] == 3):?>
                                        <div class="amount">
                                            共计
                                            <span class="number"><?=$m['activityAmount']?></span>
                                            场待举办
                                        </div>
                                        <?php endif?>

                                        <?php if($m['monthStatus'] == 4):?>
                                        <div class="amount">
                                            共计
                                            <span class="number"><?=count($m['activityDate'][0]['activityList'])?></span>
                                            场待举办
                                        </div>
                                        <?php endif?>

                                    </div>
                                </div>

                                <!-- 日维度 -->
                                <?php foreach($m['activityDate'] as $d):?>
                                <div class="day-session">
                                    <!-- 场次维度 -->
                                    <?php foreach($d['activityList'] as $aKey => $a):?>
                                    <div class="session-item">
                                        <div class="aside-date">
                                            <?php if($aKey == 0):?>

                                            <div class="date">
                                                <?=$d['dateText'];?>
                                                <?php if(count($d['activityList']) > 1):?>
                                                <span class="number"><?=count($d['activityList'])?>场</span>
                                                <?php endif?>
                                            </div>
                                            <?php endif?>

                                        </div>
                                        <div class="session-content">
                                            <div class="session-detail">
                                                <?php if($a['activityChildStatus'] == 2):?>
                                                <div class="special-tips">火热报名中</div>
                                                <?php endif?>

                                                <!-- 待举办：await-status
                                                即将开始：await-start-status
                                                进行中：start-status
                                                已结束：end-status -->
                                                <?php if($a['activityChildStatus'] == 1):?>
                                                <div class="status await-status">待举办</div>
                                                <?php endif?>
                                                <?php if($a['activityChildStatus'] == 2):?>
                                                <div class="status await-start-status">即将开始</div>
                                                <?php endif?>
                                                <?php if($a['activityChildStatus'] == 3):?>
                                                <div class="status start-status">进行中</div>
                                                <?php endif?>
                                                <?php if($a['activityChildStatus'] == 4):?>
                                                <div class="status end-status">已结束</div>
                                                <?php endif?>

                                                <div class="info">
                                                    <a class="detail-link" href="<?=$a['activityDetailLink']?>" target="_blank">
                                                        <div class="name">
                                                            <?php if($a['activityShort']):?>
                                                            <?=$a['activityShort']?>
                                                            <?php else: ?>
                                                            <?=$a['name']?>
                                                            <?php endif?>
                                                        </div>
                                                        <div class="center">
                                                            <div class="time"><?=$a['dateText']?></div>
                                                            <div class="address"><?=$a['addressText']?></div>
                                                        </div>
                                                    </a>

                                                    <?php if($a['activityCompanyList']):?>
                                                    <div class="company-content">
                                                        <div class="amount">参会单位：<span><?=count($a['activityCompanyList'])?></span>家</div>
                                                        <div class="company">
                                                            <span class="company-name">
                                                               <?php foreach($a['activityCompanyList'] as $key => $company):?>
                                                                <?php if($key != 0):?>
                                                                、
                                                                <?php endif?>
                                                                <a href="<?=$company['companyUrl']?>" target="_blank"><?=$company['fullName']?></a>
                                                                <?php endforeach;?>
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <?php endif?>
                                                </div>
                                                <button class="apply <?=$a['applyBtnData']['btnType'] == 2 ? 'gray-status' : ''?>" <?=$a['applyBtnData']['btnIsClick'] == 2 ? 'disabled' : ''?> data-link="<?=$a['applyBtnData']['btnLink']?>"><?=$a['applyBtnData']['btnText']?></button>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach;?>
                                </div>
                                <?php endforeach;?>
                            </div>
                            <?php endif?>
                            <?php endforeach;?>
                        </div>

                        <?php endforeach;?>
                    </div>
                </div>
            </div>

            <div class="tabs-pane company-pane">
                <div class="site-wrapper">
                    <?php foreach ($companyTabList as $tabKey => $tab) {?>
                    <div class="site-item <?=($tabKey == 0 ? 'active' : '')?> <?=$tab['activityChildStatus'] == 4 ? ' is-end' : ''?>" data-specialId="<?=$tab['specialId']?>" data-activityId="<?=$tab['activityId']?>">
                        <?php if(($tab['activityChildStatus'] == 2)):?>
                        <div class="status await-start-status"><?=$tab['activityChildStatusText']?></div>
                        <?php endif?>
                        <?php if(($tab['activityChildStatus'] == 3 )):?>
                        <div class="status start-status"><?=$tab['activityChildStatusText']?></div>
                        <?php endif?>
                        <?php if(($tab['activityChildStatus'] == 4 )):?>
                        <div class="status end-status"><?=$tab['activityChildStatusText']?></div>
                        <?php endif?>
                        <div class="name"><?=$tab['activityName']?></div>
                        <div class="date"><?=$tab['dateText']?></div>
                    </div>
                    <?php } ?>
                </div>

                <div id="companyComponent">
                    <div  v-if="isShowCurrentActivityInfo" class="detail-wrapper">
                        <div class="detail-content" :class="currentActivityInfo.applyStatus == 1 ? 'is-apply': ''">
                            <div class="title">{{currentActivityInfo.activityShort ? currentActivityInfo.activityShort :currentActivityInfo.name}}</div>

                            <div class="countdown-content">
                                <template v-if="currentActivityInfo.activityChildStatus == 1 || currentActivityInfo.activityChildStatus == 2">
                                    <div v-if="currentActivityInfo.startCountDown != null && currentActivityInfo.startCountDown" class="countdown">
                                        距活动开始还有：<span class="box day">{{day}}</span>&nbsp;天&nbsp;<span class="box hours">{{hours}}</span>&nbsp;时&nbsp;<span class="box min"
                                            >{{min}}</span
                                        >&nbsp;分&nbsp;<span class="box second">{{second}}</span>&nbsp;秒
                                    </div>
                                    <div v-else class="text">待举办，敬请期待</div>
                                </template>
                                <div v-if="currentActivityInfo.activityChildStatus == 3" class="text">正在进行中</div>
                                <div v-if="currentActivityInfo.activityChildStatus == 4" class="text">已结束</div>
                            </div>

                            <div class="time">活动时间：{{currentActivityInfo.dateText}}</div>
                            <div class="address">{{currentActivityInfo.toHoldType == 1 ? '活动平台：':'活动地点：'}}{{currentActivityInfo.addressText}}</div>
                        </div>
                    </div>

                    <div v-if="isShowSearchParams" class="filter-wrapper">
                        <div v-if="calcAreaList.length" class="filter-list">
                            <div class="label">
                                所在地区
                                <div class="area-tips">仅展示参会单位所在省份及部分热门城市</div>
                            </div>
                            <div class="value">
                                <span
                                    v-for="{ name, id, checked } in calcAreaList"
                                    class="item"
                                    :class="id ? '' : 'is-special'"
                                    :checked="checked"
                                    @click="handleCheck('areaId', { name, id })"
                                    >{{name}}</span
                                >
                            </div>
                        </div>

                        <div v-if="majorList.length || jobTypeList.length || companyTypeList.length" class="filter-list">
                            <div class="label">更多筛选</div>
                            <div class="value">
                                <div v-if="majorList.length" class="el-select el-select--mini filter-item" :class="majorId.length ? 'is-checked' : ''">
                                    <div class="select-trigger">
                                        <div class="el-input el-input--mini el-input--suffix" @click="handleDialogActive('majorList')">
                                            <input class="el-input__inner" type="text" readonly="" autocomplete="off" placeholder="需求学科" v-model="majorLabel" />
                                            <span class="el-input__suffix">
                                                <span class="el-input__suffix-inner filter-major-clear" @click.stop="handleDialogClear('majorId')">
                                                    <i class="el-select__caret el-input__icon el-icon-circle-close"></i>
                                                </span>
                                                <span class="el-input__suffix-inner">
                                                    <i class="el-select__caret el-input__icon el-icon-arrow-up"></i>
                                                </span>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div v-if="jobTypeList.length" class="el-select el-select--mini filter-item" :class="jobTypeId.length ? 'is-checked' : ''">
                                    <div class="select-trigger">
                                        <div class="el-input el-input--mini el-input--suffix" @click="handleDialogActive('jobTypeList')">
                                            <input class="el-input__inner" type="text" readonly="" autocomplete="off" placeholder="职位类型" v-model="jobTypeLabel" />
                                            <span class="el-input__suffix">
                                                <span class="el-input__suffix-inner filter-major-clear" @click.stop="handleDialogClear('jobTypeId')">
                                                    <i class="el-select__caret el-input__icon el-icon-circle-close"></i>
                                                </span>
                                                <span class="el-input__suffix-inner">
                                                    <i class="el-select__caret el-input__icon el-icon-arrow-up"></i>
                                                </span>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div v-if="companyTypeList.length" class="filter-item company-type-item" :class="companyTypeId.length ? 'is-select' : ''">
                                    <div class="label">{{companyTypeLabel}}</div>

                                    <el-select
                                        popper-class="company-select"
                                        :class="companyTypeId.length ? 'is-checked' : ''"
                                        v-model="companyTypeId"
                                        placeholder=" "
                                        size="mini"
                                        multiple
                                        :multiple-limit="5"
                                        collapse-tags
                                        @change="handleSearchAgain"
                                        clearable
                                    >
                                        <el-option v-for="{ name, code } in companyTypeList" :label="name" :value="code" />
                                    </el-select>
                                </div>
                            </div>
                            <div class="filter-aside">
                                <div class="clear-all" @click="clearAll">清空筛选条件</div>
                            </div>
                        </div>

                        <div class="tips">当前仅展示部分单位及招聘需求，欲知更多单位详情，请关注后续更新或现场了解</div>
                    </div>

                    <select-dialog
                        ref="selectDialogRef"
                        :title="dialogTitle"
                        :search-placeholder="dialogPlaceholder"
                        :list="dialogList"
                        :name="dialogName"
                        multiple
                        :multiple-limit="5"
                        v-model="dialogValue"
                        @update="handleDialogChange"
                    ></select-dialog>
                </div>

                <div class="company-wrapper">
                    <div class="company-content" style="display: <?=($companyList['list'] ? 'block': 'none')?>;">
                        <div class="company-data">
                            <!-- 单场活动：is-single
                                已报名：is-apply
                                置顶单位： is-top -->
                            <?=$companyList['list']?>
                        </div>

                        <?php if($companyList['isEnd'] == 1):?>
                        <div class="tips">更多单位持续更新中，敬请关注...</div>
                        <?php endif?>
                    </div>
                    
                    <div class="pagination-cotnent" id="paginationComponent">
                        <el-pagination v-show="count>0" background layout="prev, pager, next" :page-size="18" @current-change="change" :total="count" v-model:current-page="page" />
                    </div>

                    <div class="empty <?=$companyList['list'] ? '' : 'show' ?>">单位持续更新中，敬请关注...</div>
                </div>
            </div>

            <div class="tabs-pane apply-way-pane">
                <?=$detail['participationMethod']?>
            </div>

            <div class="tabs-pane review-pane">
                <?=$detail['retrospection']?>
            </div>
        </div>
    </div>
</div>

<!-- 福利详情dialog -->
<div class="el-overlay" id="welfareDialog" style="z-index: 2003">
    <div class="el-overlay-dialog">
        <div class="el-dialog dialog-center" aria-modal="true" role="dialog" aria-label="dialog">
            <div class="el-dialog__header">
                参会福利

                <button class="el-dialog__headerbtn close-dialog" type="button">
                    <i class="el-dialog__close el-icon el-icon-close"></i>
                </button>
            </div>

            <div class="el-dialog__body">
                <!-- <div class="welfare-wrapper">
                    <div class="welfare-title">参会享交通福利</div>
                    <div class="welfare-content">
                        人才参会享市区内交通报销（60元内/人），地铁、公交、打车都可以，轻松出行无负担！参会时现场扫码填写相关报销信息、上传报销凭证，会后10个工作日内完成报销。（具体操作方式可以现场找工作人员了解）
                    </div>
                </div> -->
                <?=$detail['participationBenefitDetail']?>
            </div>

            <footer class="el-dialog__footer">
                <button class="close-dialog">我知道了</button>
            </footer>
        </div>
    </div>
</div>

<!-- 侧边报名入口 -->
<?php if($detail['status'] != 4 && ($detail['personCanApply'] == 1 || $detail['companyCanApply'] == 1 || !empty($detail['imageServiceCodeUrl']))):?>
<div class="fixed-apply-enter">
    <?php if($detail['personCanApply'] == 1):?>
    <a class="apply-enter-item apply-enter-person" href="<?=$detail['applyLink']?>" target="_blank" rel="nofollow" >人才报名</a>
    <?php endif?>

    <?php if($detail['companyCanApply'] == 1):?>
    <a class="apply-enter-item apply-enter-company" href="<?=$detail['applyLinkCompany']?>" target="_blank" rel="nofollow" data-link="<?=$detail['applyLinkCompany']?>">单位报名</a>
    <?php endif?>

    <?php if(!empty($detail['imageServiceCodeUrl'])):?>
    <span class="apply-enter-item apply-enter-info">
        活动进群
        <div class="code-popup">
            <img src="<?=$detail['imageServiceCodeUrl']?>" alt="" />
        </div>
    </span>
    <?php endif?>
</div>
<?php endif?>

<script>
    $(function () {
        var specialActivityId = "<?=$detail['id']?>"
        var activityId = ''

        var $headerTabs = $('.tabs-header-wrapper')
        var $headerTabsItem = $headerTabs.find('.tabs-switch-item')

        var $openDialogBtn = $('.open-dialog-btn')
        var $closeDialog = $('.close-dialog')

        var $sessionCard = $(".session-order-wrapper:not('.review-session-wrapper') .session-detail")
        var $sessionCompany = $sessionCard.find('.info .company-content .company')
        var $sessionApplyBtn = $('.session-order-wrapper .session-item .apply')

        // function getParams(key) {
        //     const search = window.location.search
        //     const [mark, ...queryArr] = search.split('?')
        //     const query = queryArr.join('?')
        //     if (!query) return ''
        //     if (new RegExp(`${key}=([^&]*)`).test(query)) {
        //         const value = decodeURIComponent(RegExp.$1)
        //         return /^-?\d+$/.test(value) ? value * 1 : value
        //     }
        // }

        // function openTab() {
        //     var tabKey = getParams('how-tab')
        //     if (tabKey === 'review') {
        //         setTimeout(() => {
        //             $headerTabs.find('.tabs-switch-item[open-pane="review-pane"]').click()
        //         }, 10)
        //     }
        // }
        // openTab()

        $(window).on('scroll', function () {
            var scrollTop = $(this).scrollTop()
            var offsetTop = $headerTabs.offset().top
            const isFixed = scrollTop >= offsetTop
            isFixed ? $headerTabs.addClass('is-fixed') : $headerTabs.removeClass('is-fixed')
        })

        var activeTab = 'session-pane'

        $headerTabsItem.on('click', function () {
            var target = $(this).attr('open-pane')

            if (activeTab === target) return

            activeTab = target

            if (target !== 'session-pane') {
                new WOW({
                    boxClass: 'wow',
                    animateClass: 'animated',
                    offset: 100,
                    mobile: true,
                    live: true
                }).init()
            }

            $(`.${target}`).addClass('show').siblings('.tabs-pane').removeClass('show')
            $(this).addClass('active').siblings().removeClass('active')

            scrollToTarget('page-change-fixed')
        })

        $openDialogBtn.on('click', function () {
            $('body').addClass('el-popup-parent--hidden')
            var target = $(this).attr('data-target')
            $(target).fadeIn()
        })

        $closeDialog.on('click', function () {
            $('body').removeClass('el-popup-parent--hidden')
            $(this).parents('.el-overlay').fadeOut()
        })

        var swiper = new Swiper('.hot-session-swiper', {
            slidesPerView: 3,
            spaceBetween: 10,
            slidesPerGroup: 3,
            loop: true,
            loopFillGroupWithBlank: true,
            navigation: {
                nextEl: '.hot-session-swiper .swiper-button-next',
                prevEl: '.hot-session-swiper .swiper-button-prev'
            }
        })

        $('.session-pane').on('click', '.hot-list .apply', function (e) {
            e.preventDefault()
            const link = $(this).attr('data-link')
            window.open(link, '_blank')
        })

        // $('.company-pane').on('click', '.company-data a.item', function (e) {
        //     const announcementId = $(this).attr('data-announcement-id')
        //     if (!announcementId) {
        //         e.preventDefault()
        //         ElementPlus.ElMessage({
        //             message: '该单位招聘简章更新中，敬请关注！',
        //             type: 'warning'
        //         })
        //     }
        // })

        $('.company-pane').on('click', '.company-data .item .apply', function (e) {
            const link = $(this).attr('data-link')
            e.preventDefault()
            e.stopPropagation()
            window.open(link, '_blank')
        })

        var sessionCompanyScrollTimer = []
        function sessionCompanyScroll(sessionCard) {
            var speed = 60
            var $company = $(sessionCard).find('.info .company-content .company')
            var $companyName = $company.find('.company-name')
            var length = $companyName.length

            var contentWidth = $company.width()
            var contentNameWidth = $companyName.width()

            if (contentNameWidth <= contentWidth) return

            if (length === 1) {
                var html = $company.html()
                $company.append(html)
            }

            const timerIndex = setInterval(function () {
                var width = $companyName.width()
                var scrollLeft = $company.scrollLeft()
                if (width - scrollLeft < 3) {
                    $company.scrollLeft(0)
                } else {
                    $company.scrollLeft(scrollLeft + 1)
                }
            }, 20)
            sessionCompanyScrollTimer.push(timerIndex)
        }

        function clearTimer() {
            sessionCompanyScrollTimer.forEach((item) => clearInterval(item))
        }

        $sessionCard.hover(
            function () {
                $(this).find('.info .company-content .company').css('text-overflow', 'initial')
                sessionCompanyScroll(this)
            },
            function () {
                $(this).find('.info .company-content .company').css('text-overflow', 'ellipsis')
                $(this).find('.info .company-content .company').scrollLeft(0)
                clearTimer()
            }
        )

        $sessionCompany.hover(
            function () {
                clearTimer()
            },
            function () {
                sessionCompanyScroll($(this).parents('.session-detail'))
            }
        )

        $sessionApplyBtn.on('click', function () {
            var openLink = $(this).attr('data-link')
            window.open(openLink, '_blank')
        })

        $('.company-pane').on('click', '.site-item', function () {
            $(this).addClass('active').siblings().removeClass('active')

            companyComponentVue.areaId = []
            companyComponentVue.majorId = []
            companyComponentVue.jobTypeId = []
            companyComponentVue.companyTypeId = []

            specialActivityId = $(this).attr('data-specialId')
            activityId = $(this).attr('data-activityId')

            getSearchParams()
        })

        function getSearchParams() {
            httpGet('/special-activity/get-search-params', { specialActivityId, activityId }).then((resp) => {
                const { companyTabList, cityParams, typeParams, majorSelect, jobTypeList, currentActivityTab = {}, isShowSearchParams } = resp

                $('.company-pane .site-wrapper').html(companyTabList)

                companyComponentVue.areaList = cityParams
                companyComponentVue.majorList = majorSelect
                companyComponentVue.jobTypeList = jobTypeList
                companyComponentVue.companyTypeList = typeParams
                companyComponentVue.isShowCurrentActivityInfo = !!currentActivityTab.activityId
                companyComponentVue.currentActivityInfo = currentActivityTab
                companyComponentVue.isShowSearchParams = isShowSearchParams == 1

                companyComponentVue.formatTimeCountdown()
                companyComponentVue.handleSearchAgain()
            })
        }
        
        const companyComponent = {
            data() {
                return {
                    dialogActive: '',

                    areaId: [],
                    majorId: [],
                    jobTypeId: [],
                    companyTypeId: [],

                    isShowSearchParams: true,
                    areaList: <?= json_encode($companySearchParams['cityParams']) ?>,
                    jobTypeList: <?= json_encode($companySearchParams['jobTypeList']) ?>,
                    majorList: <?= json_encode($companySearchParams['majorSelect']) ?>,
                    companyTypeList: <?= json_encode($companySearchParams['typeParams']) ?>,

                    day: '',
                    hours: '',
                    min: '',
                    second: '',

                    timer: null,
                    isShowCurrentActivityInfo: false,
                    currentActivityInfo: {
                        // 剩余时间(秒)
                        startCountDown: 0,
                        name: '',
                        dateText: '',
                        addressText: '',
                        applyStatus: '',
                        // 1:待举办；2:即将开始；3:进行中；4:已结束
                        activityChildStatus: ''
                    }
                }
            },
            computed: {
                majorLabel() {
                    const { majorId } = this
                    const len = majorId.length

                    if (len) {
                        return `学科分类(${len})`
                    }
                    return ''
                },

                jobTypeLabel() {
                    const { jobTypeId } = this
                    const len = jobTypeId.length

                    if (len) {
                        return `职位类型(${len})`
                    }
                    return ''
                },

                companyTypeLabel() {
                    const { companyTypeId } = this
                    const len = companyTypeId.length

                    if (len) {
                        return `单位类型(${len})`
                    }
                    return '单位类型'
                },

                calcAreaList() {
                    const { areaId, areaList } = this
                    return this.getCalcDataList(areaId, areaList)
                },

                dialogTitle() {
                    const { dialogActive } = this
                    const options = {
                        majorList: '请选择学科',
                        jobTypeList: '请选择职位类型'
                    }

                    return options[dialogActive] || '请选择'
                },

                dialogPlaceholder() {
                    const { dialogActive } = this
                    const options = {
                        majorList: '请输入学科关键词',
                        jobTypeList: '请输入职位类型关键词'
                    }

                    return options[dialogActive] || '请输入关键词'
                },

                dialogList() {
                    const { dialogActive } = this
                    return this[dialogActive] || []
                },

                dialogName() {
                    return 'area'
                },

                dialogValueTextKey() {
                    const { dialogActive } = this
                    const options = {
                        jobTypeList: 'jobTypeId',
                        majorList: 'majorId'
                    }
                    const value = options[dialogActive]

                    if (value) {
                        return value
                    }
                    return ''
                },

                dialogValue: {
                    get() {
                        const valKey = this.dialogValueTextKey

                        if (valKey) {
                            return this[valKey]
                        }
                        return []
                    },
                    set(val) {
                        const valKey = this.dialogValueTextKey

                        if (valKey) {
                            this[valKey] = val
                        }
                    }
                }
            },

            mounted() {},

            methods: {
                formatTimeCountdown() {
                    clearTimeout(this.timer)
                    const {
                        currentActivityInfo: { startCountDown }
                    } = this

                    if(!startCountDown) return

                    const day = Math.floor(startCountDown / (24 * 3600))
                    const hours = Math.floor((startCountDown % (24 * 3600)) / 3600)
                    const min = Math.floor((startCountDown % 3600) / 60)
                    const second = startCountDown % 60

                    this.day = day
                    this.hours = hours
                    this.min = min
                    this.second = second

                    this.currentActivityInfo.startCountDown = startCountDown - 1

                    if (startCountDown - 1 === 0) {
                        getSearchParams()
                        return
                    }

                    this.timer = setTimeout(() => {
                        this.formatTimeCountdown()
                    }, 1000)
                },

                getCalcDataList(checkedList, dataList) {
                    const defaultChecked = checkedList.length ? checkedList : ['']

                    return dataList.map(function (item) {
                        const { name, id } = item
                        const checked = defaultChecked.includes(id)
                        return { name, id, checked }
                    })
                },

                handleSearchAgain(query = {}) {
                    const { areaId, majorId, jobTypeId, companyTypeId } = this
                    const { page, ...other } = query

                    if (!page) {
                        paginationComponentVue.initPage()
                    }

                    const param = { areaId: areaId.join(), majorId: majorId.join(), categoryId: jobTypeId.join(), type: companyTypeId.join(), ...other, specialActivityId,
                        activityId, page: page ? page : 1 }

                    httpGet('/special-activity/get-company-list', param).then((resp) => {
                        const { list, count, isEnd } = resp
                        const $companyContent = $('.company-pane .company-wrapper .company-content')
                        $companyContent.html(`
                            <div class="company-data">
                                ${list}
                            </div>`)
                        if (isEnd == 1) {
                            $companyContent.append('<div class="tips">更多单位持续更新中，敬请关注...</div>')
                        }

                        if (list == '') {
                            $companyContent.hide()
                            $('.company-pane .company-wrapper .empty').addClass('show')
                        } else {
                            $companyContent.show()
                            $('.company-pane .company-wrapper .empty').removeClass('show')
                        }

                        if (page) {
                            const headerHeight = $('.activity-header-container').height()
                            const tabSHeight = $('.tabs-header-content').height()

                            const offsetTop = $('.company-pane .filter-wrapper').offset().top

                            $('html, body').animate({
                                scrollTop: offsetTop - headerHeight - tabSHeight
                            })
                        }

                        const total = Number(count)

                        this.total = total

                        paginationComponentVue.count = total
                    })
                },

                handleCheck(key, data) {
                    const { name, id, limit } = data
                    const keyValue = this[key]
                    const _this = this

                    const index = keyValue.findIndex(function (item) {
                        return item === id
                    })
                    const size = limit || 5

                    let needFetchData = true

                    const validHandler = function () {
                        if (index > -1) {
                            keyValue.splice(index, 1)
                        } else {
                            if (keyValue.length < size) {
                                keyValue.push(id)
                            } else {
                                needFetchData = false
                                return ElementPlus.ElMessage.warning(`您最多可选${size}项`)
                            }
                        }
                        _this[key] = keyValue
                    }

                    const invalidHandler = function () {
                        _this[key] = []
                        _this.handleSearchAgain()
                    }

                    id ? validHandler() : invalidHandler()

                    needFetchData && this.handleSearchAgain()
                },

                handleDialogClear(name) {
                    const _this = this

                    this.$nextTick(function () {
                        _this[name] = []
                        _this.handleSearchAgain()
                    })
                },

                clearAll() {
                    this.areaId = []
                    this.majorId = []
                    this.jobTypeId = []
                    this.companyTypeId = []
                    this.handleSearchAgain()
                },

                handleDialogChange(data) {
                    this.handleSearchAgain()
                },

                handleDialogActive(name) {
                    this.dialogActive = name
                    this.$refs.selectDialogRef.handleOpen()
                }
            }
        }

        const companyComponentVue = Vue.createApp(companyComponent)
            .use(ElementPlus, {
                locale: {
                    name: 'zh-cn',
                    el: {
                        pagination: {
                            goto: '前往',
                            pagesize: '条/页',
                            total: '共 {total} 条',
                            pageClassifier: '页',
                            deprecationWarning: '你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档'
                        },
                        select: {
                            loading: '\u52A0\u8F7D\u4E2D',
                            noMatch: '\u65E0\u5339\u914D\u6570\u636E',
                            noData: '\u65E0\u6570\u636E',
                            placeholder: '\u8BF7\u9009\u62E9'
                        }
                    }
                }
            })
            .component('select-dialog', selectDialogComponent)
            .mount('#companyComponent')

        const paginationComponent = {
            data() {
                return {
                    page: 1,
                    count: <?=$companyList['count']?>
                }
            },
            mounted() {},
            methods: {
                initPage() {
                    this.page = 1
                },
                change(page) {
                    companyComponentVue.handleSearchAgain({ page })
                }
            }
        }

        const paginationComponentVue = Vue.createApp(paginationComponent)
            .use(ElementPlus, {
                locale: {
                    name: 'zh-cn',
                    el: {
                        pagination: {
                            goto: '前往',
                            pagesize: '条/页',
                            total: '共 {total} 条',
                            pageClassifier: '页',
                            deprecationWarning: '你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档'
                        }
                    }
                }
            })
            .mount('#paginationComponent')
    })
</script>