<link rel="stylesheet" href="/static/css/activityDetail.css"/>
<div class="banner-wrapper">
    <img src="<?=$activityInfo['imagePcBannerUrl']?>" alt=""/>
</div>
<div class="main-container" id="main-container">
    <div v-cloak v-if="currentActivityInfo.activityChildStatus == 2" class="countdown-content">
        <div class="countdown">
            距活动开始还有&nbsp;<span class="box day">{{day}}</span>&nbsp;天&nbsp;<span class="box hours">{{hours}}</span>&nbsp;时&nbsp;<span class="box min">{{min}}</span
        >&nbsp;分&nbsp;<span class="box second">{{second}}</span>&nbsp;秒
        </div>
    </div>

    <div :style="{height: currentActivityInfo.activityChildStatus == 2 ? '20px' : '60px'}"></div>

    <div class="detail-wrapper view-content">
        <?php if($activityInfo['specialName']){ ?>
        <div class="activity-connect">
            <div class="connect">
                <?php  foreach($activityInfo['specialName'] as  $key=>$item){ ?>
                <p>
                    <a href="<?=$item['url']?>" target="_blank" rel="noopener noreferrer"><?=$item['name']?>
                        <span>&gt;</span><?=($key+1)==count($activityInfo['specialName'])?'':'；'?></a>
                </p>
                <?php } ?>
            </div>
        </div>
        <?php } ?>
        <?php if($activityInfo['activityChildStatus']==1){ ?>
        <div class="status await">待举办</div>
        <?php }elseif($activityInfo['activityChildStatus']==2){ ?>
        <div class="status await-start">即将开始</div>
        <?php }elseif($activityInfo['activityChildStatus']==3){ ?>
        <div class="status start">进行中</div>
        <?php }else{ ?>
        <div class="status ended">已结束</div>
        <?php } ?>

        <div class="detail">
            <div class="tag">
                <span class="item yellow"><?=$activityInfo['toHoldTypeText']?></span>
                <?php  foreach($activityInfo['featureTagArray'] as $featureItem){ ?>
                <span class="item"><?=$featureItem?></span>
                <?php } ?>
                <?php  foreach($activityInfo['customFeatureTagArray'] as $customFeatureItem){ ?>
                <span class="item"><?=$customFeatureItem?></span>
                <?php } ?>
            </div>
            <h1 class="title"><?=$activityInfo['name']?></h1>
            <?php if($activityInfo['activityOrganization']){ ?>
            <div class="detail-item organization">
                <div class="label">活动组织：</div>
                <div class="value" title="<?=$activityInfo['activityOrganization']?>">
                    <?=$activityInfo['activityOrganization']?>
                </div>
            </div>
            <?php } ?>
            <div class="detail-item time">
                <div class="label">活动时间：</div>
                <div class="value"><?=$activityInfo['activityDateTime']?></div>
            </div>
            <div class="detail-item address">
                <div class="label"><?=$activityInfo['toHoldType'] == 1 ? '活动平台：' :'活动地点：'?></div>
                <div class="value"><?=$activityInfo['activityArea']?></div>
            </div>
            <div class="detail-item type">
                <div class="label">活动系列：</div>
                <div class="value"><?=$activityInfo['typeText']?></div>
            </div>
            <?php if($activityInfo['activityBenefits']){ ?>
            <div class="welfare has-detail">
                <div class="value"><?=$activityInfo['activityBenefits'] ?></div>
                <?php if($activityInfo['activityBenefitsContentFormat']){ ?>
                <div class="arrow open-dialog-btn" data-target="#welfareDialog">点击查看更多</div>
                <?php } ?>
            </div>
            <?php } ?>
        </div>

        <aside class="aside">
            <?php if($activityInfo['participationCompanyAmount']>10){ ?>
            <div class="update-status">持续更新中</div>
            <?php } ?>
            <div class="item">
                <div class="amount"><?=$activityInfo['participationCompanyAmount']<10?'更新中':$activityInfo['participationCompanyAmount'] ?></div>
                <span>参会单位</span>
            </div>
            <div class="item">
                <div class="amount"><?=$activityInfo['activityNumber']<100?'更新中':$activityInfo['activityNumber'] ?></div>
                <span>需求人数</span>
            </div>
        </aside>
    </div>
    <?php if($activityInfo['activityHighlights']){ ?>
    <div class="tips-wrapper view-content">
        <div class="tips-content">
            <div class="left">
                <div class="box">
                    <img src="/static/assets/activity/activityDetail/apply-enter.png" alt=""/>
                    <p><?=$activityInfo['activityHighlightsTitle']?:'参会须知' ?></p>
                </div>
            </div>
            <div class="right">
                <div class="text">
                    <?=$activityInfo['activityHighlights'] ?>
                </div>
            </div>
        </div>
    </div>
    <?php } ?>
    <?php if($companyHotList){ ?>
    <div class="hot-company view-content">
        <div class="tag">热门单位</div>
        <div class="swiper hot-company-swiper">
            <div class="swiper-wrapper">
                <?php  foreach($companyHotList as $hotItem){ ?>
                <a class="swiper-slide company-list" title="<?=$hotItem['companyName']?>" href="<?=$hotItem['linkUrl']?:'javascript:;'?>" target="<?=$hotItem['linkUrl']?'_blank':''?>">
                    <img class="cover" src="<?=$hotItem['companyLogo']?>" alt="<?=$hotItem['companyName']?>"/>
                    <p class="title"><?=$hotItem['companyName']?></p>
                    <p class="text"><?=$hotItem['companyNatureText']?$hotItem['companyNatureText'].'·':''?><?=$hotItem['companyTypeText']?></p>
                </a>
                <?php } ?>
            </div>
            <div class="swiper-pagination"></div>
        </div>
    </div>
    <?php } ?>

    <?php if($activityInfo['activityDetail'] || $activityInfo['participationMethod'] || $activityInfo['wonderfulReview'] || $companyParticipatingList['dataList']['count']>=2){ ?>
    <div class="tabs-wrapper view-content">
        <div class="tabs-header-wrapper is-fixe" id="page-change-fixed">
            <div class="tabs-header-content">
                <div class="tabs-header">
                    <?php if($activityInfo['activityDetail']){ ?>
                    <div class="tabs-nav tabs-switch-item active" open-pane="detail-pane">活动详情</div>
                    <?php } ?>
                    <?php if($companyParticipatingList['dataList']['count']>=2){ ?>
                    <div class="tabs-nav tabs-switch-item tabs-switch-item2 <?=empty($activityInfo['activityDetail']) ? 'active' : '' ?>" open-pane="company-pane">
                        参会单位
                        <div class="update-status">持续更新中</div>
                    </div>
                    <?php } ?>
                    <?php if($activityInfo['participationMethod']){ ?>
                    <div class="tabs-nav tabs-switch-item <?=empty($activityInfo['activityDetail'])&& $companyParticipatingList['dataList']['count']<2 ? 'active' :''?>" open-pane="apply-way-pane">参会方式</div>
                    <?php } ?>
                    <?php if($activityInfo['wonderfulReview']){ ?>
                    <div class="tabs-nav tabs-switch-item <?=empty($activityInfo['activityDetail']) && $companyParticipatingList['dataList']['count']<2 && empty($activityInfo['participationMethod']) ? 'active' :''?> " open-pane="review-pane">精彩回顾</div>
                    <?php } ?>
                    <div class="share-mini-code-container">
                        <div class="share-mini-code-trigger">微信扫码分享</div>

                        <div class="share-mini-code-popup">
                            <div class="share-mini-code">
                                <img src="<?=$miniShareLinkUrl?>" alt="" class="share-mini-code-img"/>
                            </div>
                            <div class="share-mini-code-title">微信扫一扫</div>
                            <div class="share-mini-code-tips">分享给你的朋友吧</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="tabs-content">
            <!-- 活动详情 -->
            <div class="tabs-pane detail-pane <?=$activityInfo['activityDetail']?'show':''?> <?= $activityInfo['templateId']==2?'advanced-template':'' ?>" >
                <?=$activityInfo['activityDetail'] ?>
            </div>

            <div class="tabs-pane company-pane <?=empty($activityInfo['activityDetail'])&& $companyParticipatingList['dataList']['count']>=2?'show':''?>">
                <div id="companyComponent">
                    <div v-if="calcAreaList.length || majorList.length || jobTypeList.length || companyTypeList.length" class="filter-wrapper">
                        <div v-if="calcAreaList.length" class="filter-list">
                            <div class="label">
                                所在地区
                                <div class="pop">仅展示参会单位所在省份及部分热门城市</div>
                            </div>
                            <div class="value">
                                    <span
                                            v-for="{ name, id, checked } in calcAreaList"
                                            class="item"
                                            :class="id ? '' : 'is-special'"
                                            :checked="checked"
                                            @click="handleCheck('areaId', { name, id })"
                                    >{{name}}</span
                                    >
                            </div>
                        </div>

                        <div v-if="majorList.length || jobTypeList.length || companyTypeList.length" class="filter-list">
                            <div class="label">更多筛选</div>
                            <div class="value">
                                <div v-if="majorList.length" class="el-select el-select--mini filter-item" :class="majorId.length ? 'is-checked' : ''">
                                    <div class="select-trigger">
                                        <div class="el-input el-input--mini el-input--suffix" @click="handleDialogActive('majorList')">
                                            <input class="el-input__inner" type="text" readonly="" autocomplete="off" placeholder="需求学科" v-model="majorLabel"/>
                                            <span class="el-input__suffix">
                                                            <span class="el-input__suffix-inner filter-major-clear" @click.stop="handleDialogClear('majorId')">
                                                                <i class="el-select__caret el-input__icon el-icon-circle-close"></i>
                                                            </span>
                                                            <span class="el-input__suffix-inner">
                                                                <i class="el-select__caret el-input__icon el-icon-arrow-up"></i>
                                                            </span>
                                                        </span>
                                        </div>
                                    </div>
                                </div>

                                <div v-if="jobTypeList.length" class="el-select el-select--mini filter-item" :class="jobTypeId.length ? 'is-checked' : ''">
                                    <div class="select-trigger">
                                        <div class="el-input el-input--mini el-input--suffix" @click="handleDialogActive('jobTypeList')">
                                            <input class="el-input__inner" type="text" readonly="" autocomplete="off" placeholder="职位类型" v-model="jobTypeLabel"/>
                                            <span class="el-input__suffix">
                                                            <span class="el-input__suffix-inner filter-major-clear" @click.stop="handleDialogClear('jobTypeId')">
                                                                <i class="el-select__caret el-input__icon el-icon-circle-close"></i>
                                                            </span>
                                                            <span class="el-input__suffix-inner">
                                                                <i class="el-select__caret el-input__icon el-icon-arrow-up"></i>
                                                            </span>
                                                        </span>
                                        </div>
                                    </div>
                                </div>

                                <div v-if="companyTypeList.length" class="filter-item company-type-item" :class="companyTypeId.length ? 'is-select' : ''">
                                    <div class="label">{{companyTypeLabel}}</div>

                                    <el-select
                                            popper-class="company-select"
                                            :class="companyTypeId.length ? 'is-checked' : ''"
                                            v-model="companyTypeId"
                                            placeholder=" "
                                            size="mini"
                                            multiple
                                            :multiple-limit="5"
                                            collapse-tags
                                            @change="handleSearchAgain"
                                            clearable
                                    >
                                        <el-option v-for="{ name, code } in companyTypeList" :label="name" :value="code"/>
                                    </el-select>
                                </div>
                            </div>
                            <div class="filter-aside">
                                <div class="clear-all" @click="clearAll">清空筛选条件</div>
                            </div>
                        </div>

                        <div class="tips">当前仅展示部分单位及招聘需求，欲知更多单位详情，请关注后续更新或现场了解</div>
                    </div>

                    <select-dialog
                            ref="selectDialogRef"
                            :title="dialogTitle"
                            :search-placeholder="dialogPlaceholder"
                            :list="dialogList"
                            :name="dialogName"
                            multiple
                            :multiple-limit="5"
                            v-model="dialogValue"
                            @update="handleDialogChange"
                    ></select-dialog>
                </div>

                <div class="company-wrapper">
                    <div class="company-content">
                        <div class="company-data">
                            <!-- 已报名：is-apply
                                置顶单位： is-top -->
                            <?=$companyParticipatingList['dataList']['list'] ?>
                        </div>
                        <?php if($companyParticipatingList['dataList']['isEnd'] == 1):?>
                        <div class="tips">更多单位持续更新中，敬请关注...</div>
                        <?php endif?>
                    </div>
                    <div class="pagination-cotnent" id="paginationComponent">
                        <el-pagination v-show="total>0" background layout="prev, pager, next" :page-size="18" @current-change="change" :total="total" v-model:current-page="page"/>
                    </div>
                    <div class="empty <?=$companyParticipatingList['dataList']['count']>0 ? '' : 'show' ?>">单位持续更新中，敬请关注...</div>
                </div>
            </div>

            <div class="tabs-pane apply-way-pane <?=empty($activityInfo['activityDetail'])&& $companyParticipatingList['dataList']['count']<2 && $activityInfo['participationMethod'] ? 'show' :''?>  <?= $activityInfo['templateId']==2?'advanced-template':'' ?>">
                <?=$activityInfo['participationMethod'] ?>
            </div>

            <div class="tabs-pane review-pane <?=empty($activityInfo['activityDetail'])&& $companyParticipatingList['dataList']['count']<2 &&  empty($activityInfo['participationMethod']) && $activityInfo['wonderfulReview']  ? 'show' :''?> <?= $activityInfo['templateId']==2?'advanced-template':'' ?>">
                <?=$activityInfo['wonderfulReview'] ?>
            </div>
        </div>
    </div>
    <?php } ?>

    <?php if($isRecommendActivity){ ?>
    <div class="connect-company view-content">
        <div class="hd-tag">相关场次推荐</div>
        <div class="company-wrapper scroll-wrapper">
            <div class="scroll-content">
                <?php  foreach($recommendActivityList as $recommendItem){ ?>
                <a class="company-list" title="<?=$recommendItem['name']?>" href="<?=$recommendItem['activityDetailUrl']?>" target="_blank">
                    <div class="hd">
                        <div class="tag <?='tag'.$recommendItem['activity_child_status'] ?>"><?=$recommendItem['activityChildStatusText']?></div>
                        <img class="cover" src="<?=$recommendItem['mainImgUrl']?>" alt="<?=$recommendItem['name']?>"/>
                        <?php if($recommendItem['activity_child_status']!=4){ ?>
                        <div class="hot"><?=$recommendItem['click']?></div>
                        <?php } ?>
                    </div>
                    <div class="bd">
                        <p class="title"><?=$recommendItem['name']?></p>
                        <div class="desc">
                            <div class="date"><?=$recommendItem['activityTime']?></div>
                            <div class="address"><?=$recommendItem['area']?></div>
                        </div>
                    </div>
                </a>
                <?php } ?>
            </div>
        </div>
        <?php if(count($recommendActivityList)>5){ ?>
        <div class="scrollbar">
            <div class="scrollbar-thumb"></div>
        </div>
        <?php } ?>
        <div class="more">
            <a href="/" target="_blank"><p>查看更多<span>&nbsp;&gt;</span></p></a>
        </div>
    </div>
    <?php } ?>
</div>

<!-- 福利详情dialog -->
<div class="el-overlay" id="welfareDialog" style="z-index: 2003">
    <div class="el-overlay-dialog">
        <div class="el-dialog dialog-center" aria-modal="true" role="dialog" aria-label="dialog">
            <div class="el-dialog__header">
                参会福利

                <button class="el-dialog__headerbtn close-dialog" type="button">
                    <i class="el-dialog__close el-icon el-icon-close"></i>
                </button>
            </div>

            <div class="el-dialog__body">
                <?=$activityInfo['activityBenefitsContentFormat']?>
            </div>

            <footer class="el-dialog__footer">
                <button class="close-dialog">我知道了</button>
            </footer>
        </div>
    </div>
</div>

<?php if($activityInfo['activityChildStatus']!=4 && ($activityInfo['applyStatus']!=3 || $activityInfo['applyLinkCompanyIsShow']!=2 || $activityInfo['imageServiceCodeId']!=0)){ ?>
<!-- 侧边报名入口 -->
<!-- applied 为已报名状态class -->
<!-- 如果是已报名，不允许跳转 onclick="return false" 且增加applied样式 -->
<div class="fixed-apply-enter <?=$activityInfo['applyStatus']==1?'applied':''?>">
    <?php if($activityInfo['applyStatus']==1){ ?>
    <a onclick="return false" class="apply-enter-item apply-enter-person" href="" target="_blank" rel="nofollow">人才报名</a>
    <?php }elseif($activityInfo['applyStatus']==2){ ?>
    <a class="apply-enter-item apply-enter-person" href="<?=$activityInfo['applyLink']?>" target="_blank" rel="nofollow">人才报名</a>
    <?php } ?>
    <?php if($activityInfo['applyLinkCompanyIsShow']==1){ ?>
    <a class="apply-enter-item apply-enter-company" href="<?=$activityInfo['applyLinkCompany']?>" target="_blank" rel="nofollow">单位报名</a>
    <?php } ?>
    <?php if($activityInfo['imageServiceCodeId']>0){ ?>
    <span class="apply-enter-item apply-enter-info">
        活动进群
        <div class="code-popup">
            <img src="<?=$activityInfo['imageServiceCodeUrl']?>" alt=""/>
        </div>
    </span>
    <?php } ?>
</div>
<?php } ?>
<!--修复富文本的图片问题-->
<script src="/static/js/fixRich.js"></script>
<script>
    $(function () {
        var activityId = <?=$activityInfo['id']?>

        const companyComponent = {
            data() {
                return {
                    dialogActive: '',

                    areaId: [],
                    majorId: [],
                    jobTypeId: [],
                    companyTypeId: [],
                    // areaList: [],
                    // majorList: [],
                    // jobTypeList: [],
                    // companyTypeList:[],
                    page: <?=$companyParticipatingList['dataList']['page']?>,
                    total: <?=$companyParticipatingList['dataList']['count']?>,
                    areaList: <?=json_encode($companyParticipatingList['searchList']['cityParams'])?>,
                    majorList: <?=json_encode($companyParticipatingList['searchList']['majorSelect'])?>,
                    jobTypeList: <?=json_encode($companyParticipatingList['searchList']['jobTypeList'])?>,
                    companyTypeList: <?=json_encode($companyParticipatingList['searchList']['companyTypeParams'])?>,

                    day: '',
                    hours: '',
                    min: '',
                    second: '',

                    timer: null,
                    isShowCurrentActivityInfo: true,
                    currentActivityInfo: {
                        // 剩余时间(秒)
                        startCountDown: <?=$activityInfo['activityCountDownTime']?>,
                        // 1:待举办；2:即将开始；3:进行中；4:已结束
                        activityChildStatus: <?=$activityInfo['activityChildStatus']?>
                    }
                }
            },
            computed: {
                majorLabel() {
                    const {majorId} = this
                    const len = majorId.length

                    if (len) {
                        return `学科分类(${len})`
                    }
                    return ''
                },

                jobTypeLabel() {
                    const {jobTypeId} = this
                    const len = jobTypeId.length

                    if (len) {
                        return `职位类型(${len})`
                    }
                    return ''
                },

                companyTypeLabel() {
                    const {companyTypeId} = this
                    const len = companyTypeId.length

                    if (len) {
                        return `单位类型(${len})`
                    }
                    return '单位类型'
                },

                calcAreaList() {
                    const {areaId, areaList} = this
                    return this.getCalcDataList(areaId, areaList)
                },

                dialogTitle() {
                    const {dialogActive} = this
                    const options = {
                        majorList: '请选择学科',
                        jobTypeList: '请选择职位类型'
                    }

                    return options[dialogActive] || '请选择'
                },

                dialogPlaceholder() {
                    const {dialogActive} = this
                    const options = {
                        majorList: '请输入学科关键词',
                        jobTypeList: '请输入职位类型关键词'
                    }

                    return options[dialogActive] || '请输入关键词'
                },

                dialogList() {
                    const {dialogActive} = this
                    return this[dialogActive] || []
                },

                dialogName() {
                    return 'area'
                },

                dialogValueTextKey() {
                    const {dialogActive} = this
                    const options = {
                        jobTypeList: 'jobTypeId',
                        majorList: 'majorId'
                    }
                    const value = options[dialogActive]

                    if (value) {
                        return value
                    }
                    return ''
                },

                dialogValue: {
                    get() {
                        const valKey = this.dialogValueTextKey

                        if (valKey) {
                            return this[valKey]
                        }
                        return []
                    },
                    set(val) {
                        const valKey = this.dialogValueTextKey

                        if (valKey) {
                            this[valKey] = val
                        }
                    }
                }
            },

            mounted() {
                this.formatTimeCountdown()
            },

            methods: {
                initPage() {
                    this.page = 1
                },

                change(page) {
                    this.handleSearchAgain({page})
                    scrollToTarget('page-change-fixed')
                },

                formatTimeCountdown() {
                    clearTimeout(this.timer)
                    const {
                        currentActivityInfo: {startCountDown,activityChildStatus}
                    } = this
                    const day = Math.floor(startCountDown / (24 * 3600))
                    const hours = Math.floor((startCountDown % (24 * 3600)) / 3600)
                    const min = Math.floor((startCountDown % 3600) / 60)
                    const second = startCountDown % 60

                    this.day = day
                    this.hours = hours
                    this.min = min
                    this.second = second

                    this.currentActivityInfo.startCountDown = startCountDown - 1

                    if (activityChildStatus == 2  && startCountDown === 0) {
                        window.location.reload()
                        return
                    }

                    this.timer = setTimeout(() => {
                        this.formatTimeCountdown()
                    }, 1000)
                },

                getCalcDataList(checkedList, dataList) {
                    const defaultChecked = checkedList.length ? checkedList : ['']

                    return dataList.map(function (item) {
                        const {name, id} = item
                        const checked = defaultChecked.includes(id)
                        return {name, id, checked}
                    })
                },

                handleSearchAgain(query = {}) {
                    const {areaId, majorId, jobTypeId, companyTypeId} = this
                    const {page, ...other} = query

                    if (!page) {
                        this.initPage()
                    }

                    const param = {
                        areaId: areaId.join(),
                        majorId: majorId.join(),
                        categoryId: jobTypeId.join(),
                        type: companyTypeId.join(),
                        ...other,
                        activityId,
                        page: page ? page : 1
                    }

                    httpGet('/activity/get-company-list', param).then((resp) => {
                        const {list, count, isEnd} = resp
                        const $companyContent = $('.company-pane .company-wrapper .company-content')
                        $companyContent.html(`
                                    <div class="company-data">
                                        ${list}
                                    </div>`)
                        if (isEnd == 1) {
                            $companyContent.append('<div class="tips">更多单位持续更新中，敬请关注...</div>')
                        }

                        if (list == '') {
                            $companyContent.hide()
                            $('.company-pane .company-wrapper .empty').addClass('show')
                        } else {
                            $companyContent.show()
                            $('.company-pane .company-wrapper .empty').removeClass('show')
                        }

                        this.total = Number(count)
                    })
                },

                handleCheck(key, data) {
                    const {name, id, limit} = data
                    const keyValue = this[key]
                    const _this = this

                    const index = keyValue.findIndex(function (item) {
                        return item === id
                    })
                    const size = limit || 5

                    let needFetchData = true

                    const validHandler = function () {
                        if (index > -1) {
                            keyValue.splice(index, 1)
                        } else {
                            if (keyValue.length < size) {
                                keyValue.push(id)
                            } else {
                                needFetchData = false
                                return ElementPlus.ElMessage.warning(`您最多可选${size}项`)
                            }
                        }
                        _this[key] = keyValue
                    }

                    const invalidHandler = function () {
                        _this[key] = []
                        _this.handleSearchAgain()
                    }

                    id ? validHandler() : invalidHandler()

                    needFetchData && this.handleSearchAgain()
                },

                handleDialogClear(name) {
                    const _this = this

                    this.$nextTick(function () {
                        _this[name] = []
                        _this.handleSearchAgain()
                    })
                },

                clearAll() {
                    this.areaId = []
                    this.majorId = []
                    this.jobTypeId = []
                    this.companyTypeId = []
                    this.handleSearchAgain()
                },

                handleDialogChange(data) {
                    this.handleSearchAgain()
                },

                handleDialogActive(name) {
                    this.dialogActive = name
                    this.$refs.selectDialogRef.handleOpen()
                }
            }
        }

        const companyComponentVue = Vue.createApp(companyComponent)
            .use(ElementPlus, {
                locale: {
                    name: 'zh-cn',
                    el: {
                        pagination: {
                            goto: '前往',
                            pagesize: '条/页',
                            total: '共 {total} 条',
                            pageClassifier: '页',
                            deprecationWarning: '你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档'
                        },
                        select: {
                            loading: '\u52A0\u8F7D\u4E2D',
                            noMatch: '\u65E0\u5339\u914D\u6570\u636E',
                            noData: '\u65E0\u6570\u636E',
                            placeholder: '\u8BF7\u9009\u62E9'
                        }
                    }
                }
            })
            .component('select-dialog', selectDialogComponent)
            .mount('#main-container')

        var $headerTabs = $('.tabs-header-wrapper')
        var $headerTabsItem = $headerTabs.find('.tabs-switch-item')

        var $openDialogBtn = $('.open-dialog-btn')
        var $closeDialog = $('.close-dialog')

        $(window).on('scroll', function () {
            var scrollTop = $(this).scrollTop()
            var offsetTop = $headerTabs.offset().top
            const isFixed = scrollTop >= offsetTop
            isFixed ? $headerTabs.addClass('is-fixed') : $headerTabs.removeClass('is-fixed')
        })

        var swiper = new Swiper('.hot-company-swiper', {
            slidesPerView: 5,
            spaceBetween: 20,
            slidesPerGroup: 5,
            loop: <?=count($companyHotList) > 5 ? '1' : '0'?>,
            autoplay: <?=count($companyHotList) > 5 ? '1' : '0'?>,
            autoplay: {
                delay: 5000
            },
            loopFillGroupWithBlank: true,
            pagination: {
                el: '.hot-company-swiper .swiper-pagination',
                clickable: true
            }
        })

        var activeTab = 'detail-pane'

        $headerTabsItem.on('click', function () {
            var target = $(this).attr('open-pane')

            if (activeTab === target) return

            activeTab = target

            if (target !== 'company-pane') {
                new WOW({
                    boxClass: 'wow',
                    animateClass: 'animated',
                    offset: 100,
                    mobile: true,
                    live: true
                }).init()
            }

            $(`.${target}`).addClass('show').siblings('.tabs-pane').removeClass('show')
            $(this).addClass('active').siblings().removeClass('active')

            scrollToTarget('page-change-fixed')
        })

        $openDialogBtn.on('click', function () {
            $('body').addClass('el-popup-parent--hidden')
            var target = $(this).attr('data-target')
            $(target).fadeIn()
        })

        $closeDialog.on('click', function () {
            $('body').removeClass('el-popup-parent--hidden')
            $(this).parents('.el-overlay').fadeOut()
        })

        // 检查 URL 参数
        const urlParams = new URLSearchParams(window.location.search)
        const showTab = urlParams.get('showTab')

        if (showTab === 'review') {
            $headerTabsItem.filter('[open-pane=review-pane]').click()
        }

        const wrapper = document.querySelector('.scroll-wrapper')
        const content = document.querySelector('.scroll-content')
        const scrollbar = document.querySelector('.scrollbar')
        const thumb = document.querySelector('.scrollbar-thumb')

        if (wrapper && content && scrollbar && thumb) {
            const wrapperWidth = wrapper.offsetWidth
            const contentWidth = content.scrollWidth

            // 动态更新滚动条和滑块的宽度
            function updateScrollbar() {
                const scrollbarWidth = scrollbar.offsetWidth
                const thumbWidth = scrollbarWidth / 2 // 滑块宽度是滚动条宽度的一半
                thumb.style.width = `${thumbWidth}px`

                updateThumbPosition()
            }

            // 更新滚动条滑块位置
            function updateThumbPosition() {
                const scrollLeft = wrapper.scrollLeft
                const maxScrollLeft = contentWidth - wrapperWidth
                const scrollbarWidth = scrollbar.offsetWidth
                const thumbWidth = thumb.offsetWidth
                const thumbLeft = (scrollLeft / maxScrollLeft) * (scrollbarWidth - thumbWidth)
                thumb.style.left = `${thumbLeft}px`
            }

            // 拖动滑块
            let isDragging = false
            let startX = 0

            thumb.addEventListener('mousedown', (e) => {
                e.preventDefault()
                isDragging = true
                startX = e.pageX - thumb.offsetLeft

                function onMouseMove(e) {
                    if (!isDragging) return

                    let moveX = e.pageX - startX
                    if (moveX < 0) moveX = 0
                    if (moveX > scrollbar.offsetWidth - thumb.offsetWidth) moveX = scrollbar.offsetWidth - thumb.offsetWidth

                    thumb.style.left = `${moveX}px`

                    // 更新内容的滚动位置
                    const maxScrollLeft = contentWidth - wrapperWidth
                    wrapper.scrollLeft = (moveX / (scrollbar.offsetWidth - thumb.offsetWidth)) * maxScrollLeft
                }

                function onMouseUp() {
                    isDragging = false
                    document.removeEventListener('mousemove', onMouseMove)
                    document.removeEventListener('mouseup', onMouseUp)
                }

                document.addEventListener('mousemove', onMouseMove)
                document.addEventListener('mouseup', onMouseUp)
            })

            // 监听容器滚动
            wrapper.addEventListener('scroll', updateThumbPosition)

            // 初始化时更新滚动条宽度和滑块位置
            updateScrollbar()

            // 监听窗口大小变化，以便动态更新
            window.addEventListener('resize', updateScrollbar)
        }


        $('.company-pane').on('click', '.company-data .item .apply', function (e) {
            const link = $(this).attr('data-link')
            e.preventDefault()
            e.stopPropagation()
            window.open(link, '_blank')
        })
    })
</script>