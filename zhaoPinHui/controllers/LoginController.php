<?php
/**
 * create user：shannon
 * create time：2024/9/14 上午9:00
 */
namespace zhaoPinHui\controllers;

use common\base\models\BaseBuriedPointLog;
use common\base\models\BaseMember;
use common\base\models\BaseMemberLoginForm;
use common\helpers\IpHelper;
use common\libs\Captcha;
use common\libs\SmsQueue;
use common\libs\WxMiniApp;
use common\service\messageCenter\MessageCenterApplication;
use frontendPc\models\MemberLoginForm;
use Yii;
use yii\web\Response;

class LoginController extends BaseZhaoPinHuiController
{
    /**
     * 新增弹窗统计个数
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAddLoginPopTipsAmount()
    {
        try {
            $memberId = Yii::$app->user->id;
            if ($memberId) {
                return $this->success();
            }
            $ip     = IpHelper::getIpInt();
            $amount = BaseMemberLoginForm::getDailyLoginTipsAmount($ip);

            BaseMemberLoginForm::addDailyLoginTipsAmount($ip, $amount + 1);

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取图形验证码的配置
     * @return \yii\console\Response|Response
     */
    public function actionGetCaptchaConfig()
    {
        $config = Yii::$app->params['tencentCloud'];

        return $this->success(['captchaAppId' => $config['captcha']['CaptchaAppId']]);
    }

    /**
     * 账号密码登录
     * @return \yii\console\Response|Response
     */
    public function actionAccountLogin()
    {
        $account    = Yii::$app->request->post('account');
        $password   = Yii::$app->request->post('password');
        $type       = Yii::$app->request->post('type');
        $redirect   = Yii::$app->request->post('redirect', '/job');
        $isNeedStep = Yii::$app->request->post('isNeedStep', '1');
        $token      = Yii::$app->request->post('token');

        $loginForm = new BaseMemberLoginForm();

        $loginForm->account  = $account;
        $loginForm->password = $password;
        $loginForm->type     = $type;

        // 这里先做一个图形校验
        // $ticket  = Yii::$app->request->post('ticket');
        // $randStr = Yii::$app->request->post('randstr');
        // if (!$randStr || !$ticket) {
        //     return $this->fail('图形验证码验证失败');
        // }
        //
        // $captcha = new Captcha();
        // if (!$captcha->check($ticket, $randStr)) {
        //     return $this->fail('图形验证码验证失败');
        // }

        //是否绑定0不是1是绑定，绑定则一定要有token参数
        if ($type == BaseMember::TYPE_COMPANY && $token) {
            if (empty($token)) {
                return $this->fail('扫码失效，请重新扫码');
            }
            $loginForm->token  = $token;
            $loginForm->isBind = true;
        }

        try {
            $data                = $loginForm->accountLogin();
            $data['redirectUrl'] = BaseMemberLoginForm::getRedirectUrl($data['resumeStep'], $redirect, $isNeedStep);
            //写入日志
            if ($type == BaseMember::TYPE_PERSON) {
                $logData = [
                    'account' => $account,
                ];
                BaseBuriedPointLog::dataLog($logData, BaseBuriedPointLog::ACTION_TYPE_SUBMIT_ACCOUNT_DATA);
            }

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 验证登录验证码
     * @return \yii\console\Response|Response
     */
    public function actionValidateMobileLoginCode()
    {
        $mobile     = Yii::$app->request->post('mobile');
        $mobileCode = Yii::$app->request->post('mobileCode') ?: BaseMemberLoginForm::DEFAULT_MOBILE_CODE;
        $code       = Yii::$app->request->post('code');
        $type       = Yii::$app->request->post('type', BaseMember::TYPE_PERSON);
        $redirect   = Yii::$app->request->post('redirect', '/job');
        $isNeedStep = Yii::$app->request->post('isNeedStep', '1');
        $token      = Yii::$app->request->post('token');
        //是否绑定0不是1是绑定，绑定则一定要有token参数

        $loginForm = new BaseMemberLoginForm();

        $loginForm->mobileCode = $mobileCode;
        $loginForm->mobile     = $mobile;
        $loginForm->code       = $code;
        $loginForm->loginType  = BaseMemberLoginForm::LOGIN_TYPE_MOBILE;
        $loginForm->type       = $type;
        //是单位且绑定，那么就要验证token不为空
        if ($type == BaseMember::TYPE_COMPANY && $token) {
            if (empty($token)) {
                return $this->fail('扫码失效，请重新扫码');
            }
            $loginForm->token  = $token;
            $loginForm->isBind = true;
        }

        try {
            $data                = $loginForm->validateMobileCode();
            $data['redirectUrl'] = BaseMemberLoginForm::getRedirectUrl($data['resumeStep'], $redirect, $isNeedStep);
            //写入日志
            if ($type == BaseMember::TYPE_PERSON) {
                $logData = [
                    'mobile'     => $mobile,
                    'mobileCode' => $mobileCode,
                    'code'       => $code,
                ];
                BaseBuriedPointLog::dataLog($logData, BaseBuriedPointLog::ACTION_TYPE_SUBMIT_MOBILE_DATA);
            }

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取登录的验证码
     * @return \yii\console\Response|Response
     */
    public function actionSendMobileLoginCode()
    {
        $mobile     = Yii::$app->request->post('mobile');
        $mobileCode = Yii::$app->request->post('mobileCode');
        $type       = Yii::$app->request->post('type', BaseMember::TYPE_PERSON);
        // 这里先做一个图形校验
        $ticket  = Yii::$app->request->post('ticket');
        $randStr = Yii::$app->request->post('randstr');
        if (!$randStr || !$ticket) {
            return $this->fail('图形验证码验证失败');
        }

        $captcha = new Captcha();
        if (!$captcha->check($ticket, $randStr)) {
            return $this->fail('图形验证码验证失败');
        }

        $loginForm = new BaseMemberLoginForm();

        $loginForm->mobileCode = $mobileCode;
        $loginForm->mobile     = $mobile;
        $loginForm->loginType  = BaseMemberLoginForm::LOGIN_TYPE_MOBILE;
        $loginForm->type       = $type;
        $loginForm->smsType    = SmsQueue::TYPE_LOGIN;

        try {
            $loginForm->sendMobileCode();
            //写入日志
            if ($type == BaseMember::TYPE_PERSON) {
                $logData = [
                    'mobile'     => $mobile,
                    'mobileCode' => $mobileCode,
                ];
                BaseBuriedPointLog::dataLog($logData, BaseBuriedPointLog::ACTION_TYPE_SEND_CODE_DATA);
            }

            return $this->success('验证码已发送，请注意查收');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 创建小程序登录二维码
     * @return \yii\console\Response|Response
     */
    public function actionGetMiniLoginQrcode()
    {
        try {
            $app  = WxMiniApp::getInstance();
            $info = $app->createLoginQrCode();

            return $this->success($info);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 检查小程序登录二维码状态
     * @return \yii\console\Response|Response
     */
    public function actionCheckMiniLoginQrcode()
    {
        $scene      = Yii::$app->request->post('scene');
        $redirect   = Yii::$app->request->post('redirect', '');
        $isNeedStep = Yii::$app->request->post('isNeedStep', '1');

        try {
            $app    = WxMiniApp::getInstance();
            $result = $app->checkLoginQrCodeStatus($scene);

            if ($result['status'] == WxMiniApp::LOGIN_QRCODE_STATUS_LOGIN) {
                $loginForm                         = new MemberLoginForm();
                $loginForm->memberId               = $result['memberId'];
                $result['userInfo']                = $loginForm->loginByWxScan();
                $result['userInfo']['redirectUrl'] = BaseMemberLoginForm::getRedirectUrl($result['userInfo']['resumeStep'],
                    $redirect, $isNeedStep);
                //这里调用一下微信消息通知,逻辑要改
                $resumeId = BaseMember::getMainId($result['memberId']);
                (new MessageCenterApplication())->wxSignIn($resumeId);

                //写入日志
                BaseBuriedPointLog::dataLog($result, BaseBuriedPointLog::ACTION_TYPE_SCAN);
            }

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}