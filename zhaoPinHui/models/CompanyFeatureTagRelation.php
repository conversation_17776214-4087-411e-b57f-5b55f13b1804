<?php

namespace zhao<PERSON>inHui\models;

use common\base\models\BaseCompanyFeatureTag;
use common\base\models\BaseCompanyFeatureTagRelation;

class CompanyFeatureTagRelation extends BaseCompanyFeatureTagRelation
{

    /**
     * 获取pi团队的公司id
     * @param array $companyIds
     * @return array
     */
    public static function getPICompanyByCompanyIds($companyIds)
    {
        return self::find()
            ->alias('tr')
            ->select([
                'tr.company_id',
                't.tag',
            ])
            ->innerJoin(['t' => BaseCompanyFeatureTag::tableName()], 't.id = tr.feature_tag_id')
            ->filterWhere([
                'in',
                'tr.company_id',
                $companyIds,
            ])
            ->andFilterWhere([
                'tr.feature_tag_id' => BaseCompanyFeatureTag::PI_TAG_ID,
            ])
            ->asArray()
            ->all();
    }

}