$(function () {
    var $customSelect = $('.custom-select')

    // 发布时间start
    $customSelect.click(function (e) {
        e.stopPropagation()
        $(this).toggleClass('active')
    })

    $customSelect.on('click', '.el-icon-circle-close', function (e) {
        e.stopPropagation()
    })

    $customSelect.on('mouseenter', '.el-select', function () {
        const isCustomSelect = $customSelect.hasClass('is-select')
        if (isCustomSelect) {
            $customSelect.find('.el-icon-circle-close').show().siblings('.down').hide()
        }
    })

    $customSelect.on('mouseleave', '.el-select', function () {
        $customSelect.find('.el-icon-circle-close').hide().siblings('.down').show()
    })

    $('body').on('click', function () {
        const isCustomSelectActive = $customSelect.hasClass('active')
        if (isCustomSelectActive) {
            $customSelect.toggleClass('active')
        }
    })
    // 发布时间end

    // 筛选弹框 start
    $('.dialog-target').on('click', function () {
        $('body').addClass('el-popup-parent--hidden')
        var target = $(this).attr('data-target')
        $(target).fadeIn()
    })

    $('.close-dialog').on('click', function () {
        $('body').removeClass('el-popup-parent--hidden')
        $(this).parents('.el-overlay').fadeOut()
    })
    $('html body').on('click', '.el-dialog .left-content .list', function () {
        $(this).addClass('active').siblings().removeClass('active')

        var index = $(this).index()
        $(this).parents('.el-dialog').find('.second-content').eq(index).addClass('show').siblings().removeClass('show')
    })
    // 筛选弹框 end
})

/**
 * 过滤值为空的key
 * @param {object} obj
 * @returns
 */

function filterEmptyKeys(obj) {
    return Object.keys(obj)
        .filter((key) => {
            const v = obj[key]
            return v != null && v !== '' && (typeof v !== 'object' || Object.keys(v).length > 0)
        })
        .reduce((result, key) => {
            result[key] = obj[key]
            return result
        }, {})
}

/**
 *
 * @param {object} query
 */

function updateHref(query) {
    const { origin, pathname, search } = window.location

    const queryString = search.substring(1)
    const params = queryString.split('&')

    const paramMap = {}
    for (const param of params) {
        const [key, value] = param.split('=')
        if (key) {
            paramMap[key] = value
        }
    }

    Object.keys(query).forEach((k) => {
        const v = query[k]
        paramMap[k] = v
    })

    const realQuery = filterEmptyKeys(paramMap)

    var newSearch = ''
    var href = ''
    const { length } = Object.keys(realQuery)

    if (length) {
        Object.keys(realQuery).forEach((key) => {
            newSearch += `${key}=${paramMap[key]}&`
        })
        const { length: newSearchLength } = newSearch
        newSearch = newSearch.substring(0, newSearchLength - 1)
        href = `${origin}${pathname}?${newSearch}`
    } else {
        href = `${origin}${pathname}`
    }
    window.location.href = href
}
