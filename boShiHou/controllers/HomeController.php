<?php
/**
 * create user：shannon
 * create time：2024/9/14 上午9:39
 */
namespace boShiHou\controllers;

use common\base\models\BaseMember;
use common\base\models\BaseResumeComplete;
use common\helpers\UrlHelper;
use common\libs\BaiduTimeFactor;
use common\libs\ToutiaoTimeFactor;
use common\service\boShiHouColumn\GuessLikeService;
use common\service\boShiHouColumn\HomeService;
use common\service\CommonService;
use frontendPc\models\Member;

class HomeController extends BaseBoShiHouController
{
    public function actionIndex()
    {
        $result = (new HomeService())->setPlatform(CommonService::PLATFORM_WEB)
            ->run();

        $this->setSeo($result['seo']);

        BaiduTimeFactor::create('', $result['latestAnnouncement']['refresh_time']);
        ToutiaoTimeFactor::create('', $result['latestAnnouncement']['refresh_time']);

        return $this->render('index.html', ['data' => $result]);
    }

    public function actionLoadGuessLike()
    {
        $list = (new GuessLikeService())->getList();

        // 这里根据情况来显示
        $memberId = \Yii::$app->user->id;

        $userStatus = Member::USER_STATUS_UN_LOGIN;
        if (!empty($memberId)) {
            $userStatus = Member::getUserResumeStatus($memberId);
        }

        $content = $this->renderPartial('guess_like.html', [
            'list'       => $list,
            //  0未登录  1完善简历  2未完善简历
            'userStatus' => $userStatus,
            'url'        => UrlHelper::createPcResumeStepUrl(BaseResumeComplete::getResumeStep(BaseMember::getMainId())),
        ]);

        return $this->success(['content' => $content]);
    }

    public function actionError()
    {
    }
}