<?php

namespace boShiHou\controllers;

use common\base\controllers\BaseConfigController;
use common\base\models\BaseDictionary;
use common\base\models\BaseHomePosition;
use common\base\models\BaseResume;
use common\base\models\BaseShowcase;
use common\helpers\ArrayHelper;
use Yii;

class ConfigController extends BaseBoShiHouController
{
    use BaseConfigController;

    /**
     * pc端获取弹窗显示信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetPrivate()
    {
        return $this->success([
            'private'        => Yii::$app->params['private'],
            'privateTitle'   => Yii::$app->params['privateTitle'],
            'privateMessage' => Yii::$app->params['privateMessage'],
        ]);
    }

    // /**
    //  * 添加二级栏目、公告资讯检索页广告位
    //  * @throws Exception
    //  * @throws \yii\base\Exception
    //  */
    // public function actionCarryColumnHotToPosition()
    // {
    //     try {
    //
    //
    //         return $this->success();
    //     } catch (Exception $e) {
    //         return $this->fail($e->getMessage());
    //     }
    // }

    /**
     * 拿登录注册的广告
     */
    public function actionGetLoginRegister()
    {
        try {
            $tapList = [
                'register' => [
                    'wangzhanzhuce_bg',
                    'wangzhanzhuce_ad',
                ],
                'login'    => [
                    'wangzhandenglu_bg',
                    'wangzhandenglu_ad',
                ],
            ];

            $showcaseList = [];
            foreach ($tapList as $key => $item) {
                $temp = [];
                foreach ($item as $value) {
                    $homePosition = BaseHomePosition::findOne(['number' => $value]);
                    $positionId   = $homePosition['id'];
                    $temp         = array_merge($temp, BaseShowcase::getByPositionConfig($positionId, $value, 1));
                }
                $showcaseList[$key] = $temp;
            }

            return $this->success($showcaseList);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取单位经验要求列表
     */
    public function actionGetCompanyExperienceList()
    {
        $list    = BaseDictionary::getExperienceList();
        $list[1] = BaseResume::IDENTITY_TYPE_GRADUATE_NAME;

        return $this->success(ArrayHelper::obj2Arr($list));
    }
}