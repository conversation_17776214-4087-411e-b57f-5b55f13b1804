<link rel="stylesheet" href="/static/css/postdoctorHome.css"/>


<div class="main-container">
    <div class="top-wrapper">
        <div class="swiper swiper-t1">
            <div class="swiper-wrapper">
                <?php foreach($data['showcaseT1'] as $item){ ?>
                <a class="swiper-slide showcase-browse" data-showcase-number="<?= $item['number'] ?>" data-showcase-id="<?= $item['id'] ?>" href="<?=$item['url']?>" target="_blank" >
                    <img src="<?=$item['image']?>" alt="<?=$item['title']?>"/>
                </a>
                <?php } ?>
            </div>
            <div class="swiper-pagination"></div>
        </div>
        <?php foreach($data['showcaseT2'] as $item){ ?>
        <a class="middle showcase-browse" data-showcase-number="<?= $item['number'] ?>" data-showcase-id="<?= $item['id'] ?>" href="<?=$item['url']?>" target="_blank">
            <img src="<?=$item['image']?>" alt="<?=$item['title']?>"/>
        </a>
        <?php } ?>
        <div class="aside">
            <?php foreach($data['showcaseT3'] as $item){ ?>
            <a href="<?=$item['url']?>" target="_blank" class="showcase-browse" data-showcase-number="<?= $item['number'] ?>" data-showcase-id="<?= $item['id'] ?>">
                <img src="<?=$item['image']?>" alt="<?=$item['title']?>"/>
            </a>
            <?php } ?>
        </div>
    </div>

    <div class="headline-wrapper module" module-name="博后头条">
        <div class="wrapper-title">
            <a href="<?=$data['showcaseTouTiao']['url']?>" class="<?=$data['showcaseTouTiao']['showcase'] ?  'showcase-browse' : ''?>" data-showcase-number="<?= $data['showcaseTouTiao']['showcase']['number'] ?? '' ?>" data-showcase-id="<?= $data['showcaseTouTiao']['showcase']['id'] ?? '' ?>" target="_blank" title="<?=$data['showcaseTouTiao']['name']?>"><?=$data['showcaseTouTiao']['name']?></a>
        </div>
        <div class="content">
            <?php foreach($data['announcementTouTiao'] as $item){ ?>
            <a class="list <?=$item['showcase'] ?  'showcase-browse' : ''?>" data-showcase-number="<?= $item['showcase']['number'] ?? '' ?>" data-showcase-id="<?= $item['showcase']['id'] ?? '' ?>" title="<?=$item['name']?>" href="<?=$item['url']?>"  target="_blank" ><?=$item['name']?></a>
            <?php } ?>
        </div>
    </div>

    <?php if ($data['showcaseA1']): ?>
    <div class="recommend-company-wrapper module" module-name="推荐单位">
        <?php foreach($data['showcaseA1'] as $item){ ?>
        <a class="list animation-mouseover showcase-browse" data-showcase-number="<?= $item['number'] ?>" data-showcase-id="<?= $item['id'] ?>" href="<?=$item['url']?>" title="<?= $item['title'] . $item['subTitle'] ?>" target="_blank">
            <img src="<?=$item['image']?>" alt="<?=$item['title'].$item['subTitle']?>"/>
            <div class="aside">
                <div class="title"><?=$item['subTitle']?></div>
            </div>
        </a>
        <?php } ?>
    </div>
    <?php endif; ?>

    <?php if ($data['showcasePI']): ?>
    <div class="pi-wrapper gray-bg module" module-name="PI直招专区">
        <div class="view-content">
            <div class="common-title">
                <span class="title-fill">PI直招</span>
                <span class="title-suffix">专区</span>
                <span class="title-english">Principal Investigator Zone</span>
            </div>
            <div class="content">
                <?php foreach($data['showcasePI'] as $item){ ?>
                <a class="list animation-mouseover showcase-browse" data-showcase-number="<?= $item['number'] ?>" data-showcase-id="<?= $item['id'] ?>" href="<?=$item['url']?>" title="<?=$item['title']?>" target="_blank">
                    <div class="info">
                        <img src="<?=$item['image']?>" alt="<?=$item['title']?>"/>
                        <div class="title"><?=$item['title']?></div>
                    </div>
                    <div class="bottom">
                        <?php if ($item['subTitle']): ?>
                        <span class="icon"></span>
                        <?php endif; ?>
                        <?=$item['subTitle']?>
                    </div>
                </a>
                <?php } ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="hot-recommend-wrapper module" module-name="博后热招推荐">
        <div class="view-content">
            <div class="common-title">
                <span class="title-suffix">博后</span>
                <span class="title-fill">热招</span>
                <span class="title-suffix">推荐</span>
                <span class="title-english">Featured Recruitment</span>
            </div>
            <div class="content">
                <div class="tabs-content">
                    <?php foreach ($data['showcaseK1']['specialtyList'] as $key => $specialty): ?>
                    <div class="tabs">
                        <div class="name <?= $key === 0 ? 'active' : '' ?>" style="background-image: url('<?= $specialty['icon'] ?>')">
                            <?= $specialty['name'] ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <div class="tabs-pane-content">
                    <?php foreach ($data['showcaseK1']['showcaseList'] as $index => $showcases): ?>
                    <?php if ($index == 0): ?>
                    <div class="tabs-pane <?= $index==0 ? 'show': '' ?>">
                        <a class="top-view-more" href="<?= $data['showcaseK1']['specialtyList'][$index]['moreUrl'] ?>" target="_blank">查看更多 ></a>
                        <?php if (!empty($showcases)): ?>
                        <div class="swiper hot-recommend-swiper">
                            <div class="swiper-wrapper">
                                <?php foreach ($showcases as $showcaseList): ?>
                                <div class="swiper-slide pane-content">
                                    <?php foreach ($showcaseList as $showcase): ?>
                                    <a class="list animation-mouseover <?= $showcase['isShowcase'] ? 'showcase-browse' : ''?>" data-showcase-number="<?= $showcase['number'] ?? '' ?>" data-showcase-id="<?= $showcase['id'] ?? '' ?>"  href="<?= $showcase['url'] ?>" target="_blank" title="<?= $showcase['companyName'] . $showcase['subTitle'] ?>">
                                        <img class="logo" src="<?= $showcase['companyLogo'] ?>" alt="<?= $showcase['companyName'] ?>"/>
                                        <div class="name"><?= $showcase['companyName'] ?></div>
                                        <div class="type"><?= implode(' | ', array_filter([$showcase['companyType'], $showcase['companyNature']])) ?></div>
                                        <div class="mask">
                                            <div class="mask-content">
                                                <?php if (!empty($showcase['subTitle'])): ?>
                                                <div class="title"><?=$showcase['subTitle']?></div>
                                                <?php endif; ?>
                                                <span>查看详情</span>
                                            </div>
                                        </div>
                                    </a>
                                    <?php endforeach; ?>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="swiper-pagination"></div>
                        </div>
                        <?php else: ?>
                        <div class="empty-content">暂无相关推荐，请修改筛选条件试试</div>
                        <?php endif; ?>
                        <div class="bottom">
                            <a class="view-more" href="<?= $data['showcaseK1']['specialtyList'][$index]['moreUrl'] ?>" target="_blank">查看更多</a>
                        </div>
                    </div>
                    <?php else: ?>
                    <div class="tabs-pane">
                        <a class="top-view-more" href="<?= $data['showcaseK1']['specialtyList'][$index]['moreUrl'] ?>" target="_blank">查看更多 ></a>
                        <?php if (!empty($showcases)): ?>
                        <div class="pane-content">
                            <?php foreach ($showcases as $showcase): ?>
                            <a class="list animation-mouseover <?= $showcase['isShowcase'] ?'showcase-browse' : ''?>" data-showcase-number="<?= $showcase['number'] ??'' ?>" data-showcase-id="<?= $showcase['id'] ?? '' ?>"  href="<?= $showcase['url'] ?>" target="_blank" title="<?= $showcase['companyName'] . $showcase['subTitle'] ?>">
                                <img class="logo" src="<?= $showcase['companyLogo'] ?>" alt="<?= $showcase['companyName'] ?>"/>
                                <div class="name"><?= $showcase['companyName'] ?></div>
                                <div class="type"><?= implode(' | ', array_filter([$showcase['companyType'], $showcase['companyNature']])) ?></div>
                                <div class="mask">
                                    <div class="mask-content">
                                        <?php if (!empty($showcase['subTitle'])): ?>
                                        <div class="title"><?=$showcase['subTitle']?></div>
                                        <?php endif; ?>
                                        <span>查看详情</span>
                                    </div>
                                </div>
                            </a>
                            <?php endforeach; ?>
                        </div>
                        <?php else: ?>
                        <div class="empty-content">暂无相关推荐，请修改筛选条件试试</div>
                        <?php endif; ?>
                        <div class="bottom">
                            <a class="view-more" href="<?= $data['showcaseK1']['specialtyList'][$index]['moreUrl'] ?>" target="_blank">查看更多</a>
                        </div>
                    </div>
                    <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="announcement-wrapper module" module-name="博后公告热榜">
        <div class="view-content">
            <div class="common-title">
                <div class="title"></div>
                <span class="title-english">Hot Postdoctoral Announcements</span>
            </div>

            <button class="refresh">换一批</button>

            <div class="content">
                <?php foreach ($data['announcementHot'] as $key => $announcementHots): ?>
                <div class="announcement-content <?=($key == 0) ? 'active': ''?>">
                    <?php foreach ($announcementHots as $announcement): ?>
                    <a class="list" href="<?= $announcement['url'] ?>" title="<?= $announcement['title'] ?>" target="_blank">
                        <div class="title">
                            <?= $announcement['title'] ?>
                        </div>
                        <div class="time"><?= $announcement['refresh_time'] ?></div>
                    </a>
                    <?php endforeach; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <div class="new-release-wrapper module" module-name="最新发布">
        <div class="view-content">
            <div class="common-title">
                <span class="title-fill">最新</span>
                <span class="title-suffix">发布</span>
                <span class="title-english">Recent Postdoctoral Announcements</span>
            </div>

            <div class="content">
                <div class="tabs-content">
                    <?php foreach ($data['announcementNew']['title'] as $key => $announcementNew): ?>
                    <div class="tabs <?= $key == 0 ? 'active' : '' ?>">
                        <span class="icon-default" style="background-image: url('<?= $announcementNew['activeNotIcon'] ?>')"></span>
                        <span class="icon-active" style="background-image: url('<?= $announcementNew['activeIcon']?>')"></span>
                        <?= $announcementNew['title'] ?>
                    </div>
                    <?php endforeach; ?>
                </div>

                <div class="tabs-pane-content">
                    <?php foreach ($data['announcementNew']['list'] as $key => $announcementNews): ?>
                    <div class="tabs-pane <?= $key == 0? 'show': '' ?>">
                        <a class="top-more" href="<?= $data['announcementNew']['title'][$key]['moreUrl'] ?>">查看更多 ></a>
                        <?php if (!empty($announcementNews)): ?>
                        <div class="list-content">
                            <?php foreach ($announcementNews as $announcement): ?>
                            <a class="list animation-mouseover" href="<?=$announcement['url']?>" target="_blank" title="<?=$announcement['title']?>">
                                <div class="time">
                                    <?= $announcement['refresh_time_month_day'] ?>
                                    <div class="year"><?= $announcement['refresh_time_year'] ?></div>
                                </div>
                                <div class="detail">
                                    <div class="title <?=$announcement['is_top']?'is-top' :''?>">
                                        <?= $announcement['title'] ?>
                                    </div>
                                    <div class="bottom">
                                        <?php if (!empty($announcement['highlights_describe'])): ?>
                                        <div class="welfare">
                                            <?= $announcement['highlights_describe'] ?>
                                        </div>
                                        <div class="address"><?= $announcement['areaText'] ?></div>
                                        <?php else: ?>
                                            <div class="info"><span><?= $announcement['all_job_amount'] ?></span>职位 | 招<span><?= $announcement['jobAmount'] ?></span>人 | 截止日期：<span><?= $announcement['period_date'] ?></span></div>
                                            <div class="address"><?= $announcement['areaText'] ?></div>
                                        <?php endif; ?>

                                    </div>
                                </div>
                            </a>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                        <a class="view-more" href="<?= $data['announcementNew']['title'][$key]['moreUrl'] ?>">查看更多</a>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="region-wrapper module" module-name="各地热招">
        <div class="view-content">
            <div class="region-content">
                <?php foreach ($data['areaHot']['areaList'] as $area): ?>
                <a class="list" href="<?= $area['url'] ?>" target="_blank" title="<?= $area['name'] . '博士后招聘' ?>"><?= $area['name'] ?></a>
                <?php endforeach; ?>
            </div>
            <a class="region-more" href="<?= $data['areaHot']['moreUrl'] ?>" target="_blank">更多</a>
        </div>
    </div>

    <div class="job-wrapper module" module-name="精选博后职位">
        <div class="view-content">
            <div class="common-title">
                <span class="title-fill">精选</span>
                <span class="title-suffix">博后职位</span>
                <span class="title-english">Featured Postdoctoral Jobs</span>
            </div>

            <div class="content">
                <div class="tabs-content">
                    <?php foreach ($data['selectedJob']['title'] as $key => $job): ?>
                        <div class="tabs <?= $key == 0 ? 'active' : '' ?>">
                            <span class="icon-default" style="background-image: url('<?= $job['activeNotIcon'] ?>')"></span>
                            <span class="icon-active" style="background-image: url('<?= $job['activeIcon'] ?>')"></span>
                            <?= $job['name'] ?>
                        </div>
                    <?php endforeach; ?>
                </div>

                <div class="tabs-pane-content">
                    <?php foreach ($data['selectedJob']['title'] as $key => $jobTitle): ?>
                    <?php $jobList = $data['selectedJob'][$jobTitle['tab']]['list']; ?>
                    <div class="tabs-pane <?= $key == 0 ? 'show' : '' ?>">

                        <?php if ($jobTitle['tab'] == 'newList'): ?>
                        <div class="top-tips">
                            <?php if (!$data['selectedJob']['isLogin']): ?>
                            <div class="no-login">
                                【登录】获取更多、更精准内容推荐 &nbsp;
                                <a class="open-login-dialog" href="javascript:;">立即登录></a>
                            </div>
                            <?php endif; ?>
                            <?php if ($data['selectedJob']['isLogin'] && !$data['selectedJob']['resumeStepBool']): ?>
                            <div class="no-perfect">
                                完善简历获取精准职位推荐
                                <a href="<?=$data['selectedJob'][$jobTitle['tab']]['notice']['url']?>" target="_blank">立即完善简历></a>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>

                        <?php if ($jobList): ?>
                        <div class="list-content">
                            <?php foreach ($jobList as $job): ?>
                                <a class="list animation-mouseover" href="<?= $job['jobUrl'] ?>" title="<?= $job['jobName'] ?>" target="_blank">
                                    <div class="detail">
                                        <div class="top">
                                            <div class="name"><?= $job['jobName'] ?></div>
                                            <?php if ($jobTitle['tab'] == 'onlineList'): ?>
                                            <div class="aside <?= $job['isCompanyMember15DayActive'] ? 'on-line' : '' ?>"></div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="info">
                                            <div class="welfare"><?= $job['wage'] ?></div>
                                            <div class="invite"><?= implode(' | ', [$job['cityText'], '招' . $job['amount'] . '人']) ?></div>
                                        </div>
                                        <div class="relevance">
                                            <div class="label"><?= $job['announcementName'] ? '关联公告' : '招收单位'?></div>
                                            <div class="name"><?= $job['announcementName'] ? $job['announcementName'] : $job['companyName']?></div>
                                        </div>
                                    </div>
                                    <div class="bottom">
                                        <?php if ($job['welfareTag']): ?>
                                        <div class="welfare-content">
                                            <div class="tag">有福利</div>
                                            <div class="value"><?= $job['welfareTag']?></div>
                                        </div>
                                        <?php elseif ($job['majorId']): ?>
                                        <div class="major-content">
                                            <div class="tag">专业方向</div>
                                            <div class="value"><?= $job['majorId']?></div>
                                        </div>
                                        <?php else: ?>
                                        <div class="time-content"><?= $job['refreshDate']?>发布</div>
                                        <?php endif; ?>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                        <?php else: ?>
                        <div class="empty-content">更多推荐职位正在更新，详情请点击【查看更多】</div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <a class="view-more" href="<?= $data['selectedJob']['moreUrl']?>">查看更多</a>
        </div>
    </div>

    <?php if ($data['brandCompany']['list']): ?>
    <div class="brand-wrapper gray-bg module" module-name="品牌单位">
        <div class="view-content">
            <div class="common-title">
                <span class="title-fill">品牌</span>
                <span class="title-suffix">单位</span>
                <span class="title-english">Featured Employers</span>
            </div>

            <a class="more" href="<?= $data['brandCompany']['moreUrl'] ?>">查看更多 ></a>

            <div class="content">
                <?php foreach ($data['brandCompany']['list'] as $key => $brandCompany): ?>
                <a class="list <?= $key == 0 ? 'active' : '' ?> <?= $brandCompany['showcase'] ? 'showcase-browse' : '' ?>"
                   data-showcase-number="<?= $brandCompany['showcase']['number'] ?? '' ?>"
                   data-showcase-id="<?= $brandCompany['showcase']['id'] ?? '' ?>"
                   href="<?= $brandCompany['detailUrl'] ?>"
                   title="<?= $brandCompany['companyName'] ?>" target="_blank">                    <div class="cover">
                        <div class="company" style="background-image: url(<?= $brandCompany['companyImageBg'] ?>)">
                            <div class="title"><?= $brandCompany['companyName'] ?></div>
                            <div class="sub-title"><?= $brandCompany['companySub'] ?></div>
                        </div>
                    </div>
                    <div class="show">
                        <div class="top">
                            <img class="logo" src="<?= $brandCompany['companyLogo'] ?>" alt="<?= $brandCompany['companyName'] ?>"/>
                            <div class="aside">
                                <div class="title"><?= $brandCompany['companyName'] ?></div>
                                <div class="type"><?= implode('|', [$brandCompany['companyCity'], $brandCompany['companyType']]) ?></div>
                            </div>
                        </div>
                        <div class="detail">
                            <div class="tag-content">
                                <?php foreach ($brandCompany['companyLabel'] as $name): ?>
                                    <span class="tag"><?= $name ?></span>
                                <?php endforeach; ?>
                            </div>
                            <div class="desc">
                                <?= $brandCompany['companyDesc'] ?>
                            </div>
                            <div class="total">在招公告：<span class="amount"><?= $brandCompany['companyOnLineAnnouncementAmount'] ?></span> 在招职位：<span class="amount"><?= $brandCompany['companyOnLineJobAmount'] ?></span></div>
                            <div class="look-detail">查看详情</div>
                        </div>
                    </div>
                </a>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if ($data['bohouActivity']['list']): ?>
    <div class="postdoctor-wrapper module"  module-name="博后活动">
        <div class="view-content">
            <div class="common-title">
                <span class="title-suffix">博后</span>
                <span class="title-fill">活动</span>
                <span class="title-english">Postdoctoral Activities</span>
            </div>

            <a class="more" href="<?= $data['bohouActivity']['moreUrl'] ?>">查看更多 ></a>

            <div class="content">
                <?php foreach ($data['bohouActivity']['list'] as $activity): ?>
                <div class="list animation-mouseover">
                    <a class="top" href="<?= $activity['activityDetailUrl'] ?>" target="_blank" title="<?= $activity['activityName'] ?>" rel="<?= $activity['rel'] ?>">
                        <img src="<?= $activity['activityImgUrl'] ?>" alt="<?= $activity['activityName'] ?>"/>
                        <div class="time"><?= $activity['activityDate'] ?></div>
                        <div class="title"><?= $activity['activityName'] ?></div>
                    </a>
                    <div class="bottom">
                        <div class="address"><?= $activity['activityAreaText'] ?></div>
                        <?php if ($activity['activityStatus'] == 1): ?>
                        <a href="<?= $activity['activitySignUpUrl'] ?>" target="_blank" rel="nofollow">立即参与</a>
                        <?php else: ?>
                        <a class="disabled" href="<?= $activity['activityDetailUrl'] ?>" target="_blank" rel="nofollow">已结束</a>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="cooperation-wrapper">
        <div class="top">聚焦高端人才&nbsp;专业就选高才</div>
        <div class="title">匠心定制高效博后引进方案，用人单位的睿智之选！</div>
        <div class="bottom">
            <a class="cooperation" href="https://www.gaoxiaojob.com/member/company/applyCooperation" target="_blank" rel="nofollow">合作申请</a>
            <a class="tel" href="tel:020-85611139" rel="nofollow">020-85611139</a>
        </div>
    </div>
</div>

<!-- 左侧楼层锚点 -->
<div class="fixed-tool"></div>


<script src="/static/lib/jquery-throttle-debounce/jquery-throttle-debounce.min.js"></script>
<script>
    $(function () {
        var $hotRecommendSwiper = $('.hot-recommend-swiper')

        new Swiper('.swiper-t1', {
            loop: true,
            autoplay: {
                delay: 5000
            },
            pagination: {
                el: '.swiper-t1 .swiper-pagination',
                clickable: true
            }
        })

        var windowHeight = window.innerHeight
        var hotRecommendSwiperFlag = false
        var isInitHotRecommendSwiper = <?=  (count($data['showcaseK1']['showcaseList'][0]) > 1) ? 'true' : 'false' ?>;
        const hotRecommendSwiperOffsetTop = $hotRecommendSwiper.offset().top

        // 热招推荐
        function initHotRecommendSwiper() {
            new Swiper('.hot-recommend-swiper', {
                loop: true,
                autoplay: <?=  (count($data['showcaseK1']['showcaseList'][0]) > 1) ? '{delay: 5000,disableOnInteraction: false,pauseOnMouseEnter: true}' : 'false' ?>,
                pagination: {
                    el: '.hot-recommend-swiper .swiper-pagination',
                    clickable: true
                }
            })
        }
        
        $(window).scroll(function (e) {
            const scrollHeight = $(this).scrollTop()
            if (hotRecommendSwiperFlag || !isInitHotRecommendSwiper) return

            if (hotRecommendSwiperOffsetTop - windowHeight < scrollHeight) {
                hotRecommendSwiperFlag = true
                initHotRecommendSwiper()
            }
        })

        // 热招推荐切换
        $('.hot-recommend-wrapper').on('click', '.tabs .name', function () {
            const $currentTab = $(this).parents('.tabs')
            const index = $currentTab.index()
            $currentTab.find('.name').addClass('active')
            $currentTab.siblings().find('.name').removeClass('active')
            $(this).parents('.hot-recommend-wrapper').find('.tabs-pane').eq(index).addClass('show').siblings().removeClass('show')
        })
        // 热招推荐end

        // 公告热榜start
        $('.announcement-wrapper').on('click', '.refresh', function () {
            const announcementContent = $('.announcement-wrapper .announcement-content')
            const { length } = announcementContent
            console.log(length)
            var index = $('.announcement-wrapper .announcement-content.active').index()
            index = index === -1 ? 0 : index

            const nextIndex = index === length - 1 ? 0 : index + 1
            announcementContent.eq(nextIndex).addClass('active').siblings('.announcement-content').removeClass('active')
        })
        // 公告热榜end

        // 最新发布start
        $('.new-release-wrapper').on('click', '.tabs', function () {
            const index = $(this).index()
            $(this).addClass('active').siblings().removeClass('active')

            $(this).parents('.new-release-wrapper').find('.tabs-pane').eq(index).addClass('show').siblings().removeClass('show')
        })
        // 最新发布end

        // 精选职位start
        $('.job-wrapper').on('click', '.tabs', function () {
            const index = $(this).index()
            $(this).addClass('active').siblings().removeClass('active')

            $(this).parents('.job-wrapper').find('.tabs-pane').eq(index).addClass('show').siblings().removeClass('show')
        })
        // 精选职位end

        // 品牌单位start
        $('.brand-wrapper').on('mouseover', '.list', function () {
            $(this).addClass('active').siblings().removeClass('active')
        })
        // 品牌单位end

        // 加载接口数据
        httpGet('home/load-guess-like').then(r=>{
            var targetElement = $('[module-name="博后公告热榜"]').find('.content');

            // 将新的 div 追加到目标元素内
            targetElement.append(r.content);
        })
    })
</script>