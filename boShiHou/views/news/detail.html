<link rel="stylesheet" href="/static/css/postdoctorNewsDetail.css">

<div class="main-container">
    <div class="current-location">
        <span class="current">当前位置：</span>
        <a href="/" target="_blank">高才博士后</a>＞
        <a href="<?=$huodongUrl?>" target="_blank">博后活动</a>＞<?= $info['title'] ?>
    </div>
    <div class="left">
        <div class="article-top">
            <h1 class="title"><?= $info['title'] ?></h1>
            <div class="info" id="newsDetailComponent">
                <small>发布日期：<?= $info['refresh_time']?></small>
                <small class="origin">作者：<?= $info['author']?></small>
                <small>阅读量：<?= $info['click']?></small>
                <a v-if="isCollect==1" href="javascript:;" @click="handleCollect">取消收藏</a>
                <a v-if="isCollect==0" href="javascript:;" @click="handleCollect">收藏文章</a>

            </div>
        </div>

        <div class="content">
            <div class="abstract">
                【摘要】<?= $info['abstract']?>
            </div>
            <!-- 详情start -->
            <div class="article">
                <?= $info['content']?>
            </div>
            <!-- 详情end -->
            <div class="origin">
                <?php if ($info['original'] || $info['original_url']): ?>
                来源：
                <?php endif; ?>
                <?php if ($info['original']): ?>
                &nbsp;
                <?= $info['original']?>
                <?php endif; ?>
                <?php if ($info['original_url']): ?>
                &nbsp;
                <?= $info['original_url']?>
                <?php endif; ?>
            </div>
            <div class="qr">
                <div>更多资讯！欢迎扫描下方二维码关注高才博士后公众号</div>
                <img src="https://img.gaoxiaojob.com/uploads/boshihou/sidebar/postdoctor-qrcode.webp" alt="" />
            </div>


<!--            <div class="share-custom">-->
<!--                <div class="sina-weibo">-->
<!--                    <html xmlns:wb="https://open.weibo.com/wb">-->
<!--                    <wb:follow-button uid="3702192203" type="red_3" width="100%" height="24">-->
<!--                    </wb:follow-button>-->
<!--                </div>-->
<!--                <div>-->
<!--                    <div class="bshare-custom">-->
<!--                        <a title="分享到微信" class="bshare-weixin"></a>-->
<!--                        <a title="分享到新浪微博" class="bshare-sinaminiblog"></a>-->
<!--                        <a title="分享到QQ空间" class="bshare-qzone"></a>-->
<!--                        <a title="分享到Facebook" class="bshare-facebook"></a>-->
<!--                        <a title="分享到Twitter" class="bshare-twitter"></a>-->
<!--                        <a title="更多平台" onclick="javascript:bShare.more(event);return false;" class="custom-more-btn">-->
<!--                        </a>-->
<!--                        <?= frontendPc\components\Share::widget() ?>-->
<!--                        &lt;!&ndash;                                <span class="BSHARE_COUNT bshare-share-count">0</span>&ndash;&gt;-->
<!--                    </div>-->
<!--                    &lt;!&ndash;                            <script&ndash;&gt;-->
<!--                    &lt;!&ndash;                                src="http://static.bshare.cn/b/buttonLite.js#style=-1&amp;uuid=40cb6f46-7685-42c6-8cf8-7e18be117d11&amp;pophcol=1&amp;lang=zh"></script>&ndash;&gt;-->
<!--                    &lt;!&ndash;                            <script src="http://static.bshare.cn/b/bshareC0.js"></script>&ndash;&gt;-->
<!--                </div>-->
<!--            </div>-->
        </div>
    </div>

    <div class="right">
        <!--推荐信息-->
        <div class="recommend">
            <div class="common-title">
                <h2>推荐信息</h2>
            </div>
            <ul>
                <?php foreach ($recommendList as $k => $v) { ?>
                <li>
                    <a href="<?=$v['url']?>" target="_blank" title="<?=\common\helpers\StringHelper::changeQuotationMark($v['title'])?>">
                        <div class="title"><?=$v['title'] ?></div>
                    </a>
                </li>
                <?php } ?>
            </ul>
        </div>
    </div>
</div>

<script src="https://tjs.sjs.sinajs.cn/open/api/js/wb.js" type="text/javascript" charset="utf-8"></script>
<script>
    $(function () {
        const newsDetailComponent = {
            data() {
                return {
                    id: <?= $info['id'] ?>,
                    isCollect: <?= $info['isCollect'] ?>,
                    collectLoading: false,
                }
            },

            methods: {
                handleCollect() {
                    const {userStatus} = this

                    if (userStatus === 0) {
                        window.globalComponents.loginDialogComponent.showLoginDialog()
                        return
                    } else {
                        const {isCollect} = this

                        this.collectLoading = true
                        httpPost('/news/collect', {newsId: this.id}).then(() => {
                            this.isCollect = this.isCollect === 1 ? 0 : 1
                        }).catch(error => {

                        })
                        this.collectLoading = false
                    }
                }
            },
        }
        Vue.createApp(newsDetailComponent).use(ElementPlus).mount('#newsDetailComponent')
    });
</script>

<script src="/static/js/column.js?v=2"></script>

