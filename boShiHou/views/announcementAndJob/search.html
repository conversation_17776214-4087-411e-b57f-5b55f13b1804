<link rel="stylesheet" href="/static/css/filterDialog.css"/>

<div class="filter-wrapper" id="filter-wrapper">
    <div class="filter-row kw-row">
        <div class="filter-label">关键词</div>
        <div class="filter-value">
            <div class="search" id="keywordComponent">
                <div class="search-input">
                    <el-input class="kw" v-model="keyword" @keyup.enter="search" @clear="handleKwClear" placeholder="请输入关键词搜索" clearable></el-input>
                </div>
                <button class="search-btn" @click="search">搜索</button>
            </div>
            <?php if($searchData['hotKeywordShowcase']){ ?>
            <div class="search-hot">
                <div class="search-hot-label">热门搜索:</div>
                <div class="search-hot-value">
                    <?php foreach($searchData['hotKeywordShowcase'] as $item){ ?>
                    <a data-showcase-number="<?= $item['number'] ?>" data-showcase-id="<?=$item['id'] ?>" class="showcase-browse item <?=$item['active'] ?'active':''; ?>" href="<?=$item['linkUrl'] ?>"><?=$item['title']?></a>
                    <?php } ?>
                </div>
            </div>
            <?php } ?>
        </div>
    </div>

    <div class="filter-row">
        <div class="filter-label">热门地区</div>
        <div class="filter-value">
            <?php foreach($searchData['hotAreaList'] as $item){ ?>
            <a class="filter-item <?=$item['active']?'active':'';?>" href="<?=$item['url']?>"><?=$item['name']?></a>
            <?php } ?>
        </div>
        <div class="filter-more dialog-target" data-target="#areaDialog">更多地区</div>
    </div>


    <div class="filter-row">
        <div class="filter-label">学科领域</div>
        <div class="filter-value">
            <?php if(!$searchData['isLogin']){ ?>
            <button class="open-login open-login-dialog">登录查看我的专业</button>
            <?php } ?>
            <?php foreach($searchData['majorSpecialtyList']['specialtyList'] as $item){ ?>
            <a class="filter-item <?=$item['active']?'active':'';?>" href="<?=$item['url']?>"><?=$item['v']?></a>
            <?php } ?>
        </div>
        <div class="filter-more dialog-target" data-target="#majorDialog">更多学科</div>
    </div>
    <?php if($searchData['majorSpecialtyList']['activeChildrenMajorList']){ ?>
    <div class="filter-row">
        <div class="filter-label">需求专业</div>
        <div class="filter-value">
            <?php foreach($searchData['majorSpecialtyList']['activeChildrenMajorList'] as $item){ ?>
                <a class="filter-item <?=$item['active']?'active':'';?>" href="<?=$item['url']?>"><?=$item['k']==-1?$item['name']:$item['v']?></a>
            <?php } ?>
        </div>
    </div>
    <?php } ?>

    <div class="filter-row">
        <div class="filter-label">
            单位类型
            <div class="tips">（多选）</div>
        </div>
        <div class="filter-value">
            <?php foreach($searchData['companyTypeList'] as $item){ ?>
            <?php if($item['isDiv']){ ?>
            <div class="filter-item filter-item--limit"><?=$item['name']?></div>
            <?php }else{ ?>
            <a class="filter-item <?=$item['active']?'active':'';?>" href="<?=$item['url']?>"><?=$item['name']?></a>
            <?php } ?>
            <?php } ?>
        </div>
    </div>

    <div class="filter-row more-row">
        <div class="filter-label">更多筛选</div>
        <div class="filter-value">
            <!-- active 展开下拉框， is-select有选择值 -->
            <div class="release custom-select <?=$searchData['refreshTimeList']['timeSelected']['name']?'is-select':''?>">
                <div class="el-select">
                    <div class="select-trigger">
                        <div class="el-input el-input--mini el-input--suffix">
                            <input class="el-input__inner" type="text" readonly="" autocomplete="off"
                                   placeholder="发布时间"
                                   value="<?=$searchData['refreshTimeList']['timeSelected']['name']?>"/>
                            <span class="el-input__suffix">
                                    <span class="el-input__suffix-inner">
                                        <i class="down"></i>
                                        <a class="el-select__caret el-input__icon el-icon-circle-close"
                                           href="<?=$searchData['refreshTimeList']['timeSelected']['closeUrl']?>"></a>
                                    </span>
                                </span>
                        </div>
                    </div>
                </div>
                <div class="el-select__popper el-popper is-light is-pure">
                    <div class="el-select-dropdown">
                        <div class="el-scrollbar">
                            <div class="el-select-dropdown__wrap el-scrollbar__wrap el-scrollbar__wrap--hidden-default">
                                <div class="el-scrollbar__view el-select-dropdown__list">
                                    <?php foreach($searchData['refreshTimeList']['timeList'] as $item){ ?>
                                    <a class="el-select-dropdown__item <?=$item['active']?'selected':'';?>"
                                       href="<?=$item['url']?>">
                                        <span><?=$item['name']?></span>
                                    </a>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="el-popper__arrow"></div>
                </div>
            </div>
            <!-- is-checked 已勾选 -->
            <a href="<?=$searchData['pi']['url']?>"
               class="checkbox <?=$searchData['pi']['active']?'is-checked':'';?>"> <?=$searchData['pi']['name']?> </a>
        </div>
    </div>

    <div class="filter-row">
        <div class="filter-label">已选条件</div>
        <div class="filter-value">
            <?php foreach($searchData['currentSearchParamsChecked'] as $item){ ?>
            <div class="select-item">
                <?=$item['name']?>
                <a class="filter-clear" href="<?=$item['url']?>"></a>
            </div>
            <?php } ?>
        </div>
        <a class="filter-clear-all" href="<?=$searchData['hostCurrentUrl']?>">清空筛选条件</a>
    </div>
</div>
<!-- 筛选弹出层 -->
<div class="filter-dialog-template">
    <!-- 地区 -->
    <div class="el-overlay" id="areaDialog" style="z-index: 2003">
        <div class="el-overlay-dialog">
            <div class="el-dialog dialog-center" aria-modal="true" role="dialog" aria-label="dialog">
                <div class="el-dialog__header">
                    <div class="filter-header">
                        <div class="title">请选地区</div>
                        <div class="search" id="areaSearchTemplate">
                            <el-select
                                    v-model="filterHref"
                                    :remote-method="handleSearch"
                                    filterable
                                    remote
                                    reserve-keyword
                                    :size="size"
                                    placeholder="请输入关键词"
                                    @change="handleSearchChange"
                            >
                                <el-option v-for="item in filterOptions" :key="item.k" :label="item.v" :value="item.href"/>
                            </el-select>
                        </div>
                    </div>
                    <button class="el-dialog__headerbtn close-dialog" type="button">
                        <i class="el-dialog__close el-icon el-icon-close"></i>
                    </button>
                </div>
                <div class="el-dialog__body">
                    <div class="filter-content">
                        <div class="data-content">
                            <div class="left-content">
                                <?php foreach($searchData['areaList'] as $item){ ?>
                                <span class="list <?=$item['active']?'active':''?> <?=$item['dot']?'has-select':''?>"><?=$item['v']?></span>
                                <?php } ?>
                            </div>
                            <div class="right-content">
                                <?php foreach($searchData['areaList'] as $item){ ?>
                                <div class="second-content column-4 <?=$item['active']?'show':''?>">
                                    <?php foreach($item['children'] as $childItem){ ?>
                                    <div class="second-item">
                                        <a class="<?=$childItem['active']?'active':''?>" href="<?=$childItem['url']?>"><?=$childItem['v']?></a>
                                    </div>
                                    <?php } ?>
                                </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
                <!--v-if-->
            </div>
        </div>
        <script>
            const areaSearchOptions = {
                data() {
                    return {
                        size: 'small',
                        filterHref: '',
                        options:[
                        <?php foreach($searchData['areaList'] as $item){ ?>
                            <?php foreach($item['children'] as $child_item){ ?>
                                    {
                                        "k": "<?=$child_item['k']?>",
                                        "v": "<?=$child_item['v']?>",
                                        "href": "<?=$child_item['url']?>"
                                    },
                                    <?php } ?>
                            <?php } ?>],
                        filterOptions: []
                    }
                },

                mounted() {
                },

                methods: {
                    handleSearch(query) {
                        if (query) {
                            const {options} = this
                            setTimeout(() => {
                                const objMap = new Map()
                                this.filterOptions = options.filter((item) => {
                                    const isIncludes = item.v.toLowerCase().includes(query.toLowerCase())
                                    const {v} = item
                                    const flag = isIncludes && !objMap.has(v)
                                    if (flag) objMap.set(v, 1)
                                    return flag
                                })
                            }, 200)
                        } else {
                            this.filterOptions = []
                        }
                    },
                    handleSearchChange() {
                        window.location.href = this.filterHref
                    }
                }
            }

            Vue.createApp(areaSearchOptions).use(ElementPlus).mount('#areaSearchTemplate')
        </script>
    </div>

    <div class="el-overlay" id="majorDialog" style="z-index: 2003">
        <div class="el-overlay-dialog">
            <div class="el-dialog dialog-center" aria-modal="true" role="dialog" aria-label="dialog">
                <div class="el-dialog__header">
                    <div class="filter-header">
                        <div class="title">请选择领域专业</div>
                        <div class="search" id="majorSearchTemplate">
                            <el-select
                                    v-model="filterHref"
                                    :remote-method="handleSearch"
                                    filterable
                                    remote
                                    reserve-keyword
                                    :size="size"
                                    placeholder="请输入关键词"
                                    @change="handleSearchChange"
                            >
                                <el-option v-for="item in filterOptions" :key="item.k" :label="item.v" :value="item.href"/>
                            </el-select>
                        </div>
                    </div>
                    <button class="el-dialog__headerbtn close-dialog" type="button">
                        <i class="el-dialog__close el-icon el-icon-close"></i>
                    </button>
                </div>
                <div class="el-dialog__body">
                    <div class="filter-content">
                        <div class="data-content">
                            <div class="left-content">
                                <?php foreach($searchData['majorSpecialtyList']['specialtyList']  as $key => $item){ ?>
                                    <?php if($key != 0){?>
                                        <span class="list <?=$key==1 && (!isset($searchData['currentSearchParams']['specialtyId']) || $searchData['currentSearchParams']['specialtyId']<=0)?'active':''?>  <?=$item['active']?'active':''?> <?=$item['dot']?'has-select':''?>"><?=$item['v']?></span>
                                    <?php } ?>
                                <?php } ?>
                            </div>
                            <div class="right-content">
                                <?php foreach($searchData['majorSpecialtyList']['specialtyList']  as $key => $item){ ?>
                                    <?php if($key != 0){?>
                                    <div class="second-content column-4 <?= $key==1 && (!isset($searchData['currentSearchParams']['specialtyId']) || $searchData['currentSearchParams']['specialtyId']<=0)?'show':''?> <?=$item['active']?'show':''?>">
                                        <?php foreach($item['children'] as $childKey=>$childItem){ ?>
                                            <div class="second-item">
                                                <a class="<?=$childItem['active']?'active':''?>" href="<?=$childItem['url']?>"><?=$childItem['v']?></a>
                                            </div>
                                        <?php } ?>
                                    </div>
                                    <?php } ?>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
                <!--v-if-->
            </div>
        </div>
        <script>
            const majorSearchOptions = {
                data() {
                    return {
                        size: 'small',
                        filterHref: '',
                        options:
                        [
                            <?php foreach($searchData['majorSpecialtyList']['specialtyList'] as $item){ ?>
                    <?php foreach($item['children'] as $child_item){ ?>
                            {
                                "k": "<?=$child_item['k']?>",
                                "v": "<?=$child_item['v']?>",
                                "href": "<?=$child_item['url']?>"
                            },
                            <?php } ?>
                    <?php } ?>
                        ],
                        filterOptions: []
                    }
                },

                mounted() {
                },

                methods: {
                    handleSearch(query) {
                        if (query) {
                            const {options} = this
                            setTimeout(() => {
                                const objMap = new Map()
                                this.filterOptions = options.filter((item) => {
                                    const isIncludes = item.v.toLowerCase().includes(query.toLowerCase())
                                    const {v} = item
                                    const flag = isIncludes && !objMap.has(v)
                                    if (flag) objMap.set(v, 1)
                                    return flag
                                })
                            }, 200)
                        } else {
                            this.filterOptions = []
                        }
                    },
                    handleSearchChange(href) {
                        window.location.href = href
                    }
                }
            }

            Vue.createApp(majorSearchOptions).use(ElementPlus).mount('#majorSearchTemplate')
        </script>
    </div>
</div>
<script>
    $(function () {
        const keywordComponent = {
            data() {
                return {
                    keyword: "<?=$searchData['currentSearchParams']['keyword']?>"
                }
            },
            methods: {
                search() {
                    updateHref({ keyword: this.keyword, page: '' })
                },

                handleKwClear() {
                    updateHref({ keyword: '', page: '' })
                }
            },
            mounted() {
            }
        }

        Vue.createApp(keywordComponent).use(ElementPlus).mount('#keywordComponent')

        var isToast = false
        $('.filter-wrapper').on('click', '.filter-item--limit', function () {
            if (!isToast) {
                isToast = true
                ElementPlus.ElMessage.warning('最多可选择5项')
                setTimeout(() => {
                    isToast = false
                }, 3000)
            }
        })
    })
</script>