<?php
$params = array_merge(require __DIR__ . '/../../common/config/params.php',
    require __DIR__ . '/../../common/config/params-local.php', require __DIR__ . '/params.php',
    require __DIR__ . '/params-local.php');

return [
    'id'                  => 'app-boShiHou',
    'basePath'            => dirname(__DIR__),
    'controllerNamespace' => 'boShiHou\controllers',
    'bootstrap'           => ['log'],
    'defaultRoute'        => 'home',
    'components'          => [
        'request' => [
            'csrfParam' => '_csrf-boShiHou',
        ],
        'session' => [
            'name'    => '_identity',
            'timeout' => 3600,
        ],

        'user'         => [
            'class'           => 'yii\web\User',
            'identityClass'   => 'common\base\models\BaseMember',
            'enableAutoLogin' => true,
            'identityCookie'  => [
                'name'     => '_identity',
                'httpOnly' => true,
            ],
            'idParam'         => '__member',
        ],
        'errorHandler' => [
            'errorAction' => 'home\error',
        ],
        'urlManager'   => [
            'enablePrettyUrl' => true,
            'showScriptName'  => false,
            'rules'           => [
                '1.gif'                               => 'showcase-browse-log/add-showcase-browse-log',
                '2.gif'                               => 'showcase-browse-log/add-buried-point-log',
                //公告
                'gonggao/?'                           => 'announcement/index',
                'gonggao/<level1:\w+>/?'              => 'announcement/index',
                'gonggao/<level1:\w+>/<level2:\w+>/?' => 'announcement/index',
                //职位
                'zhiwei/?'                            => 'job/index',
                'zhiwei/<level1:\w+>/?'               => 'job/index',
                'zhiwei/<level1:\w+>/<level2:\w+>/?'  => 'job/index',
                //单位
                'danwei/?'                            => 'company/index',
                //活动
                'huodong/?'                           => 'activity/index',
                //服务
                'fuwu/?'                              => 'publish/index',
                // 2.7.1版本
                'news/detail/<id:\d+>.html'           => 'news/detail',
            ],
        ],
    ],
    'params'              => $params,
];
