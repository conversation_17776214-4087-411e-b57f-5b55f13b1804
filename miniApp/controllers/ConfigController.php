<?php

namespace miniApp\controllers;

use common\base\controllers\BaseConfigController;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseDictionary;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseJob;
use common\base\models\BaseMajor;
use common\base\models\BaseResume;
use common\base\models\BaseSystemConfig;
use common\helpers\ArrayHelper;
use common\libs\Cache;
use common\service\search\SearchParamsService;
use frontendPc\models\Dictionary;
use miniApp\models\CategoryJob;
use yii\console\Response;

class ConfigController extends BaseMiniAppController
{
    use BaseConfigController;

    /**
     * 获取公告筛选类型列表
     * @return Response|\yii\web\Response
     */
    public function actionGetAnnouncementTypeList()
    {
        return $this->success(BaseHomeColumn::getAnnouncementTypeList());
    }

    // 这里其实是专门给小程序地区页面用的选择
    // https://lanhuapp.com/web/#/item/project/product?tid=8d951de4-aefb-40f7-954e-366683d68331&pid=ad0ebb54-732f-4c1b-b519-c1d4ea7f8455&versionId=e68ef84e-5790-4748-bc17-74ca35a79fa7&docId=15a68003-b3e5-4e73-b949-1bb24dce8cec&docType=axure&pageId=b2a063140847476b86595fa92a0e87ce&image_id=15a68003-b3e5-4e73-b949-1bb24dce8cec&parentId=b8c5ee395a724ee9b5ee664192ce16dc
    // 分别有当前定位,热门地区,省份,城市
    public function actionGetRegionAllSelect()
    {
        return $this->success(BaseDictionary::miniAreaPageSelect());
    }

    public function actionGetMiniShareInfo()
    {
        return $this->success([
            'title'    => '高层次人才求职招聘综合服务平台',
            'path'     => '/pages/index/index',
            'imageUrl' => 'https://img.gaoxiaojob.com/uploads/mini_other/share.png?imageView2/2/format/webp',
        ]);
    }

    /**
     * 'wageId'           => [],
     * 'majorId'          => [],
     * 'keyword'          => [],
     * 'areaId'           => [],
     * 'companyNature'    => [],
     * 'companyType'      => [],
     * 'educationType'    => [],
     * 'natureType'       => [],
     * 'companyScaleType' => [],
     * 'industryId'       => [],
     * 'companyId'        => [],
     * 'jobCategoryId'    => [],
     * 'titleType'        => [],
     * 'jobType'          => [],
     * 'welfareLabelId'   => [],
     * 'experienceType'   => [],
     * 'releaseTimeType'  => [],
     *
     * 这个接口是给前端职位筛选面板的复合搜索使用
     */
    public function actionGetJobSearchParams()
    {
        return $this->success(BaseJob::getMiniJobSearchParams());
    }

    public function actionGetJobSearchParamsV2()
    {
        return $this->success(SearchParamsService::getPublicSearchParams(1));
    }

    public function actionGetAnnouncementSearchParams()
    {
        return $this->success(SearchParamsService::getPublicSearchParams(2));
    }

    /**
     * 获取职位类型列表
     * @return Response|\yii\web\Response
     */
    public function actionGetCategoryJobList()
    {
        return $this->success(CategoryJob::getCategoryJobList());
    }

    /**
     * mini求职者可选专业类型列表
     * @return Response|\yii\web\Response
     */
    public function actionGetMiniPersonMajorList()
    {
        $list = BaseMajor::getPersonMajorList();

        $topList = [
            0 => [
                'k'        => '-1',
                'v'        => '热门学科',
                'level'    => '1',
                'parentId' => '0',
                'children' => [
                    [
                        'k'        => '59',
                        'v'        => '计算机科学与技术',
                        'level'    => '3',
                        'parentId' => '-1',
                    ],
                    [
                        'k'        => '43',
                        'v'        => '生物学',
                        'level'    => '3',
                        'parentId' => '-1',
                    ],
                    [
                        'k'        => '49',
                        'v'        => '机械工程',
                        'level'    => '3',
                        'parentId' => '-1',
                    ],
                    [
                        'k'        => '52',
                        'v'        => '材料科学与工程',
                        'level'    => '3',
                        'parentId' => '-1',
                    ],
                    [
                        'k'        => '98',
                        'v'        => '临床医学',
                        'level'    => '3',
                        'parentId' => '-1',
                    ],
                    // [
                    //     'k'        => '87',
                    //     'v'        => '电子信息',
                    //     'level'    => '3',
                    //     'parentId' => '-1',
                    // ],
                ],
            ],
        ];

        $return = array_merge($topList, $list);

        return $this->success(ArrayHelper::objMoreArr($return));
    }

    /**
     * 用于控制发现模块里面的直播是否显示
     */
    public function actionMiniIsShowDiscoverLive()
    {
        $rs = BaseSystemConfig::findOneVal(['name' => 'mini_is_show_discover_live'], 'value');

        if ($rs == 1) {
            return $this->success(['is_show' => 1]);
        } else {
            return $this->success(['is_show' => 2]);
        }
    }

    public function actionGetBaseShowcase()
    {
        return $this->success([
            'jobList' => [
                'image' => 'https://img.gaoxiaojob.com/uploads/mini/home/<USER>/2/format/webp',
                'url'   => BaseResume::BUY_URL_VIP,
            ],
        ]);
    }

    // /config/get-native-city-area-list
    // /config/get-hierarchy-city-list
    // /config/get-political-status-list
    // /config/get-education-list
    // /config/get-mini-person-major-list
    // /config/get-category-job-list
    // /config/get-nature-list
    // /config/get-wage-range-list
    // /config/get-job-status-list
    // /config/get-arrive-date-list
    // /config/get-announcement-type-list
    // /config/get-company-type-list
    // /config/get-company-nature-list
    // /config/get-job-search-params
    // /config/get-mini-share-info
    // /config/get-dictionary-text'
    // /config/mini-is-show-discover-live
    // /config/get-emote-list
    // /config/get-base-showcase

    public function actionGetCache()
    {
        $key  = Cache::MINI_ALL_CONFIG_KEY;
        $data = Cache::get($key);

        if ($data) {
            return $this->success(json_decode($data, true));
        }

        // 都放在一起返回前端
        $data = [
            'nativeCityAreaList'     => $this->backToData($this->actionGetNativeCityAreaList()),
            'hierarchyCityList'      => $this->backToData($this->actionGetHierarchyCityList()),
            'politicalStatusList'    => $this->backToData($this->actionGetPoliticalStatusList()),
            'educationList'          => $this->backToData($this->actionGetEducationList()),
            'miniPersonMajorList'    => $this->backToData($this->actionGetMiniPersonMajorList()),
            'categoryJobList'        => $this->backToData($this->actionGetCategoryJobList()),
            'natureList'             => $this->backToData($this->actionGetNatureList()),
            'wageRangeList'          => $this->backToData($this->actionGetWageRangeList()),
            'jobStatusList'          => $this->backToData($this->actionGetJobStatusList()),
            'arriveDateList'         => $this->backToData($this->actionGetArriveDateList()),
            'announcementTypeList'   => $this->backToData($this->actionGetAnnouncementTypeList()),
            'companyTypeList'        => $this->backToData($this->actionGetCompanyTypeList()),
            'companyNatureList'      => $this->backToData($this->actionGetCompanyNatureList()),
            'jobSearchParams'        => $this->backToData($this->actionGetJobSearchParams()),
            'miniShareInfo'          => $this->backToData($this->actionGetMiniShareInfo()),
            'dictionaryText'         => $this->backToData($this->actionGetDictionaryText()),
            'miniIsShowDiscoverLive' => $this->backToData($this->actionMiniIsShowDiscoverLive()),
            'emoteList'              => $this->backToData($this->actionGetEmoteList()),
            'baseShowcase'           => $this->backToData($this->actionGetBaseShowcase()),
            'sphid'                  => 'sph58cVcjcJOJ8J',
        ];

        Cache::set($key, json_encode($data), 3600);

        return $this->success($data);
    }

    private function backToData($successData)
    {
        return $successData->data['data'];
    }

    /**
     * 获取小程序接口所有条件，基础条件入口只用于这里，特殊的就单独配置
     * @return void
     */
    public function actionGetConfig()
    {
        $scene      = \Yii::$app->request->get('scene');

        SearchParamsService::getAllSearchParams($scene);

        return $this->success([]);
    }



}