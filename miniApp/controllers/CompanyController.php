<?php

namespace miniApp\controllers;

use common\base\models\BaseCommon;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyCollect;
use common\base\models\BaseMember;
use common\helpers\ArrayHelper;
use common\service\search\CompanyListService;
use common\service\specialNeedService\CompanyInformationService;
use Yii;
use yii\base\Exception;

class CompanyController extends BaseMiniAppController
{
    /**
     * 获取单位列表筛选器
     */
    public function actionListFilter()
    {
        try {
            $filter = BaseCompany::getListFilter(BaseCommon::PLATFORM_MINI);

            return $this->success($filter);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取单位详情
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDetail()
    {
        try {
            $memberId = BaseMember::getMiniMemberId();
            $id       = Yii::$app->request->get('id');
            $info     = BaseCompany::getDetailService($id, BaseCommon::PLATFORM_MINI, $memberId);

            return $this->success(['info' => $info]);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 单位详情下的筛选器
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDetailFilter()
    {
        try {
            $id          = Yii::$app->request->get('id');
            $type        = Yii::$app->request->get('type');
            $search_item = BaseCompany::getDetailFilterService($id, $type, BaseCommon::PLATFORM_MINI);

            return $this->success($search_item);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取公司详情下公告列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetAnnouncementList()
    {
        try {
            $memberId = BaseMember::getMiniMemberId();
            $id       = Yii::$app->request->get('id');
            if (!$id) {
                throw new Exception('参数错误');
            }
            $search_params = Yii::$app->request->get();
            $list          = BaseCompany::getAnnouncementListService($id, BaseCommon::PLATFORM_MINI, $memberId,
                $search_params);

            $list = (new CompanyInformationService())->handelCompanyAnnouncementList($list, $id);

            return $this->success($list);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取公司详情下职位列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetJobList()
    {
        try {
            $memberId = BaseMember::getMiniMemberId();
            $id       = Yii::$app->request->get('id');
            if (!$id) {
                throw new Exception('参数错误');
            }
            $search_params = Yii::$app->request->get();
            $list          = BaseCompany::getJobListService($id, BaseCommon::PLATFORM_MINI, $memberId, $search_params);

            $list = (new CompanyInformationService())->handelCompanyJobList($list, $id);

            return $this->success($list);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取单位详情下引才活动列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetActivityList()
    {
        try {
            $memberId = BaseMember::getMiniMemberId();
            $id       = Yii::$app->request->get('id');
            if (!$id) {
                throw new Exception('参数错误');
            }
            $search_params = Yii::$app->request->get();
            $list          = BaseCompany::getActivityListService($id, BaseCommon::PLATFORM_MINI, $memberId,
                $search_params);

            $list = (new CompanyInformationService())->handelCompanyJobList($list, $id);

            return $this->success($list);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    // 单位列表(单位搜索)
    public function actionGetList()
    {
        $memberId = $this->memberId;
        $page     = Yii::$app->request->get('page');

        if (!$memberId && $page > 1) {
            // 不登录是不允许拿第二页数据的
            return $this->fail('请先登录');
        }

        $get = ArrayHelper::clearNoValue(Yii::$app->request->get());

        $data = (new CompanyListService)->run($get, CompanyListService::TYPE_PC_COMPANY_LIST);

        return $this->success($data);
        // return $this->success(['list' => BaseCompany::searchForList(Yii::$app->request->get())]);
    }

    // 搜索单位无结构需要有一个推荐的单位信息
    public function actionGetRecommendList()
    {
        return $this->success(['list' => BaseCompany::miniRecommendList(Yii::$app->request->get('page'))]);
    }

    /**
     * 收藏单位
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCollect()
    {
        try {
            $ids      = Yii::$app->request->post('companyId');
            $memberId = $this->memberId;
            $msg      = '';
            //获取返回文案
            if (count(explode(',', $ids)) == 1) {
                $isCollect = BaseCompanyCollect::checkIsCollect($memberId, $ids);
                if ($isCollect) {
                    //如是收藏状态，那此处就是取消收藏
                    $msg = '取消关注成功';
                } else {
                    $msg = '关注成功';
                }
            }
            $server = new \common\service\person\CollectService();
            $data   = [
                'type'     => $server::TYPE_COMPANY,
                'ids'      => $ids,
                'memberId' => $memberId,
            ];
            $server->init($data)
                ->run();

            return $this->success($msg);
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 单位收藏列表
     */
    public function actionGetCollectList()
    {
        try {
            $memberId = $this->memberId;

            $searchData = Yii::$app->request->get();

            $server = new \common\service\person\CollectService();
            $data   = [
                'memberId' => $memberId,
                'resumeId' => $this->getResumeId(),
                'page'     => $searchData['page'],
            ];
            $list   = $server->getMiniCompanyList($data);

            return $this->success($list);
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

}