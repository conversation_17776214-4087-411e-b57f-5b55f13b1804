<?php

namespace miniApp\controllers;

use common\base\models\BaseResume;
use common\base\models\BaseResumeEquityPackageSetting;
use common\libs\WxMiniApp;
use common\service\payment\ResumeServer as ResumePaymentServer;
use frontendPc\models\ResumeEquityPackageSetting;
use frontendPc\models\ResumeOrder;
use yii;
use yii\base\Exception;
use yii\helpers\Url;

class ResumeOrderController extends BaseMiniAppController
{
    public function actionPay()
    {
        try {
            $equityPackageId = Yii::$app->request->post('equityPackageId');
            $code            = Yii::$app->request->post('code');
            if (empty($equityPackageId)) {
                throw new Exception('equityPackageId 不能为空');
            }

            if (empty($code)) {
                throw new Exception('code 不能为空');
            }

            // code 转成 openid
            $app    = WxMiniApp::getInstance();
            $res    = $app->codeToUserInfo($code);
            $openid = $res['openid'];

            // 求职者简历id
            $resumeId = $this->getResumeId();

            if (empty($resumeId)) {
                throw new Exception('resumeId 不能为空');
            }

            $service = new ResumePaymentServer();
            $res     = $service->setPlatform(ResumeOrder::PLATFORM_MINI)
                ->setPayway(ResumeOrder::PAYWAY_WXPAY)
                ->setPayChannel(ResumePaymentServer::CHANNEL_MINI)
                ->setNotifyUrl(Url::toRoute('notify', true) . '/payway/' . ResumeOrder::PAYWAY_WXPAY)
                ->setOparetion(ResumePaymentServer::PAY_ORDER)
                ->setParams([
                    'resumeId'        => $resumeId,
                    'equityPackageId' => $equityPackageId,
                    'openId'          => $openid,
                ])
                ->run();

            $res['detainment'] = BaseResumeEquityPackageSetting::getDetainmentForMini($equityPackageId,
                $this->getResumeId());

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionQuery()
    {
        try {
            $orderId = Yii::$app->request->get('orderId');
            if (empty($orderId)) {
                throw new Exception('orderId 不能为空');
            }

            // 求职者简历id
            $resumeId = $this->getResumeId();
            if (empty($resumeId)) {
                throw new Exception('resumeId 不能为空');
            }

            $service = new ResumePaymentServer();
            $res     = $service->setOparetion(ResumePaymentServer::PAY_QUERY)
                ->setPlatform(ResumeOrder::PLATFORM_MINI)
                ->setParams([
                    'resumeId' => $resumeId,
                    'orderId'  => $orderId,
                ])
                ->run();

            // 支付成功并且购买的权益组合包含求职资源
            // if ($res['status'] == BaseResumeOrder::STATUS_PAID) {
            //     // 查询订单的权益组合id
            //     $equityPackageId = ResumeOrder::findOneVal(['id' => $orderId], 'equity_package_id');
            //     // 查询权益组合下面的权益
            //     $equityIds = ResumeEquityPackageRelationSetting::getEquityIdsByPackageId($equityPackageId,
            //         ResumeEquitySetting::STATUS_ONLINE);
            //     // 查询权益id是否包含求职资源
            //     if (in_array(ResumeEquitySetting::ID_JOB_RESOURCES, $equityIds)) {
            //         // 获取权益组合信息
            //         $equityPackageRow = ResumeEquityPackageSetting::findOne($equityPackageId);
            //         // 获取求职资源二维码链接
            //         // $info['url']      = 'https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQHI8DwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyaDc1a2RVZjlmR0QxRVFtVWhBY18AAgS0W69kAwSAOgkA';
            //         $info                    = ResumeEquity::getJobResources($resumeId);
            //         $info['title']           = "已为您开通{$equityPackageRow['subname']}，服务时长为{$equityPackageRow['days']}天";
            //         $info['successContent']  = "“求职资料包”权益，需您扫码关注【高校人才网服务号】，回复“求职”，领取VIP专属求职学习资料包！（开通“钻石VIP”套餐 或 “黄金VIP·180天”套餐的会员用户，需回复“会员课程”，领取“高才优课”课程学习。）";
            //         $res['jobResourcesInfo'] = $info;
            //     }
            // }

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionClosePackage()
    {
        $equityPackageCategoryId = Yii::$app->request->get('equityPackageCategoryId');

        if (!$equityPackageCategoryId) {
            return $this->fail();
        }

        ResumeEquityPackageSetting::setIsFirstClose($equityPackageCategoryId, $this->getResumeId());

        return $this->success('');
    }

}
