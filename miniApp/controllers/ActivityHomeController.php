<?php
/**
 * create user：shannon
 * create time：2025/2/28 下午2:56
 */
namespace miniApp\controllers;

use common\components\MessageException;
use common\service\CommonService;
use common\service\zhaoPinHuiColumn\ActivityService;
use common\service\zhaoPinHuiColumn\HomeService;
use Yii;

class ActivityHomeController extends BaseMiniAppController
{
    /**
     * 活动汇总页面
     */
    public function actionIndex()
    {
        return $this->success((new HomeService())->setPlatform(CommonService::PLATFORM_MINI)
            ->run(Yii::$app->request->get()));
    }

    /**
     * 活动分页列表
     */
    public function actionGetActivityList()
    {
        try {
            return $this->success((new HomeService())->setPlatform(CommonService::PLATFORM_MINI)
                ->runList(Yii::$app->request->get()));
        } catch (MessageException $e) {
            return $this->fail($e->getMessage());
        }
    }
}