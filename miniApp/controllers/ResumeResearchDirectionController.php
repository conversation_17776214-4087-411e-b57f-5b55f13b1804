<?php

namespace miniApp\controllers;

use common\service\CommonService;
use common\service\resume\GetEditInfoService;
use common\service\resume\ResumeService;
use yii\base\Exception;
use Yii;

class ResumeResearchDirectionController extends BaseMiniAppController
{
    /**
     * 研究方向列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionIndex()
    {
        try {
            //获取服务对象
            $service = new ResumeService();
            //获取研究方向列表
            $research_list = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_RESEARCH_INDEX)
                ->init()
                ->run();

            return $this->success($research_list);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 研究方向添加
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAdd()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            //获取服务对象
            $service = new ResumeService();
            //调用添加服务
            $result = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_RESEARCH_ADD)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success($result);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 研究方向编辑
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEdit()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            //获取服务对象
            $service = new ResumeService();
            //调用编辑服务
            $result = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_RESEARCH_EDIT)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success($result);
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 研究方向删除
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelete()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            //获取服务对象
            $service = new ResumeService();
            //调用删除服务
            $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_RESEARCH_DELETE)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取编辑页面回显信息
     * @return void|\yii\console\Response|\yii\web\Response
     * @throws Exception
     */
    public function actionGetEditInfo()
    {
        $memberId = $this->memberId;
        $service  = new GetEditInfoService();
        $info     = $service->setOparetion(GetEditInfoService::INFO_RESEARCH_DIRECTION)
            ->init([
                'memberId' => $memberId,
            ])
            ->run();

        return $this->success($info);
    }
}