<?php

namespace miniApp\controllers;

use common\base\models\BaseChatRoom;
use common\service\chat\ChatApplication;
use common\service\CommonService;
use miniApp\models\ChatMessage;
use Yii;
use yii\db\Exception;

class ChatController extends BaseMiniAppController
{
    public function actionCreateRoom()
    {
        $jobId       = Yii::$app->request->post('jobId');
        $resumeId    = $this->getResumeId();
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $app  = ChatApplication::getInstance();
            $data = [
                'resumeId'    => $resumeId,
                'jobId'       => $jobId,
                'creatorType' => BaseChatRoom::CREATOR_TYPE_PERSON,
                'platform'    => CommonService::PLATFORM_MINI,
            ];
            $data = $app->resumeCreateRoom($data);
            $transaction->commit();

            return $this->success($data);
        } catch (\Exception $e) {
            //如果有错误数组，返回数组消息
            $errorInfo = $e->getMessage();

            $transaction->rollBack();

            return $this->fail($errorInfo);
        }
    }

    public function actionChangeJob()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $chatApp = ChatApplication::getInstance();
            $params  = [
                'chatId'   => \Yii::$app->request->post('chatId'),
                'jobId'    => \Yii::$app->request->post('jobId'),
                'memberId' => $this->memberId,
            ];
            $chatApp->resumechangeJob($params);

            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取聊天列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetChatList()
    {
        $memberId = $this->memberId;
        if (!$memberId) {
            return $this->success();
        }
        $keyword    = Yii::$app->request->get('keyword') ?: '';
        $readStatus = Yii::$app->request->get('readStatus') ?: '';

        try {
            $app = ChatApplication::getInstance();

            $data = $app->getPersonChatList($memberId, $keyword, $readStatus);

            return $this->success($data);
        } catch (\Exception $e) {
            //如果有错误数组，返回数组消息
            $errorInfo = $e->getMessage();

            return $this->fail($errorInfo);
        }
    }

    public function actionUpload()
    {
        $memberId = $this->memberId;
        $chatId   = Yii::$app->request->post('chatId');
        $fileName = Yii::$app->request->post('name');

        if (!$chatId) {
            return $this->fail('房间号不能为空');
        }

        try {
            $app          = ChatApplication::getInstance();
            $data         = $app->resumeUpload($memberId, $chatId);
            $data['name'] = $fileName;
            //修改名称
            ChatMessage::changeFileName($data['id'], $fileName);

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionDownload()
    {
        $memberId = $this->memberId;

        $messageId = Yii::$app->request->get('messageId');
        try {
            $app = ChatApplication::getInstance();

            // 这里直接是文件流了
            return $app->download($memberId, $messageId);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 申请发送附件前置检查
     * @return bool|void|\yii\console\Response|\yii\web\Response
     */
    public function actionCheckRequestFile()
    {
        $jobId  = Yii::$app->request->post('jobId');
        $chatId = Yii::$app->request->post('chatId');

        try {
            $app  = ChatApplication::getInstance();
            $data = $app->checkPersonRequestFile($jobId, $chatId);

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取聊天室信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetChatInfo()
    {
        $chatId   = Yii::$app->request->get('chatId');
        $memberId = $this->memberId;

        try {
            $app  = ChatApplication::getInstance();
            $data = $app->getChatInfo($chatId, $memberId);

            return $this->success($data);
        } catch (\yii\base\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取历史聊天记录
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetHistoryList()
    {
        $chatId    = Yii::$app->request->get('chatId');
        $messageId = Yii::$app->request->get('messageId');
        $pageLimit = Yii::$app->request->get('pageLimit');
        $memberId  = $this->memberId;

        try {
            $app  = ChatApplication::getInstance();
            $data = $app->getHistoryList($chatId, $memberId, $messageId, $pageLimit);

            return $this->success($data);
        } catch (\yii\db\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 设置聊天室置顶
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSetTop()
    {
        $transaction = Yii::$app->db->beginTransaction();
        $chatId      = Yii::$app->request->post('chatId');
        $memberId    = $this->memberId;
        try {
            $app = ChatApplication::getInstance();
            $app->setTop($chatId, $memberId);
            $transaction->commit();

            return $this->success();
        } catch (\yii\db\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除房间
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelRoom()
    {
        $transaction = Yii::$app->db->beginTransaction();
        $chatId      = Yii::$app->request->post('chatId');
        $memberId    = $this->memberId;
        try {
            $app = ChatApplication::getInstance();
            $app->delRoom($chatId, $memberId);
            $transaction->commit();

            return $this->success();
        } catch (\yii\db\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

}