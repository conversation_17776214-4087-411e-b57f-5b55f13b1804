<?php

namespace miniApp\controllers;

use common\base\models\BaseCategoryJob;
use common\base\models\BaseResume;
use common\base\models\BaseResumeIntention;
use common\helpers\IpHelper;
use common\service\CommonService;
use common\service\resume\GetEditInfoService;
use common\service\resume\ResumeService;
use miniApp\models\ResumeIntention;
use yii\base\Exception;
use Yii;

class ResumeIntentionController extends BaseMiniAppController
{
    /**
     * 保存临时求职意向
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSaveTempIntention()
    {
        $data        = \Yii::$app->request->post();
        $transaction = \Yii::$app->db->beginTransaction();

        try {
            //判断参数
            if (empty($data['educationId']) || empty($data['majorId']) || empty($data['jobCategoryId']) || empty($data['areaId'])) {
                return $this->fail('缺少必填参数');
            }
            $data['memberId'] = $this->memberId ?: '';
            $id               = ResumeIntention::saveTempIntention($data);
            $transaction->commit();

            return $this->success(['intentionId' => $id]);
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取临时求职意向信息
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetTempIntention()
    {
        $intentionId = \Yii::$app->request->post('id');
        try {
            $data = ResumeIntention::getTempIntention($intentionId);

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 求职意向列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionIndex()
    {
        try {
            //获取服务对象
            $service = new ResumeService();
            //获取求职意向列表
            $intention_list = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_INTENTION_INDEX)
                ->init()
                ->run();
            //获取求职者到岗状态
            $post_status_list = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_GET_RESUME_POST_WORK)
                ->init()
                ->run();

            return $this->success([
                'intention_list'   => $intention_list,
                'post_status_list' => $post_status_list,
            ]);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 求职意向添加
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAdd()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            //获取服务对象
            $service = new ResumeService();
            //调用添加服务
            $result = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_INTENTION_ADD)
                ->init()
                ->run();

            $transaction->commit();

            return $this->success($result);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 求职意向编辑
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEdit()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            //获取服务对象
            $service = new ResumeService();
            //调用编辑服务
            $result = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_INTENTION_EDIT)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success($result);
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 求职意向删除
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelete()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            //获取服务对象
            $service = new ResumeService();
            //调用删除服务
            $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_INTENTION_DELETE)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取求职意向经历数据
     * @return \yii\console\Response|\yii\web\Response
     * @throws Exception
     */
    public function actionGetEditInfo()
    {
        $id       = Yii::$app->request->get('id');
        $memberId = $this->memberId;
        if (!empty($id)) {
            $service = new GetEditInfoService();
            $info    = $service->setOparetion(GetEditInfoService::INFO_INTENTION)
                ->init([
                    'id'       => $id,
                    'memberId' => $memberId,
                ])
                ->run();

            return $this->success($info);
        } else {
            return $this->fail('求职意向id不能为空！');
        }
    }

    /**
     * 获取求职意向职位类型列表
     */
    public function actionGetCategoryJobList()
    {
        $resume_id      = $this->getResumeId();
        $intention_list = BaseResumeIntention::getIntentionList($resume_id);
        $result         = [];
        foreach ($intention_list as $k => $value) {
            $result[$k]['id']   = $value['id'];
            $result[$k]['name'] = BaseCategoryJob::getName($value['job_category_id']);
        }

        $this->success($result);
    }
}
