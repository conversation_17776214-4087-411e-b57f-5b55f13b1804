<?php

namespace miniApp\controllers;

use common\base\BaseController;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeComplete;
use common\libs\Cache;
use yii\db\Exception;

class BaseMiniAppController extends BaseController
{
    public $layout = false;
    public $memberId;
    public $token;
    public $_member;

    public string $errorResult = '';

    /**
     * 发生前在请求前
     * @param \yii\base\Action $action
     * @return bool|void|object
     * @throws \yii\web\BadRequestHttpException
     */
    public function beforeAction($action)
    {
        if (!parent::beforeAction($action)) {
            return false;
        }
        $this->setLogin();

        if (in_array($action->uniqueID, $this->ignoreLogin())) {
            return parent::beforeAction($action);
        }

        // 判断用户是否完成简历完善了
        $this->checkAuth($action->uniqueID);
        if (!empty($this->errorResult)) {
            echo $this->errorResult;
            exit;
        }

        return true;
    }

    /**
     * 设置登录信息
     */
    public function setLogin()
    {
        $token = \Yii::$app->request->getHeaders()
            ->get('authorization-token');

        if (empty($token)) {
            return;
        }

        $jwtAuth = new \common\libs\JwtAuth();
        $userId  = $jwtAuth->checkToken($token);

        if ($userId) {
            $this->token    = $token;
            $this->setMemberInfo($userId);

            $this->setActive();
        }
    }

    public function setMemberInfo($memberId)
    {
        $this->memberId = $memberId;
        $this->_member = BaseMember::findOne($memberId);
        // 这里做一个账号警用处理
        if ($this->_member->status != BaseMember::STATUS_ACTIVE || $this->_member->cancel_status != BaseMember::CANCEL_STATUS_NORMAL) {
            // 清理登陆状态
            $this->setErrorResult(403, 0, ($this->_member->cancel_status == BaseMember::CANCEL_STATUS_CANCELING) ? '账号注销中' : '账号已注销');
            echo $this->errorResult;
            exit;
        }
    }

    public function setActive()
    {
        // // 做一个简单的测试,把用户的id和现在的时间保存到缓存里面去,一段时间后再取出来,用于更新用户的活跃时间
        $userId   = $this->memberId;
        $actionId = \Yii::$app->controller->action->uniqueId;

        if ($userId && $actionId) {
            $key  = Cache::ALL_RESUME_ACTION_CONTROLLER_KEY;
            $time = CUR_TIMESTAMP;
            // 写集合
            Cache::zadd($key, $time, $userId);
        }
    }

    public function checkLogin()
    {
        // $token = \Yii::$app->request->getHeaders()
        //     ->get('authorization-token');
        //
        // if (empty($token)) {
        //     return false;
        // }
        //
        // $jwtAuth = new \common\libs\JwtAuth();
        // $userId  = $jwtAuth->checkToken($token);
        //
        // if ($userId) {
        //     $this->token    = $token;
        //     $this->memberId = $userId;
        //
        //     return true;
        // }

        if ($this->memberId) {
            return true;
        }

        return false;
    }

    /**
     * 获取简历ID
     * @return mixed
     */
    public function getResumeId()
    {
        $memberId = $this->memberId;

        return BaseResume::findOneVal(['member_id' => $memberId], 'id');
    }

    // 无论什么情况下都可以调用的接口
    public function ignoreLogin()
    {
        return [
            'member/save-email-account',
            'home/upload-avatar',
            'home/get-service-tips',
            'resume/upload',
            'resume/view-resume',
            'home/upload-avatar',
            'home/get-intention-job-list',
            'job/search',
            'job/detail',
            'job/get-list',
            'job/get-data-list',
            'company/detail',
            'company/get-announcement-list',
            'company/detail-filter',
            'company/get-job-list',
            'company/get-activity-list',
            'company/list-filter',
            'company/get-list',
            'company/get-recommend-list',
            'job/get-recommend-list',
            // 生成登录qrcode
            'member/login-qrcode',
            'announcement/detail',
            'announcement/get-job-list',
            'announcement/get-recommend-list',
            'job/get-recommend-list',
            'member/account-login',
            'member/send-mobile-login-code',
            'member/validate-mobile-login-code',
            'member/login-by-mini-code',
            'member/login-by-mini-login-code',
            'discover/home',
            'channel/get-config',
            'channel/get-live-config',
            'discover/hot-telecast',
            'area/get-area-default',
            'area/get-area-current',
            'area/get-area-tab-detail',
            'area/get-hot-ranking-list',
            'area/get-hot-ranking-detail',
            'area/get-search-column-list',
            'area/get-direct-city-list',
            'home/showcase',
            'home/get-recommend-job-list',
            'area/get-banner-list',
            'area/get-toutiao-list',
            'search/middle-showcase',
            'config/load-country-mobile-code',
            'config/get-company-nature-list',
            'search/get-resume-search-log',
            'search/search-for-announcement-list',
            'member/get-resume-attachment-list',
            'area/test',
            'search/search-for-job-list',
            'search/search-for-company-list',
            'announcement/search-for-announcement-list',
            'config/get-major-list',
            'config/get-all-area-list',
            'config/get-native-place',
            'config/get-household-register-list',
            'config/get-native-city-area-list',
            'config/get-announcement-type-list',
            'config/get-region-all-select',
            'config/get-education-list',
            'config/get-person-major-list',
            'config/get-hierarchy-major-list',
            'config/get-level2-major-list',
            'config/get-area-info',
            'config/get-welfare-label-list',
            'config/get-hierarchy-city-list',
            'config/get-category-job-list',
            'config/get-area-list',
            'config/get-full-area-list',
            'config/get-experience-list',
            'config/get-abroad-list',
            'config/get-school-type-list',
            'config/get-age-list',
            'config/get-title-list',
            'config/get-first-title-list',
            'config/get-political-status-list',
            'config/get-wage-range-list',
            'config/get-job-category-list',
            'config/get-nature-list',
            'config/get-job-status-list',
            'config/get-arrive-date-list',
            'config/get-project-cate-list',
            'config/get-trade-list',
            'config/get-certificate-list',
            'config/get-skill-list',
            'config/get-degree-type-list',
            'config/get-theme-list',
            'config/get-company-type-list',
            'config/get-on-site-apply-status',
            'config/get-off-site-apply-status',
            'config/get-paper-rank-list',
            'config/get-nation-list',
            'config/get-mini-share-info',
            'config/get-patent-rank-list',
            'config/get-person-category-job-list',
            'search/add-search-log',
            'search/delete-search-log',
            'search/delete-all-search-log',
            'resume/save-temp-intention',
            'home/get-person-page-default-tip',
            'home/get-person-info',
            'config/get-job-search-params',
            'resume-intention/get-temp-intention',
            'resume-intention/save-temp-intention',
            'announcement/get-home-showcase',
            'config/get-all-category-job-list',
            'job-subscribe/save',
            'home/showcase-click',
            'config/get-dictionary-text',
            'home/get-un-read-tip',
            'config/get-mini-person-major-list',
            //添加搜索记录
            'search/add-search-list-log',
            'config/mini-is-show-discover-live',
            // 下载个人附件简历(主要是用于预览)
            'resume/download-attachment',
            // 生成临时下载的token
            'resume/create-download-attachment-token',
            // 地区榜单(职位)
            'area/get-job-hot-list',
            'area/get-announcement-hot-list',
            'area/get-company-hot-list',
            // 收藏页面
            'job/get-collect-list',
            'announcement/get-collect-list',
            'company/get-collect-list',
            'config/get-base-url',
            'chat/get-chat-list',
            'config/get-emote-list',
            'config/get-base-showcase',
            'home/get-job-tool-info',
            'home/get-buy-guide-qrcode',
            'member/get-vip-filter-info',
            'resume/check-user-status',
            'member/check-mini-code-login',
            'member/scan-mini-login-code',
            'member/login-by-mini-mobile',
            'member/login-code-notice',
            'member/validate-mobile-login-mini-code',
            'home/check-is-show-follow-qr-code',
            'config/get-cache',
            'job-subscribe/get-info',
            'config/get-job-search-params-v2',
            'config/get-announcement-search-params',
            'activity/index',
            'activity-home/index',
            'activity-home/get-activity-list',
            'activity/get-company-list',
            'special-activity/get-detail',
            'special-activity/get-activity-schedule',
            'special-activity/get-activity-company-search-params',
            'special-activity/get-activity-company-search-activity-tab',
            'special-activity/get-activity-company-list',
            'special-activity/get-activity-schedule-v2',
            'resume/cancel-apply-cancel'
        ];
    }

    // 登录了但是没有完善完前三部也可以调用的接口
    public function getPerfectResumeList()
    {
        return [
            //获取用户基本信息
            'resume/get-user-base-info',
            //获取简历步数
            'resume/get-step-num',
            //保存用户基本信息
            'resume/save-user-base-info',
            //获取简历第二步信息
            'resume/get-step-two-info',
            //保存简历第二步信息
            'resume/save-step-two-info',
            //获取简历第三步信息
            'resume/get-step-three-info',
            //保存简历第三步信息
            'resume/save-step-three-info',
            //获取简历第三步下拉框信息
            'resume/get-step-three-params',
            //发送绑定手机号验证码
            'home/send-bind-mobile-code',
            //验证绑定手机号验证码
            'home/validate-bind-mobile-code',
            //更新用户状态
            'resume/update-user-resume-status',
            //获取用户信息
            'member/get-user-info',
            //政治面貌
            'config/get-political-status-list',
            //获取我的页面信息
            'home/get-person-info',
            //获取关注二维码信息
            'home/get-follow-qr-code-info',
            //判断是否绑定
            'home/check-is-bind',
            //获取隐私设置信息
            'resume/get-privacy-info',
            //修改匿名显示状态
            'resume/change-anonymous-status',
            //修改简历显示状态
            'resume/change-show-status',
            //获取谁看过我列表
            'company-view/get-list',
            //取消订阅接口
            'job-subscribe/cancel',
            // 直聊相关
            'config/get-emote-list',
            'home/get-resume-complete-pop-info',
            'home/set-resume-complete-pop-info',
            'home/check-is-subscribe',
            'job-apply/submit',

        ];
    }

    /**
     * 获取通用接口列表
     */
    public function getCurrencyApiList()
    {
        return [
            //学术论文增、删、改、查
            'resume-academic-page/index',
            'resume-academic-page/add',
            'resume-academic-page/edit',
            'resume-academic-page/delete',
            //保存学术专利增、删、改、查
            'resume-academic-patent/index',
            'resume-academic-patent/add',
            'resume-academic-patent/edit',
            'resume-academic-patent/delete',
            //保存学术专著增、删、改、查
            'resume-academic-book/index',
            'resume-academic-book/add',
            'resume-academic-book/edit',
            'resume-academic-book/delete',
            //保存学术奖励信息增、删、改、查
            'resume-academic-reward/index',
            'resume-academic-reward/add',
            'resume-academic-reward/edit',
            'resume-academic-reward/delete',
            //保存其他奖励信息增、删、改、查
            'resume-other-reward/index',
            'resume-other-reward/add',
            'resume-other-reward/edit',
            'resume-other-reward/delete',
            //保存资质证书增、删、改、查
            'resume-certificate/index',
            'resume-certificate/add',
            'resume-certificate/edit',
            'resume-certificate/delete',
            //保存技能语言增、删、改、查
            'resume-skill/index',
            'resume-skill/add',
            'resume-skill/edit',
            'resume-skill/delete',
            //获取用户信息
            'member/get-user-info',
            //获取简历第四步信息
            'resume/get-step-four-info',
            //保存简历第四步信息
            'resume/save-step-four-info',
            //获取简历完成度
            'resume/get-complete-percent',
            'company/collect',
            'job/collect',
            'announcement/collect',
            // 绑定qrcode
            'member/bind-qrcode',
            //检查用户是否可以投递
            'job-apply/check-member-complete-status',
            'home/check-is-bind',
            //获取关注二维码信息
            'home/get-follow-qr-code-info',
            //获取隐私设置信息
            'resume/get-privacy-info',
            //修改匿名显示状态
            'resume/change-anonymous-status',
            //修改简历显示状态
            'resume/change-show-status',
            //获取谁看过我列表
            'company-view/get-list',
            //获取意向职位类型列表
            'resume-intention/get-category-job-list',
            // 我的投递
            'job-apply/get-list',
            'off-site-job-apply/get-list',
            // 全部强提醒
            'resume-remind/get-all',
            // 职位邀约
            'job-invite/list',
            // 职位订阅
            'job-subscribe/get-info',
            // 查看用户是否可以投递
            'job-apply/check-user-apply-status',
            //获取简历步数
            'resume/get-step-num',
            //取消订阅接口
            'job-subscribe/cancel',
            // 常用语相关接口
            'chat-common-phrase/delete',
            'chat-common-phrase/get-list',
            'chat-common-phrase/edit',
            'chat/create-room',
            'chat/change-job',
            'chat/upload',
            'chat/download',
            'chat/check-request-file',
            'config/get-emote-list',
            'job/check-generate-report',
            'job/create-report',
            'announcement/check-generate-report',
            'announcement/create-report',
            'resume/get-vip-info',
            'resume/get-vip-pv-chart',
            'resume-order/pay',
            'resume-order/query',
            'resume-top/check',
            'resume-top/add',
            'resume-top/validate',
            'resume-order/close-package',
        ];
    }

    /**
     * 判断用户状态步数的url
     * @return string[]
     */
    public function checkUserStatusList()
    {
        return [
            'member/check-user-apply-status',
            'job-apply/check-announcement-apply',
        ];
    }

    /**
     * 根据情况来检查这个用户有没有权限操作
     */
    public function checkAuth($actionId)
    {
        if (!$this->checkLogin()) {
            $this->setErrorResult();

            return false;
        }

        //登录时缓存company表status字段，初审通过/终审通过需要更新缓存
        $info   = BaseMember::getLoginInfoByMemberId($this->memberId);
        $status = $info['status'];
        $type   = $info['type'];
        if ($type != BaseMember::TYPE_PERSON) {
            $this->setErrorResult();

            return false;
        }

        //先判断连接是否是特殊的，需要返回用户步数的
        if (in_array($actionId, $this->checkUserStatusList())) {
            //获取用户状态
            $resumeId     = BaseMember::getMainId($info['id']);
            $resumeStatus = BaseResume::findOneVal(['id' => $resumeId], 'status');
            //状态是未完成的，获取步数
            if ($resumeStatus == BaseResume::STATUS_WAIT_AUDIT) {
                $resumeStepNum = BaseResumeComplete::getResumeStep($resumeId);
                $this->setErrorResult(301, 0, (int)$resumeStepNum);

                return false;
            }
        } else {
            if ($status == BaseMember::STATUS_ACTIVE) {
                // 正常用户(就让其所有接口都走)
                // if (in_array($actionId, $this->getPerfectResumeList()) && !in_array($actionId,
                //         $this->getCurrencyApiList())) {
                //     $this->setErrorResult();
                //
                //     return false;
                // }

                return true;
            }

            if ($status == BaseMember::STATUS_WAIT_PERFECT_RESUME) {
                //未完成简历模块的用户
                //判断当前路由是否可以访问
                if (!in_array($actionId, $this->getPerfectResumeList()) && !in_array($actionId,
                        $this->getCurrencyApiList())) {
                    $this->setErrorResult();

                    return false;
                }
            }
        }
    }

    /**
     * 设置错误返回信息
     * @param $code
     * @param $result
     * @param $msg
     * @return void
     */
    public function setErrorResult($code = 403, $result = 0, $msg = '非法操作')
    {
        $rel = [
            'code'   => $code,
            'result' => $result,
        ];
        if (is_string($msg)) {
            $rel['msg'] = $msg;
        } elseif (is_int($msg)) {
            //前端要求，此处返回data
            $rel['data'] = $msg;
        }

        $this->errorResult = json_encode($rel);
    }

    public function notFound()
    {
        echo json_encode([
            'code'   => 404,
            'result' => 0,
            'msg'    => '找不到您要访问的页面',
        ], JSON_THROW_ON_ERROR);
        exit;
    }

    // /**
    //  * 不需要登录的接口
    //  * @return string[]
    //  */

    // public function ignoreLogin()
    // {
    //     return [
    //         'home/index',
    //         'member/account-login',
    //     ];
    // }

}