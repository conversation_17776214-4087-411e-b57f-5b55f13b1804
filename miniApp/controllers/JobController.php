<?php

namespace miniApp\controllers;

use common\base\models\BaseCommon;
use common\base\models\BaseJob;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobClickLog;
use common\base\models\BaseJobCollect;
use common\base\models\BaseJobReport;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEquity;
use common\base\models\BaseResumeEquitySetting;
use common\base\models\BaseResumeJobReportRecord;
use common\helpers\ArrayHelper;
use common\service\chat\CommonRule;
use common\service\job\RecommendService;
use common\service\match\MatchCompleteService;
use common\service\search\CommonSearchApplication;
use common\service\specialNeedService\JobInformationService;
use miniApp\models\Job;
use Yii;
use yii\base\Exception;

class JobController extends BaseMiniAppController
{
    /**
     * 找职位列表
     */
    public function actionGetList()
    {
        // 不登录只能看一页
        $memberId = $this->memberId;
        $get      = Yii::$app->request->get();
        if (!$memberId && $get['page'] > 1) {
            return $this->fail('请先登录');
        }

        $get['memberId'] = $memberId;
        $get['p']        = $get['p'] ?: $get['page'];

        // 这里有一个很特殊的参数,groupType,前端是["titleType_1","titleType_3","natureType_3","companyType_5","companyType_1","educationType_4","isFast_1"]这种格式的或者是数组的,我们要把前面的格式转成数组
        //        if (isset($get['groupType']) && !is_array($get['groupType'])) {
        //            // 首先得去掉前后的符号
        //            $get['groupType'] = trim($get['groupType'], '[]');
        //            // 去掉单引号双引号
        //            $get['groupType'] = str_replace('"', '', $get['groupType']);
        //            $get['groupType'] = str_replace("'", '', $get['groupType']);
        //            $get['groupType'] = explode(',', $get['groupType']);
        //        }
        // 迁移到服务层
        $app = new CommonSearchApplication();

        $get = ArrayHelper::clearNoValue($get);

        // 编制参数判断
        $resumeId = $this->getResumeId();
        if (BaseResumeEquity::checkEquity($resumeId, BaseResumeEquitySetting::ID_ESTABLISHMENT_QUERY) === false) {
            unset($get['isEstablishment'], $get['applyHeat']);
        }

        // 内存开大一点到1个g
        ini_set('memory_limit', '1024M');

        $data = $app->miniJobListSearch($get);

        foreach ($data['list'] as $k => $v) {
            $data['list'][$k]['id'] = $v['jobId'];
        }

        // 这里有一个问题,前端需要一个id,但是我们返回的是jobId,所以要加一个

        return $this->success($data);
    }

    /**
     * 获取职位详情
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionDetail()
    {
        try {
            $id = Yii::$app->request->get('id');
            if (!$id) {
                throw new Exception('id不能为空');
            }

            $memberId = $this->memberId;
            $resumeId = BaseMember::getMainId($memberId);

            $info = Job::getDetailService($id, BaseCommon::PLATFORM_MINI, $memberId);
            if (isset($info['contactInfo'])) {
                $info['contactInfo'] = ArrayHelper::merge($info['contactInfo'], CommonRule::checkChatActive($id));
            }

            $info['chat_btn_auth']  = CommonRule::checkButtonAuth($id, $memberId);
            $info['apply_status']   = BaseJobApplyRecord::checkJobApplyStatus($resumeId, $id);
            $info['collect_status'] = BaseJobCollect::getCollectStatus($id, $memberId);
            if ($info['status'] == BaseJob::STATUS_DELETE || $info['status'] == BaseJob::STATUS_WAIT || $info['is_show'] == BaseJob::IS_SHOW_NO) {
                throw new Exception('职位已下架');
            }

            $recommendService    = new RecommendService();
            $recommendSearchData = [
                'memberId' => $memberId ?: '',
                'jobId'    => $id,
                'limit'    => 18,
            ];
            $recommendListData   = $recommendService->setData($recommendSearchData)
                ->getRecommendList();
            $recommendList       = array_chunk($recommendListData, 3);
            //            $searchData    = [
            //                'job_category_id' => $info['job_category_id'],
            //                'company_id'      => $info['company_id'],
            //                'city_id'         => $info['city_id'],
            //                'job_id'          => $id,
            //            ];
            //            $recommendList = BaseJob::getDetailRecommendService($searchData, BaseCommon::PLATFORM_MINI);
            //记录点击日志
            $memberId = $this->memberId ?? 0;
            BaseJobClickLog::create($id, $memberId);

            // 1.8版本开始引入，竞争力相关内容
            $matchInfo = [
                'hasRecord'  => false,
                'url'        => '',
                'matchType'  => '',
                'showExceed' => '',
                'matchTips'  => '',
            ];

            if ($memberId) {
                $resumeId  = $this->getResumeId();
                $hasRecord = BaseResumeJobReportRecord::checkReportRecord($resumeId, $id);
                if ($hasRecord) {
                    $matchInfo['hasRecord']              = true;
                    $matchInfo['url']                    = BaseJob::getReportUrl($resumeId, $id);
                    $matchData                           = BaseJobReport::getJopMatchInfo($id, $resumeId,
                        MatchCompleteService::PLATFORM_MINI);
                    $matchInfo['matchType']              = $matchData['match_type'] ?? '';
                    $matchInfo['showExceed']             = $matchData['show_exceed'] ?? '';
                    $matchInfo['jobMatchCompleteExceed'] = $matchData['job_match_complete_exceed'] ?? '';
                    $matchInfo['matchTips']              = $matchData['match_tips'] ?? '';
                }
            }

            $info = (new JobInformationService())->handelJobDetail($info);

            return $this->success([
                'info'          => $info,
                'recommendList' => $recommendList,
                'matchInfo'     => $matchInfo,
            ]);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 收藏职位
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCollect()
    {
        try {
            $ids      = Yii::$app->request->post('jobId');
            $memberId = $this->memberId;
            $msg      = '';
            //获取返回文案
            if (count(explode(',', $ids)) == 1) {
                $isCollect = BaseJobCollect::checkIsCollect($memberId, $ids);
                if ($isCollect) {
                    //如是收藏状态，那此处就是取消收藏
                    $msg = '取消收藏成功';
                } else {
                    $msg = '收藏成功';
                }
            }
            //操作收藏/取消收藏
            $server = new \common\service\person\CollectService();
            $data   = [
                'type'     => $server::TYPE_JOB,
                'ids'      => $ids,
                'memberId' => $memberId,
            ];
            $server->init($data)
                ->run();

            return $this->success($msg);
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    public function actionGetCollectList()
    {
        try {
            $memberId = $this->memberId;

            $searchData = Yii::$app->request->get();

            $server = new \common\service\person\CollectService();
            $data   = [
                'memberId' => $memberId,
                'resumeId' => $this->getResumeId(),
                'page'     => $searchData['page'],
                'pageSize' => $searchData['pageSize'],
            ];
            $list   = $server->getMiniJobList($data);

            return $this->success($list);
        } catch (Exception $e) {
            return $this->result($e->getMessage());
        }
    }

    /**
     * 用于生成报告前的检查
     */
    public function actionCheckGenerateReport()
    {
        try {
            // 获取职位id
            $jobId = Yii::$app->request->get('jobId');
            // 是否需要确认,非必要参数
            $isConfirm = Yii::$app->request->get('isConfirm', 0);

            if (empty($jobId)) {
                throw new Exception('jobId 不能为空');
            }
            $resumeId = $this->getResumeId();
            if (empty($resumeId)) {
                throw new Exception('系统异常');
            }
            // 校验
            $res = BaseResumeJobReportRecord::checkGenerateReport($resumeId, $jobId, $isConfirm);

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 生成报告
     * 记录查看记录
     * 记录权益消耗
     */
    public function actionCreateReport()
    {
        try {
            // 获取职位id
            $jobId = Yii::$app->request->post('jobId');
            if (empty($jobId)) {
                throw new Exception('jobId 不能为空');
            }
            $resumeId = $this->getResumeId();
            if (empty($resumeId)) {
                throw new Exception('系统异常');
            }
            // 校验
            $res = BaseResumeJobReportRecord::checkGenerateReport($resumeId, $jobId);
            if ($res['jump_type'] == 1) {
                // 写记录
                BaseResumeJobReportRecord::saveReportAndEquityActionRecord($resumeId, $jobId);

                return $this->success(['jump_url' => BaseJob::getReportUrl($resumeId, $jobId)]);
            } else {
                throw new Exception('操作异常');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}