<?php

namespace miniApp\controllers;

use common\base\models\BaseDictionary;
use common\service\CommonService;
use common\service\resume\GetEditInfoService;
use common\service\resume\ResumeService;
use yii\base\Exception;
use Yii;

class ResumeAcademicPageController extends BaseMiniAppController
{
    /**
     * 学术论文列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionIndex()
    {
        try {
            //获取服务对象
            $service = new ResumeService();
            //获取学术论文列表
            $page_list = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_PAPER_INDEX)
                ->init()
                ->run();

            return $this->success($page_list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 学术论文添加
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAdd()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            //获取服务对象
            $service = new ResumeService();
            //调用添加服务
            $result = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_PAPER_ADD)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success($result);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 学术论文编辑
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEdit()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            //获取服务对象
            $service = new ResumeService();
            //调用编辑服务
            $result = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_PAPER_EDIT)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success($result);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 学术论文删除
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelete()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            //获取服务对象
            $service = new ResumeService();
            //调用删除服务
            $result = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeService::OPERATION_TYPE_PAPER_DELETE)
                ->init()
                ->run();
            $transaction->commit();

            return $this->success($result, '删除成功');
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取编辑页面回显
     * @return \yii\console\Response|\yii\web\Response
     * @throws Exception
     */
    public function actionGetEditInfo()
    {
        $id       = Yii::$app->request->get('id');
        $memberId = $this->memberId;
        $info     = [];
        if (!empty($id)) {
            $service = new GetEditInfoService();
            $info    = $service->setOparetion(GetEditInfoService::INFO_PAPER)
                ->init([
                    'id'       => $id,
                    'memberId' => $memberId,
                ])
                ->run();
        } else {
            return $this->fail('学术论文id不能为空！');
        }

        return $this->success($info);
    }
}