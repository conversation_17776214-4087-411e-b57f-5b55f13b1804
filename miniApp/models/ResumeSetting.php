<?php

namespace miniApp\models;

use common\base\models\BaseMember;
use common\base\models\BaseResumeSetting;
use frontendPc\models\ShieldCompany;

class ResumeSetting extends BaseResumeSetting
{

    /**
     * 获取隐私设置信息
     * @param $memberId
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function getPrivacyInfo($memberId)
    {
        //获取屏蔽公司列表
        $resumeId = BaseMember::getMainId($memberId);

        $info = self::find()
            ->where(['resume_id' => $resumeId])
            ->select([
                'is_hide_resume as isHideResume',
                'is_anonymous as isAnonymous',
            ])
            ->asArray()
            ->one();

        return $info;
    }

}