<?php
/**
 * create user：伍彦川
 * create time：2025/2/13 18:21
 */
namespace miniApp\models;

use common\base\models\BaseHwActivity;
use common\helpers\ArrayHelper;
use common\service\zhaoPinHuiColumn\SpecialActivityService;

class SpecialActivity
{
    public static function getDetail($specialActivityId, $resumeId)
    {
        return [
            'detail' => SpecialActivityService::getSpecialActivityDetail($specialActivityId, $resumeId),
        ];
    }

    /**
     * 获取活动时间轴列表
     * @param $specialActivityId
     * @param $resumeId
     * @return array
     */
    public static function getActivitySchedule($specialActivityId, $resumeId)
    {
        $formatActivityList    = SpecialActivityService::getCacheSpecialActivityRelationActivity([
            'specialActivityId' => $specialActivityId,
        ], $resumeId, 3);
        $activitySchedule      = SpecialActivityService::getSpecialActivitySchedule($formatActivityList, 2); // 时间轴数据

        $allActivity = $activitySchedule['effectiveActivityList'];

        // 查询是否有倒计时
        $activityStartCountDown = 0;

        $toBeHeld   = 0;
        $inProgress = 0;
        $ended      = 0;

        foreach ($allActivity as $item) {
            $toBeHeld   = $toBeHeld + $item['statusMap']['toBeHeld'];
            $inProgress = $inProgress + $item['statusMap']['inProgress'];
            $ended      = $ended + $item['statusMap']['ended'];

            foreach ($item['activityDate'] as $i) {
                foreach ($i['activityList'] as $activity) {
                    if (!empty($activity['startCountDown'])) {
                        $activityStartCountDown = $activity['startCountDown'];
                    }
                }
            }
        }

        // 根据情况设置内容
        $scrollView = 2;
        foreach ($allActivity as $key => $item) {
            if ($scrollView == 1) {
                break;
            }
            foreach ($item['activityDate'] as $k => $i) {
                if ($scrollView == 1) {
                    break;
                }
                foreach ($i['activityList'] as $activityKey => $activity) {
                    if ($toBeHeld > 0) {
                        if ($activity['activity_child_status'] == BaseHwActivity::ACTIVITY_CHILD_STATUS_NOT_START || $activity['activity_child_status'] == BaseHwActivity::ACTIVITY_CHILD_STATUS_BE_ABOUT_TO_START) {
                            $scrollView = 1;
                        }
                    } else {
                        if ($inProgress > 0) {
                            if ($activity['activity_child_status'] == BaseHwActivity::ACTIVITY_CHILD_STATUS_PROGRESS) {
                                $scrollView = 1;
                            }
                        } else {
                            $scrollView = 1;
                        }
                    }

                    if ($scrollView == 1) {
                        $allActivity[$key]['activityDate'][$k]['activityList'][$activityKey]['scrollView'] = $scrollView;
                        break;
                    }
                }
            }
        }

        $data = [
            'list' => $allActivity,
        ];

        if (!empty($activityStartCountDown)) {
            $data['startCountDown'] = $activityStartCountDown;
        }

        return $data;
    }

    /**
     * 获取活动时间轴列表
     * @param $specialActivityId
     * @param $resumeId
     * @return array
     */
    public static function getActivityScheduleV2($specialActivityId, $resumeId)
    {
        $formatActivityList    = SpecialActivityService::getCacheSpecialActivityRelationActivity([
            'specialActivityId' => $specialActivityId,
        ], $resumeId, 3);
        $activitySchedule      = SpecialActivityService::getSpecialActivitySchedule($formatActivityList, 1); // 时间轴数据

        // 查询是否有倒计时
        $activityStartCountDown = 0;

        foreach ($activitySchedule['effectiveActivityList'] as $item) {
            foreach ($item['activityDate'] as $i) {
                foreach ($i['activityList'] as $activity) {
                    if (!empty($activity['startCountDown'])) {
                        $activityStartCountDown = $activity['startCountDown'];
                    }
                }
            }
        }

        if (!empty($activityStartCountDown)) {
            $activitySchedule['startCountDown'] = $activityStartCountDown;
        }
        return $activitySchedule;
    }

    /**
     * 获取专场页面-筛选
     * @param $params
     * @param $resumeId
     * @return array
     */
    public static function getActivityCompanySearchParams($params, $resumeId)
    {
        return SpecialActivityService::getActivityCompanySearchParams($params, $resumeId);
    }

    /**
     * 获取专场-参会单位活动列表tab
     * @param $specailActivityId
     * @return void
     */
    public static function getActivityCompanySearchActivityTab($params)
    {
        return SpecialActivityService::getActivityCompanySearchActivityTab($params['specialActivityId'] ?? 0);
    }

    /**
     * 获取专场-参会单位列表
     * @param $specailActivityId
     * @return void
     */
    public static function getActivityCompanyList($params, $resumeId)
    {
        $params['pageSize'] = 30;
        return SpecialActivityService::getActivityCompany($params, $resumeId);
    }
}