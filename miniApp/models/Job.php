<?php

namespace miniApp\models;

use common\base\models\BaseAdminJobInvite;
use common\base\models\BaseAdminJobInviteConfig;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCommon;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobCollect;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseMemberActionLog;
use common\base\models\BaseOffSiteJobApply;
use common\base\models\BaseResume;
use common\base\models\BaseResumeLibraryInviteLog;
use common\base\models\BaseTrade;
use common\base\models\BaseWelfareLabel;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use common\libs\Cache;
use common\service\search\CommonSearchApplication;
use Yii;
use yii\db\Query;
use yii\helpers\Url;

class Job extends BaseJob
{
    /**
     * @throws \Exception
     */
    public static function getRecommendJobList($keyWords)
    {
        //这里做个登录未登录的数据分流
        if ($keyWords['resumeId']) {
            //这里拿一下置顶逻辑职位数组
            // $jobList = BaseJob::newSearchForList($keyWords)['list'];

            // 这里如果前端把编制参数传过来，我们就要判断一下，这个账号是否有查询编制的权限，没有就需要 unset 掉
            if (!empty($keyWords['isEstablishment'])) {
                if (!BaseResume::isEstablishment($keyWords['resumeId'])) {
                    unset($keyWords['isEstablishment']);
                }
            }

            unset($keyWords['resumeId']);

            foreach ($keyWords as $key => $item) {
                if (strlen($item) < 1) {
                    unset($keyWords[$key]);
                }
            }
            $app     = CommonSearchApplication::getInstance();
            $data    = $app->miniJobListSearch($keyWords);
            $jobList = $data['list'];
            //加多一层处理职位列表专业
            foreach ($jobList as &$value) {
                $major = '';
                if ($value['majorId']) {
                    $jobMajorIds = explode(',', $value['majorId']);
                    $length      = count($jobMajorIds);
                    if ($length > 1) {
                        $major = BaseMajor::getAllMajorName($jobMajorIds[0]);
                        $major .= "等";
                    } else {
                        $major = BaseMajor::getAllMajorName($jobMajorIds);
                    }
                }
                $value['majorName'] = $major;
            }
        } else {
            $jobList = BaseJob::getMiniAppRecommendJobList();
        }

        return $jobList;
    }

    /**
     * 获取职位邀约列表
     * @param $resumeId
     * @return array
     * @throws \Exception
     */
    public static function getInvite($resumeId)
    {
        $params    = Yii::$app->request->get();
        $admin_sql = BaseAdminJobInvite::find()
            ->alias('a')
            ->select([
                'a.id',
                'a.add_time',
                'a.company_id',
                'a.job_id',
                'a.resume_id',
                'a.is_remind_check',
                'c.full_name',
                'c.type',
                'c.nature',
                'c.label_ids',
                'c.logo_url',
                'c.package_type',
                'r.member_id',
                'j.status',
                'r.name as resume_name',
                'an.title as announcement_name',
                'IF(true,2,2) as source_type',
                'aj.text_content',
            ])
            ->innerJoin(['c' => BaseCompany::tableName()], 'a.company_id = c.id')
            ->innerJoin(['aj' => BaseAdminJobInviteConfig::tableName()], 'a.invite_id = aj.id')
            ->innerJoin(['r' => BaseResume::tableName()], 'a.resume_id = r.id')
            ->innerJoin(['j' => BaseJob::tableName()], 'a.job_id = j.id')
            ->leftJoin(['an' => BaseAnnouncement::tableName()], 'j.announcement_id = an.id')
            ->andWhere(['a.resume_id' => $resumeId])
            ->createCommand()
            ->getRawSql();
        $query     = BaseResumeLibraryInviteLog::find()
            ->alias('a')
            ->select([
                'a.id',
                'a.add_time',
                'a.company_id',
                'a.job_id',
                'a.resume_id',
                'a.is_remind_check',
                'c.full_name',
                'c.type',
                'c.nature',
                'c.label_ids',
                'c.logo_url',
                'c.package_type',
                'r.member_id',
                'j.status',
                'r.name as resume_name',
                'an.title as announcement_name',
                'IF(true,1,1) as source_type',
                'IF(true,"' . BaseResumeLibraryInviteLog::INVITE_TEXT_CONTENT . '",1) as text_content',
            ])
            ->innerJoin(['c' => BaseCompany::tableName()], 'a.company_id = c.id')
            ->innerJoin(['r' => BaseResume::tableName()], 'a.resume_id = r.id')
            ->innerJoin(['j' => BaseJob::tableName()], 'a.job_id = j.id')
            ->leftJoin(['an' => BaseAnnouncement::tableName()], 'j.announcement_id = an.id')
            ->andWhere(['a.resume_id' => $resumeId])
            ->union($admin_sql);
        $pageSize  = $params['limit'] ?: Yii::$app->params['defaultPageSize'];
        $query_all = (new Query())->from(['c' => $query]);
        $count     = $query_all->count();
        $pages     = self::setPage($count, $params['page'], $pageSize);
        $list      = $query_all->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('c.add_time desc')
            ->all();

        foreach ($list as &$v) {
            // 找到单位的全部标签和性质
            $v['type_name']         = BaseDictionary::getCompanyTypeName($v['type']);
            $v['nature_name']       = BaseDictionary::getCompanyNatureName($v['nature']);
            $v['education_name']    = BaseDictionary::getEducationName($v['education_type']);
            $v['logo_url']          = $v['logo_url'] ?: Yii::$app->params['defaultCompanyLogo'];
            $v['company_url']       = UrlHelper::createCompanyDetailPath($v['company_id']);
            $v['job_url']           = UrlHelper::createJobDetailPath($v['job_id']);
            $v['announcement_url']  = $v['announcement_id'] ? UrlHelper::createAnnouncementDetailPath($v['announcement_id']) : '';
            $v['announcement_name'] = $v['announcement_id'] ? $v['announcement_name'] : '';
            $labelArr               = [];
            if (!empty($v['label_ids'])) {
                $labelIdsArr = explode(',', $v['label_ids']);
                foreach ($labelIdsArr as $v1) {
                    array_push($labelArr, BaseDictionary::getCompanyLabelName($v1));
                }
            }
            $v['label'] = $labelArr;
            if ($v['source_type'] == 1) {
                $v['text_content'] = str_replace('{c}', $v['full_name'], $v['text_content']);
                $v['source_name']  = '单位邀约';
            } else {
                $v['text_content'] = BaseAdminJobInviteConfig::inviteTextReplace($v['text_content'], $v['resume_name'],
                    $v['job_name'], $v['full_name']);
                $v['source_name']  = '平台推荐';
            }

            $v['status']                   = intval($v['status']);
            $v['add_time']                 = TimeHelper::formatDateByYear($v['add_time']);
            $v['area_name']                = BaseArea::getAreaName($v['province_id']) . '-' . BaseArea::getAreaName($v['city_id']);
            $v['is_collect']               = (BaseJobCollect::checkIsCollect($v['member_id'],
                $v['job_id'])) ? '1' : '2';
            $v['apply_status']             = BaseJobApplyRecord::checkJobApplyStatus($v['resume_id'], $v['job_id']);
            $v['jobList']                  = self::getJobDetail($v['job_id'], $v['member_id']);
            $v['jobList']['applyStatus']   = BaseJobApplyRecord::checkJobApplyStatus($v['resume_id'], $v['job_id']);
            $v['jobList']['collectStatus'] = BaseJobCollect::getCollectStatus($v['job_id'], $v['member_id']);
            unset($v['type'], $v['nature'], $v['education_type'], $v['member_id'], $v['label_ids'], $v['resume_id'], $v['province_id'], $v['city_id']);
            $v['isTop']           = BaseJob::isTop($v['job_id']);
            $v['isFast']          = BaseJob::isFast($v['job_id']);
            $v['isEstablishment'] = BaseJob::isEstablishment($v['job_id']);
        }

        return [
            'list' => $list,
            // 'page' => [
            //     'count' => (int)$count,
            //     'limit' => (int)$pageSize,
            //     'page'  => (int)$params['page'],
            // ],
        ];
    }

    public static function getDetailService($id, $platform, $memberId = 0, $isDelCache = false)
    {
        $cacheKey = Cache::MINI_JOB_DETAIL_KEY . ':' . $id;
        //需要清空缓存重新设置
        if ($isDelCache) {
            Cache::delete($cacheKey);
        }
        $cacheData = Cache::get($cacheKey);
        if ($cacheData) {
            return json_decode($cacheData, true);
        } else {
            $detailInfo = BaseJob::getDetailService($id, $platform, $memberId);
            //设置下缓存
            Cache::set($cacheKey, json_encode($detailInfo), self::DETAIL_CACHE_TIME);

            return $detailInfo;
        }
    }
}