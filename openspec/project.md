
# Project Context

## Purpose
高校人才网平台面向高校、科研机构以及高校人才，为供需两端提供招聘与求职服务。项目目标是统一管理多端业务（PC 端、移动端、小程序、企业后台等），支撑职位发布、人才搜索、简历投递、活动宣讲、公告资讯等核心流程，同时满足运营活动、数据统计与风控审计等企业级诉求。

## Tech Stack
- PHP 7.4 + Yii2 Advanced 模板（多应用入口：`admin`、`api`、`frontendPc`、`h5`、`miniApp`、`companyH5` 等）
- MySQL 作为主数据存储，使用 ActiveRecord 访问
- Redis 提供缓存、会话、限流与队列存储（基于 `yiisoft/yii2-redis`、`yiisoft/yii2-queue`）
- 前端主要采用 Yii 视图渲染 + 定制化 JS/CSS，部分业务接入 H5 与小程序
- 队列任务、脚本由 `console` 与 `queue` 模块维护
- 搜索与推荐使用 MeiliSearch 增强人才、职位检索能力

## Project Conventions

### Code Style
- 遵循 PSR-12/PSR-2 基础规范，统一使用小驼峰命名方法与变量，大驼峰命名类名
- 控制器、服务、模型按中文注释补充复杂逻辑说明；代码注释必须使用中文
- 数组、SQL、配置统一放置在对应应用的 `config/` 或 `common/config/` 下，参数使用环境配置覆盖
- 返回 JSON 接口统一使用 `success`/`fail`/`error` 辅助方法，字段小驼峰输出

### Architecture Patterns
- 采用多应用分层架构：`Controller → Service → Model`，简单 CRUD 可直接由模型方法封装
- 控制器仅负责参数解析、基础校验、响应封装；业务校验、权限、事务由模型或服务层处理
- 公共逻辑沉淀在 `common/`（`base`、`helpers`、`service`、`models`、`libs`）中，多端共享
- 队列、定时任务与长耗时逻辑放在 `console`、`queue`、`timer`、`scripts` 子项目中

### Testing Strategy
- 采用 Codeception 进行单元测试、功能测试与 API 测试（`tests/` 目录）
- 核心模型方法需具备单元测试，关键接口提供 API 场景测试；复杂任务需在测试/预发环境回归
- 队列脚本、定时任务上线前需进行沙盒数据演练，并输出验收日志

### Git Workflow
- 默认使用 `develop`（或 `master`）为主分支，功能开发走 `feature/<topic>` 分支，Bug 修复使用 `hotfix/<issue>` 分支
- 提交信息采用动词开头的中文/英文简述（如 `feat: 新增高校宣讲会导入`），PR 内需关联需求或任务单
- 合并前必须通过 Code Review，确保 OpenSpec 变更已获批准且 `openspec validate --strict` 通过

## Domain Context
- 服务对象包括高校招聘单位、求职人才、平台运营、第三方合作伙伴
- 核心模块涵盖职位投放、人才简历、宣讲会、公告、活动、海外项目等，支持多终端差异化展现
- 平台强调合规（实名认证、资质审核）、风控（IP 黑名单、行为记录）、高并发投递与统计分析

## Important Constraints
- 控制器禁止直接持久化模型或做复杂业务校验，必须通过服务/模型方法封装并以异常形式反馈
- 需要支持高峰期职位投递与短信/邮件通知，关键接口要求 Redis 缓存与限流策略
- 所有对外数据返回需符合字段小驼峰、统一响应结构规范；日志需记录关键信息以便审计
- 生产环境运行在多服务器部署，发布需兼顾多应用入口与定时/队列服务连续性

## External Dependencies
- 短信与通知：`overtrue/easy-sms`（多渠道短信），`overtrue/wechat`（服务号/小程序），`workerman/gatewayclient`（实时推送）
- 存储与文件：阿里云 OSS（`aliyuncs/oss-sdk-php`）、七牛云（`qiniu/php-sdk`）、JDCloud SDK
- 搜索与推荐：MeiliSearch 集成（`zhuzixian520/yii2-meilisearch`）
- 文档处理：`mpdf/mpdf`、`setasign/fpdi`、`tecnickcom/tcpdf` 等 PDF/导出库，`viest/php-ext-xlswriter-ide-helper` 生成表格
- 安全与风控：`alibabacloud/green` 内容安全、`lcobucci/jwt` 鉴权、IP 数据库与分词（`fukuball/jieba-php`）用于简历分析
