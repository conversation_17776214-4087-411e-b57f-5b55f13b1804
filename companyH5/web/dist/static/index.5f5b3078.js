import{d as f,o as d,a as _,m as h,j as v,C as y}from"./@vue.55d1f6a8.js";/* empty css                      */import"./tdesign-mobile-vue.f5c0b98d.js";import{c as E,a as g}from"./vue-router.c55761ee.js";import"./@babel.a901a81c.js";import"./lodash.82bc23fe.js";import"./tdesign-icons-vue-next.93af7bac.js";(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const t of document.querySelectorAll('link[rel="modulepreload"]'))i(t);new MutationObserver(t=>{for(const e of t)if(e.type==="childList")for(const o of e.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&i(o)}).observe(document,{childList:!0,subtree:!0});function n(t){const e={};return t.integrity&&(e.integrity=t.integrity),t.referrerpolicy&&(e.referrerPolicy=t.referrerpolicy),t.crossorigin==="use-credentials"?e.credentials="include":t.crossorigin==="anonymous"?e.credentials="omit":e.credentials="same-origin",e}function i(t){if(t.ep)return;t.ep=!0;const e=n(t);fetch(t.href,e)}})();const L=f({name:"App",components:{},setup(){}});const R=(s,r)=>{const n=s.__vccOpts||s;for(const[i,t]of r)n[i]=t;return n};function A(s,r,n,i,t,e){const o=v("router-view");return d(),_("div",null,[h(o)])}const P=R(L,[["render",A],["__scopeId","data-v-8fa369ab"]]),b="modulepreload",B=function(s){return"/dist/"+s},m={},l=function(r,n,i){if(!n||n.length===0)return r();const t=document.getElementsByTagName("link");return Promise.all(n.map(e=>{if(e=B(e),e in m)return;m[e]=!0;const o=e.endsWith(".css"),p=o?'[rel="stylesheet"]':"";if(!!i)for(let a=t.length-1;a>=0;a--){const u=t[a];if(u.href===e&&(!o||u.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${e}"]${p}`))return;const c=document.createElement("link");if(c.rel=o?"stylesheet":b,o||(c.as="script",c.crossOrigin=""),c.href=e,document.head.appendChild(c),o)return new Promise((a,u)=>{c.addEventListener("load",a),c.addEventListener("error",()=>u(new Error(`Unable to preload CSS for ${e}`)))})})).then(()=>r())},q="/",O="/mobile",k=[{path:"/",name:"home",meta:{title:"\u9996\u9875"},component:()=>l(()=>import("./index.d2e38b5a.js"),["static/index.d2e38b5a.js","static/tdesign-mobile-vue.f5c0b98d.js","static/@babel.a901a81c.js","static/lodash.82bc23fe.js","static/@vue.55d1f6a8.js","static/tdesign-icons-vue-next.93af7bac.js","static/tdesign-icons-vue-next.8494edcb.css","static/tdesign-mobile-vue.2d3c9da7.css","static/vue-router.c55761ee.js","static/index.12112281.css","static/normalize.9d9ae4af.css"])},{path:"/resume/:id",name:"resume",meta:{title:"\u4EBA\u624D\u7B80\u5386"},component:()=>l(()=>import("./index.6d0af2bf.js"),["static/index.6d0af2bf.js","static/vue-router.c55761ee.js","static/@vue.55d1f6a8.js","static/axios.130f080d.js","static/qs.42923ca5.js","static/side-channel.35f3ad64.js","static/get-intrinsic.a021608e.js","static/has-symbols.0242649e.js","static/function-bind.e9dd7dba.js","static/has.d83c2916.js","static/call-bind.bad4e02d.js","static/object-inspect.3f07f329.js","static/@babel.a901a81c.js","static/tdesign-mobile-vue.f5c0b98d.js","static/lodash.82bc23fe.js","static/tdesign-icons-vue-next.93af7bac.js","static/tdesign-icons-vue-next.8494edcb.css","static/tdesign-mobile-vue.2d3c9da7.css","static/index.3120877a.css","static/normalize.9d9ae4af.css"])},{path:"/no-permission",name:"noPermission",meta:{title:"\u6743\u9650\u53D7\u9650"},component:()=>l(()=>import("./permission.1de96c02.js"),["static/permission.1de96c02.js","static/@vue.55d1f6a8.js","static/vue-router.c55761ee.js","static/tdesign-mobile-vue.f5c0b98d.js","static/@babel.a901a81c.js","static/lodash.82bc23fe.js","static/tdesign-icons-vue-next.93af7bac.js","static/tdesign-icons-vue-next.8494edcb.css","static/tdesign-mobile-vue.2d3c9da7.css","static/permission.5150ee6a.css","static/normalize.9d9ae4af.css"])}],D=[{path:"/:path(.*)*",name:"Error",meta:{title:"\u60A8\u8BBF\u95EE\u7684\u9875\u9762\u4E0D\u5B58\u5728"},component:()=>l(()=>import("./index.7bb17477.js"),["static/index.7bb17477.js","static/@vue.55d1f6a8.js"])}],$=E({history:g(O),routes:[...k,...D]}),C=y(P).use($);C.mount("#app");export{R as _,q as b};
