<?php

namespace companyH5\controllers;

use common\base\BaseController;
use common\base\models\BaseMember;
use common\base\models\BaseMemberLoginForm;
use common\base\models\BaseSeoUserAgent;
use common\helpers\DebugHelper;
use common\libs\Cache;
use EasyWeChat\Factory;
use Yii;

class BaseCompanyH5Controller extends BaseController
{
    public function beforeAction($action)
    {
        //对蜘蛛行为进行处理
        if (!BaseSeoUserAgent::isAllowAccess()) {
            BaseSeoUserAgent::redirect503();
        }
        if (!parent::beforeAction($action)) {
            return false;
        }
        $this->testLogin();
        if (in_array($action->uniqueID, $this->ignoreLogin())) {
            return parent::beforeAction($action);
        }

        if (Yii::$app->user->isGuest) {
            $redirectUrl = Yii::$app->request->get('redirect');
            if (empty($redirectUrl)) {
                Yii::$app->response->setStatusCode(302)
                    ->send();
                exit();
            }
            // 微信授权登录
            $this->wxLogin();

            return false;
        } else {
            $model = new BaseMemberLoginForm();
            $model->setMemberInfo(true);
            Yii::$app->params['user'] = BaseMember::getLoginInfo();
            $this->setActive();
        }

        return parent::beforeAction($action);
    }

    public function wxLogin()
    {
        // 使用公众号登录.easywechat那一套
        $config = \Yii::$app->params['wx']['companyPublic'];
        $app    = Factory::officialAccount($config);
        $oauth  = $app->oauth;

        // 未登录
        if (empty($_SESSION['wechat_user'])) {
            // 当前路由
            $realUrl = Yii::$app->request->url;

            Yii::$app->session->set('target_url', $realUrl);

            $redirectUrl = $oauth->redirect();

            header("Location: {$redirectUrl}");
            exit;
        }

        // $redirectUrl = $oauth->redirect();
        // // 跳转
        // header("Location: {$redirectUrl}");
        //
        // exit;
    }

    /**
     * 无需登录就可以操作的控制器
     * @return string[]
     */
    public function ignoreLogin()
    {
        return [
            // 'home/index',
            'home/wx-callback',
            //            'resume/get-info',
        ];
    }

    public function notFound()
    {
        Yii::$app->response->setStatusCode(404)
            ->send();
        echo $this->renderPartial('/home/<USER>');
        exit();
    }

    public function setActive()
    {
        // // 做一个简单的测试,把用户的id和现在的时间保存到缓存里面去,一段时间后再取出来,用于更新用户的活跃时间
        $userId   = Yii::$app->user->id;
        $actionId = Yii::$app->controller->action->uniqueId;

        if ($userId && $actionId) {
            $key  = Cache::ALL_RESUME_ACTION_CONTROLLER_KEY;
            $time = CUR_TIMESTAMP;
            // 写集合
            Cache::zadd($key, $time, $userId);
        }
    }

    public function testLogin()
    {
        if (Yii::$app->params['environment'] != 'prod') {
            // header里面有一个memberId参数
            $memberId = Yii::$app->request->getHeaders()
                ->get('memberId');
            if (Yii::$app->user->id == $memberId) {
                return true;
            }

            if ($memberId) {
                return (new BaseMemberLoginForm())->loginById($memberId);
            }
        }
    }

}