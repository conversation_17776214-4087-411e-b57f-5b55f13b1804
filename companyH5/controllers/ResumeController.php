<?php

namespace companyH5\controllers;

use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyMemberWxBind;
use common\base\models\BaseCompanyResumePvTotal;
use common\base\models\BaseCompanyViewResume;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyAbroadTotal;
use common\base\models\BaseJobApplyHandleLog;
use common\base\models\BaseJobContactSynergy;
use common\service\job\BaseService;
use common\service\job\JobApplyHandleService;
use frontendPc\models\Resume;
use Yii;
use common\base\models\BaseCompany;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\helpers\MaskHelper;
use common\helpers\DebugHelper;

class ResumeController extends BaseCompanyH5Controller
{
    public $layout = false;

    public function actionDetailIndex()
    {
        // 这里是做index.html的渲染

        return $this->render('/home/<USER>');
    }

    /**
     * 获取简历详情
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionDetail()
    {
        $type             = 1;//正常进入
        $resumeInfo       = [];
        $handleButtonList = [];//简历按钮数据
        //获取登录单位的memberID
        $companyMemberId = Yii::$app->user->id;
        // 这里是拿信息的,前端传投递的id过来,然后就返回求职者的信息回去
        $id = Yii::$app->request->get('id');
        if ($id <= 0) {
            return $this->fail('缺少参数');
        }
        //获取投递信息
        $apply_info = BaseJobApply::findOne($id);
        if (!$apply_info) {
            return $this->fail('投递记录不存在');
        }
        //看看账号绑定了没有
        $company_member_info = BaseCompanyMemberInfo::findOne(['member_id' => $companyMemberId]);

        if (!$company_member_info || ($company_member_info->is_wx_bind == BaseCompanyMemberInfo::IS_WX_BIND_NO)) {
            $type = 3;//请先绑定

            return $this->success([
                'type'             => $type,
                'resumeInfo'       => $resumeInfo,
                'handleButtonList' => $handleButtonList,
            ]);
        }
        if ($company_member_info->company_member_type == BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_SUB) {
            //获取职位协同账号信息IDs
            $companyMemberIds = BaseJobContactSynergy::find()
                ->select('company_member_info_id')
                ->andWhere(['job_id' => $apply_info->job_id])
                ->asArray()
                ->column();
            if (!in_array($company_member_info->id, $companyMemberIds)) {
                $type = 2;//无权访问

                return $this->success([
                    'type'             => $type,
                    'resumeInfo'       => $resumeInfo,
                    'handleButtonList' => $handleButtonList,
                ]);
            }
        }
        //获取绑定信息
        $bind_info = BaseCompanyMemberWxBind::findOne(['company_member_id' => $companyMemberId]);
        if ($company_member_info->company_id != $apply_info->company_id || $companyMemberId != $bind_info->company_member_id) {
            $type = 2;//无权访问

            return $this->success([
                'type'             => $type,
                'resumeInfo'       => $resumeInfo,
                'handleButtonList' => $handleButtonList,
            ]);
        }
        $resumeMemberId = $apply_info->resume_member_id;

        //获取数据
        $resumeInfo = BaseResume::getInfo($resumeMemberId);

        $resumeInfo['userInfo']['fullMobile'] = BaseMember::getFormatFullMobile($resumeInfo['userInfo']['mobile'], $resumeInfo['userInfo']['mobileCode']);

        if (!$resumeInfo['userInfo']['isDefaultAvatar']) {
            //不是默认头像，给个模糊处理
            // $resumeInfo['userInfo']['avatar'] = MaskHelper::getImage($resumeInfo['userInfo']['avatar']);
        }
        //        个人身份经验获取
        //        $resumeInfo['userInfo']['identityExperience'] = BaseResume::getIdentityExperienceText($model['resume_id']);
        //获取活跃度
        $resumeInfo['userInfo']['activeTime'] = BaseMember::getUserActiveTime($resumeMemberId);

        $resumeInfo['companyName'] = BaseCompany::findOneVal(['id' => $apply_info->company_id], 'full_name');
        //状态
        $resumeInfo['applyResumeStatus']     = $apply_info->status;
        $resumeInfo['applyResumeStatusText'] = BaseJobApply::PERSON_STATUS_LIST[$apply_info->status];
        //简历按钮数据-赋值
        $handleButtonList = BaseJobApply::COMPANY_H5_HANDLE_BUTTON_LIST[$apply_info['status']][$apply_info['company_mark_status']];

        // 放开特殊判断
        //        if ($apply_info->equity_status == BaseJobApply::EQUITY_STATUS_EFFECT) {
        if ($apply_info->is_check != BaseJobApply::IS_CHECK_YES) {
            //创建查看日志
            $service = new JobApplyHandleService();
            $service->setOperator($companyMemberId, BaseService::OPERATOR_HANDLE_TYPE_COMPANY)
                ->setData([
                    'id'          => $id,
                    'handle_type' => BaseJobApplyHandleLog::TYPE_VIEW,
                ])
                ->runCheck();
        }
        //        }

        //做一个PV统计
        BaseCompanyResumePvTotal::updateDailyTotalPv($apply_info->resume_id);

        return $this->success([
            'type'             => $type,
            'resumeInfo'       => $resumeInfo,
            'handleButtonList' => $handleButtonList,
        ]);
    }

}