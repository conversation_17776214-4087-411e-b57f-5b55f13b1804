# 获取简历详情接口

## 基本信息

- **接口名称**: 获取简历详情
- **请求方式**: GET
- **接口地址**: `/api/person/resume/get-resume-detail`
- **需要登录**: 是

## 请求参数

无

## 返回参数

### 成功响应

```json
{
  "msg": "操作成功",
  "result": 1,
  "data": {
    "basicModule": {
      "avatar": "https://example.com/avatar.jpg",
      "workStatus": 1,
      "isPostdoc": 2,
      "isAbroad": 2,
      "gender": 1,
      "name": "张三",
      "englishName": "Zhang San",
      "resumePosition": "高级研发工程师",
      "resumeTagList": ["博士", "海归","顶刊发文"],
      "resumeTitleList": ["教授", "博导"],
      "topEducationInfo": {
        "id": "1",
        "schoolId": "1",
        "school": "",
        "schoolName": "清华大学",
        "majorId": "100",
        "majorCustom": "",
        "educationId": "4",
        "majorName": "计算机科学与技术",
        "educationName": "博士"
      },
      "researchDirectionInfo": {
        "id": "1",
        "content": "用户研究方向"
      },
      "birthday": "1990.01",
      "residence": "北京市海淀区",
      "mobile": "138****1234",
      "email": "<EMAIL>",
      "academicIndexList": [
        {
          "url": "http://www.example.com",
          "title": "模版"
        }
      ],
      "topPaperInfo": {
        "id": "1",
        "title": "题目"
      },
      "topConferenceInfo": {
        "id": "1",
        "conference_name": "ICML 2023"
      }
    },
    "advantageModule": {
      "content": "具有丰富的机器学习项目经验，熟练掌握Python、TensorFlow等技术栈..."
    },
    "intentionModule": {
      "list": [
        {
          "id": "1",
          "name": "软件工程师",
          "areaId": "1,3",
          "maxWage": "25000",
          "minWage": "15000",
          "wageType": "1",
          "preference": "全职",
          "areaName": "北京市,上海市",
          "areaNameList": ["北京市", "上海市"]
        },
        {
          "id": "2",
          "name": "产品经理",
          "areaId": "",
          "maxWage": "30000",
          "minWage": "20000",
          "wageType": "1",
          "preference": "全职",
          "areaName": ""
        },
        {
          "id": "3",
          "name": "UI设计师",
          "areaId": "5",
          "maxWage": "20000",
          "minWage": "10000",
          "wageType": "2",
          "preference": "兼职",
          "areaName": "广州市",
          "areaNameList": ["广州市"]
        }
      ]
    },
    "researchDirectionModule": {
      "id": "1",
      "content": "人工智能在自然语言处理中的应用研究",
      "overview": "本研究聚焦于Transformer模型在多语言语义理解中的优化方案，通过引入动态注意力机制提升模型鲁棒性",
      "researchFieldList": [
        "人工智能",
        "自然语言处理",
        "深度学习"
      ]
    },
    "postdoctorWorkModule": {
      "list": [
        {
          "resumePostdoctorWorkId": "1",
          "institutionName": "清华大学 • 麻省理工学院 联合培养",
          "relyingInstitution": "清华大学",
          "relyingInstitutionId": "1001",
          "relyingInstitutionName": "清华大学",
          "isAbroad": "0",
          "isJointTraining": "1",
          "joinTrainingInstitution": "麻省理工学院",
          "joinTrainingInstitutionId": "2001",
          "joinTrainingInstitutionName": "麻省理工学院",
          "beginDate": "2020-09",
          "endDate": "",
          "isNow": "1",
          "workDate": "2020-09 至今",
          "collegeLabGroup": "人工智能实验室",
          "researchDirection": "机器学习与计算机视觉交叉研究",
          "content": "主要从事深度学习在医学影像分析中的应用研究，负责3项国家级课题的核心算法开发",
          "cooperativeMentor": "张明教授",
          "mentorTitleList": [
            "长江学者",
            "博士生导师"
          ],
          "stageRelation": {
            "achievementStr": [
              "发表SCI论文3篇",
              "申请发明专利2项"
            ],
            "recordStr": [
              "参与国际学术会议2次",
              "主持校级科研项目1项"
            ]
          },
          "stageTagList": [
            "国家自然科学基金项目",
            "发表SCI论文3篇",
            "申请发明专利2项",
            "参与国际学术会议2次"
          ],
          "researchFieldRelationList": [
            "计算机视觉",
            "深度学习",
            "医学影像分析"
          ]
        },
        {
          "resumePostdoctorWorkId": "2",
          "institutionName": "北京大学",
          "relyingInstitution": "北京大学",
          "relyingInstitutionId": "1002",
          "relyingInstitutionName": "北京大学",
          "isAbroad": "1",
          "isJointTraining": "0",
          "joinTrainingInstitution": "",
          "joinTrainingInstitutionId": "0",
          "joinTrainingInstitutionName": "",
          "beginDate": "2018-03",
          "endDate": "2020-08",
          "isNow": "0",
          "workDate": "2018-03 至 2020-08",
          "collegeLabGroup": "理论物理研究所",
          "researchDirection": "量子计算与量子信息理论",
          "content": "专注于量子纠缠态的数学建模研究，参与欧盟量子旗舰计划合作项目",
          "cooperativeMentor": "李华院士",
          "mentorTitleList": [
            "科学院院士",
            "国家杰出青年基金获得者"
          ],
          "stageRelation": {
            "achievementStr": [
              "发表EI论文5篇",
              "参与编写专著1部"
            ],
            "recordStr": [
              "赴剑桥大学访问交流6个月"
            ]
          },
          "stageTagList": [
            "欧盟量子旗舰计划",
            "发表EI论文5篇",
            "参与编写专著1部",
            "赴剑桥大学访问交流6个月"
          ],
          "researchFieldRelationList": [
            "量子计算",
            "量子信息",
            "理论物理"
          ]
        }
      ]
    },
    "educationModule": {
      "list": [
        {
          "id": 1,
          "schoolName": "清华大学",
          "majorName": "计算机科学与技术",
          "educationName": "博士",
          "beginDate": "2016-09",
          "endDate": "2020-06",
          "isNow": 0,
          "description": "主要研究方向为机器学习"
        }
      ]
    },
    "workModule": {
      "list": [
        {
          "id": 1,
          "companyName": "腾讯科技",
          "positionName": "算法工程师",
          "beginDate": "2020-07",
          "endDate": "2023-12",
          "isNow": 0,
          "description": "负责推荐算法的设计与优化"
        }
      ]
    },
    "overseasExperienceModule": {
      "list": [
        {
          "id": 1,
          "institutionName": "Stanford University",
          "positionName": "访问学者",
          "beginDate": "2019-01",
          "endDate": "2019-12",
          "description": "在人工智能实验室进行学术交流"
        }
      ]
    },
    "achievementModule": {
      "conferenceColumn": [
        {
          "id": 1,
          "title": "深度学习在自然语言处理中的应用",
          "conferenceLevelName": "A类",
          "subtitleStr": "第一作者",
          "description": "提出了一种新的神经网络架构",
          "awards": "最佳论文奖",
          "researchFieldList": ["自然语言处理"],
          "conferenceName": "AAAI 2023",
          "publishTime": "2023-02",
          "addressDetail": "美国华盛顿",
          "pageNumber": "1-10",
          "collectionName": "AAAI Proceedings",
          "conferenceLevel": 1,
          "authorPosition": 1,
          "acceptanceRate": "20%",
          "citationCountFiveYear": 15
        }
      ],
      "patentColumn": [
        {
          "id": 1,
          "name": "一种基于深度学习的图像识别方法",
          "patentNumber": "CN202310123456.7",
          "patentType": 1,
          "authorPosition": 1,
          "applicationDate": "2023-01-15",
          "authorizationDate": "2023-06-20",
          "description": "该专利提出了一种新的图像识别算法"
        }
      ],
      "bookColumn": [
        {
          "id": 1,
          "name": "机器学习实战指南",
          "isbn": "978-7-111-12345-6",
          "publishHouse": "机械工业出版社",
          "publishTime": "2023-03",
          "authorPosition": 1,
          "description": "一本关于机器学习实践的专业书籍"
        }
      ],
      "pageColumn": [
        {
          "id": 1,
          "title": "深度学习算法优化研究",
          "journalName": "计算机学报",
          "journalLevel": 1,
          "authorPosition": 1,
          "publishTime": "2023-04",
          "pageNumber": "1-15",
          "doi": "10.11897/SP.J.1016.2023.00001",
          "citationCountFiveYear": 25,
          "recordStrList": ["SCI", "EI"],
          "recordList": [1, 2],
          "researchFieldList": ["机器学习"]
        }
      ],
      "recordMap": {
        "1": "SCI",
        "2": "EI",
        "3": "CSSCI"
      },
      "achievementStr": "发表SCI论文5篇、会议论文3篇",
      "recordStr": "SCI、EI收录"
    }
  }
}
```

### 失败响应

```json
     {
  "msg": "操作成功",
  "result": 1,
  "data": {
    "basicModule": {
      "avatar": "https://example.com/avatar.jpg",
      "workStatus": 1,
      "isPostdoc": 2,
      "isAbroad": 1,
      "gender": 1,
      "name": "张三",
      "englishName": "Zhang San",
      "resumePosition": "高级研发工程师",
      "resumeTagList": [
        "博士",
        "海归"
      ],
      "resumeTitleList": [
        "教授",
        "博导"
      ],
      "topEducationInfo": {
        "schoolName": "清华大学",
        "majorName": "计算机科学与技术",
        "educationName": "博士"
      },
      "researchDirectionInfo": {
        "name": "人工智能"
      },
      "birthday": "1990.01",
      "residence": "北京市海淀区",
      "mobile": "138****1234",
      "email": "<EMAIL>",
      "academicIndexList": [
        {
          "name": "影响因子",
          "value": "15.2"
        }
      ],
      "topPaperInfo": {
        "title": "深度学习在图像识别中的应用",
        "journalName": "Nature"
      },
      "topConferenceInfo": {
        "title": "基于神经网络的语音识别技术",
        "conferenceName": "ICML 2023"
      }
    },
    "advantageModule": {
      "content": "具有丰富的机器学习项目经验，熟练掌握Python、TensorFlow等技术栈..."
    },
    "intentionModule": {
      "list": [
        {
          "id": "101",
          "name": "教授",
          "areaId": "110000,310000",
          "areaName": "北京市,上海市",
          "areaNameList": [
            "北京市",
            "上海市"
          ],
          "maxWage": "50",
          "minWage": "40",
          "wageType": "1",
          "preference": "科研平台健全"
        }
      ]
    },
    "researchDirectionModule": {
      "id": "5",
      "content": "专长于高性能计算平台设计",
      "overview": "主持国家重点研发计划",
      "researchFieldList": [
        "计算机科学",
        "人工智能"
      ]
    },
    "achievementModule": {
      "tagStrList": [
        "中科院分区（一区 3 篇，二区 5 篇）",
        "JCR 分区（Q1 2 篇，Q3 1 篇）",
        "SCI 10 篇",
        "北大核心 2 篇",
        "累计 IF 区间：5-10",
        "最高 IF：8.5",
        "总被引次数：120",
        "H 指数：6"
      ],
      "cas1": "3",
      "cas2": "5",
      "cas3": "",
      "cas4": "",
      "jcr1": "2",
      "jcr2": "",
      "jcr3": "1",
      "jcr4": "",
      "sciCount": "10",
      "eiCount": "",
      "ssciCount": "",
      "cscdCount": "",
      "cssciCount": "",
      "pkuCoreCount": "2",
      "techCoreCount": "",
      "cumulativeImpact": "2",
      "maxImpact": "8.5",
      "totalCitations": "120",
      "hIndex": "6",
      "resumeResearchAchievementId": "1001"
    },
    "educationModule": {
      "list": [
        {
          "resumeEducationId": "10001",
          "educationId": "4",
          "schoolInfo": {
            "id": "501",
            "name": "清华大学",
            "logoUrl": "https://example.com/school-logos/tsinghua.png",
            "isAbroad": "2",
            "qsRankingRange": "1-50",
            "labelName": "985,211, 双一流"
          },
          "jointSchoolInfo": {
            "id": "805",
            "name": "麻省理工学院",
            "logoUrl": "https://example.com/school-logos/mit.png",
            "isAbroad": "1",
            "qsRankingRange": "1-10",
            "labelName": "世界顶尖大学，常春藤联盟"
          },
          "educationName": "博士研究生",
          "schoolNameStr": "清华大学・麻省理工学院",
          "schoolLabelList": [
            "985",
            "211",
            "双一流",
            "世界顶尖大学",
            "常春藤联盟",
            "海外"
          ],
          "majorLevel1": "08",
          "majorLevel2": "0812",
          "majorLevel3": "081201",
          "majorCustom": "",
          "majorId": "3025",
          "isFullTime": "1",
          "isSinoForeignJointTraining": "1",
          "isMasterBachelorCombined": "2",
          "isBachelorMasterCombined": "2",
          "isBachelorMasterContinuous": "2",
          "isJuniorCollegeToBachelor": "2",
          "isOnlyPhd": "1",
          "isBachelorPhdCombined": "2",
          "cultivationNatureLabelStrList": [
            "直接攻博"
          ],
          "majorName": "计算机科学与技术",
          "majorLabelList": [
            "人工智能方向",
            "国家重点学科"
          ],
          "mentor": "张明教授",
          "mentorTitleList": [
            "院士",
            "博士生导师",
            "长江学者"
          ],
          "tagList": [
            "发表 5 篇期刊论文、2 篇会议论文、1 项授权专利",
            "SCI 收录 3 篇、EI 收录 2 篇、CSSCI 收录 1 篇"
          ],
          "college": "计算机科学与技术学院",
          "beginDate": "2019-09-01",
          "endDate": "2023-06-30",
          "studyDate": "2019.09~2023.06",
          "degreeType": "工学博士"
        },
        {
          "resumeEducationId": "10002",
          "educationId": "3",
          "schoolInfo": {
            "id": "501",
            "name": "清华大学",
            "logoUrl": "https://example.com/school-logos/tsinghua.png",
            "isAbroad": "2",
            "qsRankingRange": "1-50",
            "labelName": "985,211, 双一流"
          },
          "jointSchoolInfo": {
            "id": "0",
            "name": "",
            "logoUrl": "",
            "isAbroad": "2",
            "qsRankingRange": "",
            "labelName": ""
          },
          "educationName": "硕士研究生",
          "schoolNameStr": "清华大学",
          "schoolLabelList": [
            "985",
            "211",
            "双一流"
          ],
          "majorLevel1": "08",
          "majorLevel2": "0812",
          "majorLevel3": "081201",
          "majorCustom": "",
          "majorId": "3025",
          "isFullTime": "1",
          "isSinoForeignJointTraining": "2",
          "isMasterBachelorCombined": "2",
          "isBachelorMasterCombined": "2",
          "isBachelorMasterContinuous": "1",
          "isJuniorCollegeToBachelor": "2",
          "isOnlyPhd": "2",
          "isBachelorPhdCombined": "2",
          "cultivationNatureLabelStrList": [
            "本硕连读"
          ],
          "majorName": "计算机科学与技术",
          "majorLabelList": [
            "机器学习方向",
            "省级重点学科"
          ],
          "mentor": "李华教授",
          "mentorTitleList": [
            "博士生导师",
            "国家优秀青年科学基金获得者"
          ],
          "tagList": [
            "发表 3 篇期刊论文、1 篇会议论文",
            "SCI 收录 2 篇、EI 收录 1 篇"
          ],
          "college": "计算机科学与技术学院",
          "beginDate": "2017-09-01",
          "endDate": "2019-06-30",
          "studyDate": "2017.09~2019.06",
          "degreeType": "工学硕士"
        }
      ]
    },
    "workModule": {
      "list": [
        {
          "resumeWorkId": 21,
          "companyName": "北京大学",
          "natureWorkList": [
            "海外"
          ],
          "showJobName": "副教授",
          "department": "计算机学院",
          "beginDate": "2022-07-01",
          "endDate": "2024-03-31",
          "isNow": 1,
          "workDate": {
            "beginDate": "2022-07-01",
            "endDate": "2024-03-31",
            "durationDate": "（1年8个月）"
          },
          "subtitleStr": "副教授（职称：教授[38岁获得]）丨计算机学院",
          "content": "负责科研团队建设",
          "achievementStrList": [
            "发表1篇会议论文"
          ],
          "researchFieldRelationList": [
            "人工智能"
          ],
          "stageRelation": {
            "achievementStr": "发表1篇会议论文",
            "recordStr": ""
          }
        }
      ]
    },
    "postdoctorWorkModule": {
      "list": [
        {
          "resumePostdoctorWorkId": 31,
          "institutionName": "中国科学院 • 麻省理工学院 联合培养",
          "beginDate": "2020-07-01",
          "endDate": "2022-06-30",
          "workDate": {
            "beginDate": "2020-07-01",
            "endDate": "2022-06-30",
            "durationDate": "（2年）"
          },
          "isAbroad": 1,
          "isJointTraining": 1,
          "cooperativeMentor": "王五",
          "mentorTitleList": [
            "博士后合作导师"
          ],
          "researchDirection": "机器学习",
          "stageTagList": [
            "国家自然科学基金面上项目"
          ],
          "researchFieldRelationList": [
            "机器学习"
          ]
        }
      ]
    },
    "overseasExperienceModule": [
      {
        "resumeOverseasExperienceId": 41,
        "typeCode": 101,
        "typeName": "访学交流",
        "showInstitutionName": "斯坦福大学",
        "role": "访问学者",
        "workDate": {
          "beginDate": "2019-01-01",
          "endDate": "2019-12-31",
          "durationDate": "（1年）"
        },
        "content": "开展合作研究"
      }
    ],
    "researchProjectModule": {
      "list": [
        {
          "id": 51,
          "name": "国家重点研发计划项目",
          "projectDate": {
            "beginDate": "2021-01-01",
            "endDate": "2023-12-31",
            "durationDate": "（3年）"
          },
          "roleName": "项目负责人",
          "categoryName": "国家级",
          "attributeTagList": [
            "产学研合作项目"
          ],
          "tagList": [
            "国家自然科学基金重点项目"
          ],
          "stageRelation": {
            "achievementStr": "发表2篇期刊论文、1项授权专利",
            "recordStr": "SCI2篇"
          }
        }
      ],
      "resumeResearchProjectCount": 1
    },
    "academicPageModule": {
      "list": [
        {
          "resumeAcademicPageId": 61,
          "title": "AI-based Optimization",
          "subtitleStr": "Nature, 2023, IF:45, DOI:10.1038/xxx",
          "researchFieldList": [
            "人工智能"
          ],
          "recordStrList": [
            "SCI2篇",
            "JCRQ11篇"
          ]
        }
      ],
      "resumeAcademicPageCount": 5
    },
    "conferencePageModule": {
      "list": [
        {
          "id": 71,
          "title": "Graph Learning at NeurIPS",
          "subtitleStr": "NeurIPS, 2022, Vancouver, 12 pages",
          "conferenceLevelName": "国际顶级会议",
          "researchFieldList": [
            "机器学习"
          ]
        }
      ],
      "resumeConferencePageCount": 3
    },
    "academicPatentModule": {
      "list": [
        {
          "id": 81,
          "name": "一种智能芯片架构",
          "authorizationDate": "2021-06-15",
          "subtitleStr": "2021, 第一发明人, *********, 专利转化金额:500万",
          "typeName": "发明专利"
        }
      ],
      "resumeAcademicPatentCount": 2
    },
    "academicBookModule": {
      "list": [
        {
          "id": 91,
          "name": "人工智能导论",
          "publishDate": "2020-09-01",
          "subtitleStr": "清华大学出版社, 2020, 本人撰写占比:60%",
          "typeName": "教材"
        }
      ],
      "resumeAcademicBookCount": 1
    },
    "academicConferenceActivityModule": {
      "list": [
        {
          "id": 101,
          "name": "国际人工智能大会",
          "holdDate": "2023-08-01",
          "levelName": "国际会议",
          "roleName": "大会主席"
        }
      ],
      "resumeAcademicConferenceActivityCount": 4
    },
    "associationPositionModule": {
      "list": [
        {
          "id": 111,
          "name": "中国计算机学会",
          "typeName": "理事单位",
          "roleName": "常务理事",
          "workDate": {
            "durationDate": "（2020-2024）"
          }
        }
      ]
    },
    "journalPositionModule": [
      {
        "id": 121,
        "journalName": "IEEE TPAMI",
        "roleName": "客座编辑",
        "workDate": {
          "durationDate": "（2021-至今）"
        }
      }
    ],
    "socialPositionModule": {
      "list": [
        {
          "id": 131,
          "name": "北京市青年人才顾问",
          "roleName": "顾问",
          "typeName": "政府顾问",
          "workDate": {
            "durationDate": "（2022-2024）"
          }
        }
      ]
    },
    "projectReviewModule": {
      "list": [
        {
          "id": 141,
          "projectName": "国家自然科学基金重点项目",
          "roleName": "评审专家",
          "levelName": "国家级",
          "workDate": {
            "durationDate": "（2021）"
          }
        }
      ]
    },
    "thesisCommitteeModule": {
      "list": [
        {
          "id": 151,
          "name": "博士论文答辩委员会",
          "roleName": "主席",
          "typeName": "博士论文",
          "workDate": {
            "durationDate": "（2020-2023）"
          }
        }
      ]
    },
    "tachingCourseModule": {
      "list": [
        {
          "id": 161,
          "name": "机器学习",
          "targetAudienceName": "研究生",
          "courseTagList": [
            {
              "id": 1,
              "name": "双语教学"
            }
          ],
          "workDate": {
            "durationDate": "（2019-至今）"
          }
        }
      ]
    },
    "competitionGuidanceModule": {
      "list": [
        {
          "id": 171,
          "name": "全国大学生创新大赛",
          "roleName": "指导教师",
          "achievementTagList": [
            "国家一等奖"
          ],
          "workDate": {
            "durationDate": "（2021）"
          }
        }
      ]
    },
    "educationReformModule": {
      "list": [
        {
          "id": 181,
          "name": "课程体系改革项目",
          "roleName": "负责人",
          "typeName": "课程改革",
          "achievementTagList": [
            {
              "id": 1,
              "name": "省级教学成果奖"
            }
          ],
          "workDate": {
            "durationDate": "（2020-2022）"
          }
        }
      ]
    },
    "textbookWritingModule": {
      "list": [
        {
          "id": 191,
          "name": "人工智能实验指导",
          "roleName": "主编",
          "typeName": "教材",
          "tag": [
            {
              "id": 1,
              "name": "国家级精品教材"
            }
          ],
          "writingDate": "2021-03-01"
        }
      ]
    },
    "rewardModule": {
      "list": [
        {
          "id": 201,
          "name": "国家科技进步二等奖",
          "obtainTime": "2022-12-01",
          "categoryName": "科技奖项",
          "levelName": "国家级",
          "roleName": "主要完成人"
        }
      ]
    },
    "certificateModule": {
      "list": [
        {
          "id": 211,
          "certificateName": "中级职称证书",
          "certificateId": 301,
          "obtainDate": "2017-08-01"
        }
      ]
    },
    "otherSkillModule": {
      "list": [
        {
          "id": 221,
          "name": "英语 CET-6"
        }
      ]
    },
    "additionalInfoModule": {
      "list": [
        {
          "id": 231,
          "themeName": "社会活动",
          "content": "积极参与志愿服务"
        }
      ]
    }
  }
}

```

## 字段说明



## 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 0 | 简历未通过审核，不可查看 | 简历状态不是已激活状态 |
| 0 | 系统错误 | 服务器内部错误 |

## 注意事项

1. 该接口需要用户登录状态
2. 只能查看已通过审核的简历详情
3. 返回的数据会根据用户权限进行脱敏处理
4. 各模块数据可能为空，前端需要做好空值处理