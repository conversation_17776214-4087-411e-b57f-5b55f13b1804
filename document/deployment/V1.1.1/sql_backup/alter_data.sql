CREATE TABLE `company_delivery_change_log` (
   `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
   `company_id` int(11) NOT NULL DEFAULT '0' COMMENT '单位ID',
   `company_name` varchar(255) NOT NULL DEFAULT '' COMMENT '单位名称',
   `description_before` tinyint(1) NOT NULL COMMENT '修改前',
   `description_after` tinyint(1) NOT NULL COMMENT '修改后',
   `add_time` datetime NOT NULL COMMENT '添加时间',
   `update_tme` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
   `user_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '操作来源 1运营后台 2单位端',
   `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '操作人ID',
   `user_name` varchar(100) NOT NULL DEFAULT '' COMMENT '操作人名称',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='单位投递类型变更日志表';

CREATE TABLE `job_apply_record` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `delivery_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '投递类型 1站外投递 2站内投递',
    `delivery_way` tinyint(1) NOT NULL DEFAULT '1' COMMENT '投递方式 1平台投递 2邮箱投递 3网址投递 99自主录入投递',
    `add_time` datetime NOT NULL COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `apply_id` int(11) NOT NULL DEFAULT '0' COMMENT '投递ID 关联回站内投递表',
    `apply_site_id` int(11) NOT NULL DEFAULT '0' COMMENT '投递ID 关联回站外投递表',
    `company_id` int(11) NOT NULL DEFAULT '0' COMMENT '发布职位的单位id',
    `resume_id` int(11) NOT NULL DEFAULT '0' COMMENT '简历id',
    `announcement_id` int(11) NOT NULL DEFAULT '0' COMMENT '公告ID',
    `job_id` int(11) NOT NULL DEFAULT '0' COMMENT '职位id',
    `source` tinyint(1) NOT NULL DEFAULT '0' COMMENT '投递来源(1自主投递2委托投递3自主录入)',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_company_id` (`company_id`),
    KEY `idx_resume_id` (`resume_id`),
    KEY `idx_job_id` (`job_id`),
    KEY `idx_announcement_id` (`announcement_id`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='投递记录表';

alter table company add column delivery_type tinyint(1) not null default 1 comment '投递类型1=站外投递,2=站内投递,3=站内&站外投递';
alter table company add column account_nature tinyint(1) not null default 1 comment '账号性质 0不详 1愿开通账号 2不愿开通账号';
alter table off_site_job_apply add column company_tag varchar (512) not null default '' comment '企业标识的tag(逗号隔开的文案)';
-- 默认值为0含义是没有选择的时候
alter table announcement add column delivery_type tinyint(1) not null default 0 comment '投递类型1=站外投递,2=站内投递';
alter table announcement add column delivery_way tinyint(1) not null default 0 comment '投递方式 1平台投递 2邮箱投递 3网址投递';
alter table announcement add column extra_notify_address varchar (255) not null default '' comment '投递通知地址';

alter table job add column delivery_type tinyint(1) not null default 0 comment '投递类型1=站外投递,2=站内投递';
alter table job add column delivery_way tinyint(1) not null default 0 comment '投递方式 1平台投递 2邮箱投递 3网址投递';
alter table job add column extra_notify_address varchar (255) not null default '' comment '投递通知地址';


alter table job_temp add column delivery_type tinyint(1) not null default 0 comment '投递类型1=站外投递,2=站内投递';
alter table job_temp add column delivery_way tinyint(1) not null default 0 comment '投递方式 1平台投递 2邮箱投递 3网址投递';
alter table job_temp add column extra_notify_address varchar (255) not null default '' comment '投递通知地址';
