# ReadMe

### 关联产品原型版本

-

***

### 参与人员

- 林建炫
- 李健
- 龚传栋

***

### 分支

|仓库|开发分支|提測分支|备注|
|:----:|:----:|:----:|:----:|
| new_gaoxiao_yii | feature/v1.1.4 | release/v1.1.4 |-|
| new_gaoxiao_h5 | feature/V1.1.4 | feature/V1.1.4 | 合并分支即可 |
| new_gaoxiao_admin_pc_vue | feature/V1.1.4 | release/V1.1.4 |-|
| new_gaoxiao_person_pc_vue | feature/V1.1.4 | release/V1.1.4 |-|

***

### 上线部署步骤

`内容为空,填写"-"即可`

|步骤| 是否执行 |执行内容|
|:----|:----:|:----|
|提醒前端提前合并代码到master和添加权限节点|  -   |-|
|执行sql语句|  是   |见下方"执行sql语句"|
|更新后端代码|  是   |-|
|composer安装|  -   |-|
|更新配置|  -   |-|
|创建队列|  -   |-|
|执行脚本|  -   |-|
|删除redis缓存|  -   |-|
|重启队列|  -   |-|
|更新前端代码|  -   |-|
|添加定时任务|  -   |-|
|群内通知部署完毕|  -   |-|

一次性脚本

```
 php ./timer_yii script/build-all-job-auto
```

执行脚本（每天0点执行一次即可）

```
php ./timer_yii job/home-select-job  
```

执行sql语句

```
alter table company add is_hide tinyint(1) NOT NULL DEFAULT '2' COMMENT '是否隐藏（1隐藏   2展示）' after click;
alter table company add sort tinyint(1) NOT NULL DEFAULT '0' COMMENT '单位排序（0默认 1高级会员）' after is_hide;
update company set sort = 1 where package_type = 2;//更新单位的排序
```
