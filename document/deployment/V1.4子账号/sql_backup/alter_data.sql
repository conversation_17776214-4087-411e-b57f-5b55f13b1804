alter table member
    add column company_member_type tinyint(1) not null default 0 comment '单位账号类型 0主账号 1子账号类型';

##单位账户信息表
CREATE TABLE `company_member_info`
(
    `id`                  int(11)      NOT NULL AUTO_INCREMENT,
    `add_time`            datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         datetime     NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `member_id`           int(11)      NOT NULL DEFAULT '0' COMMENT '账号ID',
    `company_id`          int(11)      NOT NULL DEFAULT '0' COMMENT '单位id(隶属单位ID)',
    `contact`             varchar(64)  NOT NULL DEFAULT '' COMMENT '联系人',
    `department`          varchar(128) NOT NULL DEFAULT '' COMMENT '所在部门',
    `member_rule`         tinyint(1)   NOT NULL COMMENT '账号权限  1普通权限 2VIP权限 9超管权限',
    `company_member_type` tinyint(1)   NOT NULL COMMENT '账号类型 0主账号 1子账号',
    `create_id`           int(11)      NOT NULL DEFAULT '0' COMMENT '创建人ID',
    `source_type`         int(11)      NOT NULL DEFAULT '2' COMMENT '创建来源 1自主 2运营',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_member_id` (`member_id`),
    KEY `idx_create_id` (`create_id`),
    KEY `idx_company_id` (`company_id`),
    KEY `idx_company_member_type` (`company_member_type`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位账户信息表';


##单位子账号配置表
CREATE TABLE `company_member_config`
(
    `id`            int(11)  NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `company_id`    int(11)  NOT NULL DEFAULT '0' COMMENT '单位ID',
    `vip_total`     int(4)   NOT NULL DEFAULT '0' COMMENT '子账号vip总数量',
    `vip_available` int(4)   NOT NULL DEFAULT '0' COMMENT '子账号vip可用数量',
    `vip_used`      int(4)   NOT NULL DEFAULT '0' COMMENT '子账号vip已用数量',
    `total`         int(4)   NOT NULL DEFAULT '0' COMMENT '子账号的总数量',
    `available`     int(4)   NOT NULL DEFAULT '0' COMMENT '子账号的可用数量',
    `used`          int(4)   NOT NULL DEFAULT '0' COMMENT '子账号的已用数量',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_company_id` (`company_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位子账号配置表';


##职位联系人
CREATE TABLE `job_contact`
(
    `id`                     int(11)  NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`               datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`            datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `job_id`                 int(11)  NOT NULL DEFAULT '0' COMMENT '职位ID',
    `announcement_id`        int(11)  NOT NULL DEFAULT '0' COMMENT '公告ID',
    `company_member_info_id` int(11)  NOT NULL DEFAULT '0' COMMENT '关联联系人账号ID',
    `company_id`             int(11)  NOT NULL DEFAULT '0' COMMENT '联系人隶属单位ID',
    PRIMARY KEY (`id`),
    KEY `idx_job_id` (`job_id`),
    KEY `idx_announcement_id` (`announcement_id`),
    KEY `idx_company_member_info_id` (`company_member_info_id`),
    KEY `idx_company_id` (`company_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位联系人中间表';

##职位协同联系人
CREATE TABLE `job_contact_synergy`
(
    `id`                     int(11)  NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`               datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`            datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `job_id`                 int(11)  NOT NULL DEFAULT '0' COMMENT '职位ID',
    `announcement_id`        int(11)  NOT NULL DEFAULT '0' COMMENT '公告ID',
    `company_member_info_id` int(11)  NOT NULL DEFAULT '0' COMMENT '关联联系人账号ID',
    `company_id`             int(11)  NOT NULL DEFAULT '0' COMMENT '联系人隶属单位ID',
    PRIMARY KEY (`id`),
    KEY `idx_job_id` (`job_id`),
    KEY `idx_announcement_id` (`announcement_id`),
    KEY `idx_company_member_info_id` (`company_member_info_id`),
    KEY `idx_company_id` (`company_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='职位协同联系人';


##单位操作日志
CREATE TABLE `company_member_operation_log`
(
    `id`                int(11)    NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`          datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime   NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `type`              tinyint(4) NOT NULL DEFAULT '0' COMMENT '类型 1单位账号状态操作 2子账号配置  3服务过期 4手动变更账号权限 5创建子账号  6单位终审通过',
    `operation_id`      int(11)    NOT NULL DEFAULT '0' COMMENT '操作人',
    `operation_port`    tinyint(4) NOT NULL DEFAULT '0' COMMENT '操作端口 1系统 2运营 3单位自己',
    `operation_content` text       NOT NULL COMMENT '操作内容',
    `member_id`         int(11)    NOT NULL DEFAULT '0' COMMENT '用户ID',
    `company_id`        int(11)    NOT NULL DEFAULT '0' COMMENT '隶属单位ID',
    PRIMARY KEY (`id`),
    KEY `idx_member_id` (`member_id`) USING BTREE,
    KEY `idx_company_id` (`company_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位操作日志';

CREATE TABLE `job_apply_record_extra`
(
    `job_id`                  int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '职位ID',
    `total`                   int(11)          NOT NULL DEFAULT '0' COMMENT '投递总数',
    `delivery_type_outer`     int(11)          NOT NULL DEFAULT '0' COMMENT '站外投递总数',
    `delivery_type_outside`   int(11)          NOT NULL DEFAULT '0' COMMENT '站内投递总数',
    `delivery_way_platform`   int(11)          NOT NULL DEFAULT '0' COMMENT '投递方式平台投递总数',
    `delivery_way_email`      int(11)          NOT NULL DEFAULT '0' COMMENT '投递方式邮件投递总数',
    `delivery_way_link`       int(11)          NOT NULL DEFAULT '0' COMMENT '投递方式链接投递总数',
    `platform_pc`             int(11)          NOT NULL DEFAULT '0' COMMENT '投递平台pc总数',
    `platform_h5`             int(11)          NOT NULL DEFAULT '0' COMMENT '投递平台h5总数',
    `platform_mini`           int(11)          NOT NULL DEFAULT '0' COMMENT '投递平台mini总数',
    `platform_app`            int(11)          NOT NULL DEFAULT '0' COMMENT '投递平台app总数',
    `interview`               int(11)          NOT NULL DEFAULT '0' COMMENT '投递邀面数量',
    `education_other`         int(11)          NOT NULL DEFAULT '0' COMMENT '学历分布-其他',
    `education_junior`        int(11)          NOT NULL DEFAULT '0' COMMENT '学历分布-大专',
    `education_undergraduate` int(11)          NOT NULL DEFAULT '0' COMMENT '学历分布-本科',
    `education_master`        int(11)          NOT NULL DEFAULT '0' COMMENT '学历分布-硕士',
    `education_doctor`        int(11)          NOT NULL DEFAULT '0' COMMENT '学历分布-博士',
    PRIMARY KEY (`job_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='投递记录统计表';

alter table job_temp
    add column contact_id int(11) not null default 0 comment '联系人ID';
alter table job_temp
    add column contact_synergy_id varchar(60) not null default '' comment '协同账号ID';
alter table resume_library_invite_log
    add column company_member_id int(11) NOT NULL DEFAULT '0' COMMENT '邀约人账号id(对应member_id)';
alter table job_apply_handle_log
    add column handle_id int(11) NOT NULL DEFAULT '0' COMMENT '处理人账号id(对应member_id)';
alter table resume_library_collect
    add column `member_id` int(11) NOT NULL DEFAULT '0' COMMENT '单位用户id';
alter table company_resume_library
    add column `company_member_id` int(11) NOT NULL DEFAULT '0' COMMENT '单位用户id';