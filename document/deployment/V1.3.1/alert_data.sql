ALTER TABLE `resume`
    ADD COLUMN `identity_type` tinyint(1) NOT NULL DEFAULT '-1' COMMENT '身份类型（-1缺失，1职场人，2应届生）' AFTER `vip_expire_time`,
ADD COLUMN `begin_work_date` date NOT NULL DEFAULT '0000-00-00' COMMENT '参加工作时间' AFTER `identity_type`;

ALTER TABLE `resume_education`
    ADD COLUMN `mentor` varchar(50) NOT NULL DEFAULT '' COMMENT '导师' AFTER `major_id_level_3`;

ALTER TABLE `resume_work`
    ADD COLUMN `is_practice` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否实习（1是，2否）' AFTER `content`;

ALTER TABLE `resume_contact`
    ADD COLUMN `mobile_code` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号区号' AFTER `weixin`;

-- 更新历史数据
update `member` set `mobile_code` = 86 where `mobile_code` = '';