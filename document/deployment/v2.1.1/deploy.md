# ReadMe

### 关联产品原型版本

-[V2.1.1（新增公告高级模版）-蓝湖 (lanhuapp.com)](https://lanhuapp.com/web/#/item/project/product?tid=8d951de4-aefb-40f7-954e-366683d68331&pid=3bbad3c0-8013-4dd4-a6b1-a3fddea6e9fd&versionId=2647bf39-2e51-4564-9c26-34dccf9e7175&docId=bbee0383-f3e2-4d78-88ef-abf4b1549be9&docType=axure&pageId=abeb8969ebcc40198d7ebbf08dac8840&image_id=bbee0383-f3e2-4d78-88ef-abf4b1549be9)

***

### 参与人员

- 龚传栋
- 杜孙鹤

***

### 分支

|           仓库           |            开发分支             |            提測分支             | 备注 |
| :----------------------: | :-----------------------------: | :-----------------------------: | :--: |
|     new_gaoxiao_yii      | feature/v2.1.1_新增公告高级模版 | release/v2.1.1_新增公告高级模版 |  -   |
| new_gaoxiao_admin_pc_vue | feature/v2.1.1_新增公告高级模版 | release/v2.1.1_新增公告高级模版 |  -   |

***

### 上线部署步骤

`内容为空,填写"-"即可`

| 步骤                       | 是否执行 | 执行内容         |
|:-------------------------|:----:|:-------------|
| 提醒前端提前合并代码到master和添加权限节点 |  -   | -            |
| 执行sql语句                  |  是   | 见下方"执行sql语句" |
| 更新后端代码                   |  是   | 两个服务器都需要更新   |
| composer安装               |  -   | -            |
| 更新配置                     |  -   | -            |
| 创建队列                     |  -   | -            |
| 执行脚本                     |  -   | -            |
| 删除redis缓存                |  -   | -            |
| 重启队列                     |  -   | -            |
| 更新前端代码                   |  是   | -            |
| 添加定时任务                   |  是   | -            |
| 群内通知部署完毕                 |  -   | -            |

#### 执行sql语句(按顺序执行)

- alter_data.sql

