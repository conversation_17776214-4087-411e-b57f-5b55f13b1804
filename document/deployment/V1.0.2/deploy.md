# ReadMe

### 关联产品原型版本
- ([运营端优化-新增内容管理公告审核菜单）- 蓝湖 (lanhuapp.com)](https://lanhuapp.com/web/#/item/project/product?type=share_mark&tab=product&pid=d5932d08-54ea-41eb-9b0d-8524b017b947&versionId=5812c273-0dd0-414a-9181-3504880c72c5&docId=3502b2ed-9342-4bfb-9175-0a7c5c2bbca8&image_id=3502b2ed-9342-4bfb-9175-0a7c5c2bbca8&docType=axure&imgId=undefined&pageId=17ff0dbe5ee84d0db75a2e274f8d680c&teamId=8d951de4-aefb-40f7-954e-366683d68331&parentId=85203003-ad18-44af-9502-b7604556c5c1))

***

### 参与人员

- 赵华聪
- 王昕

***

### 分支

|仓库|开发分支|提測分支|备注|
|:----:|:----:|:----:|:----:|
|new_gaoxiao_yii|feature/V1.0.2-新增内容管理公告审核菜单|release/V1.0.2-新增内容管理公告审核菜单|-|
|new_gaoxiao_admin_pc_vue|feature/V1.0.2-新增内容管理公告审核菜单|release/V1.0.2-新增内容管理公告审核菜单|-|

***

### 上线部署步骤

`内容为空,填写"-"即可`

|步骤|是否执行|执行内容|
|:----|:----:|:----|
|提醒前端提前合并代码到master和添加权限节点|是|路由名称：name=公告审核，key=cmsAnnouncementAuditList|
|执行sql语句|-|见下方"执行sql语句"|
|更新后端代码|是|-|
|composer安装|-|见下方"composer安装"|
|更新配置|-|见下方"更新配置"|
|创建队列|-|见下方"创建队列"|
|执行脚本|-|见下方"执行脚本"|
|删除redis缓存|-|见下方"删除redis缓存"|
|重启队列|-|上线前, 暂停所有队列, 上线后再重启|
|更新前端代码|是|-|
|添加定时任务|-|见下方"定时任务"|
|群内通知部署完毕|-|-|

***

### 前端路由
`"内容管理"一级模块下面新增 "公告审核"二级菜单`
```angular2html
cmsAnnouncementAuditList
```

![img_1.png](img_1.png)








