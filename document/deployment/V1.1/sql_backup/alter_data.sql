DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config`
(
    `id`            int(11) NOT NULL AUTO_INCREMENT,
    `add_time`      datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `platform_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0全局,1pc,2h5',
    `name`          varchar(128)  NOT NULL DEFAULT '' COMMENT 'name',
    `value`         varchar(1024) NOT NULL DEFAULT '' COMMENT 'value(这里按照业务需要,可以各种,甚至json)',
    `remark`        varchar(512)  NOT NULL DEFAULT '' COMMENT '备注',
    `is_business`   tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否业务配置(业务方的就配置这里1,系统这边的就设置为2)',
    `is_secret`     tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否保密,1是,2否',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_name` (`name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='系统级别配置';


DROP TABLE IF EXISTS `company_resume_library`;
CREATE TABLE `company_resume_library`
(
    `id`            int(11) NOT NULL AUTO_INCREMENT,
    `add_time`      datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `company_id`    int(11) NOT NULL DEFAULT '0' COMMENT '单位id',
    `resume_id`     int(11) NOT NULL DEFAULT '0' COMMENT '简历id',
    `source_type`   int(11) NOT NULL DEFAULT '0' COMMENT '简历来源(这里采取位来存,1是投递,2是下载,3是两者均有)',
    `download_time` datetime      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '下载时间',
    `apply_time`    datetime      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '应聘时间',
    `tag`           varchar(1024) NOT NULL DEFAULT '' COMMENT '企业对简历对标注',
    PRIMARY KEY (`id`),
    KEY             `idx_company_id` (`company_id`),
    KEY             `idx_resume_id` (`resume_id`),
    KEY             `idx_add_time` (`add_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='单位简历库';


DROP TABLE IF EXISTS `resume_library`;
CREATE TABLE `resume_library`
(
    `id`        int(11) NOT NULL AUTO_INCREMENT,
    `add_time`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `resume_id` int(11) NOT NULL DEFAULT '0' COMMENT '简历id',
    PRIMARY KEY (`id`),
    KEY         `idx_resume_id` (`resume_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='人才库';


DROP TABLE IF EXISTS `resume_library_download_log`;
CREATE TABLE `resume_library_download_log`
(
    `id`                         int(11) NOT NULL AUTO_INCREMENT,
    `add_time`                   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `company_id`                 int(11) NOT NULL DEFAULT '0' COMMENT '单位id',
    `resume_id`                  int(11) NOT NULL DEFAULT '0' COMMENT '简历id',
    `company_package_change_log` int(11) NOT NULL DEFAULT '0' COMMENT '消费id',
    PRIMARY KEY (`id`),
    KEY                          `idx_company_id` (`company_id`),
    KEY                          `idx_resume_id` (`resume_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='人才库下载日志';


DROP TABLE IF EXISTS `resume_library_collect`;
CREATE TABLE `resume_library_collect`
(
    `id`         int(11) NOT NULL AUTO_INCREMENT,
    `add_time`   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `company_id` int(11) NOT NULL DEFAULT '0' COMMENT '单位id',
    `resume_id`  int(11) NOT NULL DEFAULT '0' COMMENT '简历id',
    PRIMARY KEY (`id`),
    KEY          `idx_company_id` (`company_id`),
    KEY          `idx_resume_id` (`resume_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='人才库收藏';

DROP TABLE IF EXISTS `resume_library_invite_log`;
CREATE TABLE `resume_library_invite_log`
(
    `id`                         int(11) NOT NULL AUTO_INCREMENT,
    `add_time`                   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `type`                       int(11) NOT NULL DEFAULT '0' COMMENT '1邮件,2短信,3邮件+短信',
    `company_id`                 int(11) NOT NULL DEFAULT '0' COMMENT '单位id',
    `resume_id`                  int(11) NOT NULL DEFAULT '0' COMMENT '简历id',
    `job_id`                     int(11) NOT NULL DEFAULT '0' COMMENT '职位id',
    `company_package_change_log` int(11) NOT NULL DEFAULT '0' COMMENT '消费id',
    `email_log_id`               int(11) NOT NULL DEFAULT '0' COMMENT '邮件id',
    `sms_log_id`                 int(11) NOT NULL DEFAULT '0' COMMENT '短信id',
    `remark`                     varchar(256) NOT NULL DEFAULT '' COMMENT '邀约备注',
    PRIMARY KEY (`id`),
    KEY                          `idx_company_id` (`company_id`),
    KEY                          `idx_resume_id` (`resume_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='人才库邀约日志';

DROP TABLE IF EXISTS `resume_share`;
CREATE TABLE `resume_share`
(
    `id`          int(10) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `add_time`    datetime                   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime                   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
    `company_id`  int(10) NOT NULL DEFAULT '0' COMMENT '单位id',
    `resume_id`   int(10) NOT NULL DEFAULT '0' COMMENT '简历id',
    `password`    char(4) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '访问密码',
    `code`        varchar(255)               NOT NULL DEFAULT '' COMMENT '唯一码',
    `expire_time` datetime                   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '有效时间',
    short_link_id int                        NOT NULL default 0 not null comment '短链接id',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_code` (`code`) USING BTREE,
    KEY           `idx_resume_id` (`resume_id`),
    KEY           `idx_company_id` (`company_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='简历分享表';

DROP TABLE IF EXISTS `short_link`;
CREATE TABLE `short_link`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
    `code`        varchar(255) NOT NULL DEFAULT '' COMMENT '短链唯一码code',
    `url`         varchar(255) NOT NULL DEFAULT '' COMMENT '长链接地址',
    `remark`      varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_code` (`code`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='短链接表';

-- 配置表写一些配置信息
insert into system_config (`name`, `value`, `remark`)
values ('resume_library_complete_min', 65, '进入简历完善度限制');
insert into system_config (`name`, `value`, `remark`)
values ('resume_library_company_invite_count_day', 20, '单位邀请人才投递日次数限制');
insert into system_config (`name`, `value`, `remark`)
values ('resume_library_download_point_elite', 10, '精英简历下载消耗点数');
insert into system_config (`name`, `value`, `remark`)
values ('resume_library_download_point_high_quality', 5, '优质简历下载消耗点数');
insert into system_config (`name`, `value`, `remark`)
values ('resume_library_download_point_ordinary', 3, '普通简历下载消耗点数');

alter table `job_apply_handle_log`
    add column resume_id int(11) NOT NULL DEFAULT '0' COMMENT '简历id' after `job_apply_id`;
alter table `job_apply_handle_log`
    add column company_id int(11) NOT NULL DEFAULT '0' COMMENT '单位id' after `resume_id`;

alter table company_package_system_config
    add resume_download_amount int(11) NOT NULL DEFAULT '0' COMMENT '简历下载点数';
alter table company_package_system_config
    add sms_amount int(11) NOT NULL DEFAULT '0' COMMENT '短信数量';
alter table company_package_system_config
    add base_job_amount int(3) NOT NULL DEFAULT '0' COMMENT '职位发布基础数量';
alter table company_package_system_config
    add base_announcement_amount int(3) NOT NULL DEFAULT '0' COMMENT '公告发布基础数量';
alter table company_package_system_config
    add base_job_refresh_amount int(3) NOT NULL DEFAULT '0' COMMENT '职位刷新基础数量';
alter table company_package_system_config
    add base_announcement_refresh_amount int(3) NOT NULL DEFAULT '0' COMMENT '公告刷新基础数量';
alter table company_package_system_config
    add base_resume_download_amount int(3) NOT NULL DEFAULT '0' COMMENT '简历下载基础点数';

alter table company_package_config
    add resume_download_amount int(11) NOT NULL DEFAULT '0' COMMENT '简历下载点数';
alter table company_package_config
    add sms_amount int(11) NOT NULL DEFAULT '0' COMMENT '短信数量';

alter table company_package_change_log
    add handle_type int(3) NOT NULL DEFAULT '0' COMMENT '操作类型';

alter table company_package_config_log
    add resume_download_amount int(11) NOT NULL DEFAULT '0' COMMENT '简历下载点数';

-- 简历添加最高学历code字段
alter table resume
    add top_education_code tinyint(1) NOT NULL DEFAULT '0' COMMENT '最高学历code' after last_education_id;

-- 简历统计数据表添加人才库相关数据
alter table resume_stat_data
    add column resume_library_download_amount int(11) NOT NULL DEFAULT '0' COMMENT '人才库下载次数';

-- 简历统计数据表添加人才库相关数据
alter table resume_stat_data
    add column resume_library_download_amount int(11) NOT NULL DEFAULT '0' COMMENT '人才库下载次数';

alter table resume_setting
    add is_job_invite tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否开启职位邀约1开启,2关闭';
alter table resume_setting
    add is_company_view_me tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否公司查看自己的简历1开启,2关闭';

alter table resume_stat_data
    add column resume_library_collect_amount int(11) NOT NULL DEFAULT '0' COMMENT '人才库收藏次数';


update job_apply_handle_log
set resume_id = (select resume_id from job_apply where id = job_apply_handle_log.job_apply_id)
where resume_id = 0;

update job_apply_handle_log
set company_id = (select company_id from job_apply where id = job_apply_handle_log.job_apply_id)
where company_id = 0;

update resume_setting
set is_job_feedback = 1
where 1;
update resume_setting
set is_system_message = 1
where 1;
update resume_setting
set is_todo_notice = 1
where 1;
update resume_setting
set is_job_invite = 1
where 1;
update resume_setting
set is_company_view_me = 1
where 1;

-- 企业套餐配置
update company_package_system_config
set base_job_amount             = 20,
    base_announcement_amount    = 50,
    base_resume_download_amount = 100,
    resume_download_amount      = 30
where code = 'A';
update company_package_system_config
set base_job_amount             = 40,
    base_announcement_amount    = 80,
    base_resume_download_amount = 100,
    resume_download_amount      = 60
where code = 'B';

INSERT INTO company_package_system_config
(`add_time`, `status`, `name`, `code`, `job_amount`, `announcement_amount`, `job_refresh_amount`,
 `announcement_refresh_amount`, `resume_download_amount`, `sms_amount`, `base_job_amount`, `base_announcement_amount`,
 `base_job_refresh_amount`, `base_announcement_refresh_amount`, `base_resume_download_amount`)
VALUES ('2022-08-03 14:36:53', 1, '试用会员', 'test', 5, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0);

INSERT INTO company_package_system_config
(`add_time`, `status`, `name`, `code`, `job_amount`,
 `announcement_amount`, `job_refresh_amount`,
 `announcement_refresh_amount`, `resume_download_amount`,
 `sms_amount`, `base_job_amount`,
 `base_announcement_amount`, `base_job_refresh_amount`,
 `base_announcement_refresh_amount`,
 `base_resume_download_amount`)
VALUES ('2022-08-03 14:43:43', 1, '免费会员', 'free', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);

alter table member_message modify column inner_link_params varchar (2048) NOT NULL DEFAULT '0' COMMENT '内部链接参数';

alter table job_apply_handle_log modify column handler_name varchar (256) NOT NULL DEFAULT '0' COMMENT '处理人的名称（快照）';