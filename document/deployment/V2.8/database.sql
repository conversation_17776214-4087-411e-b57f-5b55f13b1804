-- 简历相关数据库表SQL语句（完整版）
-- 基于 document/deployment/V2.8/简历相关表.md 生成

-- ==========================================
-- 一、修改表信息SQL
-- ==========================================

-- 1.1 修改resume（简历表）
ALTER TABLE resume
    ADD COLUMN template_type tinyint(1) NOT NULL DEFAULT 0 COMMENT '简历模板类型：常量1-学术版，2-普通版（未完善简历前使用）',
    ADD COLUMN display_template_type tinyint(1) NOT NULL DEFAULT 0 COMMENT '展示简历模板类型：常量1-学术版，2-普通版（未完善简历前使用）',
    ADD COLUMN academic_email varchar(255) NOT NULL DEFAULT '' COMMENT '学术邮箱',
    ADD COLUMN postdoctor_work_status int(11) NOT NULL DEFAULT '' COMMENT '博士学业/工作状态；对应字典type=67',
    ADD COLUMN `wechat` varchar(255) NOT NULL DEFAULT '' COMMENT '微信号' ,
ADD COLUMN `wechat_is_mobile` tinyint(1) NOT NULL DEFAULT 2 COMMENT '微信号是否是手机号;1 是；2 不是';

-- 1.2 修改resume_education（教育经历表）
ALTER TABLE resume_education
    ADD COLUMN is_full_time tinyint(1) NOT NULL DEFAULT 2 COMMENT '是否是全日制：1-是；2-否',
    ADD COLUMN is_sino_foreign_joint_training tinyint(1) NOT NULL DEFAULT 2 COMMENT '是否是中外联合培养：1-是；2-否',
    ADD COLUMN is_master_bachelor_combined tinyint(1) NOT NULL DEFAULT 2 COMMENT '是否是硕博连读：1-是；2-否',
    ADD COLUMN is_bachelor_master_combined tinyint(1) NOT NULL DEFAULT 2 COMMENT '是否是本硕博连读：1-是；2-否',
    ADD COLUMN is_bachelor_master_continuous tinyint(1) NOT NULL DEFAULT 2 COMMENT '是否是本硕连读：1-是；2-否',
    ADD COLUMN is_junior_college_to_bachelor tinyint(1) NOT NULL DEFAULT 2 COMMENT '是否是专升本：1-是；2-否',
    ADD COLUMN is_only_phd tinyint(1) NOT NULL DEFAULT 2 COMMENT '是否专攻博士：1-是；2-否',
    ADD COLUMN template_type tinyint(1) NOT NULL DEFAULT 2 COMMENT '简历模板类型：常量1-学术版，2-普通版（未完善简历前使用）',
    ADD COLUMN school_id int(11) NOT NULL DEFAULT 0 COMMENT '院校id',
    ADD COLUMN joint_school varchar(255) NOT NULL DEFAULT '' COMMENT '联合培养院校名称',
    ADD COLUMN joint_school_id int(11) NOT NULL DEFAULT 0 COMMENT '联合培养院校id',
    ADD COLUMN degree_type tinyint(1) NOT NULL DEFAULT 1 COMMENT '学位类型：1-学术型；2-专业型',
    ADD COLUMN is_only_phd tinyint(1) NOT NULL DEFAULT 2 COMMENT '是否专攻博士：1-是；2-否',
    ADD COLUMN `is_bachelor_phd_combined` tinyint(1) NOT NULL DEFAULT 2 COMMENT '是否本博连读',
    ADD COLUMN `content` text NULL COMMENT '描述';

-- 1.3 修改resume_work（工作经历表）
ALTER TABLE resume_work
    ADD COLUMN template_type tinyint(1) NOT NULL DEFAULT 0 COMMENT '简历模板类型：常量1-学术版，2-普通版（未完善简历前使用）',
    ADD COLUMN job_category_id int(11) NOT NULL DEFAULT 0 COMMENT '职位类型id（职务）',
    ADD COLUMN company_id int(11) NOT NULL DEFAULT 0 COMMENT '单位ID',
        ADD COLUMN is_now tinyint(2) NOT NULL DEFAULT 2 COMMENT '是否是至今，1-是；2-不是',
    ADD COLUMN `trade_id` int(11) NOT NULL DEFAULT 0 COMMENT '行业id',
    MODIFY COLUMN `job_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '职位名称（职务自定义）' AFTER `department`;

-- 1.4 修改resume_intention（求职意向表）
ALTER TABLE resume_intention
    ADD COLUMN template_type tinyint(1) NOT NULL DEFAULT 0 COMMENT '简历模板类型：常量1-学术版，2-普通版（未完善简历前使用）',
    ADD COLUMN preference varchar(128) NOT NULL DEFAULT '' COMMENT '求职偏好',
    ADD COLUMN `sort` tinyint(3) NOT NULL DEFAULT 0 COMMENT '排序';

-- 1.5 修改resume_academic_page（学术论文表）
ALTER TABLE resume_academic_page
    MODIFY COLUMN `serial_number` varchar (128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '刊物名/卷（期）号；新版：期刊名称；',
    ADD COLUMN citation_count_five_year varchar (128) NOT NULL DEFAULT '' COMMENT '近5年被引次数',
    ADD COLUMN doi varchar (128) NOT NULL DEFAULT '' COMMENT 'DOI',
    ADD COLUMN `is_record` tinyint(2) NOT NULL DEFAULT 1 COMMENT '是否收录；1已收录；2:暂不清楚',
    ADD COLUMN `serial_number_id` int (11) NOT NULL DEFAULT 0 COMMENT '期刊字典idJournalDictionary表',
    ADD COLUMN `page_number` varchar (255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '卷期页码';

-- 1.6 修改resume_academic_patent（学术专利表）
ALTER TABLE resume_academic_patent
    ADD COLUMN type_code int(11) NOT NULL DEFAULT 0 COMMENT '专利类型，使用字典表',
    ADD COLUMN authorization_country varchar(128) NOT NULL DEFAULT '' COMMENT '授权国家',
    ADD COLUMN commercialization_amount varchar(128) NOT NULL DEFAULT '' COMMENT '产业化金额（万元）',
    MODIFY COLUMN `authorization_date` date NOT NULL DEFAULT '0000-00-00' COMMENT '授权日期（月日填充01,存量数据不变）';

-- 1.7 修改resume_academic_book（学术著作表）
ALTER TABLE resume_academic_book
    MODIFY COLUMN `publish_date` date NOT NULL DEFAULT '0000-00-00' COMMENT '出版日期（月日补充01，存量数据不理会）',
    ADD COLUMN type_code int (11) NOT NULL DEFAULT 0 COMMENT '著作类别，使用字典表',
    ADD COLUMN publisher varchar (128) NOT NULL DEFAULT '' COMMENT '出版社',
    ADD COLUMN `role_code` int (11) NOT NULL DEFAULT 0 COMMENT '本人角色，使用字典',
    ADD COLUMN writing_ratio varchar (128) NOT NULL DEFAULT '' COMMENT '本人撰写占比（%）',
    ADD COLUMN awards varchar (128) NOT NULL DEFAULT '' COMMENT '所获奖项',
    ADD COLUMN description text NOT NULL COMMENT '著作描述',
    ADD COLUMN `is_abroad` tinyint(2) NOT NULL DEFAULT '2' COMMENT '是否海外经历（1是，2否）';


-- 1.8 修改resume_research_project（科研项目表）
ALTER TABLE resume_research_project
    ADD COLUMN is_international_cooperation tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否国际合作项目（1是，2否）',
    ADD COLUMN is_iur tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否产学研合作项目（1是，2否）',
    ADD COLUMN project_no varchar(128) NOT NULL DEFAULT '' COMMENT '项目编号',
    ADD COLUMN funding_project varchar(128) NOT NULL DEFAULT '' COMMENT '资助项目',
    ADD COLUMN funding_ratio varchar(128) NOT NULL DEFAULT '' COMMENT '资助经费/本人占比金额',
    ADD COLUMN is_now tinyint(2) NOT NULL DEFAULT 0 COMMENT '是否是至今，1-是；2-不是',
    ADD COLUMN `is_abroad` tinyint(2) NOT NULL DEFAULT 2 COMMENT '是否海外经历（1是，2否）';
-- 1.9 修改resume_research_direction（研究方向表）
ALTER TABLE resume_research_direction
    ADD COLUMN overview varchar(128) NOT NULL DEFAULT '' COMMENT '研究领域概况';

-- 1.10 修改resume_complete（简历完成度表）
ALTER TABLE resume_complete
    ADD COLUMN conference_page tinyint(1) NOT NULL DEFAULT 0 COMMENT '会议论文信息条数（对应resume_conference_page表）',
    ADD COLUMN overseas_experience tinyint(1) NOT NULL DEFAULT 0 COMMENT '其他海外经历信息条数（对应resume_overseas_experience表）',
    ADD COLUMN journal_position tinyint(1) NOT NULL DEFAULT 0 COMMENT '期刊/论文职务信息条数（对应resume_journal_position表）',
    ADD COLUMN project_review tinyint(1) NOT NULL DEFAULT 0 COMMENT '项目/基金评审信息条数（对应resume_project_review表）',
    ADD COLUMN thesis_committee tinyint(1) NOT NULL DEFAULT 0 COMMENT '学位答辩评委信息条数（对应resume_thesis_committee表）',
    ADD COLUMN association_position tinyint(1) NOT NULL DEFAULT 0 COMMENT '学会/协会任职信息条数（对应resume_association_position表）',
    ADD COLUMN social_position tinyint(1) NOT NULL DEFAULT 0 COMMENT '社会兼职/顾问信息条数（对应resume_social_position表）',
    ADD COLUMN teaching_course tinyint(1) NOT NULL DEFAULT 0 COMMENT '主讲课程信息条数（对应resume_teaching_course表）',
    ADD COLUMN education_reform tinyint(1) NOT NULL DEFAULT 0 COMMENT '教改实践信息条数（对应resume_education_reform表）',
    ADD COLUMN research_achievement tinyint(1) NOT NULL DEFAULT 0 COMMENT '近5年论文成果概况信息条数（对应resume_research_achievement表）',
    ADD COLUMN academic_conference_activity tinyint(1) NOT NULL DEFAULT 0 COMMENT '学术会议/活动信息条数（对应resume_academic_conference_activity表）',
    ADD COLUMN postdoctor_experience tinyint(1) NOT NULL DEFAULT 0 COMMENT '博士后经历信息条数',
    ADD COLUMN textbook_writing tinyint(1) NOT NULL DEFAULT 0 COMMENT '教材编写信息条数（对应教材编写相关表）',
    ADD COLUMN competition_guidance tinyint(1) NOT NULL DEFAULT 0 COMMENT '竞赛指导信息条数（对应竞赛指导相关表）';

-- 1.11 修改certificate（证书表）
ALTER TABLE certificate
    ADD COLUMN type tinyint(1) NOT NULL DEFAULT 0 COMMENT '证书类型；1：证书；2-技能';

ALTER TABLE `resume_intention_area_relation`
    ADD COLUMN `sort` tinyint(2) NOT NULL DEFAULT 0 COMMENT '展示排序' AFTER `level`;

-- ==========================================
-- ==========================================
-- 二、新增表信息SQL
-- ==========================================

-- 2.1 新增resume_academic_index（学术主页表）
CREATE TABLE resume_academic_index
(
    id          int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    member_id   int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
    resume_id   int(11) NOT NULL DEFAULT 0 COMMENT '简历ID',
    url         varchar(255) NOT NULL DEFAULT '' COMMENT '学术主页网址',
    add_time    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    title       varchar(60)  NOT NULL DEFAULT '' COMMENT '主页名称',
    update_time datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    status      tinyint(1) NOT NULL DEFAUL 1 COMMENT '状态（1有效，2无效，9冻结）',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='学术主页表';

-- 2.2 新增resume_education_major_relation（教育经历专业标签关联表）
CREATE TABLE `resume_education_major_relation`
(
    `id`                  int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `resume_education_id` int(11) NOT NULL DEFAULT '0' COMMENT '教育经历',
    `member_id`           int(11) NOT NULL DEFAULT '0' COMMENT '会员ID',
    `resume_id`           int(11) NOT NULL DEFAULT '0' COMMENT '简历ID',
    `dict_code`           int(11) NOT NULL DEFAULT '0' COMMENT '使用字典表',
    `add_time`            datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`              tinyint(1) NOT NULL DEFAUL 1 COMMENT '状态（1有效，2无效，9冻结）',
    PRIMARY KEY (`id`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教育经历专业标签关联表';
-- 2.3 教育经历导师标签关联表
CREATE TABLE `resume_education_mentor_relation`
(
    `id`                  int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `member_id`           int(11) NOT NULL DEFAULT '0' COMMENT '会员ID',
    `resume_education_id` int(11) NOT NULL DEFAULT '0' COMMENT '教育经历',
    `resume_id`           int(11) NOT NULL DEFAULT '0' COMMENT '简历ID',
    `dict_code`           int(11) NOT NULL DEFAULT '0' COMMENT '使用字典表',
    `add_time`            datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`              tinyint(1) NOT NULL DEFAUL 1 COMMENT '状态（1有效，2无效，9冻结）',
    PRIMARY KEY (`id`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教育经历导师标签关联表';

-- 2.4 工作经历研究领域标签关联表
CREATE TABLE `resume_work_research_field_relation`
(
    `id`                int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `resume_work_id`    int(11) NOT NULL DEFAULT '0' COMMENT '工作ID',
    `member_id`         int(11) NOT NULL DEFAULT '0' COMMENT '会员ID',
    `resume_id`         int(11) NOT NULL DEFAULT '0' COMMENT '简历ID',
    `research_field_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户研究领域表ID',
    `add_time`          datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`            tinyint(1) NOT NULL DEFAUL 1 COMMENT '状态（1有效，2无效，9冻结）',
    `type`              tinyint(1) NOT NULL DEFAULT '0' COMMENT '标签类型；1-系统标签；2-用户标签',
    PRIMARY KEY (`id`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作经历研究领域标签关联表';

-- 2.5 用户研究领域表
CREATE TABLE `resume_research_field`
(
    `id`                int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `member_id`         int(11) NOT NULL DEFAULT '0' COMMENT '会员ID',
    `resume_id`         int(11) NOT NULL DEFAULT '0' COMMENT '简历ID',
    `research_field_id` int(11) NOT NULL DEFAULT '0' COMMENT '研究领域id',
    `add_time`          datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`            tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态（1有效，2无效）',
    PRIMARY KEY (`id`),
    KEY                 `idx_create_resume_id_status` (`resume_id`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COMMENT='用户研究领域表';

-- 2.6 工作经历类型关联表
CREATE TABLE `resume_work_type_relation`
(
    `id`             int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `member_id`      int(11) NOT NULL DEFAULT '0' COMMENT '会员ID',
    `resume_work_id` int(11) NOT NULL DEFAULT '0' COMMENT '工作经历ID',
    `resume_id`      int(11) NOT NULL DEFAULT '0' COMMENT '简历ID',
    `dict_code`      int(11) NOT NULL DEFAULT '0' COMMENT '使用字典表',
    `add_time`       datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`         tinyint(1) NOT NULL DEFAUL 1 COMMENT '状态（1有效，2无效，9冻结）',
    PRIMARY KEY (`id`),
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='工作经历类型关联表';

-- 2.7 工作经历职称关联表
CREATE TABLE `resume_work_job_title_relation`
(
    `id`             int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `member_id`      int(11) NOT NULL DEFAULT '0' COMMENT '会员ID',
    `resume_work_id` int(11) NOT NULL DEFAULT '0' COMMENT '工作经历ID',
    `resume_id`      int(11) NOT NULL DEFAULT '0' COMMENT '简历ID',
    `dict_code`      int(11) NOT NULL DEFAULT '0' COMMENT '使用字典表',
    `get_year` year(4) NOT NULL COMMENT '获得时间',
    `add_time`       datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`         tinyint(1) NOT NULL DEFAUL 1 COMMENT '状态（1有效，2无效，9冻结）',
    PRIMARY KEY (`id`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作经历职称关联表';

-- 2.8 学术论文研究领域标签关联表
CREATE TABLE `resume_academic_page_research_field_relation`
(
    `id`                      int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `member_id`               int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
    `resume_academic_page_id` int(11) NOT NULL DEFAULT 0 COMMENT '学术论文ID',
    `resume_id`               int(11) NOT NULL DEFAULT 0 COMMENT '简历ID',
    `research_field_id`       int(11) NOT NULL DEFAULT 0 COMMENT '用户研究领域表ID',
    `add_time`                datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`             datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`                  tinyint(1) NOT NULL DEFAUL 1 COMMENT '状态（1有效，2无效，9冻结）',
    `type`                    tinyint(1) NOT NULL DEFAULT 0 COMMENT '标签类型；1-系统标签；2-用户标签',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学术论文研究领域标签关联表';

-- 2.9 学术论文阶段关联表
CREATE TABLE `resume_academic_page_stage_type_relation`
(
    `id`                      int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `member_id`               int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
    `resume_academic_page_id` int(11) NOT NULL DEFAULT 0 COMMENT '学术论文ID',
    `resume_id`               int(11) NOT NULL DEFAULT 0 COMMENT '简历ID',
    `type`                    tinyint(2) NOT NULL DEFAULT 0 COMMENT '阶段类型；1教育经历、2工作经历、3博士后经历、4项目经历',
    `add_time`                datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`             datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`                  tinyint(1) NOT NULL DEFAUL 1 COMMENT '状态（1有效，2无效，9冻结）',
    `related_stage_id`        int(11) NOT NULL DEFAULT 0 COMMENT '关联阶段ID',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学术论文阶段关联表';

-- 2.10 学术专利阶段关联表
CREATE TABLE `resume_academic_patent_stage_type_relation`
(
    `id`                        int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `member_id`                 int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
    `resume_academic_patent_id` int(11) NOT NULL DEFAULT 0 COMMENT '学术专利ID',
    `resume_id`                 int(11) NOT NULL DEFAULT 0 COMMENT '简历ID',
    `type`                      tinyint(2) NOT NULL DEFAULT 0 COMMENT '阶段类型；1教育经历、2工作经历、3博士后经历、4项目经历',
    `add_time`                  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`               datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`                    tinyint(1) NOT NULL DEFAUL 1 COMMENT '状态（1有效，2无效，9冻结）',
    `related_stage_id`          int(11) NOT NULL DEFAULT 0 COMMENT '关联阶段ID',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学术专利阶段关联表';

-- 2.11 学术著作阶段关联表
CREATE TABLE `resume_academic_book_stage_type_relation`
(
    `id`                      int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `member_id`               int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
    `resume_academic_book_id` int(11) NOT NULL DEFAULT 0 COMMENT '学术著作ID',
    `resume_id`               int(11) NOT NULL DEFAULT 0 COMMENT '简历ID',
    `type`                    tinyint(2) NOT NULL DEFAULT 0 COMMENT '阶段类型；1教育经历、2工作经历、3博士后经历、4项目经历',
    `add_time`                datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`             datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`                  tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态（1有效，2无效，9冻结）',
    `related_stage_id`        int(11) NOT NULL DEFAULT 0 COMMENT '关联阶段ID',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学术著作阶段关联表';

-- 2.12 研究方向研究领域标签联系表
CREATE TABLE `resume_research_direction_research_field_relation`
(
    `id`                           int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `member_id`                    int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
    `resume_research_direction_id` int(11) NOT NULL DEFAULT 0 COMMENT '研究方向ID',
    `resume_id`                    int(11) NOT NULL DEFAULT 0 COMMENT '简历ID',
    `research_field_id`            int(11) NOT NULL DEFAULT 0 COMMENT '用户研究领域表ID',
    `add_time`                     datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`                       tinyint(1) NOT NULL DEFAUL 1 COMMENT '状态（1有效，2无效，9冻结）',
    `type`                         tinyint(1) NOT NULL DEFAULT 0 COMMENT '标签类型；1-系统标签；2-用户标签',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='研究方向研究领域标签联系表';

-- 2.13 近5年论文成果概况表
CREATE TABLE `resume_research_achievement`
(
    `id`                int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `add_time`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `status`            tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态（0有效，1无效）',
    `resume_id`         int(11) NOT NULL DEFAULT '0' COMMENT '关联简历ID',
    `member_id`         int(11) NOT NULL DEFAULT '0' COMMENT '关联会员ID',
    `cas1`              varchar(255) NOT NULL DEFAULT '' COMMENT '中科院分区一区篇数',
    `cas2`              varchar(255) NOT NULL DEFAULT '' COMMENT '中科院分区二区篇数',
    `cas3`              varchar(255) NOT NULL DEFAULT '' COMMENT '中科院分区三区篇数',
    `cas4`              varchar(255) NOT NULL DEFAULT '' COMMENT '中科院分区四区篇数',
    `jcr1`              varchar(255) NOT NULL DEFAULT '' COMMENT 'JCR分区一区篇数',
    `jcr2`              varchar(255) NOT NULL DEFAULT '' COMMENT 'JCR分区二区篇数',
    `jcr3`              varchar(255) NOT NULL DEFAULT '' COMMENT 'JCR分区三区篇数',
    `jcr4`              varchar(255) NOT NULL DEFAULT '' COMMENT 'JCR分区四区篇数',
    `sci_count`         varchar(255) NOT NULL DEFAULT '' COMMENT 'SCI收录篇数',
    `ei_count`          varchar(255) NOT NULL DEFAULT '' COMMENT 'EI收录篇数',
    `ssci_count`        varchar(255) NOT NULL DEFAULT '' COMMENT 'SSCI收录篇数',
    `cscd_count`        varchar(255) NOT NULL DEFAULT '' COMMENT 'CSCD收录篇数',
    `cssci_count`       varchar(255) NOT NULL DEFAULT '' COMMENT 'CSSCI收录篇数',
    `pku_core_count`    varchar(255) NOT NULL DEFAULT '' COMMENT '北大核心收录篇数',
    `tech_core_count`   varchar(255) NOT NULL DEFAULT '' COMMENT '科技核心收录篇数',
    `cumulative_impact` varchar(255) NOT NULL DEFAULT '' COMMENT '论文累计影响因子',
    `max_impact`        varchar(255) NOT NULL DEFAULT '' COMMENT '单篇最高影响因子',
    `total_citations`   varchar(255) NOT NULL DEFAULT '' COMMENT '论文总被引次数',
    `h_index`           varchar(255) NOT NULL DEFAULT '' COMMENT 'H指数',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='近5年论文成果概况表';

-- 2.14 其他海外经历表
CREATE TABLE `resume_overseas_experience`
(
    `id`                  int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `resume_id`           int(11) NOT NULL DEFAULT '0' COMMENT '简历ID',
    `member_id`           int(11) NOT NULL DEFAULT '0' COMMENT '会员ID',
    `type`                varchar(255) NOT NULL DEFAULT '' COMMENT '经历类别；自定义',
    `type_code`           int(11) NOT NULL DEFAULT '0' COMMENT '经历类别；使用字典表',
    `institution_name`    varchar(255) NOT NULL DEFAULT '' COMMENT '机构名称',
    `institution_name_id` int(11) NOT NULL DEFAULT '0' COMMENT '机构名称（名录id）',
    `role`                varchar(255) NOT NULL DEFAULT '' COMMENT '担任角色',
    `begin_date`          date         NOT NULL DEFAULT '0000-00-00' COMMENT '开始时间（默认初始日期避免NULL）',
    `end_date`            date         NOT NULL DEFAULT '0000-00-00' COMMENT '结束时间（默认初始日期避免NULL）',
    `add_time`            datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`              tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：1-正常，2-删除',
    `is_now`              tinyint(1) NOT NULL DEFAULT '0' COMMENT '结束时间是否是至今1：是；2：不是',
    `content`             text COMMENT '详细说明',
    PRIMARY KEY (`id`),
    KEY                   `idx_resume_id_status` (`resume_id`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='其他海外经历表';

-- 2.15 会议论文表
CREATE TABLE `resume_conference_page`
(
    `id`                       int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增',
    `add_time`                 datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`              datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `status`                   tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态（0有效，1无效）',
    `resume_id`                int(11) NOT NULL DEFAULT '0' COMMENT '关联简历ID',
    `member_id`                int(11) NOT NULL DEFAULT '0' COMMENT '关联会员ID',
    `title`                    varchar(256) NOT NULL DEFAULT '' COMMENT '论文题目',
    `conference_name`          varchar(256) NOT NULL DEFAULT '' COMMENT '会议名称',
    `publish_time`             date         NOT NULL DEFAULT '0000-00-00' COMMENT '发表时间（表单时间选择器对应）',
    `author_position`          int(11) NOT NULL DEFAULT '0' COMMENT '本人位次（如"第一作者""通讯作者"等）',
    `conference_level`         int(11) NOT NULL DEFAULT '0' COMMENT '会议级别，使用字典表',
    `acceptance_rate`          varchar(64)  NOT NULL DEFAULT '' COMMENT '论文录用率（支持百分比/文本描述，如"15%""同行评审严格"）',
    `citation_count_five_year` int(11) NOT NULL DEFAULT '0' COMMENT '近5年被引次数',
    `awards`                   varchar(256) NOT NULL DEFAULT '' COMMENT '所获奖项（示例：最佳论文奖）',
    `description`              text         NOT NULL COMMENT '论文描述（项目背景、成果、个人贡献等详细内容）',
    `address_detail`           varchar(255) NOT NULL DEFAULT '' COMMENT '举办地点',
    `page_number`              varchar(255) NOT NULL DEFAULT '' COMMENT '卷期页码',
    `collection_name`          varchar(255) NOT NULL DEFAULT '' COMMENT '论文集名称',
    PRIMARY KEY (`id`),
    KEY                        `idx_resume_id_status` (`resume_id`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='会议论文表';

-- 2.16 会议论文研究领域标签关联表
CREATE TABLE `resume_conference_page_research_field_relation`
(
    `id`                        int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `member_id`                 int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
    `resume_conference_page_id` int(11) NOT NULL DEFAULT 0 COMMENT '会议论文ID',
    `resume_id`                 int(11) NOT NULL DEFAULT 0 COMMENT '简历ID',
    `research_field_id`         int(11) NOT NULL DEFAULT 0 COMMENT '用户研究领域表ID',
    `add_time`                  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`               datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`                    tinyint(1) NOT NULL DEFAUL 1 COMMENT '状态（1有效，2无效，9冻结）',
    `type`                      tinyint(1) NOT NULL DEFAULT 0 COMMENT '标签类型；1-系统标签；2-用户标签',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会议论文研究领域标签关联表';

-- 2.17 会议论文阶段关联表
CREATE TABLE `resume_conference_page_stage_type_relation`
(
    `id`                        int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `member_id`                 int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
    `resume_conference_page_id` int(11) NOT NULL DEFAULT 0 COMMENT '会议论文ID',
    `resume_id`                 int(11) NOT NULL DEFAULT 0 COMMENT '简历ID',
    `type`                      tinyint(2) NOT NULL DEFAULT 0 COMMENT '阶段类型；1教育经历、2工作经历、3博士后经历、4项目经历',
    `add_time`                  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`               datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`                    tinyint(1) NOT NULL DEFAUL 1 COMMENT '状态（1有效，2无效，9冻结）',
    `related_stage_id`          int(11) NOT NULL DEFAULT 0 COMMENT '关联阶段ID',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会议论文阶段关联表';

-- 2.18 期刊/论文职务表
CREATE TABLE `resume_journal_position`
(
    `id`           int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `resume_id`    int(11) NOT NULL DEFAULT '0' COMMENT '简历ID',
    `member_id`    int(11) NOT NULL DEFAULT '0' COMMENT '会员ID',
    `journal_name` varchar(200) NOT NULL DEFAULT '' COMMENT '期刊名称',
    `role`         varchar(100) NOT NULL DEFAULT '' COMMENT '担任角色',
    `role_code`    int(11) NOT NULL DEFAULT '0' COMMENT '担任角色id，使用字典表',
    `type_code`    int(11) NOT NULL DEFAULT '0' COMMENT '期刊类别',
    `begin_date`   date         NOT NULL DEFAULT '1970-01-01' COMMENT '开始时间（默认初始日期避免NULL）',
    `end_date`     date         NOT NULL DEFAULT '1970-01-01' COMMENT '结束时间（默认初始日期避免NULL）',
    `add_time`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`       tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：1-正常，2-删除',
    `is_now`       tinyint(1) NOT NULL DEFAULT '0' COMMENT '结束时间是否是至今1：是；2：不是',
    `description`  text COMMENT '详情描述',
    PRIMARY KEY (`id`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='期刊/论文职务表';

-- 2.19 项目/基金评审表
CREATE TABLE `resume_project_review`
(
    `id`           int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `resume_id`    int(11) NOT NULL DEFAULT '0' COMMENT '简历ID',
    `member_id`    int(11) NOT NULL DEFAULT '0' COMMENT '会员ID',
    `project_name` varchar(300) NOT NULL DEFAULT '' COMMENT '项目/基金名称',
    `role`         varchar(100) NOT NULL DEFAULT '' COMMENT '担任角色',
    `role_code`    int(11) NOT NULL DEFAULT '0' COMMENT '担任角色id，使用字典表',
    `level_code`   int(11) NOT NULL DEFAULT '0' COMMENT '项目级别，使用字典表',
    `description`  text         NOT NULL COMMENT '评审描述',
    `add_time`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`       tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：1-正常，2-删除',
    `is_now`       tinyint(1) NOT NULL DEFAULT '0' COMMENT '结束时间是否是至今1：是；2：不是',
    `begin_date`   date         NOT NULL DEFAULT '0000-00-00' COMMENT '任职时间,开始',
    `end_date`     date         NOT NULL DEFAULT '0000-00-00' COMMENT '任职时间，结束，如果默认值则等于至今',
    PRIMARY KEY (`id`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目/基金评审表';

-- 2.20 学位答辩评委表
CREATE TABLE `resume_thesis_committee`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `resume_id`   int(11) NOT NULL DEFAULT 0 COMMENT '简历ID',
    `member_id`   int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
    `role`        varchar(100) NOT NULL DEFAULT '' COMMENT '担任角色',
    `role_code`   int(11) NOT NULL DEFAULT 0 COMMENT '担任角色id，使用字典表',
    `name`        varchar(300) NOT NULL DEFAULT '' COMMENT '答辩名称',
    `begin_date`  date         NOT NULL DEFAULT '0000-00-00' COMMENT '开始时间（默认初始日期避免NULL）',
    `end_date`    date         NOT NULL DEFAULT '0000-00-00' COMMENT '结束时间（默认初始日期避免NULL）',
    `type_code`   int(11) NOT NULL DEFAULT 0 COMMENT '答辩类别，使用字典表',
    `description` text         NOT NULL COMMENT '答辩描述',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`      tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-删除',
    `is_now`      tinyint(1) NOT NULL DEFAULT 2 COMMENT '结束时间是否是至今1：是；2：不是',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学位答辩评委表';

-- 2.21 学会/协会任职表
CREATE TABLE `resume_association_position`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `resume_id`   int(11) NOT NULL DEFAULT 0 COMMENT '简历ID',
    `member_id`   int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
    `name`        varchar(200) NOT NULL DEFAULT '' COMMENT '学会/协会名称',
    `type_code`   int(11) NOT NULL DEFAULT 0 COMMENT '学会类别，使用字典表',
    `begin_date`  date         NOT NULL DEFAULT '0000-00-00' COMMENT '开始时间（默认初始日期避免NULL）',
    `end_date`    date         NOT NULL DEFAULT '0000-00-00' COMMENT '结束时间（默认初始日期避免NULL）',
    `description` text         NOT NULL COMMENT '详细说明',
    `role`        varchar(100) NOT NULL DEFAULT '' COMMENT '担任角色',
    `role_code`   int(11) NOT NULL DEFAULT 0 COMMENT '担任角色id，使用字典表',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`      tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-删除',
    `is_now`      tinyint(1) NOT NULL DEFAULT 2 COMMENT '结束时间是否是至今1：是；2：不是',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学会/协会任职表';

-- 2.22 社会兼职/顾问表
CREATE TABLE `resume_social_position`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `resume_id`   int(11) NOT NULL DEFAULT 0 COMMENT '简历ID',
    `member_id`   int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
    `name`        varchar(200) NOT NULL DEFAULT '' COMMENT '机构名称',
    `role`        varchar(100) NOT NULL DEFAULT '' COMMENT '担任角色',
    `role_code`   int(11) NOT NULL DEFAULT 0 COMMENT '担任角色id，使用字典表',
    `begin_date`  date         NOT NULL DEFAULT '0000-00-00' COMMENT '开始时间（默认初始日期避免NULL）',
    `end_date`    date         NOT NULL DEFAULT '0000-00-00' COMMENT '结束时间（默认初始日期避免NULL）',
    `type_code`   int(11) NOT NULL DEFAULT 0 COMMENT '组织类型，使用字典',
    `description` text         NOT NULL COMMENT '详细说明',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`      tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-删除',
    `is_now`      tinyint(1) NOT NULL DEFAULT 2 COMMENT '结束时间是否是至今',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='社会兼职/顾问表';

-- 2.23 主讲课程表
CREATE TABLE `resume_teaching_course`
(
    `id`                   int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `resume_id`            int(11) NOT NULL DEFAULT '0' COMMENT '简历ID',
    `member_id`            int(11) NOT NULL DEFAULT '0' COMMENT '会员ID',
    `name`                 varchar(200) NOT NULL DEFAULT '' COMMENT '课程名称',
    `number`               varchar(50)  NOT NULL DEFAULT '' COMMENT '课程数量',
    `begin_date`           date         NOT NULL DEFAULT '0000-00-00' COMMENT '开始时间（默认初始日期避免NULL）',
    `end_date`             date         NOT NULL DEFAULT '0000-00-00' COMMENT '结束时间（默认初始日期避免NULL）',
    `add_time`             datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`               tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：1-正常，2-删除',
    `is_now`               tinyint(1) NOT NULL DEFAULT '0' COMMENT '结束时间是否是至今',
    `target_audience_code` int(11) NOT NULL DEFAULT '0' COMMENT '授课对象字典type=53',
    PRIMARY KEY (`id`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='主讲课程表';

-- 2.24 主讲课程标签联系表
CREATE TABLE `resume_teaching_course_tag_relation`
(
    `id`                        int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `member_id`                 int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
    `resume_teaching_course_id` int(11) NOT NULL DEFAULT 0 COMMENT '课程ID',
    `resume_id`                 int(11) NOT NULL DEFAULT 0 COMMENT '简历ID',
    `name`                      varchar(60) NOT NULL DEFAULT '' COMMENT '标签名称',
    `add_time`                  datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`               datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`                    tinyint(1) NOT NULL DEFAUL 1 COMMENT '状态（1有效，2无效，9冻结）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='主讲课程标签联系表';

-- 2.25 教改实践表
CREATE TABLE `resume_education_reform`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `resume_id`   int(11) NOT NULL DEFAULT 0 COMMENT '简历ID',
    `member_id`   int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
    `name`        varchar(300) NOT NULL DEFAULT '' COMMENT '项目名称',
    `type_code`   int(11) NOT NULL DEFAULT 0 COMMENT '项目类别，使用字典表',
    `begin_date`  date         NOT NULL DEFAULT '0000-00-00' COMMENT '开始时间（默认初始日期避免NULL）',
    `end_date`    date         NOT NULL DEFAULT '0000-00-00' COMMENT '结束时间（默认初始日期避免NULL）',
    `role`        varchar(100) NOT NULL DEFAULT '' COMMENT '担任角色',
    `role_code`   tinyint(11) NOT NULL DEFAULT 0 COMMENT '担任角色id，使用字典表',
    `description` text         NOT NULL COMMENT '项目描述',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`      tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-删除',
    `is_now`      tinyint(1) NOT NULL DEFAULT 2 COMMENT '结束时间是否是至今',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教改实践表';

-- 2.26 教改实践成果标签联系表
CREATE TABLE `resume_education_reform_achievement_tag_relation`
(
    `id`                  int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `member_id`           int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
    `education_reform_id` int(11) NOT NULL DEFAULT 0 COMMENT '教改实践ID',
    `resume_id`           int(11) NOT NULL DEFAULT 0 COMMENT '简历ID',
    `name`                varchar(60) NOT NULL DEFAULT '' COMMENT '标签名称',
    `add_time`            datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`              tinyint(1) NOT NULL DEFAUL 1 COMMENT '状态（1有效，2无效，9冻结）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教改实践成果标签联系表';

-- 2.27 竞赛指导表
CREATE TABLE `resume_competition_guidance`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `resume_id`   int(11) NOT NULL DEFAULT '0' COMMENT '简历ID',
    `member_id`   int(11) NOT NULL DEFAULT '0' COMMENT '会员ID',
    `name`        varchar(200) NOT NULL DEFAULT '' COMMENT '竞赛名称',
    `role`        varchar(100) NOT NULL DEFAULT '' COMMENT '担任角色',
    `role_code`   int(11) NOT NULL DEFAULT '0' COMMENT '担任角色id，使用字典表',
    `type_code`   int(11) NOT NULL DEFAULT '0' COMMENT '竞赛类型，使用字典表',
    `begin_date`  date         NOT NULL DEFAULT '0000-00-00' COMMENT '开始时间（默认初始日期避免NULL）',
    `end_date`    date         NOT NULL DEFAULT '0000-00-00' COMMENT '结束时间（默认初始日期避免NULL）',
    `description` text         NOT NULL COMMENT '指导描述',
    `add_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：1-正常，2-删除',
    `is_now`      tinyint(2) NOT NULL DEFAULT '2' COMMENT '是否是至今；1:是；2:不是',
    PRIMARY KEY (`id`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='竞赛指导表';

-- 2.28 竞赛指导成果标签联系表
CREATE TABLE `resume_competition_guidance_achievement_tag_relation`
(
    `id`                             int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `member_id`                      int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
    `resume_competition_guidance_id` int(11) NOT NULL DEFAULT 0 COMMENT '竞赛指导ID',
    `resume_id`                      int(11) NOT NULL DEFAULT 0 COMMENT '简历ID',
    `name`                           varchar(60) NOT NULL DEFAULT '' COMMENT '标签名称',
    `add_time`                       datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`                         tinyint(1) NOT NULL DEFAUL 1 COMMENT '状态（1有效，2无效，9冻结）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='竞赛指导成果标签联系表';

-- 2.29 教材编写表
CREATE TABLE `resume_textbook_writing`
(
    `id`           int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `resume_id`    int(11) NOT NULL DEFAULT 0 COMMENT '简历ID',
    `member_id`    int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
    `name`         varchar(300) NOT NULL DEFAULT '' COMMENT '教材名称',
    `role`         varchar(100) NOT NULL DEFAULT '' COMMENT '担任角色',
    `role_code`    int(11) NOT NULL DEFAULT 0 COMMENT '担任角色id，使用字典表',
    `type_code`    int(11) NOT NULL DEFAULT 0 COMMENT '教材类型，使用字典表',
    `writing_date` date         NOT NULL DEFAULT '0000-00-00' COMMENT '编写时间（默认初始日期避免NULL）',
    `description`  text         NOT NULL COMMENT '教材描述',
    `add_time`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`       tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教材编写表';

-- 2.30 教材编写成果标签联系表
CREATE TABLE `resume_textbook_writing_achievement_tag_relation`
(
    `id`                         int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `member_id`                  int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
    `resume_textbook_writing_id` int(11) NOT NULL DEFAULT 0 COMMENT '教材编写ID',
    `resume_id`                  int(11) NOT NULL DEFAULT 0 COMMENT '简历ID',
    `name`                       varchar(60) NOT NULL DEFAULT '' COMMENT '标签名称',
    `add_time`                   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`                     tinyint(1) NOT NULL DEFAUL 1 COMMENT '状态（1有效，2无效，9冻结）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教材编写成果标签联系表';

-- 2.31 简历-奖励/荣誉/称号/头衔表
CREATE TABLE `resume_reward`
(
    `id`                  int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `resume_id`           int(11) NOT NULL DEFAULT 0 COMMENT '简历ID，关联简历表',
    `member_id`           int(11) NOT NULL DEFAULT 0 COMMENT '会员ID，关联会员表',
    `category_code`       int(11) NOT NULL DEFAULT 0 COMMENT '奖励类别，使用字典表',
    `name`                varchar(255) NOT NULL DEFAULT '' COMMENT '奖项名称',
    `obtain_time`         date         NOT NULL DEFAULT '0000-00-00' COMMENT '获得时间（默认初始日期避免NULL）',
    `issuing_institution` varchar(255) NOT NULL DEFAULT '' COMMENT '颁发机构名称',
    `level`               varchar(255) NOT NULL DEFAULT '' COMMENT '奖励级别（存量数据保存）',
    `level_code`          int(11) NOT NULL DEFAULT 0 COMMENT '奖励级别，使用字典表',
    `role`                varchar(255) NOT NULL DEFAULT '' COMMENT '本人位次/获奖角色（存量数据保存）',
    `role_code`           varchar(255) NOT NULL DEFAULT '' COMMENT '本人位次/获奖角色,type=62',
    `description`         text         NOT NULL COMMENT '详细说明，对奖励/荣誉等的详细描述，最多2000字',
    `add_time`            datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`              tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态（1有效，2无效，9冻结）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='简历-获奖表';


-- 2.33 学术会议/活动表
CREATE TABLE `resume_academic_conference_activity`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `resume_id`   int(11) NOT NULL DEFAULT 0 COMMENT '简历ID',
    `member_id`   int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
    `name`        varchar(255) NOT NULL DEFAULT '' COMMENT '会议/活动名称，如示例中的“中国化学会第34届学术年会理论与计算催化分会”',
    `hold_date`   date         NOT NULL DEFAULT '0000-00-00' COMMENT '举办时间（默认初始日期避免NULL）',
    `hold_place`  varchar(255) NOT NULL DEFAULT '' COMMENT '举办地点',
    `role`        varchar(100) NOT NULL DEFAULT '' COMMENT '担任角色',
    `role_code`   int(11) NOT NULL DEFAULT 0 COMMENT '担任角色id，使用字典表',
    `level_code`  int(11) NOT NULL DEFAULT 0 COMMENT '会议/活动级别，使用字典表',
    `description` text         NOT NULL COMMENT '详细说明，对会议或活动的详细描述，最多2000字',
    `status`      tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态：1-正常，2-删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学术会议/活动表';

-- 2.34 博士后经历表
CREATE TABLE `resume_postdoctor_work`
(
    `id`                           int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `resume_id`                    int(11) NOT NULL DEFAULT '0' COMMENT '简历ID',
    `member_id`                    int(11) NOT NULL DEFAULT '0' COMMENT '会员ID',
    `relying_institution`          varchar(255) NOT NULL DEFAULT '' COMMENT '依托院校/机构全称',
    `relying_institution_id`       int(11) NOT NULL DEFAULT '0' COMMENT '依托院校/机构全称Id',
    `is_abroad`                    tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否海外经历（1是，2否）',
    `is_joint_training`            tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否联合培养（1是，2否）',
    `join_training_institution`    varchar(255) NOT NULL DEFAULT '' COMMENT '联合培养院校/机构全称',
    `join_training_institution_id` int(11) NOT NULL COMMENT '联合培养院校/机构全称Id',
    `college_lab_group`            varchar(255) NOT NULL DEFAULT '' COMMENT '学院/流动站/团队/实验室/课题组名称（选填）',
    `begin_date`                   date         NOT NULL DEFAULT '0000-00-00' COMMENT '进站时间（默认初始日期避免NULL）',
    `end_date`                     date         NOT NULL DEFAULT '0000-00-00' COMMENT '出站时间（含预计）（默认初始日期避免NULL）',
    `cooperative_mentor`           varchar(100) NOT NULL DEFAULT '' COMMENT '合作导师（选填）',
    `add_time`                     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`                       tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态（0有效，1无效等）',
    `template_type`                tinyint(1) NOT NULL DEFAULT '0' COMMENT '简历模板类型：1-学术版，2-普通版（前三步使用）',
    `research_direction`           varchar(255) NOT NULL DEFAULT '' COMMENT '研究方向',
    `content`                      text COMMENT '补充说明',
    `complete_status`              tinyint(2) NOT NULL DEFAULT '1' COMMENT '完善状态；1:已完善；2:待完善',
    `is_now`                       tinyint(2) NOT NULL DEFAULT '2' COMMENT '是否是至今；1:是；2不是',
    PRIMARY KEY (`id`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='博士后经历表';

-- 2.35 博士后经历研究领域标签关联表
CREATE TABLE `resume_postdoctor_work_research_field_relation`
(
    `id`                        int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `resume_postdoctor_work_id` int(11) NOT NULL DEFAULT 0 COMMENT '博士后工作ID',
    `member_id`                 int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
    `resume_id`                 int(11) NOT NULL DEFAULT 0 COMMENT '简历ID',
    `research_field_id`         int(11) NOT NULL DEFAULT 0 COMMENT '用户研究领域表ID',
    `add_time`                  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`               datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`                    tinyint(1) NOT NULL DEFAUL 1 COMMENT '状态（1有效，2无效，9冻结）',
    `type`                      tinyint(1) NOT NULL DEFAULT 0 COMMENT '标签类型；1-系统标签；2-用户标签',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='博士后经历研究领域标签关联表';

-- 2.36 博士后经历导师头衔联系表
CREATE TABLE `resume_postdoctor_work_mentor_title_relation`
(
    `id`                        int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `member_id`                 int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
    `resume_postdoctor_work_id` int(11) NOT NULL DEFAULT 0 COMMENT '博士后经历导师头衔ID',
    `resume_id`                 int(11) NOT NULL DEFAULT 0 COMMENT '简历ID',
    `dict_code`                 int(11) NOT NULL DEFAULT 0 COMMENT '头衔ID，使用字典表',
    `add_time`                  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`               datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`                    tinyint(1) NOT NULL DEFAUL 1 COMMENT '状态（1有效，2无效，9冻结）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='博士后经历导师头衔联系表';

-- 2.37 博士后经历主持或重要参与的科研或资助项目联系表
CREATE TABLE `resume_postdoctor_work_project_relation`
(
    `id`                        int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `member_id`                 int(11) NOT NULL DEFAULT 0 COMMENT '会员ID',
    `resume_postdoctor_work_id` int(11) NOT NULL DEFAULT 0 COMMENT '博士后经历导师头衔ID',
    `resume_id`                 int(11) NOT NULL DEFAULT 0 COMMENT '简历ID',
    `dict_code`                 int(11) NOT NULL DEFAULT 0 COMMENT '主持或重要参与的科研或资助项目名称ID，type=33，使用字典表',
    `project_name`              varchar(125) NOT NULL DEFAULT '' COMMENT '主持或重要参与的科研或资助项目名称',
    `add_time`                  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`               datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`                    tinyint(1) NOT NULL DEFAUL 1 COMMENT '状态（1有效，2无效，9冻结）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='博士后经历主持或重要参与的科研或资助项目联系表';


create table school_dictionary
(
    id               int(11)      not null auto_increment comment 'id',
    add_time         datetime     not null default current_timestamp comment '创建时间',
    update_time      datetime     not null default current_timestamp on update current_timestamp comment '修改时间',
    status           tinyint(1)   not null default 1 comment '状态 1:正常 2:隐藏',
    school_code      varchar(64)  not null default '' comment '学校标识码',
    name_cn          varchar(128) not null default '' comment '高校全称（中文）',
    short_name_cn    varchar(128) not null default '' comment '高校简称（中文）',
    name_en          varchar(128) not null default '' comment ' 高校全称（英文）',
    short_name_en    varchar(128) not null default '' comment ' 高校简称（英文）',
    main_department  varchar(128) not null default '' comment ' 主管部门 ',
    country          varchar(128) not null default '' comment ' 所在国家（中文）',
    country_en       varchar(128) not null default '' comment ' 所在国家（英文）',
    province         varchar(128) not null default '' comment ' 所在省份 ',
    city             varchar(128) not null default '' comment ' 所在市 ',
    education_level  varchar(128) not null default '' comment ' 办学层次 ',
    education_nature varchar(128) not null default '' comment ' 办学性质 ',
    school_category  varchar(128) not null default '' comment ' 学校类别 ',
    school_type      varchar(128) not null default '' comment ' 学校类型 ',
    school_character varchar(128) not null default '' comment ' 院校特性 ',
    logo_url         varchar(255) not null default '' comment ' 学校Logo ',
    label_name       varchar(255) not null default '' comment ' 名校标签 ',
    qs_ranking       int(11)      not null default 0 comment ' QS排名 ',
    qs_ranking_range varchar(64)  not null default '' comment ' QS排名区间 ',
    primary key (id)
) engine = innodb
  default charset = utf8mb4 comment =' 高校名录 ';

CREATE TABLE `resume_title_relation`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `member_id`   int(11) NOT NULL COMMENT '会员ID',
    `resume_id`   int(11) NOT NULL COMMENT '简历ID',
    `title_id`    int(11) NOT NULL COMMENT '职称Id',
    `get_year` year(4) NOT NULL COMMENT '获得时间',
    `add_time`    datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`      tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态（1有效，2无效，9冻结）',
    PRIMARY KEY (`id`),
    KEY           `idx_resume_id` (`resume_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户职称关联表';

create table company_dictionary
(
    id             int(11)      not null auto_increment comment ' id ',
    add_time       datetime     not null default current_timestamp comment ' 创建时间 ',
    update_time    datetime     not null default current_timestamp on update current_timestamp comment ' 修改时间 ',
    status         tinyint(1)   not null default 1 comment ' 状态 1:正常 2:隐藏 ',
    name_cn        varchar(128) not null default '' comment ' 单位全称（中文）',
    short_name_cn  varchar(128) not null default '' comment '单位简称（中文）',
    name_en        varchar(128) not null default '' comment ' 单位全称（英文）',
    short_name_en  varchar(128) not null default '' comment ' 单位简称（英文）',
    province       varchar(128) not null default '' comment ' 所在省份 ',
    city           varchar(128) not null default '' comment ' 所在市 ',
    company_nature varchar(128) not null default '' comment ' 单位性质 ',
    company_type   varchar(128) not null default '' comment ' 单位类型 ',
    logo_url       varchar(255) not null default '' comment ' Logo ',
    label_name     varchar(255) not null default '' comment ' 标签 ',
    primary key (id)
) engine = innodb
  default charset = utf8mb4 comment =' 单位名录 ';

CREATE TABLE `research_field`
(
    `id`               int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `create_member_id` int(11) NOT NULL DEFAULT '0' COMMENT '创建的会员ID',
    `create_resume_id` int(11) NOT NULL DEFAULT '0' COMMENT '创建的简历ID',
    `name`             varchar(255) NOT NULL DEFAULT '' COMMENT '标签名称',
    `add_time`         datetime     NOT NULL DEFAULT '0000-00-00' COMMENT '创建时间',
    `update_time`      datetime     NOT NULL DEFAULT '0000-00-00' COMMENT '更新时间',
    `status`           tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态（1有效，2无效）',
    PRIMARY KEY (`id`),
    KEY                `idx_create_resume_id_status` (`create_resume_id`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='研究领域表';

CREATE TABLE `resume_academic_page_record_relation`
(
    `id`                      int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `resume_id`               int(11) NOT NULL DEFAULT '0' COMMENT '简历id',
    `member_id`               int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
    `resume_academic_page_id` int(11) NOT NULL DEFAULT '0' COMMENT '学术论文id',
    `status`                  tinyint(2) NOT NULL DEFAULT '1' COMMENT '1正常，2 删除',
    `dict_code`               varchar(255) NOT NULL DEFAULT '' COMMENT '代码常量',
    `name`                    varchar(255) NOT NULL DEFAULT '' COMMENT '自定义名称',
    `add_time`                datetime     NOT NULL DEFAULT '0000-00-00 00:00:00',
    `update_time`             datetime     NOT NULL DEFAULT '0000-00-00 00:00:00',
    PRIMARY KEY (`id`),
    KEY                       `idx_resume_academic_page_id` (`resume_academic_page_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学术论文收录情况';
-- ==========================================
-- 三、为新创建的表添加索引
-- ==========================================

-- 3.1 学术主页表索引
ALTER TABLE `resume_academic_index`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`);

-- 3.2 教育经历专业标签关联表索引
ALTER TABLE `resume_education_major_relation`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`),
    ADD KEY `idx_resume_education_id` (`resume_education_id`);

-- 3.3 教育经历导师标签关联表索引
ALTER TABLE `resume_education_mentor_relation`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`),
    ADD KEY `idx_resume_education_id` (`resume_education_id`);

-- 3.4 工作经历研究领域标签关联表索引
ALTER TABLE `resume_work_research_field_relation`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`),
    ADD KEY `idx_resume_work_id` (`resume_work_id`);

-- 3.5 用户研究领域表索引
ALTER TABLE `resume_research_field`
    ADD KEY `idx_create_resume_id_status` (`create_resume_id`, `status`);

-- 3.6 工作经历类型关联表索引
ALTER TABLE `resume_work_type_relation`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`),
    ADD KEY `idx_resume_work_id` (`resume_work_id`);

-- 3.7 工作经历职称关联表索引
ALTER TABLE `resume_work_job_title_relation`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`),
    ADD KEY `idx_resume_work_id` (`resume_work_id`);

-- 3.8 学术论文研究领域标签关联表索引
ALTER TABLE `resume_academic_page_research_field_relation`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`),
    ADD KEY `idx_resume_academic_page_id` (`resume_academic_page_id`);

-- 3.9 学术论文阶段关联表索引
ALTER TABLE `resume_academic_page_stage_type_relation`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`),
    ADD KEY `idx_resume_academic_page_id` (`resume_academic_page_id`);

-- 3.10 学术专利阶段关联表索引
ALTER TABLE `resume_academic_patent_stage_type_relation`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`),
    ADD KEY `idx_resume_academic_patent_id` (`resume_academic_patent_id`);

-- 3.15 会议论文表索引
ALTER TABLE `resume_conference_page`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`);

-- 3.16 会议论文研究领域标签关联表索引
ALTER TABLE `resume_conference_page_research_field_relation`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`),
    ADD KEY `idx_resume_conference_page_id` (`resume_conference_page_id`);

-- 3.17 会议论文阶段关联表索引
ALTER TABLE `resume_conference_page_stage_type_relation`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`),
    ADD KEY `idx_resume_conference_page_id` (`resume_conference_page_id`);

-- 3.18 期刊/论文职务表索引
ALTER TABLE `resume_journal_position`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`);

-- 3.19 项目/基金评审表索引
ALTER TABLE `resume_project_review`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`);

-- 3.20 学位答辩评委表索引
ALTER TABLE `resume_thesis_committee`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`);

-- 3.21 学会/协会任职表索引
ALTER TABLE `resume_association_position`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`);

-- 3.22 社会兼职/顾问表索引
ALTER TABLE `resume_social_position`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`);

-- 3.23 主讲课程表索引
ALTER TABLE `resume_teaching_course`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`);

-- 3.24 主讲课程标签联系表索引
ALTER TABLE `resume_teaching_course_tag_relation`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`),
    ADD KEY `idx_resume_teaching_course_id` (`resume_teaching_course_id`);

-- 3.25 教改实践表索引
ALTER TABLE `resume_education_reform`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`);

-- 3.26 教改实践成果标签联系表索引
ALTER TABLE `resume_education_reform_achievement_tag_relation`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`);

-- 3.27 竞赛指导表索引
ALTER TABLE `resume_competition_guidance`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`);

-- 3.28 竞赛指导成果标签联系表索引
ALTER TABLE `resume_competition_guidance_achievement_tag_relation`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`),
    ADD KEY `idx_resume_competition_guidance_id` (`resume_competition_guidance_id`);

-- 3.29 教材编写表索引
ALTER TABLE `resume_textbook_writing`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`);

-- 3.30 教材编写成果标签联系表索引
ALTER TABLE `resume_textbook_writing_achievement_tag_relation`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`),
    ADD KEY `idx_resume_textbook_writing_id` (`resume_textbook_writing_id`);

-- 3.31 简历-获奖表索引
ALTER TABLE `resume_reward`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`);

-- 3.32 技能特长标签表索引
ALTER TABLE `resume_skill_relation`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`);

-- 3.33 学术会议/活动表索引
ALTER TABLE `resume_academic_conference_activity`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`);

-- 3.34 博士后经历表索引
ALTER TABLE `resume_postdoctor_work`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`);

-- 3.35 博士后经历研究领域标签关联表索引
ALTER TABLE `resume_postdoctor_work_research_field_relation`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`),
    ADD KEY `idx_resume_postdoctor_work_id` (`resume_postdoctor_work_id`);

-- 3.36 博士后经历导师头衔联系表索引
ALTER TABLE `resume_postdoctor_work_mentor_title_relation`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`),
    ADD KEY `idx_resume_postdoctor_work_id` (`resume_postdoctor_work_id`);

-- 3.37 博士后经历主持或重要参与的科研或资助项目联系表索引
ALTER TABLE `resume_postdoctor_work_project_relation`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`),
    ADD KEY `idx_resume_postdoctor_work_id` (`resume_postdoctor_work_id`);

-- 3.38 用户职称关联表索引
ALTER TABLE `resume_title_relation`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`);

-- 3.11 学术著作阶段关联表索引
ALTER TABLE `resume_academic_book_stage_type_relation`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`),
    ADD KEY `idx_resume_academic_book_id` (`resume_academic_book_id`);

-- 3.12 研究方向研究领域标签联系表索引
ALTER TABLE `resume_research_direction_research_field_relation`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`),
    ADD KEY `idx_resume_research_direction_id` (`resume_research_direction_id`);

-- 3.13 近5年论文成果概况表索引
ALTER TABLE `resume_research_achievement`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`);

-- 3.14 其他海外经历表索引
ALTER TABLE `resume_overseas_experience`
    ADD KEY `idx_resume_id_status` (`resume_id`, `status`);
