-- auto-generated definition
create table resume_wx_bind
(
    id           int auto_increment
        primary key,
    add_time     datetime     default CURRENT_TIMESTAMP     not null comment '创建时间',
    update_time  datetime     default '0000-00-00 00:00:00' not null comment '修改时间',
    status       tinyint(1)   default 0                     not null comment '状态',
    resume_id    int          default 0                     not null comment '简历id',
    openid       varchar(64)  default ''                    not null comment 'openid',
    unionid      varchar(64)  default ''                    not null comment 'unionid',
    avatar       varchar(256) default ''                    not null comment '头像url',
    is_subscribe tinyint(1)   default 0                     not null comment '是否关注0否1是'
);

create index idx_openid
    on resume_wx_bind (openid);

create index idx_resume_id
    on resume_wx_bind (resume_id);

create index idx_unionid
    on resume_wx_bind (unionid);

alter table member_schedule modify column title varchar(512)  NOT NULL DEFAULT '' COMMENT '主题';
alter table member_message modify column content varchar(512)  NOT NULL DEFAULT '' COMMENT '消息内容';
