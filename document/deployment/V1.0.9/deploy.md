# ReadMe

### 关联产品原型版本
- [V1.0.9_求职者端选择控件优化](https://lanhuapp.com/web/#/item/project/product?pid=fead3b7d-5995-4474-86d2-a8a709fce835&versionId=649424ce-5b62-48d5-ac11-4641b76719cd&docId=282cfd8d-ff6d-45ed-9d9e-fb775cba9370&docType=axure&pageId=6123b899b8164a52b63ddff920ad481e&image_id=282cfd8d-ff6d-45ed-9d9e-fb775cba9370)

***

### 参与人员

- 丘群森
- 杜孙鹤

***

### 分支

|仓库|开发分支|提測分支|备注|
|:----:|:----:|:----:|:----:|
|new_gaoxiao_yii|feature/V1.0.9_求职者端选择控件优化|release/V1.0.9_求职者端选择控件优化 |-|
|new_gaoxiao_person_pc_vue|feature/V1.0.9|release/V1.0.9 |-|
|new_gaoxiao_h5|feature/V1.0.9|release/V1.0.9 |-|

***

### 上线部署步骤

`内容为空,填写"-"即可`

|步骤|是否执行|执行内容|
|:----|:----:|:----|
|提醒前端提前合并代码到master和添加权限节点|-|-|
|执行sql语句|是|见下方"执行sql语句"|
|更新后端代码|是|-|
|composer安装|-|-|
|更新配置|-|-|
|创建队列|-|-|
|执行脚本|-|-|
|删除redis缓存|-|-|
|重启队列|-|-|
|更新前端代码|是|-|
|添加定时任务|-|-|
|群内通知部署完毕|-|-|

#### 执行sql语句(按顺序执行)
* alter_data.sql
