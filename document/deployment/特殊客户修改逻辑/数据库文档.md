# 特殊需求功能数据库文档

## 1. 数据库设计概述

特殊需求功能使用两个核心数据表来存储配置信息，替代原有的硬编码逻辑。

### 1.1 设计原则
- **统一性**: 统一的配置存储和管理方式
- **灵活性**: 支持多种配置类型和平台
- **扩展性**: 便于后续功能扩展
- **性能**: 合理的索引设计保证查询性能

## 2. 数据表结构

### 2.1 特殊需求配置表 (special_need_config)

**表说明**: 存储各种特殊需求的配置信息，包括单位、公告、职位的特殊字段处理规则。

```sql
CREATE TABLE `special_need_config` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '配置名称',
  `type` varchar(20) NOT NULL DEFAULT '' COMMENT '类型：company,announcement,job',
  `target_id` int(11) NOT NULL DEFAULT 0 COMMENT '目标ID（单位ID/公告ID/职位ID）',
  `field_name` varchar(50) NOT NULL DEFAULT '' COMMENT '字段名称',
  `field_value` text COMMENT '字段值',
  `platform` varchar(20) NOT NULL DEFAULT 'ALL' COMMENT '适用平台：ALL,PC,H5,MINI',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `remark` text COMMENT '备注说明',
  `created_by` int(11) NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type_target` (`type`, `target_id`),
  KEY `idx_status_time` (`status`, `start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='特殊需求配置表';
```

**字段详细说明**:

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| id | int(11) unsigned | 主键ID | 1 |
| name | varchar(100) | 配置名称，便于管理识别 | "单位1214专业要求修改" |
| type | varchar(20) | 配置类型 | company/announcement/job |
| target_id | int(11) | 目标对象ID | 1214（单位ID） |
| field_name | varchar(50) | 要修改的字段名 | major/education/salary |
| field_value | text | 字段的新值或处理规则 | "中共党史党建学" |
| platform | varchar(20) | 适用平台 | ALL/PC/H5/MINI |
| status | tinyint(1) | 启用状态 | 0=禁用，1=启用 |
| start_time | datetime | 生效开始时间 | 2024-01-01 00:00:00 |
| end_time | datetime | 生效结束时间 | 2024-12-31 23:59:59 |
| remark | text | 备注说明 | "禅道944需求" |
| created_by | int(11) | 创建人ID | 1 |
| add_time | datetime | 创建时间 | 自动生成 |
| update_time | datetime | 更新时间 | 自动更新 |

### 2.2 投递限制配置表 (special_need_apply_limit)

**表说明**: 存储投递限制规则，包括次数限制和条件限制。

```sql
CREATE TABLE `special_need_apply_limit` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '限制名称',
  `company_id` int(11) NOT NULL DEFAULT 0 COMMENT '单位ID',
  `limit_type` varchar(20) NOT NULL DEFAULT 'count' COMMENT '限制类型：count=次数限制,condition=条件限制',
  `time_limit` int(11) NOT NULL DEFAULT 0 COMMENT '时间限制（天）',
  `count_limit` int(11) NOT NULL DEFAULT 1 COMMENT '次数限制',
  `condition_field` varchar(50) NOT NULL DEFAULT '' COMMENT '条件字段（如is_abroad）',
  `condition_value` varchar(100) NOT NULL DEFAULT '' COMMENT '条件值',
  `error_message` varchar(255) NOT NULL DEFAULT '' COMMENT '错误提示信息',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `remark` text COMMENT '备注说明',
  `created_by` int(11) NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `add_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_company_status` (`company_id`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='投递限制配置表';
```

**字段详细说明**:

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| id | int(11) unsigned | 主键ID | 1 |
| name | varchar(100) | 限制名称 | "海外经历限制" |
| company_id | int(11) | 单位ID | 1214 |
| limit_type | varchar(20) | 限制类型 | count/condition |
| time_limit | int(11) | 时间限制（天） | 30 |
| count_limit | int(11) | 次数限制 | 1 |
| condition_field | varchar(50) | 条件字段名 | is_abroad |
| condition_value | varchar(100) | 条件值 | "1" |
| error_message | varchar(255) | 错误提示信息 | "该职位要求有海外经历" |
| status | tinyint(1) | 启用状态 | 0=禁用，1=启用 |
| remark | text | 备注说明 | "特殊投递限制" |
| created_by | int(11) | 创建人ID | 1 |
| add_time | datetime | 创建时间 | 自动生成 |
| update_time | datetime | 更新时间 | 自动更新 |

## 3. 索引设计

### 3.1 special_need_config 表索引
- **主键索引**: `PRIMARY KEY (id)`
- **类型目标索引**: `KEY idx_type_target (type, target_id)` - 用于快速查找特定类型和目标的配置
- **状态时间索引**: `KEY idx_status_time (status, start_time, end_time)` - 用于查找有效期内的启用配置

### 3.2 special_need_apply_limit 表索引
- **主键索引**: `PRIMARY KEY (id)`
- **单位状态索引**: `KEY idx_company_status (company_id, status)` - 用于快速查找特定单位的启用限制

## 4. 数据迁移

### 4.1 硬编码数据迁移概述

特殊需求功能的数据迁移涉及将分散在多个Service类中的硬编码逻辑转换为数据库配置。主要迁移内容包括：

1. **BaseService配置迁移**: 禅道944等核心配置
2. **公告信息特殊处理**: AnnouncementInformationService中的硬编码
3. **职位信息特殊处理**: JobInformationService中的硬编码
4. **投递限制规则**: JobApplyService中的硬编码

### 4.2 主要硬编码来源

#### 4.2.1 BaseService核心配置
```php
// common/service/specialNeedService/BaseService.php
public $config = [
    '944' => [
        'announcementId' => 251855,
        'companyId'      => 1214,
        'jobRanking'     => [1278773, 1278774, 1278775],
        'jobAddMajor'    => '中共党史党建学',
    ],
];
```

#### 4.2.2 公告信息特殊处理
```php
// AnnouncementInformationService.php 中的硬编码示例
if ($announcementId == 206013) {
    $data['majorName'] = '药学,基础医学,临床医学,生物学';
}
if ($announcementId == 289744) {
    $data['majorName'] = '详见招聘简章';
    $data['applyTypeText'] = '详见正文';
}
```

#### 4.2.3 职位信息特殊处理
```php
// JobInformationService.php 中的硬编码示例
if ($announcementId == 225958) {
    $majorConfig = [
        1129053 => '材料加工工程，材料科学与工程...',
        1129054 => '机械制造及其自动化...',
    ];
}
```

#### 4.2.4 投递限制规则
```php
// JobApplyService.php 中的硬编码示例
if ($companyId == 89616) {
    // 15天内只能投递1次
}
if ($companyId == 60) {
    // 海外经历限制
}
```

### 4.3 完整迁移SQL脚本

**执行顺序**:
1. `simple_special_need_tables.sql` - 创建数据表
2. `data_migration.sql` - 迁移硬编码数据
3. `migration_verification.sql` - 验证迁移结果

**主要迁移内容统计**:
- 禅道944需求: 约15条配置记录
- 公告特殊处理: 约50条配置记录
- 职位特殊处理: 约80条配置记录
- 投递限制规则: 约6条限制记录

### 4.4 迁移脚本示例

#### 4.4.1 禅道944需求迁移
```sql
-- 单位级别配置
INSERT INTO special_need_config
(name, type, target_id, field_name, field_value, platform, status, remark, created_by)
VALUES
('禅道944-单位1214专业要求追加', 'company', 1214, 'major', ',中共党史党建学', 'ALL', 1, '禅道944需求：单位级别专业要求追加', 1);

-- 公告级别配置
INSERT INTO special_need_config
(name, type, target_id, field_name, field_value, platform, status, remark, created_by)
VALUES
('禅道944-公告251855专业要求追加', 'announcement', 251855, 'major', ',中共党史党建学', 'ALL', 1, '禅道944需求：公告级别专业要求追加', 1);

-- 职位级别配置
INSERT INTO special_need_config
(name, type, target_id, field_name, field_value, platform, status, remark, created_by)
VALUES
('禅道944-职位1278773专业要求', 'job', 1278773, 'major', ',中共党史党建学', 'ALL', 1, '禅道944需求：领军人才职位专业要求', 1);
```

#### 4.4.2 投递限制迁移
```sql
-- 次数限制
INSERT INTO special_need_apply_limit
(name, company_id, limit_type, time_limit, count_limit, error_message, status, remark, created_by)
VALUES
('中国科学院金属研究所投递限制', 89616, 'count', 15, 1, '您已超出该单位简历投递次数限制，无法再行投递', 1, '禅道775需求：15天内只能投递1次', 1);

-- 条件限制
INSERT INTO special_need_apply_limit
(name, company_id, limit_type, condition_field, condition_value, error_message, status, remark, created_by)
VALUES
('四川工商职业技术学院海外经历限制', 60, 'condition', 'is_abroad', '1', '对不起，您暂不符合招聘要求，建议尝试其他机会！', 1, '禅道1177需求：有海外经历的不能投递', 1);
```

### 4.2 配置参数迁移

**原有参数配置**:
```php
// 在 params.php 中
'specialNeed' => [
    'AnnouncementInformationProfessionalRanking' => 1,
    'AnnouncementInformationDetail' => 1,
    'AnnouncementInformationJobList' => 1,
    'JobApplyCheck' => 1,
    'JobInformationDetail' => 1,
]
```

**迁移策略**:
- 保留原有参数作为总开关
- 新增数据库配置作为细粒度控制
- 实现双重检查机制

## 5. 数据示例

### 5.1 配置数据示例

```sql
-- 单位级别配置示例
INSERT INTO special_need_config VALUES 
(1, '单位1214专业要求修改', 'company', 1214, 'major', ',中共党史党建学', 'ALL', 1, '2024-01-01 00:00:00', '2024-12-31 23:59:59', '禅道944需求', 1, NOW(), NOW());

-- 公告级别配置示例  
INSERT INTO special_need_config VALUES 
(2, '公告251855学历要求修改', 'announcement', 251855, 'education', '博士', 'PC', 1, NULL, NULL, '特殊学历要求', 1, NOW(), NOW());

-- 职位级别配置示例
INSERT INTO special_need_config VALUES 
(3, '职位1278773薪资显示', 'job', 1278773, 'salary', '面议', 'H5', 1, '2024-06-01 00:00:00', '2024-08-31 23:59:59', '临时薪资调整', 1, NOW(), NOW());
```

### 5.2 投递限制数据示例

```sql
-- 次数限制示例
INSERT INTO special_need_apply_limit VALUES 
(1, '单位1214投递次数限制', 1214, 'count', 30, 1, '', '', '30天内只能投递1次', 1, '防止重复投递', 1, NOW(), NOW());

-- 条件限制示例
INSERT INTO special_need_apply_limit VALUES 
(2, '单位1214海外经历要求', 1214, 'condition', 0, 0, 'is_abroad', '1', '该职位要求有海外经历', 1, '特殊条件限制', 1, NOW(), NOW());
```

## 6. 查询优化建议

### 6.1 常用查询模式
```sql
-- 查询特定单位的所有配置
SELECT * FROM special_need_config 
WHERE type = 'company' AND target_id = 1214 AND status = 1
AND (start_time IS NULL OR start_time <= NOW())
AND (end_time IS NULL OR end_time >= NOW());

-- 查询特定单位的投递限制
SELECT * FROM special_need_apply_limit 
WHERE company_id = 1214 AND status = 1;
```

### 6.2 性能监控
- 定期检查慢查询日志
- 监控索引使用情况
- 根据实际查询模式调整索引

## 7. 数据维护

### 7.1 定期清理
- 清理过期的配置记录
- 清理无效的限制规则
- 归档历史数据

### 7.2 数据备份
- 定期备份配置数据
- 重要变更前进行数据快照
- 建立数据恢复机制

## 8. 部署检查清单

### 8.1 数据库部署前检查
- [ ] 确认数据库连接正常
- [ ] 备份现有数据
- [ ] 检查表名是否冲突
- [ ] 确认字符集设置正确

### 8.2 数据库部署步骤
1. 执行建表SQL脚本
2. 创建必要的索引
3. 插入初始配置数据
4. 验证表结构正确性

### 8.3 数据迁移验证
- [ ] 验证硬编码数据已正确迁移
- [ ] 检查配置数据完整性
- [ ] 测试查询性能
- [ ] 验证业务逻辑正确性
