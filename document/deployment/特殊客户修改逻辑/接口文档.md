# 特殊需求功能接口文档

## 1. 接口概述

特殊需求功能提供了完整的配置管理和投递限制管理接口，支持CRUD操作和业务查询。

### 1.1 基础信息
- **基础路径**: `/admin/special-need/`
- **认证方式**: 基于Session的用户认证
- **数据格式**: JSON
- **字符编码**: UTF-8
- **参数命名**: 使用小驼峰命名法（camelCase）
- **响应格式**: 统一使用success()/fail()方法返回

### 1.2 通用响应格式

#### 成功响应
```json
{
    "msg": "操作成功",
    "result": 1,
    "data": {[BaseSpecialNeedConfig.php](../../../common/base/models/BaseSpecialNeedConfig.php)
        // 具体数据，字段名使用小驼峰命名
    }
}
```

#### 失败响应
```json
{
    "msg": "错误信息",
    "result": 0,
    "data": null
}
```

#### 特殊状态码响应
```json
{
    "code": 403,
    "msg": "未登录用户",
    "result": 0,
    "data": null
}
```

## 2. 特殊需求配置接口

### 2.1 配置列表查询
**接口地址**: `GET /admin/special-need/config-index`

**请求参数**:
| 参数名 | 类型 | 必需 | 示例值 | 说明 |
|--------|------|------|--------|------|
| id | Number | 否 | 1 | 配置ID |
| name | String | 否 | 配置名称 | 配置名称（模糊查询） |
| type | String | 否 | company | 配置类型：company/announcement/job |
| targetId | Number | 否 | 1214 | 目标ID |
| fieldName | String | 否 | major | 字段名称（模糊查询） |
| status | Number | 否 | 1 | 状态：0=禁用，1=启用 |
| createdBy | Number | 否 | 1 | 创建人ID |
| page | Number | 否 | 1 | 页码，默认1 |
| pageSize | Number | 否 | 20 | 每页数量，默认20 |

**响应示例**:
```json
{
    "msg": "",
    "result": 1,
    "data": {
        "list": [
            {
                "id": 1,
                "name": "单位1214专业要求修改",
                "type": "company",
                "targetId": 1214,
                "fieldName": "major",
                "fieldValue": "中共党史党建学",
                "platform": "ALL",
                "status": 1,
                "startTime": "2024-01-01 00:00:00",
                "endTime": "2024-12-31 23:59:59",
                "remark": "禅道944需求",
                "createdBy": 1,
                "addTime": "2024-01-01 10:00:00",
                "updateTime": "2024-01-01 10:00:00"
            }
        ],
        "pages": {
            "pageSize": 20,
            "page": 1,
            "total": 1
        }
    }
}
```

### 2.2 配置详情查询
**接口地址**: `GET /admin/special-need/config-view`

**请求参数**:
| 参数名 | 类型 | 必需 | 示例值 | 说明 |
|--------|------|------|--------|------|
| id | Number | 是 | 1 | 配置ID |

**响应示例**:
```json
{
    "msg": "",
    "result": 1,
    "data": {
        "id": 1,
        "name": "单位1214专业要求修改",
        "type": "company",
        "targetId": 1214,
        "fieldName": "major",
        "fieldValue": "中共党史党建学",
        "platform": "ALL",
        "status": 1,
        "startTime": "2024-01-01 00:00:00",
        "endTime": "2024-12-31 23:59:59",
        "remark": "禅道944需求",
        "createdBy": 1,
        "addTime": "2024-01-01 10:00:00",
        "updateTime": "2024-01-01 10:00:00"
    }
}
```

### 2.3 创建配置
**接口地址**: `POST /admin/special-need/config-create`

**请求参数**:
| 参数名 | 类型 | 必需 | 示例值 | 说明 |
|--------|------|------|--------|------|
| name | String | 是 | 单位1214专业要求修改 | 配置名称 |
| type | String | 是 | company | 配置类型：company/announcement/job |
| targetId | Number | 是 | 1214 | 目标ID |
| fieldName | String | 是 | major | 字段名称：education/major/recruitAmount/applyType/title/jobCategory/jobBasicsInfo |
| fieldValue | String | 否 | 中共党史党建学 | 字段值 |
| platform | String | 否 | ALL | 适用平台：ALL/PC/H5/MINI，默认ALL |
| status | Number | 否 | 1 | 状态：0=禁用，1=启用，默认1 |
| startTime | String | 否 | 2024-01-01 00:00:00 | 生效开始时间 |
| endTime | String | 否 | 2024-12-31 23:59:59 | 生效结束时间 |
| remark | String | 否 | 禅道944需求 | 备注说明 |

**响应示例**:
```json
{
    "msg": "操作成功",
    "result": 1,
    "data": {
        "id": 1
    }
}
```

### 2.4 更新配置
**接口地址**: `POST /admin/special-need/config-update`

**请求参数**:
| 参数名 | 类型 | 必需 | 示例值 | 说明 |
|--------|------|------|--------|------|
| id | Number | 是 | 1 | 配置ID |
| name | String | 否 | 单位1214专业要求修改 | 配置名称 |
| type | String | 否 | company | 配置类型：company/announcement/job |
| targetId | Number | 否 | 1214 | 目标ID |
| fieldName | String | 否 | major | 字段名称：education/major/recruitAmount/applyType/title/jobCategory/jobBasicsInfo |
| fieldValue | String | 否 | 中共党史党建学 | 字段值 |
| platform | String | 否 | ALL | 适用平台：ALL/PC/H5/MINI |
| status | Number | 否 | 1 | 状态：0=禁用，1=启用 |
| startTime | String | 否 | 2024-01-01 00:00:00 | 生效开始时间 |
| endTime | String | 否 | 2024-12-31 23:59:59 | 生效结束时间 |
| remark | String | 否 | 禅道944需求 | 备注说明 |

**响应示例**:
```json
{
    "msg": "操作成功",
    "result": 1,
    "data": null
}
```

### 2.5 删除配置
**接口地址**: `POST /admin/special-need/config-delete`

**请求参数**:
| 参数名 | 类型 | 必需 | 示例值 | 说明 |
|--------|------|------|--------|------|
| id | Number | 是 | 1 | 配置ID |

**响应示例**:
```json
{
    "msg": "操作成功",
    "result": 1,
    "data": null
}
```

### 2.6 获取字段选项
**接口地址**: `GET /admin/special-need/get-field-options`

**请求参数**:
| 参数名 | 类型 | 必需 | 示例值 | 说明 |
|--------|------|------|--------|------|
| type | String | 是 | company | 配置类型：company/announcement/job |

**响应示例**:
```json
{
    "msg": "",
    "result": 1,
    "data": {
        "education": "学历要求",
        "major": "专业要求",
        "recruitAmount": "招聘人数",
        "applyType": "报名方式",
        "title": "职称要求",
        "jobCategory": "职位类型"
    }
}
```

## 3. 投递限制接口

### 3.1 投递限制列表查询
**接口地址**: `GET /admin/special-need/limit-index`

**请求参数**:
| 参数名 | 类型 | 必需 | 示例值 | 说明 |
|--------|------|------|--------|------|
| id | Number | 否 | 1 | 限制ID |
| name | String | 否 | 限制名称 | 限制名称（模糊查询） |
| companyId | Number | 否 | 1214 | 单位ID |
| limitType | String | 否 | count | 限制类型：count/condition |
| timeLimit | Number | 否 | 30 | 时间限制（天） |
| countLimit | Number | 否 | 1 | 次数限制 |
| status | Number | 否 | 1 | 状态：0=禁用，1=启用 |
| createdBy | Number | 否 | 1 | 创建人ID |
| page | Number | 否 | 1 | 页码，默认1 |
| pageSize | Number | 否 | 20 | 每页数量，默认20 |

**响应示例**:
```json
{
    "msg": "",
    "result": 1,
    "data": {
        "list": [
            {
                "id": 1,
                "name": "海外经历限制",
                "companyId": 1214,
                "limitType": "condition",
                "timeLimit": 0,
                "countLimit": 0,
                "conditionField": "isAbroad",
                "conditionValue": "1",
                "errorMessage": "该职位要求有海外经历",
                "status": 1,
                "remark": "特殊投递限制",
                "createdBy": 1,
                "addTime": "2024-01-01 10:00:00",
                "updateTime": "2024-01-01 10:00:00"
            }
        ],
        "pages": {
            "pageSize": 20,
            "page": 1,
            "total": 1
        }
    }
}
```

### 3.2 创建投递限制
**接口地址**: `POST /admin/special-need/limit-create`

**请求参数**:
| 参数名 | 类型 | 必需 | 示例值 | 说明 |
|--------|------|------|--------|------|
| name | String | 是 | 海外经历限制 | 限制名称 |
| companyId | Number | 是 | 1214 | 单位ID |
| limitType | String | 是 | condition | 限制类型：count/condition |
| timeLimit | Number | 否 | 30 | 时间限制（天），count类型时使用 |
| countLimit | Number | 否 | 1 | 次数限制，count类型时使用 |
| conditionField | String | 否 | isAbroad | 条件字段，condition类型时使用 |
| conditionValue | String | 否 | 1 | 条件值，condition类型时使用 |
| errorMessage | String | 是 | 该职位要求有海外经历 | 错误提示信息 |
| status | Number | 否 | 1 | 状态：0=禁用，1=启用，默认1 |
| remark | String | 否 | 特殊投递限制 | 备注说明 |

**响应示例**:
```json
{
    "msg": "操作成功",
    "result": 1,
    "data": {
        "id": 1
    }
}
```

### 3.3 更新投递限制
**接口地址**: `POST /admin/special-need/limit-update`

**请求参数**:
| 参数名 | 类型 | 必需 | 示例值 | 说明 |
|--------|------|------|--------|------|
| id | Number | 是 | 1 | 限制ID |
| name | String | 否 | 海外经历限制 | 限制名称 |
| companyId | Number | 否 | 1214 | 单位ID |
| limitType | String | 否 | condition | 限制类型：count/condition |
| timeLimit | Number | 否 | 30 | 时间限制（天） |
| countLimit | Number | 否 | 1 | 次数限制 |
| conditionField | String | 否 | isAbroad | 条件字段 |
| conditionValue | String | 否 | 1 | 条件值 |
| errorMessage | String | 否 | 该职位要求有海外经历 | 错误提示信息 |
| status | Number | 否 | 1 | 状态：0=禁用，1=启用 |
| remark | String | 否 | 特殊投递限制 | 备注说明 |

**响应示例**:
```json
{
    "msg": "操作成功",
    "result": 1,
    "data": null
}
```

### 3.4 删除投递限制
**接口地址**: `POST /admin/special-need/limit-delete`

**请求参数**:
| 参数名 | 类型 | 必需 | 示例值 | 说明 |
|--------|------|------|--------|------|
| id | Number | 是 | 1 | 限制ID |

**响应示例**:
```json
{
    "msg": "操作成功",
    "result": 1,
    "data": null
}
```

### 3.5 投递限制详情查询
**接口地址**: `GET /admin/special-need/limit-view`

**请求参数**:
| 参数名 | 类型 | 必需 | 示例值 | 说明 |
|--------|------|------|--------|------|
| id | Number | 是 | 1 | 限制ID |

**响应示例**:
```json
{
    "msg": "",
    "result": 1,
    "data": {
        "id": 1,
        "name": "海外经历限制",
        "companyId": 1214,
        "limitType": "condition",
        "timeLimit": 0,
        "countLimit": 0,
        "conditionField": "isAbroad",
        "conditionValue": "1",
        "errorMessage": "该职位要求有海外经历",
        "status": 1,
        "remark": "特殊投递限制",
        "createdBy": 1,
        "addTime": "2024-01-01 10:00:00",
        "updateTime": "2024-01-01 10:00:00"
    }
}
```

## 4. 响应状态说明

### 4.1 通用状态
| 状态 | 说明 |
|------|------|
| result: 1 | 成功 |
| result: 0 | 失败 |

### 4.2 特殊状态码
| 状态码 | 说明 | 使用场景 |
|--------|------|----------|
| code: 403 | 未授权 | 用户未登录或权限不足 |
| code: 500 | 服务器错误 | 系统内部错误 |

## 5. 字段说明

### 5.1 配置类型 (type)
- `company`: 单位信息配置
- `announcement`: 公告信息配置
- `job`: 职位信息配置

### 5.2 字段名称 (fieldName)
- `education`: 学历要求
- `major`: 专业要求
- `recruitAmount`: 招聘人数
- `applyType`: 报名方式
- `title`: 职称要求
- `jobCategory`: 职位类型
- `jobBasicsInfo`: 职位基础信息（小程序专用，格式：招聘人数|学历|地区）

### 5.3 平台类型 (platform)
- `ALL`: 全平台
- `PC`: PC端
- `H5`: H5端
- `MINI`: 小程序端

### 5.4 限制类型 (limitType)
- `count`: 次数限制
- `condition`: 条件限制

### 5.5 状态 (status)
- `0`: 禁用
- `1`: 启用

## 6. Apifox导入参数格式

### 6.1 配置列表查询参数
```
id,Number,否,1,-,配置ID
name,String,否,配置名称,-,配置名称（模糊查询）
type,String,否,company,company/announcement/job,配置类型
targetId,Number,否,1214,-,目标ID
fieldName,String,否,major,-,字段名称（模糊查询）
status,Number,否,1,0=禁用/1=启用,状态
createdBy,Number,否,1,-,创建人ID
page,Number,否,1,-,页码，默认1
pageSize,Number,否,20,-,每页数量，默认20
```

### 6.2 创建配置参数
```
name,String,是,单位1214专业要求修改,-,配置名称
type,String,是,company,company/announcement/job,配置类型
targetId,Number,是,1214,-,目标ID
fieldName,String,是,major,education/major/recruitAmount/applyType/title/jobCategory/jobBasicsInfo,字段名称
fieldValue,String,否,中共党史党建学,-,字段值
platform,String,否,ALL,ALL/PC/H5/MINI,适用平台，默认ALL
status,Number,否,1,0=禁用/1=启用,状态，默认1
startTime,String,否,2024-01-01 00:00:00,-,生效开始时间
endTime,String,否,2024-12-31 23:59:59,-,生效结束时间
remark,String,否,禅道944需求,-,备注说明
```

### 6.3 创建投递限制参数
```
name,String,是,海外经历限制,-,限制名称
companyId,Number,是,1214,-,单位ID
limitType,String,是,condition,count/condition,限制类型
timeLimit,Number,否,30,-,时间限制（天），count类型时使用
countLimit,Number,否,1,-,次数限制，count类型时使用
conditionField,String,否,isAbroad,-,条件字段，condition类型时使用
conditionValue,String,否,1,-,条件值，condition类型时使用
errorMessage,String,是,该职位要求有海外经历,-,错误提示信息
status,Number,否,1,0=禁用/1=启用,状态，默认1
remark,String,否,特殊投递限制,-,备注说明
```

## 8. 小程序jobBasicsInfo字段特殊处理

### 8.1 字段说明
小程序的职位详情中，`jobBasicsInfo` 字段包含了多个信息的组合，格式为：
```
招聘人数 | 学历要求 | 工作地点
```

**示例**：
- `"招2人 | 大专 | 成都"`
- `"招聘若干 | 本科 | 北京"`
- `"招5人 | 硕士研究生 | 上海 | 全职"`

### 8.2 智能更新机制

系统支持对 `jobBasicsInfo` 字段进行智能更新，可以：

1. **部分更新**：只更新其中的招聘人数或学历要求部分
2. **完整替换**：直接替换整个 `jobBasicsInfo` 内容

#### 部分更新示例

**更新招聘人数**：
```json
{
    "fieldName": "recruitAmount",
    "fieldValue": "10",
    "platform": "MINI"
}
```
结果：`"招2人 | 大专 | 成都"` → `"招10人 | 大专 | 成都"`

**更新学历要求**：
```json
{
    "fieldName": "education",
    "fieldValue": "本科",
    "platform": "MINI"
}
```
结果：`"招2人 | 大专 | 成都"` → `"招2人 | 本科 | 成都"`

**同时更新多个字段**：
```json
[
    {
        "fieldName": "recruitAmount",
        "fieldValue": "5"
    },
    {
        "fieldName": "education",
        "fieldValue": "硕士研究生"
    }
]
```
结果：`"招2人 | 大专 | 成都"` → `"招5人 | 硕士研究生 | 成都"`

#### 完整替换示例

```json
{
    "fieldName": "jobBasicsInfo",
    "fieldValue": "招聘若干 | 博士研究生 | 北京 | 全职",
    "platform": "MINI"
}
```
结果：直接替换为新的完整内容

### 8.3 平台兼容性

- **小程序平台（MINI）**：启用 `jobBasicsInfo` 智能更新
- **其他平台（PC/H5）**：不处理 `jobBasicsInfo` 字段，保持原值

### 8.4 字段优先级

当同时配置了单独字段和 `jobBasicsInfo` 字段时：

1. `jobBasicsInfo` 配置优先级最高，会直接替换
2. 单独字段配置会智能更新 `jobBasicsInfo` 中的对应部分
3. 其他字段（如 `education`、`recruitAmount`）同时也会被更新

### 8.5 使用建议

1. **推荐做法**：使用单独字段配置（`education`、`recruitAmount`），系统会自动更新 `jobBasicsInfo`
2. **特殊情况**：需要完全自定义格式时，直接配置 `jobBasicsInfo` 字段
3. **测试验证**：配置后在小程序端验证显示效果

## 7. 缓存管理接口

### 7.1 清除缓存
**接口地址**: `POST /admin/special-need/clear-cache`

**请求参数**:
| 参数名 | 类型 | 必需 | 示例值 | 说明 |
|--------|------|------|--------|------|
| type | String | 否 | job | 配置类型：company/announcement/job，不传则清除所有缓存 |
| targetId | Number | 否 | 1214 | 目标ID，配合type使用 |
| platform | String | 否 | PC | 平台类型：ALL/PC/H5/MINI，不传则清除所有平台 |

**响应示例**:
```json
{
    "msg": "缓存清除成功",
    "result": 1,
    "data": null
}
```

### 7.2 预热缓存
**接口地址**: `POST /admin/special-need/warmup-cache`

**请求参数**:
| 参数名 | 类型 | 必需 | 示例值 | 说明 |
|--------|------|------|--------|------|
| type | String | 是 | job | 配置类型：company/announcement/job |
| targetIds | Array | 否 | [1214,1215] | 目标ID数组，不传则预热所有 |
| platform | String | 否 | PC | 平台类型：ALL/PC/H5/MINI，默认PC |

**响应示例**:
```json
{
    "msg": "缓存预热成功",
    "result": 1,
    "data": null
}
```

### 7.3 获取缓存统计
**接口地址**: `GET /admin/special-need/cache-stats`

**响应示例**:
```json
{
    "msg": "",
    "result": 1,
    "data": {
        "totalKeys": 156,
        "cacheSize": 245760,
        "byType": {
            "job": 89,
            "announcement": 45,
            "company": 22
        },
        "byPlatform": {
            "PC": 78,
            "H5": 45,
            "MINI": 23,
            "ALL": 10
        }
    }
}
```
