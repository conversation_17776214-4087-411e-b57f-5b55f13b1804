# ReadMe

### 关联产品原型版本
-[V1.1.1(2.0)](https://lanhuapp.com/web/#/item/project/product?pid=2f21bc32-c3b7-4ab4-b965-e78c08d50c8a&image_id=fa6c4799-1ac0-480f-a9d8-a78a9d85e481&versionId=e7f72b73-4743-42b7-a684-828e5c64350a&docId=fa6c4799-1ac0-480f-a9d8-a78a9d85e481&docType=axure&pageId=df14740c2194424789d63da994fcc09b)

***

### 参与人员


- 丘群森
- 杜孙鹤


***

### 分支

|仓库|开发分支|提測分支|备注|
|:----:|:----:|:----:|:----:|
| new_gaoxiao_yii | feature/V1.1.1_投递通知邮件模版 | release/V1.1.1_投递通知邮件模版 |-|
| new_gaoxiao_company_pc_vue | feature/V1.1.1_投递通知邮件模版 | release/V1.1.1_投递通知邮件模版 |-|

***

### 上线部署步骤

`内容为空,填写"-"即可`

|步骤| 是否执行 |执行内容|
|:----|:----:|:----|
|提醒前端提前合并代码到master和添加权限节点|  -   |-|
|执行sql语句|  -   |见下方"执行sql语句"|
|更新后端代码|  是   |-|
|composer安装|  -   |-|
|更新配置|  -   |-|
|创建队列|  -   |-|
|执行脚本|  -   |-|
|删除redis缓存|  -   |-|
|重启队列|  -   |-|
|更新前端代码|  是   |-|
|添加定时任务|  -   |-|
|群内通知部署完毕|  -   |-|



