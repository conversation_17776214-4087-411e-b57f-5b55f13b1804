ALTER TABLE `hw_activity`
    ADD COLUMN `sub_type` tinyint NOT NULL DEFAULT 0 COMMENT '调用活动类型:5=学者论坛,6=学子归国行,7=创业大赛,8=人才大会,9=其他活动' AFTER `type`;

ALTER TABLE `hw_activity`
    ADD COLUMN `to_hold_type` tinyint NOT NULL DEFAULT 0 COMMENT '举办方式；0:未知；1：线上；2:线下；3:线上+线下' AFTER `tags`;

ALTER TABLE `hw_activity`
    ADD COLUMN `custom_feature_tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '自定义特色标签，多个用,隔开' AFTER `to_hold_type`;

ALTER TABLE `hw_activity`
    ADD COLUMN `longitude` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '经度' AFTER `custom_feature_tag`;

ALTER TABLE `hw_activity`
    ADD COLUMN `latitude` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '纬度' AFTER `longitude`;

ALTER TABLE `hw_activity`
    ADD COLUMN `activity_organization` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动组织' AFTER `latitude`;

ALTER TABLE `hw_activity`
    ADD COLUMN `activity_detail` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动详情' AFTER `activity_organization`;

ALTER TABLE `hw_activity`
    ADD COLUMN `participation_method` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '参会方式' AFTER `activity_detail`;

ALTER TABLE `hw_activity`
    ADD COLUMN `activity_highlights` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动亮点' AFTER `participation_method`;

ALTER TABLE `hw_activity`
    ADD COLUMN `activity_benefits` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '参会福利' AFTER `activity_highlights`;

ALTER TABLE `hw_activity`
    ADD COLUMN `activity_benefits_content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '福利详情' AFTER `activity_benefits`;

ALTER TABLE `hw_activity`
    ADD COLUMN `attendance_notes` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '参会须知' AFTER `activity_benefits_content`;

ALTER TABLE `hw_activity`
    ADD COLUMN `image_pc_banner_id` int NOT NULL DEFAULT 0 COMMENT 'PC banner图' AFTER `attendance_notes`;

ALTER TABLE `hw_activity`
    ADD COLUMN `image_service_code_id` int NOT NULL DEFAULT 0 COMMENT '客服二维码' AFTER `image_pc_banner_id`;

ALTER TABLE `hw_activity`
    ADD COLUMN `image_mini_master_id` int NOT NULL DEFAULT 0 COMMENT 'MINI主图' AFTER `image_service_code_id`;

ALTER TABLE `hw_activity`
    ADD COLUMN `image_mini_banner_id` int NOT NULL DEFAULT 0 COMMENT 'MINI banner图' AFTER `image_mini_master_id`;

ALTER TABLE `hw_activity`
    ADD COLUMN `image_notice_id` int NOT NULL DEFAULT 0 COMMENT '提示图' AFTER `image_mini_banner_id`;

ALTER TABLE `hw_activity`
    ADD COLUMN `apply_link_person_type` tinyint NOT NULL DEFAULT 1 COMMENT '人才报名链接：1=表单链接；2=第三方链接（点击跳转）；3=表单选项' AFTER `image_notice_id`;

ALTER TABLE `hw_activity`
    ADD COLUMN `apply_link_person_form_id` int NOT NULL DEFAULT 0 COMMENT '人才报名链接选择表单类型时；填写表单id，activity_form' AFTER `apply_link_person_type`;

ALTER TABLE `hw_activity`
    ADD COLUMN `apply_link_person_form_option_id` int NOT NULL DEFAULT 0 COMMENT '人才报名链接选择表单类型时；填写表单选项id，activity_form_intention_option' AFTER `apply_link_person_form_id`;

ALTER TABLE `hw_activity`
    ADD COLUMN `apply_link_person` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '人才报名链接' AFTER `apply_link_person_form_option_id`;

ALTER TABLE `hw_activity`
    ADD COLUMN `apply_link_company` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '单位报名链接' AFTER `apply_link_person`;

ALTER TABLE `hw_activity`
    ADD COLUMN `apply_company_time` date NOT NULL DEFAULT '0000-00-00' COMMENT '单位报名截止时间' AFTER `apply_link_company`;

ALTER TABLE `hw_activity`
    ADD COLUMN `template_id` tinyint NOT NULL DEFAULT 1 COMMENT '模板ID：1=默认；2=通用模板' AFTER `apply_company_time`;

ALTER TABLE `hw_activity`
    ADD COLUMN `activity_link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '页面链接' AFTER `template_id`;

ALTER TABLE `hw_activity`
    ADD COLUMN `wonderful_review` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '精彩回顾' AFTER `activity_link`;

ALTER TABLE `hw_activity`
    ADD COLUMN `participation_company_amount` int NOT NULL DEFAULT 0 COMMENT '参加招聘会单位数量（更新时一同更新）' AFTER `wonderful_review`;

ALTER TABLE `hw_activity`
    ADD COLUMN `click` int NOT NULL DEFAULT 0 COMMENT '浏览量' AFTER `participation_company_amount`;

ALTER TABLE `hw_activity`
    ADD COLUMN `activity_number` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动需求人数' AFTER `click`;

ALTER TABLE `hw_activity`
    MODIFY COLUMN `series_type` tinyint NOT NULL DEFAULT 0 COMMENT '活动系列:1=归国活动,2=出海引才,3=更多活动（出海）,4=高才博士后,5=国内线下招聘会,6=线上引才活动' AFTER `name`;

ALTER TABLE `hw_activity`
    MODIFY COLUMN `type` tinyint NOT NULL DEFAULT 0 COMMENT '活动类型:1=海外专场,2=组团招聘,3=海外活动,4=其他活动,5=学者论坛,6=学子归国行,7=创业大赛,8=人才大会,9=其他活动,10=第三方活动,11=博后活动,12=全国巡回现场招聘会,13=RPO项目人才交流会,14=组团&特色专场招聘会,15=RPO线上面试会,16=英才职通车（全球引才直播交流会）' AFTER `series_type`;

ALTER TABLE `hw_activity`
    MODIFY COLUMN `activity_child_status` tinyint NOT NULL DEFAULT 0 COMMENT '活动子状态-1=报名中;1=待举办;2=即将开始;3=正在进行;4=结束' AFTER `is_outside_url`;



CREATE TABLE `hw_activity_announcement`
(
    `id`              int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '专场ID',
    `add_time`        datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `activity_id`     int          NOT NULL DEFAULT 0 COMMENT '活动ID',
    `company_id`      int          NOT NULL DEFAULT 0 COMMENT '单位ID',
    `announcement_id` int          NOT NULL DEFAULT 0 COMMENT '公告ID',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_activity_id` (`activity_id` ASC) USING BTREE,
    INDEX `idx_company_id` (`company_id` ASC) USING BTREE,
    INDEX `idx_announcement_id` (`announcement_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '活动关联公告';

CREATE TABLE `hw_activity_click_log`
(
    `id`           int                                                            NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `add_time`     datetime                                                       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `member_id`    int                                                            NOT NULL DEFAULT 0 COMMENT '点击的会员id',
    `activity_id`  int                                                            NOT NULL DEFAULT 0 COMMENT '单位的id',
    `source`       tinyint(1)                                                     NOT NULL DEFAULT 1 COMMENT '1:pc,2:h5;3mini',
    `useragent`    varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '请求表头信息',
    `user_cookies` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL DEFAULT '' COMMENT '用户的cookie',
    `ip`           int UNSIGNED                                                   NOT NULL DEFAULT 0 COMMENT '操作ip',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_add_time` (`add_time` ASC) USING BTREE,
    INDEX `idx_member_id` (`member_id` ASC) USING BTREE,
    INDEX `idx_activity_id` (`activity_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '活动页浏览日志';

CREATE TABLE `hw_activity_company`
(
    `id`          int      NOT NULL AUTO_INCREMENT COMMENT '专场ID',
    `add_time`    datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `activity_id` int      NOT NULL DEFAULT 0 COMMENT '活动ID',
    `company_id`  int      NOT NULL DEFAULT 0 COMMENT '单位ID',
    `is_top`      tinyint  NOT NULL DEFAULT 2 COMMENT '是否置顶：1=是；2=否',
    `sort`        int      NOT NULL DEFAULT 0 COMMENT '排序',
    `sort_point`  int      NOT NULL DEFAULT 0 COMMENT '计算单位参会单位分数，用于活动参会单位总排序（定时任务更新）',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_activity_id` (`activity_id` ASC) USING BTREE,
    INDEX `idx_company_id` (`company_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '活动关联单位';

CREATE TABLE `hw_activity_company_hot`
(
    `id`          int      NOT NULL AUTO_INCREMENT COMMENT '专场ID',
    `add_time`    datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `activity_id` int      NOT NULL DEFAULT 0 COMMENT '活动ID',
    `company_id`  int      NOT NULL DEFAULT 0 COMMENT '单位ID',
    `sort`        int      NOT NULL DEFAULT 0 COMMENT '排序',
    `link_type`   tinyint  NOT NULL DEFAULT 1 COMMENT '落地链接类型:1=公告详情；2=单位主页',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_activity_id` (`activity_id` ASC) USING BTREE,
    INDEX `idx_company_id` (`company_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '活动热门单位';

CREATE TABLE `hw_activity_feature_tag_relation`
(
    `id`             int      NOT NULL AUTO_INCREMENT,
    `add_time`       datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `activity_id`    int      NOT NULL DEFAULT 0 COMMENT '关联的活动id',
    `feature_tag_id` int      NOT NULL COMMENT '关联的特色标签id:1=博士专场；2=出站博士后专场；3=硕博综合场；4=海外优青；5=校园场；6=城市综合场；7=海外名校行；8=政府引才专场',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_activity_id` (`activity_id` ASC) USING BTREE,
    INDEX `idx_feature_tag_id` (`feature_tag_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '活动系统特色标签';

ALTER TABLE `hw_activity_session`
    ADD COLUMN `is_top` tinyint NOT NULL DEFAULT 2 COMMENT '是否置顶；1:置顶；2:不置顶' AFTER `sort`;

ALTER TABLE `hw_activity_session`
    ADD INDEX `idx_activity_id` (`activity_id` ASC) USING BTREE;


CREATE TABLE `new_gaoxiaojob`.`hw_special_activity`
(
    `id`                                 int                                                           NOT NULL AUTO_INCREMENT COMMENT '专场ID',
    `add_time`                           datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                        datetime                                                      NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `name`                               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '专场名称',
    `to_hold_type`                       tinyint                                                       NOT NULL DEFAULT 1 COMMENT '举办方式：1=线上；2=线下；3=线上+线下',
    `custom_time`                        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '自定义举办时间',
    `start_date`                         date                                                          NULL     DEFAULT NULL COMMENT '举办开始日期',
    `start_time`                         char(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '' COMMENT '举办开始时间',
    `event_organization`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动组织',
    `end_date`                           date                                                          NULL     DEFAULT NULL COMMENT '举办结束日期',
    `end_time`                           char(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL DEFAULT '' COMMENT '举办结束时间',
    `custom_address`                     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '自定义举办地点',
    `detail_address`                     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详细地址',
    `activity_detail`                    mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL COMMENT '活动详情',
    `participation_method`               mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL COMMENT '参会方式',
    `retrospection`                      mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL COMMENT '往届回顾',
    `participation_benefit`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '参会福利',
    `participation_benefit_detail`       mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NULL COMMENT '参会福利详情',
    `image_service_code_id`              int                                                           NOT NULL DEFAULT 0 COMMENT '客服二维码',
    `image_pc_banner_id`                 int                                                           NOT NULL DEFAULT 0 COMMENT 'PC-banner图',
    `image_mini_banner_id`               int                                                           NOT NULL DEFAULT 0 COMMENT '小程序-banner图',
    `apply_link_person_type`             tinyint                                                       NOT NULL DEFAULT 1 COMMENT '人才报名链接：1=表单报名链接；2:其他链接；3:报名表单选项',
    `apply_link_person_form_id`          int                                                           NOT NULL DEFAULT 0 COMMENT '人才报名链接-表单id',
    `apply_link_person_form_option_id`   int                                                           NOT NULL DEFAULT 0 COMMENT '人才报名链接-表单选项id',
    `apply_link_person`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '第三方人才报名链接',
    `apply_link_company`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '单位报名链接',
    `apply_company_time`                 date                                                          NULL     DEFAULT NULL COMMENT '单位报名截止时间',
    `template_id`                        tinyint                                                       NOT NULL DEFAULT 1 COMMENT '模板ID；1:默认；2:通用',
    `special_link`                       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '专场页面链接',
    `status`                             tinyint                                                       NOT NULL DEFAULT 1 COMMENT '专场状态；1：待举办;2：即将开始；3:进行中；4:已结束；',
    `real_participation_activity_amount` smallint                                                      NOT NULL DEFAULT 0 COMMENT '关联活动数量（编辑新增时一同更新）',
    `tag_ids`                            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '特色标签，多个使用,隔开：1=博士专场；2=出站博士后专场；3=硕博综合场；4=海外优青；5=校园场；6=城市综合场；7=海外名校行；8=政府引才专场\n',
    `custom_tag`                         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '自定义特色标签中文，多个使用,隔开',
    `type`                               tinyint                                                       NOT NULL DEFAULT 0 COMMENT '活动类型',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '专场表';


CREATE TABLE `hw_special_activity_relation`
(
    `id`             int UNSIGNED                                                  NOT NULL AUTO_INCREMENT,
    `special_id`     int                                                           NOT NULL DEFAULT 0 COMMENT '专场ID',
    `activity_id`    int                                                           NOT NULL DEFAULT 0 COMMENT '活动ID',
    `sort`           int                                                           NOT NULL DEFAULT 0 COMMENT '排序',
    `add_time`       datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `activity_short` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '活动简称',
    `is_recommend`   tinyint                                                       NOT NULL DEFAULT 2 COMMENT '是否推广；1:推广；2:不推广',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_special_id` (`special_id` ASC) USING BTREE,
    INDEX `idx_activity_id` (`activity_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '专场关联活动中间表';

CREATE TABLE `hw_special_activity_session_area`
(
    `id`          int      NOT NULL AUTO_INCREMENT,
    `add_time`    datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `special_id`  int      NOT NULL DEFAULT 0 COMMENT '专场id',
    `area_id`     int      NOT NULL DEFAULT 0 COMMENT '地点id',
    `level`       tinyint  NOT NULL COMMENT '地区级别',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_special_id` (`special_id` ASC) USING BTREE,
    INDEX `idx_area_id` (`area_id` ASC) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '专场地点';

ALTER TABLE `announcement`
    MODIFY COLUMN `highlights_describe` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '公告亮点描述' AFTER `sub_title`;

ALTER TABLE `announcement`
    ADD COLUMN `activity_job_content` varchar(600) NOT NULL DEFAULT '' COMMENT '活动招聘岗位';

INSERT INTO `home_position` (`id`, `add_time`, `update_time`, `status`, `platform_type`, `number`, `name`,
                             `chinese_name`, `width`, `height`, `creator_type`, `creator`, `creator_id`, `sort`,
                             `describe`)
VALUES (null, '2025-03-10 14:29:38', '0000-00-00 00:00:00', 1, 4, 'mini_zhaopinhui_tesezhuanchang', '招聘会_特色专场',
        '', '', '', 1, 'admin', 1, 1, '');
INSERT INTO `home_position` (`id`, `add_time`, `update_time`, `status`, `platform_type`, `number`, `name`,
                             `chinese_name`, `width`, `height`, `creator_type`, `creator`, `creator_id`, `sort`,
                             `describe`)
VALUES (null, '2025-03-10 14:29:15', '0000-00-00 00:00:00', 1, 4, 'mini_zhaopinhui_HF', '招聘会_HF  ', '', '', '', 1,
        'admin', 1, 1, '');
INSERT INTO `home_position` (`id`, `add_time`, `update_time`, `status`, `platform_type`, `number`, `name`,
                             `chinese_name`, `width`, `height`, `creator_type`, `creator`, `creator_id`, `sort`,
                             `describe`)
VALUES (null, '2025-03-10 14:27:56', '0000-00-00 00:00:00', 1, 8, 'zhaopinhui_tesezhuanchang', '招聘会_特色专场', '',
        '', '', 1, 'admin', 1, 1, '');
INSERT INTO `home_position` (`id`, `add_time`, `update_time`, `status`, `platform_type`, `number`, `name`,
                             `chinese_name`, `width`, `height`, `creator_type`, `creator`, `creator_id`, `sort`,
                             `describe`)
VALUES (null, '2025-03-10 14:27:33', '0000-00-00 00:00:00', 1, 8, 'zhaopinhui_HF', '招聘会_HF', '', '', '', 1, 'admin',
        1, 1, '');
INSERT INTO `home_position` (`id`, `add_time`, `update_time`, `status`, `platform_type`, `number`,
                             `name`, `chinese_name`, `width`, `height`, `creator_type`, `creator`,
                             `creator_id`, `sort`, `describe`)
VALUES (null, '2025-03-14 09:36:51', '0000-00-00 00:00:00', 1, 8, 'zhaopinhui_hezuoanli', '招聘会_合作案例', '', '', '',
        1, 'admin', 1, 1, '');
