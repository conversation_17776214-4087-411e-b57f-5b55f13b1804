# ReadMe

### 关联产品原型版本

- [蓝湖 (lanhuapp.com)](https://lanhuapp.com/web/#/item/project/stage?tid=8d951de4-aefb-40f7-954e-366683d68331&pid=fa546800-9234-4ce0-8bc0-b63e0af32f27&from=dashboard)

***

### 参与人员

- 单文超
- 王昕
- 龚传栋
- 李健
- 林建炫

***

### 分支

|仓库|开发分支|提測分支|备注|
|:----:|:----:|:----:|:----:|
|new_gaoxiao_yii|feature/V1.2人岗匹配|release/V1.2人岗匹配|-|

***

### 上线部署步骤

`内容为空,填写"-"即可`

|步骤|是否执行|执行内容|
|:----|:----:|:----|
|提醒前端提前合并代码到master和添加权限节点|-|-|
|执行sql语句|是|见下方"执行sql语句"|
|更新后端代码|是|-|
|composer安装|是|见下方"composer安装"|
|更新配置|-|见下方"更新配置"|
|创建队列|-|见下方"创建队列"|
|执行脚本|是|见下方"执行脚本"|
|删除redis缓存|-|见下方"删除redis缓存"|
|重启队列|-|上线前, 暂停所有队列, 上线后再重启|
|更新前端代码|-|-|
|添加定时任务|是|见下方"定时任务"|
|群内通知部署完毕|是|-|

#### 执行sql语句(按顺序执行)

* alter_data.sql

#### 执行脚本

* php timer_yii script/update-resume-expansion
* php timer_yii person/create-resume-remind
* php timer_yii script/active-person-match
* PHP timer_yii admin-task/invite（定时触发）

### 创建队列

* person-resume-intention-match-job-queue

### 运营平台邀约投递

* 邀约投递按钮权限 key：deliveryInviteOpen 名称：邀约投递按钮
* 邀约投递按钮权限 key：configurationInviteList 名称：投递邀约查询



### 添加队列和配置文件

![image-20230406201547400](http://img.ideaboat.cn/uPic/image-20230406201547400.png)

```
订阅需要走新的邮件通道，所以在阿里云申请了一个新邮箱，上线的时候需要在main-local.php里面添加配置

'mailer4' => [

'class'            => 'yii\swiftmailer\Mailer',
//false发送邮件，true只是生成邮件在runtime文件夹下，不发邮件
'useFileTransport' => false,
'transport'        => [
'class'      => 'Swift_SmtpTransport',
//每种邮箱的host配置不一样
'host'       => 'smtpdm.aliyun.com',
//发件人邮箱
'username'   => 'subscribe.gaoxiaojob.com',
//授权码
'password'   => 'xxxxxx',
'port'       => '465',
'encryption' => 'ssl',
],
'messageConfig'    => [
'charset' => 'UTF-8',
//发件人昵称
'from'    => ['<EMAIL>' => '高校人才网'],
],
],
```

