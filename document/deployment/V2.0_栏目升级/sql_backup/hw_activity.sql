CREATE TABLE `hw_activity`
(
    `id`                      int(10)      NOT NULL AUTO_INCREMENT,
    `add_time`                datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`             datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`                  tinyint(1)   NOT NULL DEFAULT '0' COMMENT '状态(1正常，2删除）',
    `name`                    varchar(255) NOT NULL DEFAULT '' COMMENT '活动名称',
    `series_type`             tinyint(2)   NOT NULL DEFAULT '0' COMMENT '活动系列',
    `type`                    tinyint(2)   NOT NULL DEFAULT '0' COMMENT '活动类型',
    `introduce`               text         NOT NULL COMMENT '活动简介',
    `sign_end_date`           date         NOT NULL DEFAULT '0000-00-00' COMMENT '报名截止日期',
    `sign_custom_end_date`    varchar(255) NOT NULL DEFAULT '' COMMENT '自定义报名截止日期',
    `detail_url`              varchar(255) NOT NULL DEFAULT '' COMMENT '详情链接',
    `sign_up_url`             varchar(255) NOT NULL DEFAULT '' COMMENT '报名链接',
    `main_img_file_id`        varchar(255) NOT NULL DEFAULT '' COMMENT '主图文件id',
    `logo_file_id`            varchar(255) NOT NULL DEFAULT '' COMMENT 'logo文件id',
    `review_img_file_ids`     varchar(255) NOT NULL DEFAULT '' COMMENT '精彩回顾图片，逗号分隔',
    `other_img_one_file_id`   varchar(255) NOT NULL DEFAULT '' COMMENT '其他图片1',
    `other_img_two_file_id`   varchar(255) NOT NULL DEFAULT '' COMMENT '其他图片2',
    `other_img_three_file_id` varchar(255) NOT NULL DEFAULT '' COMMENT '其他图片3',
    `other_description_one`   varchar(255) NOT NULL DEFAULT '' COMMENT '其他说明1',
    `other_description_two`   varchar(255) NOT NULL DEFAULT '' COMMENT '其他说明2',
    `other_description_three` varchar(255) NOT NULL DEFAULT '' COMMENT '其他说明3',
    `grounding_status`        tinyint(2)   NOT NULL DEFAULT '0' COMMENT '上架状态（1上架 、2下架）',
    `sort`                    tinyint(2)   NOT NULL DEFAULT '0' COMMENT '活动排序',
    `company_id`              int(10)      NOT NULL DEFAULT '0' COMMENT '关联单位id',
    `activity_status`         tinyint(2)   NOT NULL DEFAULT '0' COMMENT '活动状态(1进行中，2已结束）',
    `activity_start_date`     date         NOT NULL DEFAULT '0000-00-00' COMMENT '活动开始时间',
    `activity_end_date`       date         NOT NULL DEFAULT '0000-00-00' COMMENT '活动结束时间',
    `is_outside_url`          tinyint(2)   NOT NULL DEFAULT '0' COMMENT '是否站外链接',
    `activity_child_status`   tinyint(2)   NOT NULL DEFAULT '0' COMMENT '活动子状态1、''报名中'',2、''待举办'',3、''正在进行'',4、''已结束'')',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='海外活动表';



CREATE TABLE `hw_activity_promotion`
(
    `id`            int(10)      NOT NULL AUTO_INCREMENT,
    `add_time`      datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`        tinyint(4)   NOT NULL DEFAULT '0' COMMENT '状态',
    `position_type` tinyint(4)   NOT NULL DEFAULT '0' COMMENT '推广位置type',
    `activity_id`   int(10)      NOT NULL DEFAULT '0' COMMENT '活动id',
    `sort`          int(10)      NOT NULL DEFAULT '0' COMMENT '推广排序',
    `start_date`    date         NOT NULL DEFAULT '0000-00-00' COMMENT '推广开始时间',
    `end_date`      date         NOT NULL DEFAULT '0000-00-00' COMMENT '推广结束时间',
    `img_type`      tinyint(4)   NOT NULL DEFAULT '0' COMMENT '显示图片类型',
    `img_file_id`   varchar(255) NOT NULL DEFAULT '' COMMENT '图片地址',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='海外活动推广记录表';


CREATE TABLE `hw_activity_session`
(
    `id`             int(10)      NOT NULL AUTO_INCREMENT,
    `add_time`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`         tinyint(2)   NOT NULL DEFAULT '0' COMMENT '状态',
    `activity_id`    int(10)      NOT NULL DEFAULT '0' COMMENT '活动id',
    `name`           varchar(255) NOT NULL DEFAULT '' COMMENT '场次名称',
    `number`         varchar(255) NOT NULL DEFAULT '' COMMENT '场次编号',
    `custom_time`    varchar(255) NOT NULL DEFAULT '' COMMENT '自定义举办时间',
    `start_date`     date         NOT NULL DEFAULT '0000-00-00' COMMENT '举办开始日期',
    `start_time`     char(4)      NOT NULL DEFAULT '' COMMENT '举办开始时间',
    `end_date`       date         NOT NULL DEFAULT '0000-00-00' COMMENT '举办结束日期',
    `end_time`       char(4)      NOT NULL DEFAULT '' COMMENT '举办结束时间',
    `time_type`      tinyint(2)   NOT NULL DEFAULT '0' COMMENT '举办时间类型（1当地  2北京）',
    `custom_address` varchar(255) NOT NULL DEFAULT '' COMMENT '自定义举办地点',
    `detail_address` varchar(255) NOT NULL DEFAULT '' COMMENT '详细地址',
    `sort`           tinyint(2)   NOT NULL DEFAULT '0' COMMENT '排序',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;


CREATE TABLE `hw_activity_session_area`
(
    `id`          int(10)    NOT NULL AUTO_INCREMENT,
    `add_time`    datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
    `activity_id` int(10)    NOT NULL DEFAULT '0' COMMENT '活动id',
    `session_id`  int(10)    NOT NULL DEFAULT '0' COMMENT '场次id',
    `area_id`     int(10)    NOT NULL DEFAULT '0' COMMENT '地点id',
    `level`       tinyint(2) NOT NULL COMMENT '地区级别',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='海外活动场次地点表';


