update resume_equity_package_setting set original_amount = 68,real_amount = 38  WHERE id = 4;
update resume_equity_package_setting set original_amount = 118,real_amount = 58  WHERE id = 5;
update resume_equity_package_setting set original_amount = 198,real_amount = 78  WHERE id = 6;


update resume_equity_package_setting set buy_type = 0 where id = 4;
update resume_equity_package_setting set buy_type = 0 where id = 5;
update resume_equity_package_setting set buy_type = 6  where id = 6;


update resume_equity_setting set name = '高级筛选' where name = '编制查询';


# 这部分字段已提前更新
# alter table job
#     add column apply_heat_type tinyint(2)  NOT NULL DEFAULT '0' COMMENT '该职位近90天的投递热度（V1.9需求，投递触发更新）';
#
# ALTER TABLE job ADD  INDEX idx_apply_heat_type (apply_heat_type);
#
#
# alter table announcement
#     add column announcement_heat_type tinyint(2)  NOT NULL DEFAULT '0' COMMENT '公告类型热度（90天内点击数量计算热度类型）';
#
# alter table announcement
#     add column establishment_type tinyint(2)  NOT NULL DEFAULT '0' COMMENT '编制类型';
#
# ALTER TABLE announcement ADD  INDEX idx_announcement_heat_type (announcement_heat_type);
# ALTER TABLE announcement ADD  INDEX idx_establishment_type (establishment_type);
