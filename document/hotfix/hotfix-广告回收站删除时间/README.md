# 广告回收站删除时间功能实现

## 需求描述
在运营后台的内容管理模块中，为回收站的广告列表功能添加删除时间显示和筛选功能。

## 实现内容

### 1. 数据库修改
- 为 `showcase` 表添加 `delete_time` 字段
- 添加索引优化查询性能

```sql
-- 执行 alter_data.sql 中的SQL语句
ALTER TABLE `showcase` ADD COLUMN `delete_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '删除时间' AFTER `update_time`;
ALTER TABLE `showcase` ADD INDEX `idx_delete_time` (`delete_time`);
```

### 2. 后端修改

#### 2.1 模型类更新
- **文件**: `common/models/Showcase.php`
- **修改**: 添加 `delete_time` 属性注释

#### 2.2 删除逻辑更新
- **文件**: `admin/models/Showcase.php`
- **方法**: `deleteShowcase()`
- **修改**: 在删除广告时记录删除时间

#### 2.3 列表查询更新
- **文件**: `admin/models/Showcase.php`
- **方法**: `getShowcaseList()`
- **修改**: 
  - 添加 `delete_time` 字段到查询结果
  - 修正删除时间筛选逻辑（`delete_time_start`, `delete_time_end`）
  - 添加格式化删除时间字段 `deleteTimeFormatted`

### 3. API接口

#### 3.1 广告列表接口
- **接口**: `GET /admin/showcase/get-showcase-list`
- **新增参数**:
  - `delete_time_start`: 删除时间开始日期 (格式: YYYY-MM-DD)
  - `delete_time_end`: 删除时间结束日期 (格式: YYYY-MM-DD)
- **返回字段新增**:
  - `delete_time`: 删除时间 (原始格式)
  - `deleteTimeFormatted`: 格式化删除时间 (用于前端显示)

#### 3.2 删除广告接口
- **接口**: `POST /admin/showcase/delete-showcase`
- **功能**: 删除时自动记录删除时间

### 4. 使用说明

#### 4.1 查询回收站广告列表
```javascript
// 查询已删除的广告，按删除时间筛选
fetch('/admin/showcase/get-showcase-list?status=9&delete_time_start=2024-01-01&delete_time_end=2024-12-31')
```

#### 4.2 删除广告
```javascript
// 删除广告，会自动记录删除时间
fetch('/admin/showcase/delete-showcase', {
    method: 'POST',
    body: JSON.stringify({id: 123})
})
```

### 5. 前端集成建议

#### 5.1 列表显示
在广告列表的"广告状态"列后面添加"删除时间"列：

```html
<th>删除时间</th>
```

```javascript
// 显示删除时间
{
    field: 'deleteTimeFormatted',
    title: '删除时间',
    formatter: function(value) {
        return value || '-';
    }
}
```

#### 5.2 筛选功能
在筛选区域添加删除时间范围选择器：

```html
<div class="filter-item">
    <label>删除时间：</label>
    <input type="date" name="delete_time_start" placeholder="开始时间">
    <span>至</span>
    <input type="date" name="delete_time_end" placeholder="结束时间">
</div>
```

### 6. 测试

#### 6.1 功能测试
- 执行 `test_showcase_delete_time.php` 脚本进行基本功能测试
- 测试删除广告是否正确记录删除时间
- 测试列表查询是否正确返回删除时间字段
- 测试删除时间筛选是否正常工作

#### 6.2 接口测试
```bash
# 测试删除广告
curl -X POST "http://your-domain/admin/showcase/delete-showcase" \
  -H "Content-Type: application/json" \
  -d '{"id": 123}'

# 测试查询列表
curl "http://your-domain/admin/showcase/get-showcase-list?status=9&delete_time_start=2024-01-01&delete_time_end=2024-12-31"
```

### 7. 注意事项

1. **历史数据处理**: 历史删除的广告 `delete_time` 字段为默认值 `0000-00-00 00:00:00`，前端显示为空
2. **性能优化**: 已为 `delete_time` 字段添加索引
3. **时间格式**: 删除时间使用 `YYYY-MM-DD HH:mm:ss` 格式
4. **兼容性**: 修改保持向后兼容，不影响现有功能

### 8. 部署步骤

1. 执行数据库脚本 `alter_data.sql`
2. 部署后端代码修改
3. 测试功能是否正常
4. 如需前端修改，请根据具体前端框架进行相应调整

## 完成状态

- ✅ 数据库字段添加
- ✅ 后端逻辑实现
- ✅ API接口更新
- ✅ 测试脚本编写
- ⏳ 前端界面调整（根据需要）
