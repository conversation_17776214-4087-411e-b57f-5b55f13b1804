# 邀约数据大屏API修改总结

## 修改说明

根据需求进行了以下两个主要修改：

1. **数据来源调整**: 所有邀约数据统计都修改为**仅基于单位邀约**(`BaseResumeLibraryInviteLog`)，**不再包含管理员邀约**(`BaseAdminJobInvite`)数据。

2. **KPI接口增强**: KPI概览接口新增了当月与上月同期对比的趋势数据，并调整为返回当月数据而非全量数据。

3. **数据类型优化**: 所有接口返回的数值字段都强制转换为数字类型(int/float)，便于前端处理和计算。

4. **专业获取逻辑修正**: 修正了专业字段的获取逻辑，优先从major表通过major_id获取，没有时才使用major_custom自定义专业。

5. **日期查询性能优化**: 将所有使用`DATE(add_time)`的查询改为`BETWEEN`时间范围查询，大幅提升查询性能。

6. **重复触达率计算修正**: 修正了重复触达率的计算逻辑，从原来的"平均重复次数比例"改为"重复邀约占总邀约的比例"，更加直观易懂。

## 修改的文件和内容

### 1. Service层修改 (`common/service/jobInvite/InvitationScreenService.php`)

#### 移除的依赖
- 移除了 `BaseAdminJobInvite` 的引用

#### 修改的方法

**KPI概览相关方法**:
- `getKpiOverview()`: 重构为返回当月数据和趋势对比
- `getMonthKpiData()`: 新增方法，获取指定月份的KPI数据，默认获取当月1日到今天
- `getLastMonthSamePeriodData()`: 新增方法，获取上月同期数据（上月1日到上月今天对应日期）
- `calculateTrends()`: 新增方法，计算当月与上月同期的趋势对比
- `calculateTrendPercentage()`: 新增方法，计算趋势百分比
- `getMajorName()`: 新增方法，正确获取专业名称（优先major表，后备major_custom）

**日期查询优化相关方法**:
- 所有涉及日期查询的方法都改用`TimeHelper::dayToBeginTime()`和`TimeHelper::dayToEndTime()`
- 查询条件从`DATE(add_time)`改为`BETWEEN add_time`，提升查询性能
- `getTotalInvites()`: 仅统计单位邀约总数
- `getUniqueResumes()`: 仅从单位邀约中获取唯一求职者数
- `getJobApplyCount()`: 仅统计单位邀约产生的投递数

**趋势分析相关方法**:
- `getDayTotalInvites($date)`: 仅统计某日单位邀约数
- `getDayUniqueResumes($date)`: 仅统计某日单位邀约的唯一求职者数
- `getDayJobApplyCount($date)`: 仅统计某日单位邀约产生的投递数

#### 修改前后对比

**1. 数据来源修改前**:
```php
// 获取总邀约数（单位邀约 + 平台邀约）
$companyInvites = BaseResumeLibraryInviteLog::find()->count();
$adminInvites = BaseAdminJobInvite::find()->count();
return $companyInvites + $adminInvites;
```

**数据来源修改后**:
```php
// 仅统计单位邀约数
return BaseResumeLibraryInviteLog::find()->count();
```

**2. KPI接口修改前**:
```php
return [
    'totalInvites' => $totalInvites,
    'uniqueResumes' => $uniqueResumes,
    'jobApplyCount' => $jobApplyCount,
    'jobApplyRate' => $jobApplyRate
];
```

**KPI接口修改后**:
```php
return [
    'totalInvites' => $currentMonthData['totalInvites'],
    'uniqueResumes' => $currentMonthData['uniqueResumes'],
    'jobApplyCount' => $currentMonthData['jobApplyCount'],
    'jobApplyRate' => $currentMonthData['jobApplyRate'],
    'repeatTouchRate' => $currentMonthData['repeatTouchRate'],
    'trends' => $trends
];
```

### 2. 文档更新

#### 测试脚本 (`test_invitation_screen_api.php`)
- 添加了说明注释，明确只统计单位邀约

#### Apifox导入文档 (`apifox-import-params.md`)
- 在文档开头添加了重要说明，明确数据来源

#### 新增修改总结文档 (`modification-summary.md`)
- 详细记录了所有修改内容

## 数据统计范围

### 包含的数据
- ✅ 单位邀约记录 (`resume_library_invite_log` 表)
- ✅ 单位邀约产生的投递记录
- ✅ 单位邀约的求职者信息

### 不包含的数据
- ❌ 管理员邀约记录 (`admin_job_invite` 表)
- ❌ 管理员邀约产生的投递记录
- ❌ 管理员邀约的求职者信息

## 接口功能变更

### 1. KPI概览接口功能增强

**KPI概览接口** (`/invitation-screen/kpi-overview`) 进行了重大升级：
   - **数据范围**: 从全量数据改为当月数据
   - **新增字段**: 重复触达率(repeatTouchRate)
   - **新增趋势**: 当月与上月对比的趋势数据(trends对象)
   - **趋势指标**: 包含所有KPI指标的环比增长率

### 2. 其他接口保持不变

其余4个接口的功能和返回格式保持不变，只是数据来源调整为仅统计单位邀约：

2. **趋势分析接口** (`/invitation-screen/trend-analysis`)
   - 按日期的邀约趋势数据

3. **单位排行接口** (`/invitation-screen/company-ranking`)
   - 按邀约数排序的单位排行榜

4. **求职者排行接口** (`/invitation-screen/candidate-ranking`)
   - 按被邀约次数排序的求职者排行榜

5. **单位对求职者邀约排行** (`/invitation-screen/company-to-candidate-ranking`)
   - 单位对特定求职者的邀约统计

## 影响分析

### 正面影响
1. **数据一致性**: 所有统计都基于同一数据源，确保数据一致性
2. **业务聚焦**: 专注于单位邀约行为分析，更符合业务需求
3. **性能提升**: 减少了跨表查询，提升查询性能
4. **维护简化**: 减少了数据源复杂性，降低维护成本

### 需要注意的点
1. **数据完整性**: 如果之前有管理员邀约数据，现在不会显示在统计中
2. **历史对比**: 与之前包含管理员邀约的统计数据可能存在差异
3. **业务理解**: 需要确保相关人员了解数据统计范围的变化

## 测试建议

1. **功能测试**: 运行测试脚本验证所有接口正常工作
2. **数据验证**: 对比修改前后的数据差异，确保符合预期
3. **性能测试**: 验证查询性能是否有提升
4. **业务验证**: 与业务人员确认统计结果符合业务需求

## 部署注意事项

1. **数据备份**: 部署前建议备份相关数据
2. **灰度发布**: 建议先在测试环境验证，再发布到生产环境
3. **监控告警**: 部署后监控接口响应时间和错误率
4. **用户通知**: 如有必要，通知相关用户数据统计范围的变化

## 总结

本次修改成功将邀约数据大屏的所有统计功能调整为仅基于单位邀约数据，简化了数据来源，提升了数据一致性和查询性能。所有接口功能保持不变，只是统计范围更加聚焦和明确。
