# ReadMe

### 职位列表优化2.0.1
- [职位列表优化processon](https://weixin.processon.com/team/60ee4f30e4b08d10d130cea6#diagrams)

***

### 参与人员

- 龚传栋
- 单文超
- xxx

***

### 分支

|仓库|bug分支|备注|
|:----:|:----:|:----:|
|new_gaoxiao_yii|hotfix/职位列表优化|-|



***

### 上线部署步骤

`内容为空,填写"-"即可`

|步骤|是否执行|执行内容|
|:----|:----:|:----|
|提醒前端提前合并代码到master和添加权限节点|-|-|
|执行sql语句|是|见下方"执行sql语句"|
|更新后端代码|是|-|
|composer安装|是|见下方"composer安装"|
|更新配置|-|见下方"更新配置"|
|创建队列|-|见下方"创建队列"|
|执行脚本|是|见下方"执行脚本"|
|删除redis缓存|-|见下方"删除redis缓存"|
|重启队列|-|上线前, 暂停所有队列, 上线后再重启|
|更新前端代码|-|-|
|添加定时任务|是|见下方"定时任务"|
|群内通知部署完毕|是|-|

#### composer安装
执行以下两条命令(按顺序), 安装对应的包
```
composer require "xxxxx"
```
#### 执行sql语句(按顺序执行)

* alter_data.sql

#### 执行脚本(一次性脚本)
同步职位数据到中间表功能脚本
```shell
    php timer_yii script/update-migration-data
```
#### 删除redis缓存
``` shell
      ALL:*
```

