-- 清空 job_boshihou 表
TRUNCATE TABLE job_boshihou;
### 初始化职位博士后数据表
INSERT INTO job_boshihou (job_id)
SELECT j.id
FROM job as j
WHERE j.job_category_id in (29, 263)
  and j.status in (0, 1)
  and j.is_show = 1;
-- 清空 announcement_boshihou 表
TRUNCATE TABLE announcement_boshihou;
### 初始化公告博士后数据表
INSERT INTO announcement_boshihou (announcement_id)
SELECT j.announcement_id
FROM job as j
WHERE j.job_category_id in (29, 263)
  and j.status in (0, 1)
  and j.is_show = 1
  and j.announcement_id > 0
group by j.announcement_id;