-- 1. 在resume表中添加is_postdoc字段
ALTER TABLE `resume`
    ADD COLUMN `is_postdoc` TINYINT(1) NOT NULL DEFAULT 2 COMMENT '是否有博士后经历,1是,2否' AFTER `is_abroad`;

-- 2. 添加索引以提高查询性能
ALTER TABLE `resume`
    ADD INDEX `idx_is_postdoc` (`is_postdoc`);

-- 3. 初始化数据：根据resume_work表的数据更新resume表
UPDATE `resume` r
SET `is_postdoc` = 1
WHERE EXISTS (
    SELECT 1
    FROM `resume_work` rw
    WHERE rw.resume_id = r.id
      AND rw.is_postdoc = 1
      AND rw.status = 1
);

-- 4. 验证数据更新结果
SELECT
    COUNT(*) as total_resumes,
    SUM(CASE WHEN is_postdoc = 1 THEN 1 ELSE 0 END) as postdoc_count,
    SUM(CASE WHEN is_postdoc = 2 THEN 1 ELSE 0 END) as non_postdoc_count
FROM resume;

-- 5. 验证数据一致性
SELECT
    r.id,
    r.name,
    r.is_postdoc as resume_is_postdoc,
    (SELECT COUNT(*) FROM resume_work rw WHERE rw.resume_id = r.id AND rw.is_postdoc = 1 AND rw.status = 1) as work_postdoc_count
FROM resume r
WHERE r.is_postdoc = 1
LIMIT 10;