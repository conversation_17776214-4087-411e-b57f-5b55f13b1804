# 开发工具需求与API接口文档

## 项目背景

### 业务需求
研发团队在开发过程中经常需要查询数据库中的用户信息，包括：
- 根据单一信息（ID、手机号、邮箱等）查找其他相关信息
- 查看用户的完整关联数据（求职者信息、单位信息、账号信息等）
- 提高开发和调试效率

### 解决方案
创建一个内部开发工具页面，支持：
1. **智能查询**：用户输入任意关键词，系统自动识别数据类型
2. **精确查询**：用户选择具体查询类型进行查询
3. **关联数据展示**：自动展示所有相关联的信息

## 功能设计

### 查询模式
- **智能查询**（推荐）：输入关键词，自动识别类型
- **精确查询**：手动选择查询类型

### 支持的查询类型
- 个人用户：ID、手机号、UID、邮箱、用户名
- 单位相关：单位ID、单位账号ID、单位UID

### 数据展示
- 用户基本信息
- 求职者详细信息
- 单位信息
- 单位账号信息（1对多关系）
- 投递记录
- 登录日志

## API接口规范

### 接口1：精确查询 ⭐ **必须实现**

**接口地址**：`GET /dev-tools/get-user-related-info`

**请求参数**：
```json
{
  "searchType": "string", // 查询类型
  "searchValue": "string" // 查询值
}
```

**searchType枚举**：
- `id` - 用户ID
- `phone` - 手机号
- `uid` - 用户UID  
- `email` - 邮箱
- `username` - 用户名
- `companyId` - 单位ID
- `companyAccountId` - 单位账号ID
- `companyUid` - 单位UID

### 接口2：智能查询 ⭐ **推荐实现**

**接口地址**：`GET /dev-tools/intelligent-search`

**请求参数**：
```json
{
  "keyword": "string" // 查询关键词
}
```

**智能搜索规则**：
智能查询不再基于模式识别，而是在所有相关字段中进行全量搜索：

**搜索范围**：
- **个人用户字段**：用户ID、手机号、邮箱、用户名
- **单位字段**：单位ID、单位名称
- **单位账号字段**：单位账号ID、联系人姓名

**搜索逻辑**：
- 对输入关键词在上述所有字段中进行精确匹配和模糊匹配
- 返回所有匹配的结果，支持多个匹配项
- 每个结果都标明匹配的字段和类型

## 返回数据结构

### 精确查询返回结构

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "userInfo": {
      "id": "string",
      "name": "string", 
      "phone": "string",
      "email": "string",
      "uid": "string",
      "username": "string",
      "userType": "person|company", // person:求职者 company:单位用户
      "status": "string", // 正常|禁用|待审核
      "createTime": "2023-01-15T08:30:00Z", // ISO 8601格式
      "lastLoginTime": "2024-01-10T14:20:00Z"
    },
    "personInfo": { // 当userType为person时返回
      "resumeId": "string",
      "education": "string", // 学历
      "major": "string", // 专业
      "school": "string", // 学校
      "workExperience": "string", // 工作经验
      "expectedSalary": "string", // 期望薪资
      "resumeStatus": "string" // 已完善|待完善|已隐藏
    },
    "companyInfo": { // 单位信息
      "companyId": "string",
      "companyName": "string",
      "companyType": "string", // 单位类型
      "industry": "string", // 行业
      "location": "string", // 地区
      "auditStatus": "string", // 已通过|待审核|已拒绝
      "createTime": "2022-06-15T10:30:00Z"
    },
    "companyAccounts": [ // 单位账号列表（1对多关系）⭐ 重点
      {
        "accountId": "string",
        "uid": "string", // 单位账号UID
        "accountName": "string", // 账号名称
        "username": "string",
        "phone": "string",
        "email": "string", 
        "role": "string", // 角色
        "status": "string", // 正常|禁用|待审核|已锁定|已注销
        "isMain": true, // 是否主账号
        "createTime": "2022-06-15T10:30:00Z",
        "lastLoginTime": "2024-01-10T09:15:00Z"
      }
    ],
    "applyRecords": [ // 投递记录
      {
        "jobTitle": "string",
        "companyName": "string",
        "applyType": "string", // 站内|站外
        "status": "string", // 已投递|已查看|已面试|已录用|已拒绝
        "applyTime": "2024-01-08T16:45:00Z"
      }
    ],
    "loginLogs": [ // 最近登录记录（建议限制10条）
      {
        "loginTime": "2024-01-10T14:20:00Z",
        "ip": "*************",
        "userAgent": "Chrome 120.0.0.0",
        "location": "北京"
      }
    ]
  }
}
```

### 智能查询返回结构

智能查询支持多个匹配结果，返回结构如下：

```json
{
  "msg": "查询成功",
  "result": 1,
  "data": {
    "keyword": "搜索关键词",
    "totalCount": 2,
    "results": [
      {
        "matchedFields": ["手机号", "用户名"], // 匹配的字段
        "matchedType": "个人用户", // 匹配类型：个人用户/单位/单位账号
        "userInfo": {
          "id": "12345",
          "name": "张三",
          "phone": "13800138000",
          "email": "<EMAIL>",
          "uid": "",
          "username": "zhangsan",
          "userType": "person",
          "status": "正常",
          "createTime": "2023-01-15T08:30:00+00:00",
          "lastLoginTime": "2024-01-10T14:20:00+00:00"
        },
        "personInfo": {
          "resumeId": "67890",
          "education": "本科",
          "major": "计算机科学与技术",
          "school": "北京大学",
          "workExperience": "3年",
          "expectedSalary": "",
          "resumeStatus": "已完善"
        },
        "companyInfo": null,
        "companyAccounts": [],
        "applyRecords": [...],
        "loginLogs": [...]
      },
      {
        "matchedFields": ["单位名称"],
        "matchedType": "单位",
        "userInfo": null,
        "personInfo": null,
        "companyInfo": {
          "companyId": "100",
          "companyName": "北京科技有限公司",
          "companyType": "",
          "industry": "",
          "location": "",
          "auditStatus": "已通过",
          "createTime": "2022-06-15T10:30:00+00:00"
        },
        "companyAccounts": [...],
        "applyRecords": [],
        "loginLogs": []
      }
    ]
  }
}
```

## 查询逻辑说明

### 根据searchType的查询逻辑
1. **id/phone/uid/email/username** → 查询用户表，返回用户信息+关联数据
2. **companyId** → 查询单位表，返回单位信息+所有关联账号
3. **companyAccountId** → 查询单位账号表，返回账号信息+关联单位信息  
4. **companyUid** → 通过单位UID查询单位账号表

### 数据库表关系
```
用户表 (users)
├── 求职者信息表 (person_info) - 1对1
└── 投递记录表 (apply_records) - 1对多

单位表 (companies)
└── 单位账号表 (company_accounts) - 1对多 ⭐ 重点

登录日志表 (login_logs) - 记录所有用户登录
```

## 错误处理

```json
{
  "code": 400,
  "message": "请求参数错误", 
  "data": null
}
```

**常见错误码**：
- 200：成功
- 400：请求参数错误
- 401：未授权访问
- 403：权限不足
- 404：数据不存在
- 500：服务器内部错误

## 开发要求

### 数据格式要求
1. 时间字段统一使用ISO 8601格式：`YYYY-MM-DDTHH:mm:ssZ`
2. 布尔字段使用true/false
3. 空值返回null，空数组返回[]
4. 字符串字段不要返回空字符串，用null代替

### 性能要求
1. 为常用查询字段建立数据库索引（手机号、邮箱、UID等）
2. 登录日志建议限制返回最近10条
3. 考虑实现查询结果缓存

### 安全要求
1. 实现管理员权限验证
2. 记录所有查询操作的审计日志
3. 敏感信息可考虑部分脱敏
4. 实现查询频率限制防止滥用

## 测试数据

建议准备以下测试数据便于联调：
- 手机号：`13800138000`
- 邮箱：`<EMAIL>`
- 用户ID：`12345`
- 姓名：`张三`
- 单位名称：`北京科技有限公司`
- 单位UID：`COMP_UID_12345`

每个测试数据应包含完整的关联信息（用户信息、单位信息、账号信息、投递记录、登录日志等）。

## 实现优先级

### 第一阶段（必须）
1. 实现精确查询接口
2. 支持基本查询类型：id, phone, email, companyId
3. 返回基本用户信息和单位信息

### 第二阶段（推荐）
1. 完善单位账号关联查询（1对多关系）
2. 实现智能查询接口
3. 添加投递记录和登录日志

### 第三阶段（可选）
1. 性能优化和缓存
2. 完善权限控制
3. 添加操作审计日志

## 联调说明

前端已完成开发，使用模拟数据测试。接口完成后：
1. 前端会取消注释真实API调用代码
2. 使用上述测试数据进行功能验证
3. 确认数据格式和字段完整性

前端代码位置：`src/views/devTools/index.vue`
