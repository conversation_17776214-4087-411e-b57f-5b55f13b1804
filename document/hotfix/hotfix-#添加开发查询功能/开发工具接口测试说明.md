# 开发工具接口测试说明

## 接口地址

### 1. 精确查询接口
- **URL**: `GET /admin/dev-tools/get-user-related-info`
- **参数**: 
  - `searchType`: 查询类型
  - `searchValue`: 查询值

### 2. 智能查询接口  
- **URL**: `GET /admin/dev-tools/intelligent-search`
- **参数**:
  - `keyword`: 查询关键词

## 测试用例

### 精确查询测试

#### 1. 通过用户ID查询
```
GET /admin/dev-tools/get-user-related-info?searchType=id&searchValue=12345
```

#### 2. 通过手机号查询
```
GET /admin/dev-tools/get-user-related-info?searchType=phone&searchValue=***********
```

#### 3. 通过邮箱查询
```
GET /admin/dev-tools/get-user-related-info?searchType=email&searchValue=<EMAIL>
```

#### 4. 通过单位ID查询
```
GET /admin/dev-tools/get-user-related-info?searchType=companyId&searchValue=100
```

### 智能查询测试

#### 1. 手机号智能识别
```
GET /admin/dev-tools/intelligent-search?keyword=***********
```

#### 2. 邮箱智能识别
```
GET /admin/dev-tools/intelligent-search?keyword=<EMAIL>
```

#### 3. 用户ID智能识别
```
GET /admin/dev-tools/intelligent-search?keyword=12345
```

#### 4. 姓名智能识别
```
GET /admin/dev-tools/intelligent-search?keyword=张三
```

#### 5. 单位名称智能识别
```
GET /admin/dev-tools/intelligent-search?keyword=北京科技有限公司
```

## 预期返回格式

### 成功响应
```json
{
  "msg": "查询成功",
  "result": 1,
  "data": {
    "matchType": "手机号", // 仅智能查询返回
    "userInfo": {
      "id": "12345",
      "name": "张三",
      "phone": "***********",
      "email": "<EMAIL>",
      "uid": "USER_12345",
      "username": "zhangsan",
      "userType": "person",
      "status": "正常",
      "createTime": "2023-01-15T08:30:00+00:00",
      "lastLoginTime": "2024-01-10T14:20:00+00:00"
    },
    "personInfo": {
      "resumeId": "67890",
      "education": "本科",
      "major": "计算机科学与技术",
      "school": "北京大学",
      "workExperience": "3年",
      "expectedSalary": "",
      "resumeStatus": "已完善"
    },
    "companyInfo": null,
    "companyAccounts": [],
    "applyRecords": [
      {
        "jobTitle": "软件工程师",
        "companyName": "腾讯科技",
        "applyType": "站内",
        "status": "已投递",
        "applyTime": "2024-01-08T16:45:00+00:00"
      }
    ],
    "loginLogs": [
      {
        "loginTime": "2024-01-10T14:20:00+00:00",
        "ip": "*************",
        "userAgent": "Chrome 120.0.0.0",
        "location": "北京"
      }
    ]
  }
}
```

### 失败响应
```json
{
  "msg": "未找到相关数据",
  "result": 0,
  "data": null
}
```

## 权限说明

- 开发工具接口需要管理员权限
- 建议只对超级管理员开放
- 需要在权限管理中配置相应的权限

## 安全注意事项

1. **权限控制**: 确保只有授权的管理员可以访问
2. **操作日志**: 记录所有查询操作的审计日志
3. **敏感信息**: 考虑对敏感信息进行脱敏处理
4. **频率限制**: 实现查询频率限制防止滥用

## 部署步骤

1. 将控制器文件放置到 `admin/controllers/DevToolsController.php`
2. 将模型文件放置到 `admin/models/DevTools.php`
3. 配置权限管理，添加相应的权限项
4. 测试接口功能是否正常

## 故障排除

### 常见问题

1. **权限不足**: 检查用户是否有相应权限
2. **数据库连接**: 确认数据库连接正常
3. **模型关联**: 检查模型之间的关联关系是否正确
4. **字段映射**: 确认数据库字段名与模型属性的映射

### 调试建议

1. 开启Yii2的调试模式
2. 查看日志文件中的错误信息
3. 使用数据库查询日志检查SQL语句
4. 逐步测试各个查询类型

## Apifox导入参数

### 精确查询接口参数
```
searchType,String,是,phone,id/phone/uid/email/username/companyId/companyAccountId/companyUid,查询类型
searchValue,String,是,***********,-,查询值
```

### 智能查询接口参数
```
keyword,String,是,***********,-,查询关键词，支持手机号/邮箱/ID/姓名/单位名称/单位UID
```
