# 强制下线功能测试用例

## 文档信息
- **文档版本**：v1.0
- **创建日期**：2024-12-15
- **测试范围**：用户强制下线功能完整测试
- **测试环境**：测试环境、预发布环境

---

## 1. 测试环境准备

### 1.1 测试数据准备
- **测试用户**：准备个人用户和企业用户各10个
- **管理员账号**：具有强制下线权限的管理员账号
- **设备环境**：PC端、H5端、小程序端测试环境

### 1.2 前置条件检查
- [ ] Redis服务正常运行
- [ ] 强制下线功能开关已开启
- [ ] 管理员权限配置正确
- [ ] 前端拦截器已配置403处理逻辑

---

## 2. 功能测试用例

### 2.1 用户密码修改强制下线测试

#### 测试用例 TC001：通过旧密码修改密码强制下线
**测试目标**：验证用户通过旧密码修改新密码后，其他设备被强制下线

**测试步骤**：
1. 用户A在设备1（PC端）登录成功
2. 用户A在设备2（H5端）登录成功
3. 用户A在设备2通过旧密码修改为新密码
4. 设备1发起任意需要认证的请求

**预期结果**：
- 密码修改成功，显示提示："密码修改成功，其他设备需要重新登录"
- 设备1收到403响应，显示："您的密码已修改，请使用新密码重新登录"
- 设备1自动清除本地认证信息并跳转登录页
- 设备2继续正常使用

**测试数据**：
- 用户ID：12345
- 旧密码：Test123456
- 新密码：NewTest123456

---

#### 测试用例 TC002：通过手机验证码重置密码强制下线
**测试目标**：验证用户通过手机验证码重置密码后，所有设备被强制下线

**测试步骤**：
1. 用户B在设备1和设备2都已登录
2. 用户B通过手机验证码重置密码
3. 设备1和设备2分别发起请求

**预期结果**：
- 密码重置成功
- 两个设备都收到403响应并被强制下线
- 用户需要使用新密码重新登录

**测试数据**：
- 用户手机：13800138001
- 验证码：123456
- 新密码：ResetPass123

---

#### 测试用例 TC003：通过邮箱验证码重置密码强制下线
**测试目标**：验证用户通过邮箱验证码重置密码后，所有设备被强制下线

**测试步骤**：
1. 用户C在多个设备登录
2. 用户C通过邮箱验证码重置密码
3. 各设备发起请求验证

**预期结果**：
- 密码重置成功
- 所有设备被强制下线
- 强制下线日志记录正确

**测试数据**：
- 用户邮箱：<EMAIL>
- 验证码：654321
- 新密码：EmailReset123

---

### 2.2 管理员强制下线测试

#### 测试用例 TC004：管理员强制下线单个用户
**测试目标**：验证管理员可以成功强制下线指定用户

**测试步骤**：
1. 用户D在多个设备登录
2. 管理员登录后台管理系统
3. 搜索用户D
4. 选择强制下线原因为"ADMIN_FORCE"
5. 填写备注"测试强制下线"
6. 执行强制下线操作
7. 用户D的设备发起请求

**预期结果**：
- 管理员操作成功，返回新版本号
- 用户D所有设备收到403响应
- 显示："您的账号已被管理员下线，请重新登录"
- 操作日志记录完整

**接口调用**：
```
POST /admin/member-force-logout/force-logout
{
  "memberId": 12348,
  "reason": "ADMIN_FORCE",
  "remark": "测试强制下线",
  "forceOldUser": 0
}
```

---

#### 测试用例 TC005：批量强制下线用户
**测试目标**：验证管理员可以批量强制下线多个用户

**测试步骤**：
1. 准备3个已登录的测试用户（ID：12349, 12350, 12351）
2. 管理员执行批量强制下线操作
3. 验证所有用户都被强制下线

**预期结果**：
- 批量操作成功，返回每个用户的操作结果
- 所有目标用户被强制下线
- 批量操作日志记录正确

**接口调用**：
```
POST /admin/member-force-logout/batch-force-logout
{
  "memberIds": "12349_12350_12351",
  "reason": "SECURITY_UPDATE",
  "remark": "批量安全更新",
  "forceOldUser": 0
}
```

---

#### 测试用例 TC006：搜索用户功能
**测试目标**：验证管理员可以通过多种方式搜索用户

**测试步骤**：
1. 通过用户名搜索
2. 通过手机号搜索
3. 通过邮箱搜索
4. 通过用户ID搜索
5. 按用户类型筛选

**预期结果**：
- 各种搜索方式都能正确返回匹配的用户
- 搜索结果包含必要的用户信息
- 分页功能正常

**接口调用**：
```
GET /admin/member-force-logout/search-members?keyword=张三&type=1&page=1&pageSize=20
```

---

### 2.3 日志查询测试

#### 测试用例 TC007：查看强制下线日志
**测试目标**：验证可以正确查看用户的强制下线历史记录

**测试步骤**：
1. 对用户执行多次强制下线操作（不同原因）
2. 查询该用户的强制下线日志
3. 验证日志内容的完整性

**预期结果**：
- 日志按时间倒序排列
- 包含版本号、原因、操作者、时间、备注等信息
- 分页功能正常

**接口调用**：
```
GET /admin/member-force-logout/get-force-logout-logs?memberId=12345&page=1&pageSize=10
```

---

## 3. 兼容性测试用例

### 3.1 老用户兼容性测试

#### 测试用例 TC008：老用户首次强制下线
**测试目标**：验证版本号为0的老用户能被正确强制下线

**测试步骤**：
1. 模拟老用户状态（Redis中无版本号记录）
2. 老用户正常使用系统
3. 对老用户执行强制下线操作（forceOldUser=true）
4. 老用户发起请求

**预期结果**：
- 老用户正常使用不受影响
- 强制下线操作成功，版本号设置为1
- 老用户被正确强制下线

---

#### 测试用例 TC009：新老用户混合场景
**测试目标**：验证新老用户同时在线时的兼容性

**测试步骤**：
1. 准备新用户（有版本号）和老用户（无版本号）
2. 分别对两类用户执行强制下线
3. 验证处理逻辑的正确性

**预期结果**：
- 新用户按正常逻辑处理
- 老用户按兼容逻辑处理
- 两类用户都能正确被强制下线

---

## 4. 异常场景测试用例

### 4.1 Redis异常测试

#### 测试用例 TC010：Redis连接失败
**测试目标**：验证Redis不可用时的降级处理

**测试步骤**：
1. 停止Redis服务
2. 尝试执行强制下线操作
3. 用户发起认证请求

**预期结果**：
- 强制下线操作返回失败
- 用户认证请求正常通过（降级处理）
- 系统记录异常日志

---

#### 测试用例 TC011：Redis数据异常
**测试目标**：验证Redis数据异常时的容错处理

**测试步骤**：
1. 手动修改Redis中的版本号为非法值
2. 用户发起认证请求
3. 执行强制下线操作

**预期结果**：
- 系统能正确处理异常数据
- 不影响正常功能使用
- 记录异常处理日志

---

### 4.2 并发测试

#### 测试用例 TC012：高并发强制下线
**测试目标**：验证高并发场景下的数据一致性

**测试步骤**：
1. 同时对同一用户执行多次强制下线操作
2. 验证版本号的正确性
3. 验证日志记录的完整性

**预期结果**：
- 版本号正确递增，无重复
- 所有操作都有日志记录
- 用户最终被正确强制下线

---

#### 测试用例 TC013：批量操作性能测试
**测试目标**：验证批量强制下线的性能表现

**测试步骤**：
1. 准备100个测试用户
2. 执行批量强制下线操作
3. 记录操作耗时和成功率

**预期结果**：
- 操作耗时 < 10秒
- 成功率 > 99%
- 系统资源使用正常

---

## 5. 前端集成测试用例

### 5.1 PC端测试

#### 测试用例 TC014：PC端403错误处理
**测试目标**：验证PC端正确处理强制下线响应

**测试步骤**：
1. PC端用户登录
2. 后台强制下线该用户
3. PC端发起请求

**预期结果**：
- 收到403响应
- 显示友好错误提示
- 自动清除本地认证信息
- 显示登录对话框

---

### 5.2 H5端测试

#### 测试用例 TC015：H5端403错误处理
**测试目标**：验证H5端正确处理强制下线响应

**测试步骤**：
1. H5端用户登录
2. 后台强制下线该用户
3. H5端发起请求

**预期结果**：
- 收到403响应
- 显示Toast提示
- 清除本地认证信息
- 自动跳转登录页面

---

### 5.3 小程序端测试

#### 测试用例 TC016：小程序端403错误处理
**测试目标**：验证小程序端正确处理强制下线响应

**测试步骤**：
1. 小程序端用户登录
2. 后台强制下线该用户
3. 小程序端发起请求

**预期结果**：
- 收到403响应
- 显示原生提示
- 清除本地认证信息
- 跳转到登录页面

---

## 6. 权限测试用例

### 6.1 权限控制测试

#### 测试用例 TC017：非管理员权限测试
**测试目标**：验证非管理员无法执行强制下线操作

**测试步骤**：
1. 使用普通用户账号登录后台
2. 尝试访问强制下线功能
3. 尝试调用强制下线接口

**预期结果**：
- 无法访问强制下线页面
- 接口调用返回权限错误
- 操作被正确拦截

---

#### 测试用例 TC018：管理员权限边界测试
**测试目标**：验证管理员权限的边界控制

**测试步骤**：
1. 测试不同级别管理员的权限
2. 验证权限范围的正确性

**预期结果**：
- 权限控制准确
- 超出权限范围的操作被拒绝

---

## 7. 性能测试用例

### 7.1 响应时间测试

#### 测试用例 TC019：接口响应时间测试
**测试目标**：验证各接口的响应时间符合要求

**测试指标**：
- 强制下线操作：< 100ms
- 用户搜索：< 200ms
- 日志查询：< 300ms
- 认证检查：< 10ms

---

### 7.2 并发性能测试

#### 测试用例 TC020：并发认证检查测试
**测试目标**：验证高并发下的认证检查性能

**测试参数**：
- 并发用户：1000
- 持续时间：5分钟
- 成功率要求：> 99.9%

---

## 8. 测试执行计划

### 8.1 测试阶段安排
- **第1天**：功能测试用例（TC001-TC007）
- **第2天**：兼容性和异常测试（TC008-TC013）
- **第3天**：前端集成和权限测试（TC014-TC018）
- **第4天**：性能测试和回归测试（TC019-TC020）

### 8.2 测试环境要求
- 测试环境：功能测试和集成测试
- 预发布环境：性能测试和最终验证
- 生产环境：灰度测试（可选）

### 8.3 测试完成标准
- [ ] 所有功能测试用例通过
- [ ] 兼容性测试通过
- [ ] 异常场景处理正确
- [ ] 前端集成正常
- [ ] 权限控制有效
- [ ] 性能指标达标
- [ ] 无阻塞性缺陷

---

## 9. 测试报告模板

### 9.1 测试结果统计
- 总用例数：20个
- 通过用例：X个
- 失败用例：X个
- 阻塞用例：X个
- 通过率：X%

### 9.2 缺陷统计
- 严重缺陷：X个
- 一般缺陷：X个
- 轻微缺陷：X个
- 建议优化：X个

### 9.3 测试结论
根据测试结果，给出功能是否可以上线的建议。

---

## 10. 测试执行检查清单

### 10.1 测试前准备检查
- [ ] 测试环境Redis服务正常
- [ ] 测试数据准备完成（用户账号、管理员账号）
- [ ] 各端测试环境部署最新代码
- [ ] 强制下线功能开关已开启
- [ ] 管理员权限配置正确
- [ ] 前端拦截器配置正确

### 10.2 核心功能检查
- [ ] TC001：旧密码修改强制下线 ✅/❌
- [ ] TC002：手机验证码重置密码强制下线 ✅/❌
- [ ] TC003：邮箱验证码重置密码强制下线 ✅/❌
- [ ] TC004：管理员强制下线单个用户 ✅/❌
- [ ] TC005：批量强制下线用户 ✅/❌
- [ ] TC006：搜索用户功能 ✅/❌
- [ ] TC007：查看强制下线日志 ✅/❌

### 10.3 兼容性检查
- [ ] TC008：老用户首次强制下线 ✅/❌
- [ ] TC009：新老用户混合场景 ✅/❌

### 10.4 异常场景检查
- [ ] TC010：Redis连接失败处理 ✅/❌
- [ ] TC011：Redis数据异常处理 ✅/❌
- [ ] TC012：高并发强制下线 ✅/❌
- [ ] TC013：批量操作性能测试 ✅/❌

### 10.5 前端集成检查
- [ ] TC014：PC端403错误处理 ✅/❌
- [ ] TC015：H5端403错误处理 ✅/❌
- [ ] TC016：小程序端403错误处理 ✅/❌

### 10.6 权限控制检查
- [ ] TC017：非管理员权限测试 ✅/❌
- [ ] TC018：管理员权限边界测试 ✅/❌

### 10.7 性能指标检查
- [ ] TC019：接口响应时间测试 ✅/❌
- [ ] TC020：并发认证检查测试 ✅/❌

### 10.8 回归测试检查
- [ ] 原有登录功能正常
- [ ] 原有认证流程正常
- [ ] 密码修改功能正常
- [ ] 后台管理功能正常

### 10.9 上线前最终检查
- [ ] 所有测试用例执行完成
- [ ] 严重和阻塞缺陷已修复
- [ ] 性能指标达到要求
- [ ] 前端各端集成正常
- [ ] 监控和告警配置完成
- [ ] 应急预案准备完成

---

## 11. 常见问题排查指南

### 11.1 功能异常排查
**问题**：强制下线不生效
**排查步骤**：
1. 检查Redis中版本号是否正确更新
2. 检查JWT/Session中版本号是否正确
3. 检查认证中间件是否正确调用检查逻辑
4. 查看相关错误日志

**问题**：前端没有正确处理403错误
**排查步骤**：
1. 检查前端拦截器配置
2. 检查403响应的错误信息格式
3. 检查前端错误处理逻辑
4. 验证本地存储清除逻辑

### 11.2 性能问题排查
**问题**：认证检查耗时过长
**排查步骤**：
1. 检查Redis响应时间
2. 检查网络连接状况
3. 分析认证逻辑的性能瓶颈
4. 考虑增加缓存或优化查询

### 11.3 数据一致性问题排查
**问题**：版本号不一致
**排查步骤**：
1. 检查并发操作的原子性
2. 验证Redis事务处理
3. 检查异常情况的回滚逻辑
4. 分析日志记录的完整性

---

*本测试用例覆盖了强制下线功能的所有核心场景，请测试团队按照用例执行测试并记录结果。如有问题请及时反馈给开发团队。*
