# ReadMe

### 禅道bug(罗列bug地址或者id)

***

### 参与人员

- 单文超

***

### 分支

|       仓库        |    bug分支     | 备注 |
|:---------------:|:------------:|:--:|
| new_gaoxiao_yii | hotfix/人才库优化 | -  |

***

### 上线部署步骤

`内容为空,填写"-"即可`

| 步骤                       | 是否执行 | 执行内容                |
|:-------------------------|:----:|:--------------------|
| 提醒前端提前合并代码到master和添加权限节点 |  -   | -                   |
| 执行sql语句                  |  是   | 见下方"执行sql语句"        |
| 更新后端代码                   |  是   | -                   |
| composer安装               |  -   | 见下方"composer安装"     |
| 更新配置                     |  -   | 见下方"更新配置"           |
| 创建队列                     |  -   | 见下方"创建队列"           |
| 执行脚本                     |  -   | 见下方"执行脚本"           |
| 删除redis缓存                |  -   | 见下方"删除redis缓存"      |
| 重启队列                     |  -   | 上线前, 暂停所有队列, 上线后再重启 |
| 更新前端代码                   |  是   | -                   |
| 添加定时任务                   |  -   | 见下方"定时任务"           |
| 群内通知部署完毕                 |  -   | -                   |

#### 执行sql语句(按顺序执行)

- alter_data.sql

#### 脚本

- php timer_yii script/update-resume-talent-pool (一次性更新所有简历的是否人才库)
- php timer_yii script/update-resume-type (一次性更新所有简历类型 5分钟执行一次5000)