<?php
namespace frontendPc\components;

use common\base\BaseActiveRecord;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomePosition;
use common\base\models\BaseShowcase;
use common\base\models\BaseSystemConfig;
use Faker\Provider\Base;
use Yii;
use yii\base\Widget;

/**
 * Web
 * 二级栏目页
 */
class ReservedWidget extends BaseWidget
{
    public $list;
    public $columnId;
    public $limit = 1;

    public function init()
    {
        parent::init();
        $name       = BaseHomeColumn::carryColumnToPinyin($this->columnId);
        $name       = $name . "_Z1";
        $positionId = BaseHomePosition::findOneVal(['number' => $name], 'id');
        $showList   = BaseShowcase::getByPositionConfig($positionId, $name, $this->limit);
        foreach ($showList as &$item) {
            $item['targetLink'] = $item['target_link'];
            $item['imageLink']  = $item['image_link'];
        }
        $this->list = $showList;
        
        if (sizeof($this->list) < 1) {
            // $this->list = [
            //     0 => [
            //         'target_link' => 'https://wj.qq.com/s2/11217885/ad63/',
            //         'image_link'  => 'https://img.gaoxiaojob.com/uploads/static/image/20221221/1.png',
            //     ],
            // ];
            $this->list = [
                0 => BaseSystemConfig::getPcSecondColumnReserved(),
            ];
        }
    }

    public function run(): string
    {
        return $this->render('reserved.html', [
            'list' => $this->list,
        ]);
    }
}