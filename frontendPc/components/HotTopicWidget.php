<?php
namespace frontendPc\components;

use common\base\models\BaseFile;
use common\base\models\BaseSystemConfig;
use common\helpers\FileHelper;
use common\helpers\StringHelper;
use common\helpers\UrlHelper;
use common\libs\Cache;
use yii\base\Widget;
use yii;

class HotTopicWidget extends BaseWidget
{

    private $positionKey = 'hotTopic';

    private $list = [];
    private $top  = [];

    public function init()
    {
        parent::init();
        // 拿配置文件做为list
        // $key = Cache::PC_HOME_TOPIC_KEY;
        // if (Cache::get($key)) {
        //     $list = json_decode(Cache::get($key), true);
        // } else {
        //     $list = Yii::$app->params['homePosition']['otherShowcase'][$this->positionKey];
        //     foreach ($list as &$item) {
        //         $item['img'] = FileHelper::getFullUrl($item['img'], BaseFile::PLATFORM_TYPE_LOCAL);
        //         $item['url'] = UrlHelper::fix($item['url']);
        //     }
        //
        //     // 暂时缓存7200
        //     Cache::set($key, json_encode($list), 7200);
        // }

        $list = BaseSystemConfig::getPcHomeHotTopic();

        $this->top  = $list[0];
        $this->list = array_slice($list, 1);
    }

    public function run()
    {
        return $this->render('hot_topic.html', [
            'top'  => $this->top,
            'list' => $this->list,
        ]);
    }
}