<?php
namespace frontendPc\components;

use admin\models\Showcase;
use common\libs\Cache;
use frontendPc\models\HomePosition;
use Yii;
use yii\base\Widget;

class HomeHotCompanyWidget extends BaseWidget
{

    private $areaList = [];
    private $typeList = [];

    public function init()
    {
        parent::init();

        $key = Cache::PC_HOME_HOT_COMPANY_KEY;

        if (Cache::get($key)) {
            $data = json_decode(Cache::get($key), true);
        } else {
            $config   = Yii::$app->params['homePosition']['otherShowcase']['hotCompany'];
            $areaList = $config['area'];
            $typeList = $config['type'];
            foreach ($areaList as $k => &$item) {
                $positionId   = HomePosition::findOneVal([
                    'number'        => $item['number'],
                    'status'        => HomePosition::STATUS_ACTIVE,
                    'platform_type' => HomePosition::PLATFORM_PC_HOME,
                ], 'id');
                $data         = Showcase::getByPositionConfig($positionId, $item['number']);
                $item['list'] = $data;
            }
            foreach ($typeList as $k => &$item) {
                $positionId   = HomePosition::findOneVal([
                    'number'        => $item['number'],
                    'status'        => HomePosition::STATUS_ACTIVE,
                    'platform_type' => HomePosition::PLATFORM_PC_HOME,
                ], 'id');
                $data         = Showcase::getByPositionConfig($positionId, $item['number']);
                $item['list'] = $data;
            }
            $data = [
                'areaList' => $areaList,
                'typeList' => $typeList,
            ];

            Cache::set($key, json_encode($data), 3600);
        }

        $this->areaList = $data['areaList'];
        $this->typeList = $data['typeList'];
    }


    public function run()
    {
        // 加载js
        $this->getView()->registerJsFile('/static/js/index.js?v=0.5');


        return $this->getView()->render('@frontendPcWidgetView/home_hot_company.html', [
            'areaList' => $this->areaList,
            'typeList' => $this->typeList,
        ]);
    }
}