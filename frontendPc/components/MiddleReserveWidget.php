<?php
namespace frontendPc\components;

use common\base\BaseActiveRecord;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomePosition;
use common\base\models\BaseShowcase;
use Yii;
use yii\base\Widget;

class MiddleReserveWidget extends BaseWidget
{

    public $list;
    public $columnId;
    public $limit = 8;

    public function init()
    {
        parent::init();

        $name       = BaseHomeColumn::carryColumnToPinyin($this->columnId);
        $name       = $name . "_redianzixun";
        $positionId = BaseHomePosition::findOneVal(['number' => $name], 'id');
        $this->list = BaseShowcase::getByPositionConfig($positionId, $name, $this->limit);
    }

    public function run(): string
    {
        return $this->render('middle_reserve.html', [
            'list' => $this->list,
        ]);
    }
}