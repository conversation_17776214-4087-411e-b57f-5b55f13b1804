<?php
namespace frontendPc\components;

use common\base\models\BaseSystemConfig;
use common\helpers\ArrayHelper;
use common\helpers\StringHelper;
use common\helpers\UrlHelper;
use common\models\Article;
use common\models\News;
use frontendPc\models\HomeColumn;
use yii\base\Widget;
use Yii;

class HeadNavWidget extends BaseWidget
{

    private $navConfig;
    private $province;
    private $city;

    private $isHome = true;

    public function init()
    {
        parent::init();

        $navConfig = Yii::$app->params['homeNav'];
        $area      = Yii::$app->params['homeNavArea'];

        $this->province = $this->supplementUrl($area['province']);
        $this->city     = $this->supplementUrl($area['city']);

        // 当前的控制器
        $controller = \Yii::$app->controller->id;
        // 当前的action
        $action = \Yii::$app->controller->action->id;
        // 如果是栏目页
        if ($controller == 'home' && $action == 'column') {
            $id             = Yii::$app->request->get('id');
            $column         = HomeColumn::find()
                ->select('id,parent_id,id')
                ->where(['id' => $id])
                ->one();
            $activeColumnId = $column->parent_id ?: $column->id;
        } elseif ($controller == 'news' && $action == 'detail') {
            // 这里是资讯详情页
            $activeColumnId = HomeColumn::NEWS_TOP_ID;
        }

        foreach ($navConfig as $k => $item) {
            if ($item['url']) {
                $url = UrlHelper::fix($item['url']);
            } elseif ($item['variable']) {
                $value = $item['variable'];
                $url   = $this->getVariableValue($value);
            } elseif (isset($item['action']) && $item['action']) {
                $action = $item['action'];
                $url    = UrlHelper::$action();
            } else {
                $url = HomeColumn::getDetailUrl($item['id']);
            }

            if ($item['id'] && $item['id'] == $activeColumnId) {
                $active       = true;
                $this->isHome = false;
            } else {
                $active = false;
            }
            if ($item['children']) {
                // 这里如果index=0,也就是人才专栏,就需要特殊处理
                if ($k == 0 && BaseSystemConfig::getPcHomeNavTalentTag()) {
                    $item['children'] = array_merge($item['children'], BaseSystemConfig::getPcHomeNavTalentTag());
                }
                $children = [];
                foreach ($item['children'] as $child) {
                    if ($child['url']) {
                        $children[] = [
                            'name' => $child['name'],
                            'url'  => UrlHelper::fix($child['url']),
                        ];
                    } elseif ($child['variable']) {
                        $value      = $child['variable'];
                        $children[] = [
                            'name' => $child['name'],
                            'url'  => $this->getVariableValue($value),
                        ];
                    } elseif (isset($child['action']) && $child['action']) {
                        $childItem  = $child['action'];
                        $children[] = [
                            'name' => $child['name'],
                            'url'  => UrlHelper::$childItem(),
                        ];
                    } else {
                        $children[] = [
                            'name' => $child['name'],
                            'url'  => HomeColumn::getDetailUrl($child['id']),
                        ];
                    }
                }
                $list[] = [
                    'name'     => $item['name'],
                    'url'      => $url,
                    'children' => $children,
                    'active'   => $active,
                    'class'    => $item['class'] ?? '',
                ];
            } else {
                $list[] = [
                    'name'   => $item['name'],
                    'active' => $active,
                    'url'    => $url,
                ];
            }
        }

        $this->navConfig = $list;
    }

    public function supplementUrl($list)
    {
        foreach ($list as &$item) {
            $item['url'] = HomeColumn::getDetailUrl($item['id']);
        }

        return $list;
    }

    public function getVariableValue($variable)
    {
        switch ($variable) {
            case 'haiHaiHome':
                return UrlHelper::getHaiwaiHome();
            case 'chuHaiUrl':
                return UrlHelper::getHaiwaiChuhai();
            case 'guiGuoUrl':
                return UrlHelper::getHaiwaiGuiguo();
            case 'qiuXianUrl':
                return UrlHelper::getHaiwaiQiuxian();
            case 'haiYouUrl':
                return UrlHelper::getHaiwaiYouqing();
            case 'danWeiUrl':
                return UrlHelper::getHaiwaiCompany();
            default:
                return '';
        }
    }

    public function run()
    {
        return $this->render('head_nav.html', [
            'list'     => $this->navConfig,
            'isHome'   => $this->isHome,
            'province' => $this->province,
            'city'     => $this->city,

        ]);
    }
}