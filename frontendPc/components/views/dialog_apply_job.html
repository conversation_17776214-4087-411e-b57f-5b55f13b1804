<link rel="stylesheet" href="/static/css/applyDialog.css">
<script src="/static/js/ready.js"></script>

<div id="applyDialogTemplate" class="apply-dialog-template" v-cloak>
    <el-dialog v-model="dialogVisible" title="简历投递" :center="true" @close="close">
        <el-form :model="resumeFormData" label-width="70px" ref="applyFormRef">
            <div class="education-tips" v-if="applyDialogData.systemTips">
                <div class="tips-content">{{applyDialogData.systemTips}}</div>
            </div>
            <el-form-item label="投递岗位" v-if="isAnnouncement" prop="jobId">
                <el-select v-model="resumeFormData.jobId" placeholder="选择职位" @change="changeJob">
                    <el-option v-for="{id, name ,type} in announcementJobList" :key="id" :label="name" :value="id"
                        :disabled="type"> </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="在线简历" class="form-item-resume-online">
                <div class="resume-status">
                    <div class="resume-status-tips">{{applyDialogData.resumeTips}}</div>
                    <div class="resume-operate">
                        <a href="/member/person/resume" class="color-primary" target="_blank">完善</a>
                        <a href="/member/person/resume/preview" class="color-primary" target="_blank">预览</a>
                    </div>
                </div>
            </el-form-item>

            <el-form-item label="附件简历" class="form-item-resume" prop="token">
                <div class="resume-options">
                    <el-select v-model="resumeFormData.token" placeholder="选择附件简历" :clearable="true" :disabled="checkAnnoucementDisabled">
                        <el-option v-for="{label, value} in applyDialogData?.resumeAttachmentList" :key="value"
                            :label="label" :value="value"> </el-option>
                    </el-select>

                    <el-upload class="resume-upload" :action="resumeUploadUrl" :show-file-list="false" :disabled="checkAnnoucementDisabled"
                        :on-success="uploadResumeSuccess">
                        <span class="color-primary">+ 上传</span>
                    </el-upload>
                </div>
            </el-form-item>

            <el-form-item label="应聘材料" class="form-item-attachment">
                <div class="file-tips" v-if="applyDialogData.resumeFileTips" v-html="applyDialogData.resumeFileTips">
                </div>
                <el-upload class="resume-upload" :action="attachmentUploadUrl" :limit="limit" ref="attachUploadRef"
                    :file-list="resumeFormData.stuffFileId" :on-success="uploadAttachSuccess" :disabled="checkAnnoucementDisabled"
                    :on-error="uploadAttachError" :on-remove="uploadAttachRemove" :on-exceed="uploadAttachExceed"
                    :before-upload="beforeFileUpload" :on-progress="handleProgress">
                    <el-button class="upload-btn" type="primary" size="small" :disabled="checkAnnoucementDisabled">+ 上传文件</el-button>
                </el-upload>
            </el-form-item>

            <el-form-item v-if="applyDialogData.deliveryTopIsShow" label="投递置顶" class="form-item-add-top"
                prop="deliveryTopIsCheckBox">
                        <el-checkbox class="apply-add-top" v-model="applyDialogData.deliveryTopIsCheckBox" :true-label="1" :false-label="2" :disabled="applyDialogData.deliveryTopIsCheckBoxDisabled">
                    <div class="option-label" v-html="applyDialogData.deliveryTopTips"></div>
                </el-checkbox>
                <a v-if="applyDialogData.deliveryTopButton" href="/job-fast.html" class="go-pay" target="_blank">立即续费</a>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="close">取消</el-button>
                <el-button type="primary" :loading="confirmLoading" @click="handleDeliver" :disabled="checkAnnoucementDisabled"> 投递 </el-button>
            </div>
            <div class="dialog-tips" v-if="applyDialogData.unCooperateEmailTips"
                v-html="applyDialogData.unCooperateEmailTips"></div>
        </template>
    </el-dialog>
</div>

<script defer>
    // 这里总是报错,没有jquery或者vue,所以在这里动态判断一下
    if (typeof jQuery === 'undefined') {
        var script = document.createElement('script');
        script.src = '//img.gaoxiaojob.com/uploads/static/lib/jquery/jquery.min.js';
        document.body.appendChild(script);
    }
    if (typeof Vue === 'undefined') {
        var script = document.createElement('script');
        script.src = '//img.gaoxiaojob.com/uploads/static/lib/vue/vue.min.js';
        document.body.appendChild(script);
    }

    readyRun(function () {
           const applyDialogOptions = {
            data() {
                return {
                    isAnnouncement: false,
                    dialogVisible: false,
                    limit: 5,
                    announcementApi: '/api/person/announcement/check-announcement-apply',
                    beforeApi: '/api/person/member/check-user-apply-status',
                    applyApi: '/api/person/job/apply',
                    resumeUploadUrl: '/api/person/resume/upload',
                    attachmentUploadUrl: '/api/upload/resume-attachment',
                    announcementJobList: [],
                    resumeFormData: {
                        jobId: '',
                        token: '',
                        stuffFileId: [],
                    },
                    successCallback: null,
                    applyDialogData: {},
                    toastDialogData: {}
                }
            },

            computed: {
                checkAnnoucementDisabled() {
                    return this.resumeFormData.jobId === ''
                }
            },

            methods: {
                async getJobList(announcementId) {
                    const { toastDialogData, resumeInfo, jobList } = await httpPost(this.announcementApi, { announcementId })

                    if (Object.keys(toastDialogData).length !== 0) {
                        this.showTipsDialog(toastDialogData)
                        return
                    }

                    // 公告的时候要展示简历完善度，赋值在resumeFileTips展示

                    this.applyDialogData.resumeTips = resumeInfo.completeText

                    this.announcementJobList = jobList

                    const jobId = this.announcementJobList?.map((item) => item.id)[0]
                    // story#912
                    this.announcementJobList?.forEach((item) => {
                        if (item.active && !item.type) {
                            this.resumeFormData.jobId = item.id
                        }
                    }) 

                    jobId ? (this.dialogVisible = true) : this.$message.error('暂无职位可投递，敬请期待')

                    if (this.resumeFormData.jobId) {
                        this.showApplyDialog(jobId)
                    }
                },

                // 页面调用申请之前
                beforeApply(id, callback) {
                    this.isAnnouncement = false
                    this.successCallback = callback
                    this.showApplyDialog(id)
                },

                showTipsDialog(data) {
                    const { title, content, confirmButtonText, cancelButtonText, link , isSendRequest } = data

                    this.$confirm(content, title, { confirmButtonText, cancelButtonText, buttonSize: 'large' })
                        .then(() => {
                            window.open(link)

                            if (isSendRequest) {
                                httpPost(this.applyApi, { jobId: this.resumeFormData.jobId })
                            }
                        })
                        .catch(() => { })
                },

                async showApplyDialog(id) {
                    this.resumeFormData.jobId = id ? id : ''


                    const { toastDialogData, applyDialogData } = await httpPost(this.beforeApi, { jobId: id })

                    if (
                        Object.keys(toastDialogData).length === 0 &&
                        Object.keys(applyDialogData).length === 0
                    ) {
                        this.handleDeliver()
                        return
                    }

                    if (Object.keys(toastDialogData).length !== 0) {
                        const { isAutoJump, isSendRequest, link } = toastDialogData

                        if (isAutoJump) {
                            if (isSendRequest) {
                                httpPost(this.applyApi, { jobId: id })
                            }
                            this.dialogVisible = false
                            window.open(link, '_blank')
                            return
                        }

                        this.showTipsDialog(toastDialogData)

                        // 切换职位如果是站外需要清空一下上一个的applyDialogData，resumeTips除外
                        const { resumeTips } = this.applyDialogData

                        this.applyDialogData = { resumeTips }
                        this.toastDialogData = toastDialogData
                        return
                    }

                    this.applyDialogData = applyDialogData
                    this.toastDialogData = {}
                    this.dialogVisible = true
                },

                async announcementApply(id) {
                    this.isAnnouncement = true
                    this.getJobList(id)
                },

                async handleDeliver() {
                    const { isSendRequest } = this.toastDialogData

                    if (isSendRequest && isSendRequest === true) {
                        this.showTipsDialog(this.toastDialogData)
                        return
                    }

                    const {
                        resumeFormData: { stuffFileId, ...data }
                    } = this
                    const postData = { ...data, stuffFileId: '', deliveryTopIsCheckBox: this.applyDialogData?.deliveryTopIsCheckBox }

                    if (this.isAnnouncement && !postData.jobId) {
                        this.$message.error('请先选择投递岗位')
                        return
                    }

                    this.confirmLoading = true

                    if (stuffFileId.length) {
                        const result = stuffFileId.reduce((previous, current) => {
                            previous.push(current?.response?.data?.id)
                            return previous
                        }, [])
                        postData.stuffFileId = result.join()
                    }

                    httpPost(this.applyApi, postData)
                        .then((res) => {
                            const { toastType } = res


                            toastType === 2
                                ? window.globalComponents.successDialogApplyJob.showSuccessDialog(res)
                                : window.globalComponents.SuccessDialogAlertComponent.showSuccessDialogAlert(res, this.resumeFormData.jobId)

                            this.successCallback && this.successCallback(res)
                            this.close()
                        })
                        .catch((error) => {
                            this.confirmLoading = false
                        })
                },

                uploadResumeSuccess(response, file, fileList) {
                    const { data, msg, result } = response

                    if (result === 1) {
                        const { resumeAttachmentList } = this.applyDialogData
                        const { name, token } = data
                        this.applyDialogData.resumeAttachmentList = [...resumeAttachmentList, { label: name ? name : file.name, value: token }]
                        this.resumeFormData.token = token
                    } else {
                        this.$message.error(msg)
                    }
                },

                changeJob(id) {
                    this.showApplyDialog(id)
                },

                uploadAttachSuccess(response, file, fileList) {
                    this.resumeFormData.stuffFileId = fileList
                },

                uploadAttachError(error, file, fileList) {
                    const { msg } = JSON.parse(error.message)
                    this.$message.error(msg)
                },

                uploadAttachRemove(file, fileList) {
                    this.resumeFormData.stuffFileId = fileList
                },

                uploadAttachExceed(files, uploadFiles) {
                    this.$message.warning(`最多只能有${this.limit}份应聘材料，请删除一份后再上传。`)
                },

                beforeFileUpload(rawFile) {
                    if (rawFile.size / 1024 / 1024 > 15) {
                        this.$message.error('限单个文件大小15M以内')
                        return false
                    }
                },

                close() {
                    this.$refs.applyFormRef.resetFields()
                    this.$refs.attachUploadRef.clearFiles()
                    this.resumeFormData.stuffFileId = []
                    this.applyDialogData = {}
                    this.confirmLoading = false
                    this.dialogVisible = false
                },

                handleProgress(e) {
                    e.percent = e.percent.toFixed(0)
                }
            }
        }

        const applyDialogComponent = Vue.createApp(applyDialogOptions).use(ElementPlus).mount('#applyDialogTemplate')

        window.globalComponents = { ...window.globalComponents, applyDialogComponent }

    })
</script>