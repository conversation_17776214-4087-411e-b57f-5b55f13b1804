<link rel="stylesheet" href="/static/css/paymentSuccessDialog.css">

<div id="paymentSuccessDialog" class="payment-dialog-alert-template" v-cloak>
    <el-dialog v-model="wechatVisible" @close="handelWxClose">
        <div class="wechat-container" v-if="showWechatCode">
            <div class="paymentTips">
                <strong>下单成功</strong>
            </div>
            <div class="payment-text">
                <div class="text-mid">
                    <span>{{title}}</span>
                    <span>{{successContent}}</span>
                </div>
                <div class="qr-code" v-loading="loading">
                    <img :src="urCodeUrl" alt="" />
                    <img class="logo" src="https://img.gaoxiaojob.com/uploads/static/image/logo/logo_square.png" alt="" />
                </div>
                <el-button type="primary" @click="removeWechat">我知道了</el-button>
            </div>
        </div>
    </el-dialog>
</div>

<script>
    $(function () {
        const successDialogAlertOptions = {
            data() {
                return {
                    successVisible: false,
                    wechatVisible: false,
                    showWechatCode: true,
                    title: '',
                    timer: null,
                    ticket: '',
                    token: '',
                    successContent: '',
                    urCodeUrl: ''
                }
            },

            methods: {
                paymentSuccessDialogAlert(successData) {
                    const { title, successContent, url, ticket, token } = successData
                    this.title = title
                    this.successContent = successContent
                    this.urCodeUrl = url
                    this.wechatVisible = true
                    if (token !== '' && ticket !== '') {
                        this.ticket = ticket
                        this.token = token
                        this.checkBind()
                    }
                },
                checkBind() {
                    this.closeTimer()
                    this.timer = setInterval(async () => {
                        const rs = await httpPost('/api/member/check-bind-qrcode', {
                            ticket: this.ticket,
                            token: this.token
                        })
                        switch (rs.status) {
                            case 1:
                                this.checkBind()
                                break
                            case 2:
                                this.closeTimer()
                                this.$message({
                                    message: '绑定成功',
                                    type: 'success'
                                })
                                break
                            default:
                                // 异常
                                this.closeTimer()
                                break
                        }
                    }, 2000)
                },
                closeTimer() {
                    if (this.timer) {
                        clearInterval(this.timer)
                        this.timer = null
                    }
                },
                removeWechat() {
                    this.wechatVisible = false
                },
                handelWxClose() {
                    this.wechatVisible = false
                }
            },
            beforeDestroy() {
                this.closeTimer()
            }
        }

        const paymentSuccessDialogAlertComponent = Vue.createApp(successDialogAlertOptions).use(ElementPlus).mount('#paymentSuccessDialog')

        window.globalComponents = { ...window.globalComponents, paymentSuccessDialogAlertComponent }
    })
</script>