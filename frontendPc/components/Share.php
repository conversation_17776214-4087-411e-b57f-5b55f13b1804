<?php
namespace frontendPc\components;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseCompany;
use common\base\models\BaseJob;
use common\libs\WxMiniApp;

class Share extends BaseWidget
{

    private $codeUrl;
    private $id;

    const TYPE_JOB          = 'JOB';
    const TYPE_ANNOUNCEMENT = 'ANNOUNCEMENT';
    const TYPE_COMPANY      = 'COMPANY';
    const TYPE_OTHER        = 'OTHER';

    public function init()
    {
        parent::init();

        $this->setCodeUrl();
    }

    public function setCodeUrl()
    {
        $route = \Yii::$app->controller->route;
        switch ($route) {
            case 'job/detail':
                $id            = \Yii::$app->request->get('id');
                $this->codeUrl = BaseJob::getDetailMiniCode($id);
                break;
            case 'announcement/detail':
                $id            = \Yii::$app->request->get('id');
                $this->codeUrl = BaseAnnouncement::getDetailMiniCode($id);
                break;
            // case 'company/detail':
            //     $id            = \Yii::$app->request->get('id');
            //     $this->codeUrl = BaseCompany::getDetailMiniCode($id);
            //     break;
            default:
                $this->codeUrl = 'https://img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/25.png';
        }
    }

    public function getImageUrl($type)
    {
    }

    public function run()
    {
        // 获取当前所在路由

        return $this->render('share.html', [
            'codeUrl' => $this->codeUrl,
        ]);
    }

}