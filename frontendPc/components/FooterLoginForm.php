<?php

namespace frontendPc\components;

use common\base\models\BaseMember;
use Yii;

class FooterLoginForm extends BaseWidget
{

    // 根据不同的类型显示不同的情况 0 不显示 1显示登录引导,2显示完善度，3显示博士后引导
    public $showType = 0;

    public function run()
    {
        // // 看看简历完善度
        // if ($this->isShow()) {
        //     $user = Yii::$app->params['user'];
        //     if (!$user || $user['type'] == BaseMember::TYPE_COMPANY) {
        //         return $this->render('footer_login_form.html');
        //     }
        //
        //     $completeResumePercent = Yii::$app->params['completeResumePercent'];
        //     if ($user['resumePercent'] < $completeResumePercent) {
        //         // 另外一个去完善
        //
        //         return $this->render('footer_perfect_form.html', ['completeResumePercent' => $completeResumePercent]);
        //     }
        //     // 如果是都已经完善了,就不需要做任何事情了
        // }

        $this->setShowType();
        if ($this->showType == 1) {
            // 显示登录引导
            return $this->render('footer_login_form.html');
        }

        if ($this->showType == 2) {
            // 完善度
            $completeResumePercent = Yii::$app->params['completeResumePercent'];

            return $this->render('footer_perfect_form.html', ['completeResumePercent' => $completeResumePercent]);
        }

        if ($this->showType == 3) {
            // 博士后引导
            return $this->render('footer_postdoctor_guide_form.html');
        }

        return '';
    }

    public function setShowType()
    {
        // 首先有一些路由是直接显示博士后的，也就是showType=2 1、引流悬浮栏展示页面：首页、一级&二级栏目页（www.gaoxiaojob.com/column/xxx.html）、找职位页、找单位页、单位主页（4个tab页都展示）；
        $controller = Yii::$app->controller->route;

        // 2025.04.22 09:00 开始执行下面逻辑  https://zentao.jugaocai.com/index.php?m=story&f=view&id=1139
        if (CUR_DATETIME <= strtotime('2025-06-31 23:59:59')) {
            if ($controller === 'home/column') {
                $columnId = \Yii::$app->request->get('id');
                if (in_array($columnId, [
                    3,
                    247,
                    248,
                    249,
                    250,
                ])) {
                    $this->showType = 3;

                    return true;
                }
            }
        }

        // 判断是否出登录
        if ($controller === 'home/column') {
            // 参数
            $id = \Yii::$app->request->get('id');

            if ($id >= 45 && $id <= 51) {
                $isShowTypeLogin = true;
            }

            if ($id >= 1 && $id <= 9) {
                $isShowTypeLogin = true;
            }

            if ($id >= 11 && $id <= 41) {
                $isShowTypeLogin = true;
            }
            /**
             * 2022-11-09 16:05:11, 由 郑鑫 指派给 龚传栋。
             * 考虑到首页展示效果，进行呈现页面调整：
             *
             * 1.移除首页吸底模块
             *
             * 2.所有学科栏目新增吸底模块（ID119-236）
             */

            if ($id >= 119 && $id <= 236) {
                $isShowTypeLogin = true;
            }
        }

        if ($isShowTypeLogin) {
            $user = Yii::$app->params['user'];
            if (!$user || $user['type'] == BaseMember::TYPE_COMPANY) {
                $this->showType = 1;

                return true;
            }

            $completeResumePercent = Yii::$app->params['completeResumePercent'];
            if ($user['resumePercent'] < $completeResumePercent) {
                // 另外一个去完善

                $this->showType = 2;

                return true;
            }
        }

        return true;
    }

    /**
     * 以下页面删除吸底模块：单位主页、职位列表、职位详情页、公告普页、
     *
     * 新增吸底模块：首页+一级栏目页，栏目ID为1-9 11-41
     * 45-51（人才专场、高校招聘、科研人才、政府与事业单位、中小学校、医学人才、企业招聘、博士后、海归人才、省区栏目（除港澳台）、北京、上海、天津、重庆、广州、深圳、武汉、南京、西安、成都、杭州）
     */
    public function isShow()
    {
        $controllerId = \Yii::$app->controller->id;
        $actionId     = \Yii::$app->controller->action->id;
        $action       = $controllerId . '/' . $actionId;

        // if ($action == 'home/index') {
        //     return true;
        // }

        if ($action === 'home/column') {
            // 参数
            $id = \Yii::$app->request->get('id');

            if ($id >= 45 && $id <= 51) {
                return true;
            }

            if ($id >= 1 && $id <= 9) {
                return true;
            }

            if ($id >= 11 && $id <= 41) {
                return true;
            }
            /**
             * 2022-11-09 16:05:11, 由 郑鑫 指派给 龚传栋。
             * 考虑到首页展示效果，进行呈现页面调整：
             *
             * 1.移除首页吸底模块
             *
             * 2.所有学科栏目新增吸底模块（ID119-236）
             */

            if ($id >= 119 && $id <= 236) {
                return true;
            }
        }

        return false;
    }

}