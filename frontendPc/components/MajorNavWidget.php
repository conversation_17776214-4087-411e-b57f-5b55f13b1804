<?php
namespace frontendPc\components;

use frontendPc\models\HomeColumn;
use Yii;
use yii\base\Widget;

class MajorNavWidget extends BaseWidget
{

    private $list;

    public function init()
    {
        parent::init();

        $list = Yii::$app->params['homeMajorNav'];

        $tmpList = [];
        foreach ($list as $k1 => $item) {
            $tmpList[$k1]['name']  = $item['name'];
            $tmpList[$k1]['class'] = $item['class'];
            $tmpList[$k1]['url']   = $item['url'];
            $tmpList[$k1]['list']  = [];
            foreach ($item['list'] as $k2 => $item2) {
                $tmpList[$k1]['list'][$k2]['name'] = $item2['name'];
                foreach ($item2['list'] as $k3 => $item3) {
                    $tmpList[$k1]['list'][$k2]['list'][$k3]['name'] = $item3['name'];
                    $tmpList[$k1]['list'][$k2]['list'][$k3]['url']  = HomeColumn::getDetailUrl($item3['id']);
                }
            }
        }

        $this->list = $tmpList;
    }

    public function run()
    {
        return $this->render('major_nav.html', ['list' => $this->list]);
    }
}