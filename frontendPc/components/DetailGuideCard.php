<?php

namespace frontendPc\components;

use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeComplete;
use common\helpers\UrlHelper;
use http\Url;
use yii\base\Widget;

class DetailGuideCard extends BaseWidget
{
    public function run()
    {
        $memberId = \Yii::$app->user->id;
        if (!empty($memberId)) {
            //判断用户的建立完善
            //度，如果大于等于75，不展示
            $completePercent = BaseResume::getComplete($memberId);
            if ($completePercent < 75) {
                $url = UrlHelper::toRoute('/member/person/resume');

                return $this->render('guide_card.html', ['url' => $url]);
            }
        }
    }
}