<?php
namespace frontendPc\components;

use common\base\models\BaseFile;
use common\helpers\FileHelper;
use common\helpers\StringHelper;
use common\helpers\UrlHelper;
use common\libs\Cache;
use frontendPc\models\Article;
use frontendPc\models\ArticleAttribute;
use frontendPc\models\News;
use yii\base\Widget;
use yii;

class CIAWidget extends BaseWidget
{

    private $top  = [];
    private $list = [];

    public function init()
    {
        parent::init();
        // 拿配置文件做为list

        // 拿标识为系列的资讯

        $key = Cache::PC_HOME_CIA_KEY;

        if (Cache::get($key)) {
            $list = json_decode(Cache::get($key), true);
        } else {
            $list = Article::find()
                ->alias('a')
                ->select('title,a.type,c.id,cover_thumb')
                ->innerJoin(['b' => ArticleAttribute::tableName()], 'a.id=b.article_id')
                ->innerJoin(['c' => News::tableName()], 'a.id=c.article_id')
                ->where([
                    'b.type'      => ArticleAttribute::ATTRIBUTE_SERIES,
                    'a.is_delete' => Article::IS_DELETE_NO,
                    'c.status'    => News::STATUS_ACTIVE,
                    'a.is_show'   => Article::IS_SHOW_YES,
                    'a.type'      => Article::TYPE_NEWS,
                ])
                ->orderBy('b.sort_time desc')
                ->limit(10)
                ->asArray()
                ->all();

            foreach ($list as &$item) {
                $item['url']   = News::getDetailUrl($item['id']);
            }

            Cache::set($key, json_encode($list), 3600);
        }

        $this->top  = array_slice($list, 0, 1)[0];
        $this->list = array_slice($list, 1, 9);;
    }

    public function run()
    {
        return $this->render('CIA.html', [
            'list' => $this->list,
            'top'  => $this->top,
        ]);
    }
}