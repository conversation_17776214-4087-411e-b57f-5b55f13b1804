<link rel="stylesheet" href="/static/css/announcement.css?v=20241231">
<link rel="stylesheet" href="/static/css/newMediaGroup.css?v=20241230">

<div id="component">
    <div class="el-main">
        <div class="detail-container announcement">
            <div class="detail-header">
                <div class="detail-header-container">
                    <div class="breadcrumb">
                        位置：
                        <a href="/">高校人才网</a>＞
                        <?php foreach($info['columnInfo'] as $item):?>
                        <a href="<?= $item['url'] ?>"><?= $item['name'] ?></a>＞
                        <?php endforeach;?>
                        <a href="<?= $info['announcementUrl']?>"><?= $info['title']?></a>
                    </div>

                    <div class="main">
                        <section>
                            <div class="title" title="<?=\common\helpers\StringHelper::changeQuotationMark($info['title'])?>">
                                <h1 title="<?=\common\helpers\StringHelper::changeQuotationMark($info['title'])?>"><?=$info['title']?></h1>
                            </div>

                            <div class="info">
                                <?php if($info['establishmentTypeText']):?><span class="establishment-tag"><?= $info['establishmentTypeText']?></span><?php endif?>

                                共计<span class="color-primary"><?=$info['jobAmount']?></span>个岗位，招
                                <span class="color-primary"><?=$info['jobRecruitAmount']?></span>人
                                <a class="view-relation" href="<?=$jobListUrl?>">查看此公告的职位列表</a>

                            </div>

                            <div id="boonsTemplate" class="boons">
                                <?php if($info['establishmentTypeText']):?><span class="establishment-tag"><?= $info['establishmentTypeText']?></span><?php endif?>

                                <?php if(count($info['allWelfareLabel']) > 0):?>
                                <?php foreach($info['allWelfareLabel'] as $k=> $item):?>
                                <?php if($k < $welfareLabelViewAmount):?>
                                <span class="boon"><?=$item?></span>
                                <?php endif;?>
                                <?php endforeach?>
                                <?php endif;?>

                                <?php if(count($info['allWelfareLabel']) > $welfareLabelViewAmount):?>
                                <el-popover placement="bottom" :width="430" trigger="hover" v-cloak>
                                    <template #reference>
                                        <i class="el-icon boon-more" style="--font-size: 12px;">
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                                                <path fill="currentColor"
                                                      d="M176 416a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224z">
                                                </path>
                                            </svg>
                                        </i>
                                    </template>
                                    <!-- <span class="boon">全勤奖</span> -->
                                    <?php foreach($info['allWelfareLabel'] as $k=> $item):?>
                                    <?php if($k >= $welfareLabelViewAmount):?>
                                    <span class="boon"><?=$item?></span>
                                    <?php endif;?>
                                    <?php endforeach?>
                                </el-popover>
                                <?php endif;?>
                            </div>
                        </section>

                        <aside>
                            <div class="announcement emit">

                                <div class="detail-button announcement-button">
                                    <div class="share-mini-code-container">
                                        <div class="share-mini-code-trigger share-mini-code-trigger--primary">分享</div>

                                        <div class="share-mini-code-popup">
                                            <div class="share-mini-code">
                                                <img src="<?=$shareUrlCode?>" alt="" class="share-mini-code-img" />
                                            </div>
                                            <div class="share-mini-code-title">微信扫一扫</div>
                                            <div class="share-mini-code-tips">分享给你的朋友吧</div>
                                        </div>
                                    </div>
                                    <?php if($info['isCollect'] == 1):?>
                                    <div class="el-button--collect collected">已收藏</div>
                                    <?php else:?>
                                    <div class="el-button--collect">收藏</div>
                                    <?php endif?>
                                    <div class="el-button--analyse see-heat">公告热度</div>
                                </div>
                                    <?php if($info['status'] == 1):?>
                                    <button class="announcement el-button el-button--primary el-button--apply">
                                        <span>立即投递</span>
                                    </button>
                                    <?php  elseif($info['status'] == 2):?>
                                    <button class="el-button el-button&#45;&#45;info is-plain is-disabled" disabled>
                                        <span>已下线</span>
                                    </button>
                                    <?php endif;?>
                            </div>

                        </aside>

                        <a href="<?=$jobListUrl?>" class="el-button el-button--primary view-button">
                            <span>查看此公告的职位列表</span>
                        </a>
                    </div>
                </div>
            </div>
                <div class="detail-main">
                <section>
                    <div class="section-main">
                    <div class="detail-subtitle is-basic">基本信息</div>

                    <div class="detail-list">
                        <div class="detail-item">
                            <ul class="detail-content el-row announcement">
                                <li class="el-col">发布时间：<?=$info['refreshTime']?></li>
                                <li class="el-col">截止日期：<?=$info['periodDate']?></li>
                                <li class="el-col">学历要求：<?=$info['minEducation']?></li>

                                <li class="el-row">
                                    <div class="el-col el-col-8 show-complete is-complete">
                                        <span class="label">所属省份：</span>
                                        <div class="value"><?php foreach($info['provinceList'] as $list){?><a href="<?=$list['url']?>" target="_blank" class="color-primary"><?php echo $list['provinceText'];?></a><?php }?></div>
                                    </div>

                                    <div class="el-col el-col-16 show-complete">
                                        <span class="label">工作地点：</span>
                                        <div class="value"><?php foreach($info['cityList'] as $list1){?><a href="<?=$list1['url']?>" target="_blank" class="color-primary"><?php echo $list1['cityText'];?></a><?php }?></div>
                                    </div>
                                </li>

                                <li class="el-row">
                                    <div class="el-col el-col-8 show-complete is-complete">
                                        <span class="label">报名方式：</span>
                                        <div class="value"><?=$info['applyTypeText']?></div>
                                    </div>

                                    <div class="el-col el-col-16 show-complete">
                                        <span class="label">栏目分类：</span>
                                        <div class="value"><?php foreach($columnInfo as $item):?><a href="<?=$item['url']?>" class="color-primary" target="_blank"><?=$item['name']?></a><?php endforeach;?>
                                        </div>
                                    </div>
                                </li>


                                <li class="el-row major-row">
                                    <div class="el-col show-complete">
                                        <span class="label">需求学科（供参考）：</span>

                                        <div class="value" id="major-content">
                                            <?php if($info['majorName']){?>
                                            <?php echo implode(', ', array_map(function($item) { return '<a href="' . $item['url'] . '" target="_blank" class="color-primary">' . $item['major'] . '</a>'; }, $info['majorName'])); ?>
                                            <?php }?>
                                            <?php if(!$info['majorName']){?>详见正文<?php }?>                                        </div>
                                    </div>

                                    <div id="major-trigger" class="bottom is-hidden">
                                        <a href="javascript:;" class="show">展开</a>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="detail-subtitle is-detail">公告详情</div>

                    <div class="detail-list">
                        <div class="detail-main-content">
                            <?=$info['content']?>
                        </div>

                        <div class="feedback-link">
                            <?php if ($info['is_cooperation'] != 1): ?>
                            <div class="feedback-source-tips">
                                <a class="color-primary" href="javascript:;">附件/来源打开异常提示</a>
                                <p>附件下载异常：因平台URL协议兼容性原因，公告正文附件材料可能存在无法下载情况，届时您可复制附件链接并新建页面打开查看。</p>
                                <p>来源链接打开异常：若文中“来源链接XXX”无法正常打开，属内容来源网站异常情况，本平台无法监控处理，如有需求，可持续关注相应链接修复情况。</p>
                            </div>
                            <?php endif; ?>

                            <a class="color-primary" href="https://wj.qq.com/s2/14613633/51bf/" target="_blank">[我要纠错]</a>

                            <script>
                                $(function () {
                                    $('.feedback-source-tips').on('click', 'a', function () {
                                        $(this).parent().toggleClass('is-extend')
                                    })
                                })
                            </script>
                        </div>
                    </div>
                    <?php if(!empty($info['fileList'])):?>
                    <div class="detail-subtitle is-file">附件下载</div>

                    <div class="file-list">
                        <?php foreach($info['fileList'] as $item):?>
                        <el-popover placement="bottom" :width="300" trigger="hover" content="<?=$item['name']?>">
                            <template #reference>
                                <a href="<?=$item['path']?>" download="<?=$item['name']?>" class="file" target="_blank">
                                    <div class="<?=$item['suffix']?>"></div>
                                    <div class="file-name"><?=$item['name']?></div>
                                </a>
                            </template>
                        </el-popover>
                        <?php endforeach;?>
                    </div>
                    <?php endif;?>

                    <!-- 未查看公告 start -->
                    <?php if(empty($heatInfo['hasRecord'])):?>
                    <div class="detail-subtitle is-heat heat see-heat">
                        <div>公告热度</div>
                        <div class="open-heat">解锁详细分析</div>
                    </div>

                    <div class="power-heat">
                        <div class="text-heat">该公告在同类公告中的热度为 <span class="active">***</span>，目前已有 <span class="active">***</span> 对其非常感兴趣</div>
                    </div>
                    <?php endif;?>
                    <!-- 未查看该公告 end -->

                    <!-- 已查看公告 start -->
                    <?php if($heatInfo['hasRecord']):?>
                    <div class="detail-subtitle is-heat heat">
                        <div>公告热度</div>
                        <div class="open-heat">
                            <a href="<?= $heatInfo['reportUrl']?>" target="_blank">查看详细分析</a>
                        </div>
                    </div>

                    <div class="power-heat">
                        <div class="text-heat">该公告在同类公告中的热度为 <span><?= $heatInfo['data']['heat_name']?></span>，目前已有 <span><?= $heatInfo['data']['announcement_click_txt']?></span> 对其非常感兴趣</div>
                    </div>
                    <?php endif;?>
                    <!-- 已查看公告 end -->

                    <div class="detail-emit">
                        <a href="<?=$jobListUrl?>" class="el-button el-button--primary">
                            <span>查看此公告的职位列表</span>
                        </a>
                        <?php if($info['status'] == 1):?>
                        <!--正常-->
                        <button class="announcement el-button el-button--primary el-button--apply">
                            <span>立即投递</span>
                        </button>
                        <?php elseif($info['status'] == 2):?>
                        <!--下线-->
                        <button class="el-button el-button--info is-plain is-disabled" disabled>
                            <span>已下线</span>
                        </button>
                        <?php endif;?>

                    </div>

                        <?= frontendPc\components\QrcodeScanWidget::widget() ?>

<!--                    <div class="share-custom">-->
<!--                        <div class="sina-weibo">-->
<!--                            <html xmlns:wb="https://open.weibo.com/wb">-->
<!--                            <wb:follow-button uid="3702192203" type="red_3" width="100%" height="24">-->
<!--                            </wb:follow-button>-->
<!--                        </div>-->
<!--                        <div>-->
<!--                            <div class="bshare-custom">-->
<!--                                <a title="分享到微信" class="bshare-weixin"></a>-->
<!--                                <a title="分享到新浪微博" class="bshare-sinaminiblog"></a>-->
<!--                                <a title="分享到QQ空间" class="bshare-qzone"></a>-->
<!--                                <a title="分享到Facebook" class="bshare-facebook"></a>-->
<!--                                <a title="分享到Twitter" class="bshare-twitter"></a>-->
<!--                                <a title="更多平台" onclick="javascript:bShare.more(event);return false;" class="custom-more-btn">-->
<!--                                </a>-->
<!--                                <?= frontendPc\components\Share::widget() ?>-->
<!--&lt;!&ndash;                                <span class="BSHARE_COUNT bshare-share-count">0</span>&ndash;&gt;-->
<!--                            </div>-->
<!--&lt;!&ndash;                            <script&ndash;&gt;-->
<!--&lt;!&ndash;                                src="http://static.bshare.cn/b/buttonLite.js#style=-1&amp;uuid=40cb6f46-7685-42c6-8cf8-7e18be117d11&amp;pophcol=1&amp;lang=zh"></script>&ndash;&gt;-->
<!--&lt;!&ndash;                            <script src="http://static.bshare.cn/b/bshareC0.js"></script>&ndash;&gt;-->
<!--                        </div>-->
<!--                    </div>-->
                </div>
                    <?= frontendPc\components\InternalLinkWidget::widget(['isColumn'=>false]) ?>
                </section>

                <aside>
                    <?= frontendPc\components\DetailRightCompanyForm::widget(['companyId'=>$info['companyId']]) ?>


                    <!--                    //登录窗口-->
                    <?php if(empty(Yii::$app->params['user'])):?>
                    <?= frontendPc\components\RightLoginForm::widget() ?>
                    <?php else:?>
                    <?= frontendPc\components\DetailGuideCard::widget() ?>
                    <?php endif;?>
                    <span style="display: none">read:<?=$info['click']?></span>

                    <div id="recommendContainer">
                        <?=$recommendHtml?>
                    </div>

                    <div class="job">
                        <?= frontendPc\components\NewMediaGroupWidget::widget() ?>
                    </div>

                </aside>
            </div>
        </div>
    </div>
</div>

<script src="https://tjs.sjs.sinajs.cn/open/api/js/wb.js" type="text/javascript" charset="utf-8"></script>
<script src="/static/js/detailService.js"></script>

<script>
    $(function () {
        Vue.createApp({}).use(ElementPlus).mount('#boonsTemplate')
        Vue.createApp({}).use(ElementPlus).mount('.file-list')

        var id = '<?=$info["id"]?>'
        var $collectButton = $('.el-button--collect')
        var $jobApplyButton = $('.el-button--apply')
        // var $recommendContainer = $('#recommendContainer')
        //
        // httpPost('/api/person/announcement/get-recommend-list', { id: id }).then(function (data) {
        //     if (data.html) {
        //         $recommendContainer.html(data.html)
        //     }
        // })

        var $majorContent = $('#major-content')
        var $majorTrigger = $('#major-trigger')
        handleIntroHeight()

        function handleIntroHeight() {
            // 超过7行，默认展示6行
            var maxLine = 6
            var height = $majorContent.height()
            var lineHeight = $majorContent.css('line-height')
            var fontSize = $majorContent.css('font-size')

            if (/(px)$/.test(lineHeight)) {
                lineHeight = parseInt(lineHeight)
            } else {
                lineHeight = parseInt(lineHeight) * parseInt(fontSize)
            }

            if (height > lineHeight * maxLine) {
                $majorContent.addClass('is-limit')
                $majorTrigger.show()
            } else {
                $majorContent.removeClass('is-limit')
                $majorTrigger.hide()
            }

            $majorContent.removeClass('is-hidden')
            $majorTrigger.removeClass('is-hidden')
        }

        $majorTrigger.click(function () {
            $(this).toggleClass('is-show')
            $majorContent.toggleClass('is-limit')
            $(this).hasClass('is-show') ? $(this).find('a').text('收起') : $(this).find('a').text('展开')
        })

        $collectButton.on('click', function () {
            var $this = $(this)
            var isCollected = $this.hasClass('collected')

            httpPost('/api/person/announcement/collect', {id}).then(function () {
                $this.toggleClass('collected').text(isCollected ? '收藏' : '已收藏')
            })
        })
          $jobApplyButton.on('click', function () {
            window.globalComponents.applyDialogComponent.announcementApply(id)
        })
    })
</script>

<script>
    $(function () {

        // 在这里处理一些预览的逻辑,比如去掉一些不需要的元素,拦截一些点击事件,防止跳转,但是又不希望影响到原来的逻辑和模板

        <?php if($isPreview == 1):?>
            $('#loginAsideTemplate').remove()
            $('#recommendContainer').remove()
            $('.announcement .emit').remove()
            $('.group').remove()
            // 所有a标签的点击都拦截掉
            $('a').on('click', function (e) {
                e.preventDefault()
            })
        <?php endif;?>



        var $moreBtn = $('.more-btn')
        var $dialog = $('.group-dialog')
        var $groupItems = $('.group-list li')
        var $closeBtn = $('.closeBtn')
        var $dialogTab = $('.dialog-nav span')
        var $groupBox = $('.group-box')

        $groupItems.on('click', function () {
            $(this).addClass('active').siblings().removeClass('active')
        })

        $moreBtn.on('click', function () {
            $dialog.show()
        })

        $closeBtn.on('click', function () {
            $dialog.hide()
        })

        $dialogTab.on('click', function () {
            var index = $(this).index()
            $(this).addClass('active').siblings().removeClass('active')
            $groupBox.eq(index).addClass('active').siblings().removeClass('active')
        })



    })
</script>
<script>
   $(function() {
        var seeHeat = $('.see-heat')
        var params = {
            apiPull: '/api/person/announcement/check-generate-report',
            apiCreate: '/api/person/announcement/create-report',
            param: {announcementId: '<?=$info["id"]?>'}
        }
        seeHeat.on('click', function () {
            window.globalComponents.PromptDialogComponent.pull(params)
        })
   })
</script>
