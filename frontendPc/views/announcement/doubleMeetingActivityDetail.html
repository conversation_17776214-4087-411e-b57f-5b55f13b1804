<link rel="stylesheet" href="/static/css/doubleDetail.css?v=20241230">

<div id="component">
    <div class="el-main">
        <div class="detail-container">
            <div class="detail-header">
                <div class="detail-header-container">
                    <div class="breadcrumb">
                        位置：
                        <a href="/">高校人才网</a>＞
                        <?php if($info['columnInfo']):?>
                        <?php foreach($info['columnInfo'] as $item):?>
                        <a href="<?= $item['url'] ?>"><?= $item['name'] ?></a>＞
                        <?php endforeach;?>
                        <?php endif;?>
                        <a href="<?= $info['announcementUrl']?>"><?= $info['title']?></a>
                    </div>

                    <div class="main">
                        <section>
                            <div class="title">
                                <h1><?=$info['title']?></h1>
                            </div>
                            <div class="unit">
                                <ul>
                                    <li>发布时间：<?=$info['refreshTime']?></li>
                                    <li>截止时间：<?=$info['periodDate']?></li>
                                    <li>招聘会地点：<?=$info['cityName']?></li>
                                </ul>
                            </div>

                            <div class="tips" id="tipsTemplate">
                                <?php if(count($info['allWelfareLabel']) > 0):?>
                                <?php foreach($info['allWelfareLabel'] as $item):?>
                                <?php if($k < $welfareLabelViewAmount):?>
                                <span class="boon"><?=$item?></span>
                                <?php endif?>
                                <?php endforeach?>

                                <?php if(count($info['allWelfareLabel']) > $welfareLabelViewAmount):?>
                                <el-popover placement="bottom" :width="430" trigger="hover" v-cloak>
                                    <template #reference>
                                                <span>
                                                    <i class="el-icon boon-more" style="--font-size: 12px; color: white;">
                                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                                                            <path fill="currentColor"
                                                                  d="M176 416a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224z">
                                                            </path>
                                                        </svg>
                                                    </i>
                                                </span>
                                    </template>
                                    <!-- <span class="boon">全勤奖</span> -->
                                    <?php foreach($info['allWelfareLabel'] as $k=> $item):?>
                                    <?php if($k >= $welfareLabelViewAmount):?>
                                    <span class="boon"><?=$item?></span>
                                    <?php endif;?>
                                    <?php endforeach?>

                                </el-popover>
                                <?php endif?>
                                <?php endif?>
                            </div>
                        </section>

                        <aside>
                            <div class="emit">
                                <?php if($info['status'] == 1):?>
                                <?php if($info['isCollect'] == 1):?>
                                <button class="el-button el-button--primary el-button--collect collected">
                                    <span>已收藏</span>
                                </button>
                                <?php else:?>
                                <button class="el-button el-button--primary el-button--collect">
                                    <span>收藏</span>
                                </button>
                                <?php endif;?>
                                <button class="announcement el-button el-button--primary el-button--apply">
                                    <span>立即报名</span>
                                </button>
                                <?php elseif($info['status'] == 2):?>
                                <button class="el-button el-button--info is-plain is-disabled" disabled>
                                    <span>已下线</span>
                                </button>
                                <?php endif;?>
                                <!--                                <a href="<?='/company/detail/'.$info['companyId'].'.html' ?>" target="_blank" class="announcement el-button el-button&#45;&#45;primary">-->
                                <!--                                    <span>立即报名</span>-->
                                <!--                                </a>-->

                                <!--                                <?php if($info['status'] == 1):?>-->
                                <!--                                <button class="announcement el-button el-button&#45;&#45;primary el-button&#45;&#45;apply">-->
                                <!--                                    <span>立即报名</span>-->
                                <!--                                </button>-->
                                <!--                                <?php elseif($info['status'] == 2):?>-->
                                <!--                                <button class="el-button el-button&#45;&#45;info is-plain is-disabled" disabled>-->
                                <!--                                    <span>已下线</span>-->
                                <!--                                </button>-->
                                <!--                                <?php endif;?>-->
                            </div>
                            <p class="tips">
                                <!-- <span class="view">浏览次数：<?=$info['click']?>次</span> -->
                                <!-- <span class="share">分享</span> -->
                            </p>
                        </aside>

                        <div class="view-button">
                            <span><?=$info['companyName']?> &nbsp;|&nbsp; 招 <?=$info['jobRecruitAmount']?>
                                人，共计 <?=$info['jobAmount']?> 个场次</span>
                            <a href="<?=$jobListUrl?>">查看招聘会场次<img src="/static/assets/notice/view-notice-arrow.png"/></a>
                        </div>
                    </div>
                </div>
            </div>
            <span style="display: none">read:<?=$info['click']?></span>
            <div class="detail-main">
                <?= $info['content']?>
                <?php if(!empty($info['fileList'])):?>
                <h6 class="detail-title"><span>附件下载</span></h6>
                <div class="file-list">
                    <?php foreach($info['fileList'] as $item):?>
                    <el-popover placement="bottom" :width="300" trigger="hover" content="<?=$item['name']?>">
                        <template #reference>
                            <a href="<?=$item['path']?>" download="<?=$item['name']?>" class="file" target="_blank">
                                <div class="<?=$item['suffix']?>"></div>
                                <div class="file-name"><?=$item['name']?></div>
                            </a>
                        </template>
                    </el-popover>
                    <?php endforeach;?>
                </div>
                <?php endif;?>
                <div class="detail-emit">
                    <a href="<?=$jobListUrl?>" class="el-button el-button--primary">
                        <span>查看招聘会场次</span>
                    </a>
                    <?php if($info['status'] == 1):?>
                    <button class="announcement el-button el-button--primary el-button--apply">
                        <span>立即报名</span>
                    </button>
                    <?php elseif($info['status'] == 2):?>
                    <button class="el-button el-button--info is-plain is-disabled" disabled>
                        <span>已下线</span>
                    </button>
                    <?php endif;?>

                </div>
                <!--                <div class="share-custom">-->
                <!--                    <div class="sina-weibo">-->
                <!--                        <html xmlns:wb="http://open.weibo.com/wb">-->
                <!--                        <wb:follow-button uid="3702192203" type="red_3" width="100%" height="24">-->
                <!--                        </wb:follow-button>-->
                <!--                    </div>-->
                <!--                    <div>-->
                <!--                        <div class="bshare-custom">-->
                <!--                            <a title="分享到微信" class="bshare-weixin"></a>-->
                <!--                            <a title="分享到新浪微博" class="bshare-sinaminiblog"></a>-->
                <!--                            <a title="分享到QQ空间" class="bshare-qzone"></a>-->
                <!--                            <a title="分享到Facebook" class="bshare-facebook"></a>-->
                <!--                            <a title="分享到Twitter" class="bshare-twitter"></a>-->
                <!--                            <a title="更多平台" onclick="javascript:bShare.more(event);return false;" class="custom-more-btn">-->
                <!--                            </a>-->
                <!--                            <span class="BSHARE_COUNT bshare-share-count">0</span>-->
                <!--                        </div>-->
                <!--                        <script-->
                <!--                            src="http://static.bshare.cn/b/buttonLite.js#style=-1&amp;uuid=40cb6f46-7685-42c6-8cf8-7e18be117d11&amp;pophcol=1&amp;lang=zh"></script>-->
                <!--                        <script src="http://static.bshare.cn/b/bshareC0.js"></script>-->
                <!--                    </div>-->
                <!--                </div>-->
            </div>
        </div>
    </div>
</div>

<script src="http://tjs.sjs.sinajs.cn/open/api/js/wb.js" type="text/javascript" charset="utf-8"></script>
<script>
    $(function () {
        Vue.createApp({}).use(ElementPlus).mount('#tipsTemplate')
        Vue.createApp({}).use(ElementPlus).mount('.file-list')

        var id = '<?=$info["id"]?>'
        var $collectButton = $('.el-button--collect')
        var $jobApplyButton = $('.el-button--apply')

        $collectButton.on('click', function () {

            var $this = $(this)
            var isCollected = $this.hasClass('collected')

            httpPost('/api/person/announcement/collect', {id}).then(function () {
                $this.toggleClass('collected').find('span').text(isCollected ? '收藏' : '已收藏')
            })
        })


        $jobApplyButton.on('click', function () {
            window.globalComponents.activityDialogApplyJob.getJobList(id)
        })
    })
</script>
