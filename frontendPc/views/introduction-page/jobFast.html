<link rel="stylesheet" href="/static/css/jobFast.css">
<script src="https://img.gaoxiaojob.com/uploads/static/lib/crypto/index.js"></script>


<div id="component" v-cloak>
    <a href="#package-wrapper" class="banner"></a>

    <div class="introduce-wrapper">
        <div class="main-wrapper">
            <div class="wrapper-title">如果您也有以下求职困扰，建议了解「求职快」服务</div>
            <div class="compare"></div>
        </div>
    </div>

    <div class="privilege-wrapper gray-bg">
        <div class="main-wrapper">
            <div class="wrapper-title">三大特权</div>
            <div class="privilege-content">
                <div class="review">
                    <div class="swiper resume-top-swiper">
                        <div class="swiper-wrapper">
                            <div :class="`swiper-slide slide${index}`" v-for="index in 3">
                                <div class="effect"></div>
                            </div>
                        </div>
                    </div>
                    <div class="swiper-pagination resume-top-swiper-pagination"></div>
                </div>
                <div class="introduce">
                    <div class="title resume-top">简历置顶</div>
                    <div class="desc">简历置顶展示在高校人才网硕博人才库，从50W+人才中脱颖而出，加速提升简历曝光及求职效率。</div>
                    <p class="item">单位打开高校人才网人才库，<span class="primary">优先看到您</span></p>
                    <p class="item">单位搜索目标人选，您的简历<span class="primary">优先展示</span></p>
                    <p class="item">算法匹配全平台优质职位，简历<span class="primary">优先展示</span>于在招岗位匹配人才列表</p>
                </div>
            </div>
        </div>
    </div>

    <div class="privilege-wrapper">
        <div class="main-wrapper">
            <div class="privilege-content reverse">
                <div class="review">
                    <div class="swiper deliver-top-swiper">
                        <div class="swiper-wrapper">
                            <div :class="`swiper-slide slide${index}`" v-for="index in 2">
                                <div class="effect"></div>
                                <div v-if="index === 2" class="effect-next"></div>
                            </div>
                        </div>
                    </div>
                    <div class="swiper-pagination deliver-top-swiper-pagination"></div>
                </div>
                <div class="introduce">
                    <div class="title deliver-top">投递置顶</div>
                    <div class="desc">您的投递信息将通过多渠道实时通知至单位端，并置顶展示在单位端投递列表，优先获取单位关注，便于单位第一时间查看及处理您的简历！</div>
                </div>
            </div>
        </div>
    </div>

    <div class="privilege-wrapper gray-bg">
        <div class="main-wrapper">
            <div class="privilege-content">
                <div class="review refresh-gif"></div>
                <div class="introduce">
                    <div class="title refresh">简历刷新</div>
                    <div class="desc">刷新简历可提高简历在高校人才网硕博人才库中的排名，靠前的排名更有助于获得单位关注。</div>
                    <div class="desc">系统每天自动为您刷新简历，同时您也可以无限次主动刷新简历，让您的简历排名始终靠前！</div>
                </div>
            </div>
        </div>
    </div>

    <div class="evaluate-wrapper">
        <div class="main-wrapper">
            <div class="wrapper-title">用户评价</div>
            <div class="evaluate-content">
                <div class="list">
                    <div class="avatar">
                        <img src="//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/avatar-1.png" alt="" />
                    </div>
                    <div class="name">陈*辉</div>
                    <div class="ask">华南理工大学丨硕士丨材料科学与工程</div>
                    <div class="content">很满意！使用了投递置顶和简历置顶功能后，投递回复率和单位邀约的数量有明显提升，真的很有效，希望可以尽快拿到好的offer。</div>
                    <div class="tag">
                        <span>效果明显</span>
                        <span>回复快</span>
                    </div>
                </div>
                <div class="list">
                    <div class="avatar">
                        <img src="//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/avatar-2.png" alt="" />
                    </div>
                    <div class="name">李*静</div>
                    <div class="ask">中南大学丨博士丨土木工程</div>
                    <div class="content">投递置顶非常好用。之前投递简历总是石沉大海杳无音信，使用了投递置顶后一周内基本都会收到回复，目前也拿到了2个offer。</div>
                    <div class="tag">
                        <span>效果明显</span>
                        <span>收到面试快</span>
                    </div>
                </div>
                <div class="list">
                    <div class="avatar">
                        <img src="//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/avatar-3.png" alt="" />
                    </div>
                    <div class="name">柴*</div>
                    <div class="ask">密歇根州立大学丨硕士丨经济学</div>
                    <div class="content">被毕业论文折磨的孩子……很需要自动刷新简历提升活跃度。在没有那么多时间主动找工作的情况下，也会有很多单位来联系我，就挺省心。</div>
                    <div class="tag">
                        <span>性价比高</span>
                        <span>体验好</span>
                    </div>
                </div>
                <div class="list">
                    <div class="avatar">
                        <img src="//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/avatar-4.png" alt="" />
                    </div>
                    <div class="name">黄*樊</div>
                    <div class="ask">浙江师范大学丨博士丨教育学</div>
                    <div class="content">自我感觉自身条件在高校人才网硕博人才堆里不占优势，抱着试试看的态度购买了服务，近期已有几家单位邀请我去面试...</div>
                    <div class="tag">
                        <span>值得购买</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="package-wrapper" id="package-wrapper">
        <div class="main-wrapper">
            <div class="wrapper-title">立即解锁高效求职特权</div>
            <div class="package">
                <div
                    :class="`list ${index === hoverPackageIndex ? 'active' : ''}`"
                    v-for="(item, index) in packageList"
                    :key="index"
                    @mouseenter="packageSelect(item, index)"
                    @click="openpayDialog(item.equityPackageCategoryId, index)"
                >
                    <div v-if="item.buyTypeTxt" class="tag">{{item.buyTypeTxt}}</div>
                    <div class="name">{{item.name}}</div>
                    <div class="detail">
                        <div class="item" v-for="(equity, i) in item.equityList" :key="i">
                            <div class="label">{{equity.name}}*<span>{{equity.description}}</span></div>
                            <div class="value">{{equity.singlePrice}}</div>
                        </div>
                    </div>
                    <div class="price">
                        <div class="real">{{item.realAmount}}</div>
                        <div class="original">￥{{item.originalAmount}}</div>
                    </div>
                    <div class="avg-price">已优惠￥{{item.discountAmount}}</div>
                    <div class="open">立即开通</div>
                </div>
            </div>
        </div>
    </div>

    <div class="instructions-wrapper gray-bg">
        <div class="main-wrapper">
            <div class="title">服务说明</div>

            <div class="instructions-content">
                <p>1、请在本页面通过官方支付方式——微信扫码支付，并向唯一官方收款方“高校人才网”完成服务费用的支付。请勿尝试任何私下转账方式。</p>
                <p>2、服务将于付款成功后自动开通，服务过期则所有特权失效。购买后请合理安排时间并尽快使用。</p>
                <p>3、使用简历置顶 & 简历刷新权益时，请务必确认您未“隐藏简历”，且在线简历完整度≥65%（以平台显示的完整度为准），否则无法提升简历曝光效果。</p>
                <p>4、投递置顶权益限“报名方式”为“站内投递”的职位使用；您可在投递简历时选择是否使用该项服务。同一职位，30天内只能使用一次投递置顶权益.</p>
                <p>5、您屏蔽的单位无法搜索到您的简历，请放心使用服务。</p>
                <p>6、不同城市、职位类型下的曝光效果不同，实际曝光效果可能会存在波动。</p>
                <p>7、本产品为虚拟服务，不支持退款，敬请谅解。</p>
                <p>
                    8、购买即表示同意<a href="/agreement/value-added-services?type=2" target="_blank"><span>《高校人才网求职快服务协议》</span></a
                    >。
                </p>
            </div>
        </div>
    </div>

    <div v-if="showFooterFixed" class="fixed-wrapper">
        <div class="main-wrapper">
            <div class="buy-info">
                <div class="real">{{selectPackage.realAmount}}</div>
                元/{{selectPackage.days}}天
                <div class="original">{{selectPackage.originalAmount}}元</div>
            </div>
            <button class="pay" @click="openVipDialog">立即开通</button>
        </div>
    </div>
</div>

<script>
  $(function () {
      const component = {
          data() {
              return {
                  hoverPackageIndex: 1,
                  selectPackage: {},
                  packageList: [],
                  showFooterFixed: false,
              }
          },
          methods: {
              initSwiper() {
                  const swiperArray = ['resume-top-swiper', 'deliver-top-swiper']
                  swiperArray.map(function (item) {
                      new Swiper(`.${item}`, {
                          autoplay: {
                              delay: 5000,
                              pauseOnMouseEnter: true,
                              disableOnInteraction: false
                          },
                          delay: 7000,
                          loop: true,
                          pagination: {
                              el: `.${item}-pagination`,
                              clickable: true
                          }
                      })
                  })
              },
              scroll() {
                  const _this = this
                  window.addEventListener('scroll', function () {
                      const bannerHeight = 420
                      const bannerOffsetTop = bannerHeight + 68
                      const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
                      const pageHeight = document.documentElement.scrollHeight || document.body.scrollHeight
                      const creenHeight = document.body.clientHeight
                      _this.showFooterFixed = bannerOffsetTop < scrollTop && scrollTop < pageHeight - creenHeight * 2
                  })
              },
              packageSelect(item, index) {
                  this.hoverPackageIndex = index
                  this.selectPackage = item
              },
              openVipDialog() {
                  const {
                      selectPackage: { equityPackageCategoryId },
                      hoverPackageIndex
                  } = this
                  this.openpayDialog(equityPackageCategoryId, hoverPackageIndex, 2)
              },
              openpayDialog(id, index, position = 1) {
                  //position点击开通位置，1是页面套餐选择，2是底部开通按钮
                  //-------------用户付费转化数据埋点-开始---------------
                  // let productName = $('.list:eq('+index+') .name').text()
                  // let logData = {
                  //     params : { productId:id, productName:productName, clickPosition: position  },
                  //     actionType : '1',
                  //     actionId : '10030002'
                  // }
                  // this.payBuriedPoint(logData)
                  //-------------用户付费转化数据埋点-结束---------------

                  let api = "<?=$data['api_popup']?>"
                  let uuid = "<?=$data['uuid']?>"
                  window.globalComponents.PayDialogAlertComponent.show(api, id, index, uuid, position)
              },
              getPackage(type) {
                  // type 4求职快
                  let api = "<?=$data['api_buy']?>"
                  httpGet(api + '?equityPackageCategoryId=' + type).then((r) => {
                      const { isDiamondVip, isGoldVip, list } = r
                      this.packageList = list
                      this.selectPackage = list[1]
                  })
              },
              // payBuriedPoint(data) {
              //     const jsonString = JSON.stringify(data);
              //
              //     let configKey = '123abcgaoxiaorencaiwang'
              //     let code = CryptoJS.MD5(configKey).toString()
              //     let iv = CryptoJS.enc.Utf8.parse(code.substring(0,16))
              //     let key = CryptoJS.enc.Utf8.parse(code.substring(16))
              //
              //     let encryptString = CryptoJS.AES.encrypt(jsonString,key,{mode:CryptoJS.mode.ECB,iv:iv});
              //     let img = new Image()
              //     img.src = `3.gif?data=`+encodeURIComponent(encryptString)
              // }
          },
          mounted() {
              let that = this
              this.scroll()
              this.initSwiper()
              this.getPackage(4)
              //-------------用户付费转化数据埋点-开始---------------
              window.onbeforeunload = function (){
                  httpGet("/showcase-browse-log/update-job-fast-view-point-log?uuid=<?=$data['uuid']?>")
              }
              //-------------用户付费转化数据埋点-结束---------------
          }
      }

      Vue.createApp(component).use(ElementPlus).mount('#component')
  })
</script>

<?= \frontendPc\components\DialogPaymentSuccess::widget() ?>
<?= \frontendPc\components\DialogResumePayWidget::widget() ?>