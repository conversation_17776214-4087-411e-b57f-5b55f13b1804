<link rel="stylesheet" href="/static/css/companyDetail.css" />

<div id="component">
    <div class="el-main auth">
        <div class="detail-container">
            <?= frontendPc\components\CompanyDetailBannerWidget::widget(['companyId'=>$info['companyId']]) ?>

            <div class="detail-main">
                <section id="companyTemplate" class="section">
                    <div class="tabs-common detail-cotent-auth">
                        <div class="tab-content company-notice active">
                            <div class="wrapper">
                                <div class="top">
                                    <div class="title">招聘公告</div>
                                </div>
                                <div class="filter">
                                    <el-select :class="{'is-select': noticeData.areaId}" v-model="noticeData.areaId" :clearable="true" placeholder="地区">
                                        <el-option v-for="item in announcementAreaOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                                    </el-select>

                                    <el-select :class="{'is-select': noticeData.jobType}" v-model="noticeData.jobType" :clearable="true" placeholder="职位类型">
                                        <el-option v-for="item in announceJobCategoryList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                                    </el-select>

                                    <el-select :class="{'is-select': noticeData.majorId}" v-model="noticeData.majorId" :clearable="true" placeholder="专业">
                                        <el-option v-for="item in majorOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                                    </el-select>

                                    <el-select :class="{'is-select': noticeData.educationId}" v-model="noticeData.educationId" :clearable="true" placeholder="学历">
                                        <el-option v-for="item in educationOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                                    </el-select>
                                </div>

                                <!-- 首屏需判断if -->
                                <?php if(count($info['announcementList']) > 0):?>
                                    <ul class="result">
                                        <?php foreach($info['announcementList'] as $k=>$announcement):?>
                                            <li class="item <?php if($announcement['invalid'] == 1): ?>offline-mark<?php endif?>">
                                                <a href="<?= $announcement['url']?>" target="_blank">
                                                    <div class="notice-top">
                                                        <h5 class="offline-gray-first">
                                                            <span class="offline-tag">已下线</span><?=addslashes($announcement["title"])?>
                                                        </h5>
                                                        <div class="date"> <?=$announcement["refreshTime"]?>发布</div>
                                                    </div>
                                                    <div class="notice-info">
                                                        <div class="info offline-gray-first">
                                                            <span>共<?=$announcement["jobAmount"]?>个职位</span>
                                                            <span>招<?=$announcement["recruitAmount"]?>人</span>
                                                        </div>
                                                        <div class="address">
                                                            <span class="text popper-reference">
                                                                <?=$announcement["fiveAreaName"]?>
                                                                <?php if($announcement['isAllArea'] == 1):?>
                                                                <span >...</span>
                                                                <?php endif?>
                                                            </span>
                                                            <?php if($announcement['isAllArea'] == 1):?>
                                                            <div id="custom-popper">
                                                                <span class="arrow"></span>
                                                                <div class="content"><?=$announcement["allAreaName"]?></div>
                                                            </div>
                                                            <?php endif?>
                                                        </div>
                                                    </div>
                                                </a>
                                            </li>
                                        <?php endforeach?>
                                    </ul>

                                    <?php else:?>
                                        <el-empty description="暂无数据"></el-empty>
                                    <?php endif?>
                                 
                                <el-pagination
                                    background
                                    :layout="'total, sizes, prev, pager, next, jumper'"
                                    v-model:current-page="noticePagination.page"
                                    v-model:page-size="noticePagination.size"
                                    :total="noticePagination.total"
                                >
                                </el-pagination>
                            </div>
                        </div>
                    </div>
                </section>

                <?= frontendPc\components\CompanyDetailRightWidget::widget(['companyId'=>$info['companyId'],'predicationTalentPreviewList'=>$info['activityList']['previewList']]) ?>
            </div>
        </div>
    </div>
</div>

<script src="./lib/jquery-throttle-debounce/jquery-throttle-debounce.min.js"></script>
<script src="./lib/swiper/swiper.min.js"></script>
<script src="./lib/popper/popper.min.js"></script>
<script>
    $(function () {
        Vue.createApp({}).use(ElementPlus).mount('#tagsTemplate')

        const companyOptions = {
            data() {
                return {
                    companyId: <?= $info['companyId']?>,
                    announcementAreaOptions: [
                        <?php foreach($info['announceCityList'] as $k=>$city):?>
                        { label: '<?=$city["v"]?>', value: '<?=$city["k"]?>' },
                        <?php endforeach?>
                    ],
                    announceJobCategoryList: [
                        <?php foreach($info['announceJobCategoryList'] as $k=>$jobCategory):?>
                        { label: '<?=$jobCategory["v"]?>', value: '<?=$jobCategory["k"]?>' },
                        <?php endforeach?>
                    ],

                    majorOptions: [
                        <?php foreach($info['announceMajorList'] as $k=>$major):?>
                        { label: '<?=$major["v"]?>', value: '<?=$major["k"]?>' },
                        <?php endforeach?>
                    ],

                    educationOptions: [
                        <?php foreach($info['announceEducationList'] as $k=>$education):?>
                        { label: '<?=$education["v"]?>', value: '<?=$education["k"]?>' },
                        <?php endforeach?>
                    ],

                    noticeApi: '/api/person/company/get-announcement-list-html',
                    noticeData: {
                        areaId: '',
                        jobType: '',
                        majorId: '',
                        educationId: ''
                    },

                    noticePagination: {
                        page: 1,
                        size: 20,
                        total: <?= $info['announcementAmount']?$info['announcementAmount']:0?>,
                    }
                }
            },

            watch: {
                noticeData: {
                    handler(val) {
                        this.handleFilter()
                    },
                    deep: true
                },
                noticePagination:{
                    handler(val) {
                        this.handleFilter()
                    },
                    deep: true
                }
            },

            mounted() {
                this.initSwiper()
            },

            methods: {
                initSwiper() {
                    var swiper = new Swiper('.company-style-swiper', {
                        loop: true,
                        autoplay: {
                            pauseOnMouseEnter: true,
                            disableOnInteraction: false,
                            delay: 3000
                        },
                        navigation: {
                            nextEl: '.company-style-next',
                            prevEl: '.company-style-prev'
                        },
                        preventClicks: false,
                        pagination: {
                            el: '.swiper-pagination'
                        },
                        allowTouchMove: false
                    })
                },

                handleFilter() {
                    const { jobType: jobCategoryId, ...data } = this.noticeData
                    const { page, size: pageSize } = this.noticePagination
                    const { companyId, noticeApi } = this
                    const api = noticeApi
                    const query = { ...data, jobCategoryId, page, pageSize, companyId }

                    httpGet(api, query).then((res) => {
                        const { list, total } = res
                    
                        $('.company-notice').find('.result').html(list)
                        this.noticePagination.total = total
                    })
                }
            }
        }

        Vue.createApp(companyOptions)
            .use(ElementPlus, {
                locale: {
                    name: 'zh-cn',
                    el: {
                        pagination: {
                            goto: '前往',
                            pagesize: '条/页',
                            total: '共 {total} 条',
                            pageClassifier: '页',
                            deprecationWarning: '你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档'
                        }
                    }
                }
            })
            .mount('#companyTemplate')

        var id = "<?= $info['companyId']?>"
        var $collectButtons = $('.company-collect-button')

        // get miniprogram qrcode
        var miniprogramSlot = $('.to-miniprogram')

        httpGet('/company/get-detail-mini-code?id=' + id).then(function (data) {
            miniprogramSlot.append($('<img src="' + data.url + '" />'))
        })

        $collectButtons.on('click', function () {
            var $this = $(this)
            var isCollected = $this.hasClass('el-button--default')

            httpPost('/api/person/company/collect', { companyId: id }).then(function () {
                if (isCollected) {
                    $this.removeClass('el-button--default').addClass('el-button--primary').find('span').text('立即关注')
                } else {
                    $this.removeClass('el-button--primary').addClass('el-button--default').find('span').text('已关注')
                }
            })
        })

        // 公告地址气泡
        !(function addressPopper() {
            $('body').on('mouseover', '.popper-reference', function () {
                $(this).siblings('#custom-popper').show()
            })
            $('body').on('mouseout', '.popper-reference', function () {
                $(this).siblings('#custom-popper').hide()
            })
        })()
        
    })
</script>
