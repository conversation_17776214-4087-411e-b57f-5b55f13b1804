<?php foreach($list as $k=>$job):?>
    <li class="<?php if($job['status'] == 0): ?>offline-mark<?php endif?>">
        <a href="<?=$job['url']?>" target="_blank">
            <div>
                <div class="job-top">
                    <h5 class="name"><?=$job["jobName"]?></h5>
                    <span class="date"> <?=$job["refreshTime"]?>发布</span>
                </div>
                <div class="job-bottom">
                    <div class="salary"><?=$job["wage"]?></div>
                    <div class="tags">
                        <span><?=$job["areaName"]?></span>
                        <span><?=$job["education"]?></span>
                        <span>招<?=$job["amount"]?>人</span>
                    </div>
                </div>
            </div>
            <div class="major"><?=$job["major"]?></div>
            <div>
                <?php if($job["status"] == "0"):?>
                <button disabled data-id="<?=$job['jobId']?>" class="el-button el-button--primary job-apply-button">已下线</button>
                <?php elseif($job["applyStatus"] == "1"):?>
                <button disabled data-id="<?=$job['jobId']?>" class="el-button el-button--primary job-apply-button has-applied">已申请</button>
                <?php else:?>
                <button data-id="<?=$job['jobId']?>" class="el-button el-button--primary job-apply-button">立即申请</button>
                <?php endif;?>
            </div>
        </a>
        <?php if($job["announcementId"]):?>
        <a href="<?=$job['announcementUrl']?>" target="_blank" class="announcement-detail offline-gray-first"><?=$job['announcementName']?></a>
        <?php endif;?>
    </li>
<?php endforeach?>