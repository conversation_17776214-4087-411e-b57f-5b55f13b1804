<link rel="stylesheet" href="/static/css/columnSpecial.css">
<link rel="stylesheet" href="/static/css/footerLink.css">

<?= frontendPc\components\HengFuWidget::widget(['columnId'=>$columnId]) ?>

<div class="main" id="component">
    <!-- 当前位置 -->
    <?= frontendPc\components\CurrentLocationWidget::widget(['columnId'=>$columnId]) ?>

    <!-- headlines.html -->
    <?=$headlines?>

    <!-- A1-->
    <?= frontendPc\components\TopBannerWidget::widget(['columnId'=>$columnId,'templateType'=>$templateType]) ?>

    <!-- 热门单位 -->
    <?= frontendPc\components\HotUnitWidget::widget(['columnId'=>$columnId,'templateType'=>$templateType]) ?>

    <div class="propaganda-container w">
        <?=$left_hot_announcement?>

        <?=$right_hot_announcement?>
    </div>

    <div class="job-container flex">
        <div class="left-content">
            <!-- 博士人才交流 -->
            <?= frontendPc\components\CommunicationWidget::widget() ?>

            <!-- 高校招聘地区导航 -->
            <?= frontendPc\components\LeftCityNavWidget::widget() ?>

            <!-- Z1区 -->
            <?=frontendPc\components\ReservedWidget::widget(['columnId'=>$columnId])?>

            <!-- 按学科查看职位 -->
            <div class="sidebar job-classification">
                <?= frontendPc\components\JobNavWidget::widget() ?>
            </div>

            <!-- 招人求职 -->
            <?=frontendPc\components\FastManWidget::widget()?>

            <!-- 热门专题 -->
            <?=frontendPc\components\HotTopicWidget::widget()?>
        </div>

        <div class="right-content" id="component">
            <!-- 最新公告&简章 -->

            <div class="release-container w" style="min-height: 565px;">
                <div class="common-title new-announcement component">
                    <h2>最新公告&简章</h2>
                    <div class="data">
                        <div class="amount" style="display: block;">
                            共<span>{{latestAnnouncementTotal}}</span>则公告
                        </div>
                    </div>
                </div>

                <div class="type-select">
                    <el-form :model="latestAnnouncementForm" inline>
                        <?php  foreach ($tapList as $key => $item) {?>
                        <el-form-item>
                            <el-select @change="() => handleLatestAnnouncement()" filterable
                                placeholder="<?php echo $item[0]['v'];?>" clearable
                                v-model="<?php echo 'latestAnnouncementForm.'.$key?>" class="m-2" size="small">
                                <?php foreach ($item as $i => $value) { ?>
                                <el-option label="<?php echo $value['v'];?>" value="<?php echo $value['k'];?>">
                                </el-option>
                                <?php } ?>
                            </el-select>
                        </el-form-item>
                        <?php }?>
                    </el-form>

                    <div class="superior-filter-global" :class="{active: hasSuperiorAnnouncementFilter}" @click="handleSuperior(2)">
                        高级筛选
                        <div class="superior-tips">含编制、热度等会员专属筛选特权</div>
                    </div>
                </div>

                <div class="announcement-content">
                    <!-- 标题 -->
                    <div class="recruit-information recruit-title">
                        <span class="date">日期</span>
                        <span class="title">公告标题</span>
                        <span class="place">地点</span>
                        <span class="job-quantity">职位数量</span>
                        <span class="recruit-quantity">招聘人数</span>
                        <span class="closing-date">截止日期</span>
                    </div>

                    <!-- 内容 -->
                    <?=$special_latest_announcement?>
                </div>

                <el-pagination background :layout="paginationLayout" :current-page="latestAnnouncementForm.page"
                    :page-size="latestAnnouncementPageSize" :total="latestAnnouncementTotal"
                    @current-change="(val) => handleLatestAnnouncement(val, 'page')">
                </el-pagination>
            </div>

            <!-- 最新职位-->

            <div class="latest-job w">
                <div class="common-title new-announcement component">
                    <h2>最新职位</h2>
                    <div class="data">
                        <div class="amount">
                            共<span>{{latestJobTotal}}</span>则职位
                        </div>
                    </div>
                </div>

                <div class="type-select">
                    <el-form :model="formData" inline>
                        <?php  foreach ($tapList as $key => $item) { ?>
                        <el-form-item>
                            <el-select @change="() => handleLatestJob()" filterable
                                placeholder="<?php echo $item[0]['v'];?>" clearable
                                v-model="<?php echo 'latestJobForm.'.$key?>" class="m-2" size="small">
                                <?php foreach ($item as $i => $value) { ?>
                                <el-option label="<?php echo $value['v'];?>" value="<?php echo $value['k'];?>">
                                </el-option>
                                <?php } ?>
                            </el-select>
                        </el-form-item>
                        <?php }?>
                    </el-form>

                    <div class="superior-filter-global" :class="{active: hasSuperiorJobFilter}" @click="handleSuperior(1)">
                        高级筛选
                        <div class="superior-tips">含编制、热度等会员专属筛选特权</div>
                    </div>

                    <div class="type-select-tips">
                        <span>说明：通过自定义上方职位筛选框内容可匹配更精准的职位信息。</span>
                    </div>
                </div>

                <?=$special_latest_job?>

                <el-pagination background :layout="paginationLayout" :current-page="latestJobForm.page"
                    :total="latestJobTotal" :page-size="latestJobPageSize"
                    @current-change="(val) => handleLatestJob(val)">
                </el-pagination>
            </div>


            <!-- 推荐单位 -->
            <?= frontendPc\components\RightRecommendUnitWidget::widget(['columnId'=>
            $columnId,'templateType'=>$templateType]) ?>
        </div>
    </div>
</div>
<!-- 锚点导航 -->
<div class="fixed-tool">
    <ul>
        <li class="current">头条</li>
        <li>热门单位</li>
        <li>热门公告</li>
        <li>最新公告</li>
        <li>最新职位</li>
        <li>推荐单位</li>
    </ul>
</div>
<!-- 友情链接 -->
<?= frontendPc\components\FriendLinkWidget::widget() ?>
<?= frontendPc\components\InternalLinkWidget::widget() ?>

<!-- 高级筛选组件 -->
<?= frontendPc\components\DialogSuperiorWidget::widget() ?>

<script>
    $(function () {

        let columnId = Number("<?php echo $columnId;?>");
        let latestAnnouncementPageSize = Number("<?php echo $latestAnnouncementPageSize;?>");
        let latestAnnouncementTotal = Number("<?php echo $announcement_account;?>");
        let latestJobPageSize = Number("<?php echo $latestJobPageSize;?>");

        let jobAccount = Number("<?php echo $jobAccount;?>");

        // 每切换n页循环一次
        let maxPage = 6
        let recommendPage = 3

        const component = {
            data() {
                return {
                    paginationLayout: 'prev, pager, next, jumper',

                    // 热门公告&简章
                    hotAnnouncementForm: {
                        columnId: columnId,
                        key: 'left_hot_announcement',
                        page: 2,
                    },

                    // 推荐公告&简章
                    recommendAnnouncementForm: {
                        columnId: columnId,
                        key: 'right_hot_announcement',
                        page: 2,
                    },

                    // 最新公告&简章表单
                    latestAnnouncementTotal: latestAnnouncementTotal,
                    latestAnnouncementPageSize: latestAnnouncementPageSize,
                    latestAnnouncementForm: {
                        columnId: columnId,
                        education_type: '0',
                        major_id: '0',
                        city_id: '0',
                        job_category_id: '0',
                        announcementHeat: '',
                        isEstablishment: '',
                        page: 1
                    },
                    latestJobTotal: jobAccount,
                    latestJobPageSize: latestJobPageSize,
                    latestJobForm: {
                        columnId: columnId,
                        city_id: '0',
                        education_type: '0',
                        job_category_id: '0',
                        major_id: '0',
                        applyHeat: '',
                        isEstablishment: '',
                        page: 1
                    },
                }
            },
            computed: {
                hasSuperiorAnnouncementFilter() {
                    let { isEstablishment, announcementHeat } = this.latestAnnouncementForm
                    isEstablishment = isEstablishment === '2' ? '' : isEstablishment
                    return !!isEstablishment || !!announcementHeat
                },
                hasSuperiorJobFilter() {
                    let { isEstablishment, applyHeat } = this.latestJobForm
                    isEstablishment = isEstablishment === '2' ? '' : isEstablishment
                    return !!isEstablishment || !!applyHeat
                }
            },
            mounted() {
                this.getAnnouncementTotal()
            },
            methods: {
                getAnnouncementTotal() {
                    const {
                        columnId,
                        education_type,
                        major_id,
                        city_id,
                        job_category_id,
                        announcementHeat,
                        isEstablishment,
                        page
                    } = this.latestAnnouncementForm

                    httpGet('/home/<USER>', {
                        columnId,
                        educationType: education_type,
                        majorId: major_id,
                        cityId: city_id,
                        jobCategoryId: job_category_id,
                        announcementHeat,
                        isEstablishment,
                    }).then((resp) => {
                        this.latestAnnouncementTotal = +resp.count
                    })

                },

                // 热门公告&简章
                handleHotAnnouncement() {
                    httpGet('/home/<USER>', this.hotAnnouncementForm).then((resp) => {
                        const content = resp.list
                        const msg = resp.msg
                        const page = resp.page
                        const reg = /(<ul class="announcement-news"[\s\S]*<\/ul>)/g
                        const html = content.match(reg)[0]

                        if (page) {
                            this.hotAnnouncementForm.page = page
                        } else {
                            const curPage = this.hotAnnouncementForm.page
                            this.hotAnnouncementForm.page = curPage > maxPage ? 1 : curPage + 1
                        }

                        if (msg) {
                            ElementPlus.ElMessage.warning(msg)
                        } else {
                            $('.propaganda-hot .announcement-content').html(html)
                        }
                    })
                },

                // 推荐公告&简章
                handleRecommendAnnouncement() {
                    httpGet('/home/<USER>', this.recommendAnnouncementForm).then((resp) => {
                        const content = resp.list
                        const msg = resp.msg
                        const page = resp.page
                        const reg = /(<ul class="component">[\s\S]*<\/ul>)/g
                        const html = content.match(reg)[0]

                        if (page) {
                            this.recommendAnnouncementForm.page = page
                        } else {
                            const curPage = this.recommendAnnouncementForm.page
                            this.recommendAnnouncementForm.page = curPage > recommendPage ? 1 : curPage + 1
                        }

                        if (msg) {
                            ElementPlus.ElMessage.warning(msg)
                        } else {
                            $('.propaganda-recommend .recommend-content').html(html)
                        }
                    })
                },

                handleLatestAnnouncement(pageValue = 1) {
                    this.latestAnnouncementForm.page = pageValue
                    const {
                        columnId,
                        education_type,
                        major_id,
                        city_id,
                        job_category_id,
                        announcementHeat,
                        isEstablishment,
                        page
                    } = this.latestAnnouncementForm

                    this.getAnnouncementTotal()

                    httpGet('/home/<USER>', {
                        columnId,
                        educationType: education_type,
                        majorId: major_id,
                        cityId: city_id,
                        jobCategoryId: job_category_id,
                        announcementHeat,
                        isEstablishment,
                        page
                    }).then((resp) => {
                        var content = resp.list
                        let latestAnnouncementTotal = +resp.announcement_account;
                        $('.announcement-content .recruit-information-container').html(content)

                    })
                },
                handleLatestJob(pageValue = 1) {
                    this.latestJobForm.page = pageValue
                    const { columnId, education_type, major_id, city_id, job_category_id,applyHeat,
                        isEstablishment, page } = this.latestJobForm
                    httpGet('/home/<USER>', {
                        columnId,
                        educationType: education_type,
                        majorId: major_id,
                        cityId: city_id,
                        jobCategoryId: job_category_id,
                        applyHeat,
                        isEstablishment,
                        page
                    }).then((resp) => {
                        var content = resp.list
                        if (!resp.list) {
                            return
                        }
                        var reg = /(<ul>[\s\S]*<\/ul>)/g
                        var html = content.match(reg)[0]
                        this.latestJobTotal = +resp.jobAccount;

                        $('.latest-job .recruitment-announcement').html(html)
                    })
                },

                handleSuperior(type) {
                    const formKey = type === 1 ? 'latestJobForm' : 'latestAnnouncementForm'
                    const fetchKey = type === 1 ? 'handleLatestJob' : 'handleLatestAnnouncement'
                    const { isEstablishment } = this[formKey]
                    const heat = this[formKey][type === 1 ? 'applyHeat' : 'announcementHeat']
                    window.globalComponents.SuperiorDialogComponent.show(type, { isEstablishment, heat }, (data) => {
                        const params = {
                            ...this[formKey],
                            ...data
                        }
                        this[formKey] = {...params}
                        this[fetchKey]()
                    })
                }
            },
        }

        Vue.createApp(component).use(ElementPlus, {
            locale: {
                name: 'zh-cn',
                el: {
                    pagination: {
                        goto: '前往',
                        pagesize: '条/页',
                        total: '共 {total} 条',
                        pageClassifier: '页',
                        deprecationWarning: '你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档',
                    }
                }
            }
        }).mount('#component')
    });
</script>

<script src="/static/js/column.js?v=2"></script>
<script src="/static/js/citySwitch.js?v=2"></script>
<script src="/static/lib/popper/popper.min.js?v=2"></script>
<script src="/static/js/popper.js"></script>