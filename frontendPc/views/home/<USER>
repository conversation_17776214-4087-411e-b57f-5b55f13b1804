<!--<style>-->
<!--    html {-->
<!--        -webkit-filter: grayscale(100%);-->
<!--        -moz-filter: grayscale(100%);-->
<!--        -ms-filter: grayscale(100%);-->
<!--        -o-filter: grayscale(100%);-->
<!--        filter: grayscale(1);-->
<!--    }-->
<!--</style>-->
<link rel="stylesheet" href="/static/css/home.css?v=1.2">
<link rel="stylesheet" href="/static/css/homeLogin.css">
<!--<script src="/static/js/index.js?v=0.5"></script>-->
<div class="banner-container">
    <div class="slide">
        <div class="swiper slide-picture-display">
            <?=$HF?>
        </div>
        <div class="swiper-pagination slide-picture-pagination"></div>
    </div>

    <div class="subnav-personal-state">
        <?= frontendPc\components\SubNavWidget::widget() ?>
        <?php if (Yii::$app->params['user']['type']=='1'): ?>
        <?= frontendPc\components\PersonalStateWidget::widget() ?>
        <?php endif; ?>

        <!-- 只要不是登录求职者端,都应该有这个       -->
        <?php if (Yii::$app->params['user']['type']!='1'): ?>
        <?= frontendPc\components\HomeLoginFormWidget::widget() ?>
        <?php endif; ?>
    </div>
</div>

<div class="main">
    <?=$T01?>

    <div id="rangeA" class="w">
        <?=$A1?>
        <?=$A2?>
    </div>

    <div class="left-loop">
        <div class="left-marquee flex" id="scrollDiv">
            <div id="scrollBegin" class="flex">
                <?php foreach ($rolling as  $k=>$v ) { ?>
                <a href="<?=$v['url'];?>" target="_blank"><?=$v['title'];?></a>
                <?php } ?>
            </div>
            <div id="scrollEnd" class="flex"></div>
        </div>

        <?=$A3?>
    </div>

    <?=$B1?>
    <?=$B2?>
    <?=$B3?>
    <?=$B4?>
    <div class="headlines flex w">
        <div class="headlines-sidebar">
            <?=$C1?>
            <?=$C3?>
        </div>
        <div class="headlines-middle flex">
            <div class="headline-content">
                <div class="headline-news">
                    <h4>
                        <a href="<?=$head[0]['url']?>" target="_blank" class="first" title="<?=\common\helpers\StringHelper::changeQuotationMark($head[0]['title'])?>">
                            <i>头条！</i>
                            <?=$head[0]['title']?>
                        </a>
                    </h4>
                    <p>
                        <?=$head[0]['content']?>
                    </p>
                </div>
                <div class="headline-news">
                    <h4>
                        <a href="<?=$head[1]['url']?>" target="_blank" title="<?=\common\helpers\StringHelper::changeQuotationMark($head[1]['title'])?>">
                            <i>头条！</i>
                            <?=$head[1]['title']?>
                        </a>
                    </h4>
                    <p>
                        <?=$head[1]['content']?>
                    </p>
                </div>
                <div class="recommend-news">
                    <h4>
                        <a href="<?=$head[2]['url']?>" target="_blank" title="<?=\common\helpers\StringHelper::changeQuotationMark($head[2]['title'])?>">
                            <i class="recommend">推荐！</i>
                            <?=$head[2]['title']?>
                        </a>
                    </h4>
                    <ul class="component">
                        <li disc>
                            <a href="<?=$head[3]['url']?>" target="_blank" title="<?=\common\helpers\StringHelper::changeQuotationMark($head[3]['title'])?>"><?=$head[3]['title']?></a>
                        </li>
                        <li>
                            <a href="<?=$head[4]['url']?>" target="_blank" title="<?=\common\helpers\StringHelper::changeQuotationMark($head[4]['title'])?>"><?=common\helpers\StringHelper::changeQuotationMark($head[4]['title'])?></a>
                        </li>
                    </ul>
                </div>
            </div>
            <?=$C4?>

            <div class="search flex" id="searchComponent">
                <div class="search-content flex">
                    <el-form>
                        <el-form-item>
                            <el-select v-model="type" class="m-2" size="small">
                                <el-option label="职位" value="1"></el-option>
                                <el-option label="公告" value="2"></el-option>
                                <el-option label="单位" value="3"></el-option>
                                <el-option label="资讯" value="4"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-form>
                    <input type="text" v-model="keyword" placeholder="搜索职位 / 公告 / 单位 / 资讯" class="input"
                        @keyup.enter="search"/>
                    <a @click="search"></a>
                </div>

                <a class="showcase-link" href="/vip.html" target="_blank"></a>
            </div>
        </div>
        <div class="headlines-ad">
            <?=$C2_TOP?>
        </div>
    </div>

    <div class="info-bar flex">
        <?=$C5?>
        <?=$C2_BOTTOM?>

    </div>


    <div class="d1 w">
        <div class="navbox">
            <ul class="flex">
                <li class="list-change">推荐单位</li>
                <li>华东地区</li>
                <li>华中、华南地区</li>
                <li>华北、东北地区</li>
                <li>西南、西北地区</li>
            </ul>
        </div>
        <div class="cardcon">
            <?=$D1_1?>
            <?=$D1_2?>
            <?=$D1_3?>
            <?=$D1_4?>
            <?=$D1_5?>
        </div>
    </div>

    <div class="e w">
        <?=$E1?>

        <div class="school-information-content flex">
            <?=$E2?>
            <?=$E3?>
            <?=$E4?>
        </div>
    </div>

    <?=$F?>
    <?=$F1?>
    <?=$H1?>
    <?=$ZD01?>


    <div class="part flex">
        <div class="l">
            <div class="left-siderbar-box">
                <div class="common-title">
                    <h2>信息导航分类</h2>
                </div>
                <?= frontendPc\components\MessageNavWidget::widget() ?>
            </div>
            <div class="left-siderbar-box">
                <div class="common-title">
                    <h2>学科导航分类</h2>
                </div>
                <?= frontendPc\components\MajorNavWidget::widget() ?>
            </div>

            <?= frontendPc\components\JobSearchWidget::widget() ?>
            <?=$L01?>
            <?= frontendPc\components\WxWidget::widget() ?>
            <div class="left-siderbar-box development flex">
                <span class="gx-media">高校人才网媒体渠阵</span>
                <a href="http://www.gaoxiaojob.com/zhaopin/aboutUs/mediaMatrix.html" target="_blank" class="see">查看</a>
            </div>

            <div class="left-siderbar-box weibo-show">
                <iframe width="200" height="488" class="share_self" frameborder="0" scrolling="no"
                        src="https://widget.weibo.com/weiboshow/index.php?language=&width=200&height=488&fansRow=2&ptype=1&speed=300&skin=1&isTitle=1&noborder=0&isWeibo=1&isFans=0&uid=3702192203&verifier=f6685b04&dpc=1"></iframe>
            </div>
            <?=$L02?>
            <?= frontendPc\components\HotTopicWidget::widget() ?>
            <?=$L03?>
            <?= frontendPc\components\TalkTopicWidget::widget() ?>
            <?=$L04?>
            <?= frontendPc\components\HotNewsWidget::widget() ?>
            <?=$L05?>

            <?= frontendPc\components\ActivityWidget::widget() ?>

            <!-- 高才情报局 -->
            <div class="left-siderbar-box hot-topics">
                <?= frontendPc\components\CIAWidget::widget() ?>
            </div>

            <!-- 圈子 -->
            <?= frontendPc\components\GroupWidget::widget() ?>

            <?=$SHOUYE_ZUIXIN_ZUOCE3?>

        </div>


        <div class="middle">
            <div class="common-title">
                <h2 class="istop-announcement">置顶公告&简章<span>欢迎加入博硕QQ群：<?=Yii::$app->params['qqGroupNumber']?></span></h2>
            </div>

            <div class="istop">
                <ul>
                    <?php foreach ($top as  $k=>$v ) { ?>
                    <li class="flex">
                        <a href="<?=$v['url']?>" target="_blank" class="orange">【急聘】</a>
                        <a href="<?=$v['url']?>" target="_blank" class="announcement" title="<?=\common\helpers\StringHelper::changeQuotationMark($v['title'])?>"><?=$v['title']?></a>
                        <small><?=$v['refresh_time']?></small>
                    </li>
                    <?php } ?>
                </ul>
            </div>

            <div class="common-title newest">
                <h2>最新公告 & 简章</h2>
            </div>

            <?php foreach ($newest as $item ): ?>
            <div class="box">

                <div class="themes">

                    <div class="job-recruit">
                        <span><?=$item['title']?></span>
                    </div>
                    <div class="more">
                        <a href="<?=$item['url']?>" target="_blank">更多></a>
                    </div>
                </div>

                <div class="istop">
                    <ul>
                        <?php foreach ($item['list'] as  $v ): ?>
                        <li class="flex">
                            <div class="flex-1 flex">
                                <a href="<?=$v['url']?>" target="_blank" class="orange city">【<?=$v['city']?>】</a>
                                <a href="<?=$v['url']?>" target="_blank" class="announcement" title="<?=\common\helpers\StringHelper::changeQuotationMark($v['title'])?>"><?=$v['title']?></a>
                            </div>
                            <small><?=$v['time']?></small>
                        </li>
                        <?php endforeach ?>
                    </ul>
                </div>
            </div>
            <?php endforeach ?>

        </div>


        <div class="h2">
            <?=$H2_1?>
            <div class="right-siderbar-box brand-unit">
                <div class="common-title">
                    <h2>品牌单位</h2>
                </div>

                <?=$H2_2?>

                <ul>
                    <?php foreach ($brandCompanyOne as  $k=>$v ) { ?>
                    <li>
                        <a href="<?=$v['url'];?>" target="_blank" title="<?=\common\helpers\StringHelper::changeQuotationMark($v['title']);?>"><?=$v['title'];?></a>
                    </li>
                    <?php } ?>
                </ul>
                <ul>
                    <?php foreach ($brandCompanyTwo as  $k=>$v ) { ?>
                    <li>
                        <a href="<?=$v['url'];?>" target="_blank" title="<?=\common\helpers\StringHelper::changeQuotationMark($v['title']);?>"><?=$v['title'];?></a>
                    </li>
                    <?php } ?>
                </ul>
                <ul>
                    <?php foreach ($brandCompanyThree as  $k=>$v ) { ?>
                    <li>
                        <a href="<?=$v['url'];?>" target="_blank" title="<?=\common\helpers\StringHelper::changeQuotationMark($v['title']);?>"><?=$v['title'];?></a>
                    </li>
                    <?php } ?>
                </ul>
                <ul>
                    <?php foreach ($brandCompanyFore as  $k=>$v ) { ?>
                    <li>
                        <a href="<?=$v['url'];?>" target="_blank" title="<?=\common\helpers\StringHelper::changeQuotationMark($v['title']);?>"><?=$v['title'];?></a>
                    </li>
                    <?php } ?>
                </ul>
                <!--                <ul>-->
                <!--                    <?php foreach ($brandCompanyMiddle as  $k=>$v ) { ?>-->
                <!--                    <li>-->
                <!--                        <a href="<?=$v['url'];?>" target="_blank"><?=$v['title'];?></a>-->
                <!--                    </li>-->
                <!--                    <?php } ?>-->
                <!--                </ul>-->
                <!--                <ul>-->
                <!--                    <?php foreach ($brandCompanyBottom as  $k=>$v ) { ?>-->
                <!--                    <li>-->
                <!--                        <a href="<?=$v['url'];?>" target="_blank"><?=$v['title'];?></a>-->
                <!--                    </li>-->
                <!--                    <?php } ?>-->
                <!--                </ul>-->
            </div>


            <?=$H2_3?>

            <div class="right-siderbar-box focus-information">
                <div class="common-title">
                    <h2>焦点信息</h2>
                </div>
                <ul class="icon-focus">
                    <?php foreach ($focus as  $k=>$v ) { ?>
                    <li></li>
                    <?php } ?>
                </ul>
                <div id="scrollText">
                    <div id="scroll1">
                        <ul>
                            <?php foreach ($focus as  $k=>$v ) { ?>
                            <li>
                                <a href="<?=$v['url'];?>" target="_blank" title="<?=\common\helpers\StringHelper::changeQuotationMark($v['title']);?>"> <?=$v['time'];?>  <?=$v['title'];?></a>
                            </li>
                            <?php } ?>
                        </ul>
                    </div>
                    <div id="scroll2"></div>
                </div>
            </div>

            <?=$H2_4?>


            <div class="right-siderbar-box recommended-information r">
                <div class="common-title">
                    <h2>推荐信息</h2>
                </div>

                <?=$R?>

                <?php if ($recommendOne[0]): ?>
                <ul class="textbreak">
                    <?php foreach ($recommendOne as  $k=>$v ) { ?>
                    <li>
                        <a href="<?=$v['url'];?>" target="_blank" title="<?=\common\helpers\StringHelper::changeQuotationMark($v['title']);?>"> <?=$v['time'];?>  <?=$v['title'];?></a>
                    </li>
                    <?php } ?>
                </ul>
                <?php endif; ?>
                <?php if ($recommendTwo[0]): ?>
                <ul class="textbreak">
                    <?php foreach ($recommendTwo as  $k=>$v ) { ?>
                    <li>
                        <a href="<?=$v['url'];?>" target="_blank" title="<?=\common\helpers\StringHelper::changeQuotationMark($v['title']);?>"> <?=$v['time'];?>  <?=$v['title'];?></a>
                    </li>
                    <?php } ?>
                </ul>
                <?php endif; ?>
                <?php if ($recommendThree[0]): ?>
                <ul class="textbreak">
                    <?php foreach ($recommendThree as  $k=>$v ) { ?>
                    <li>
                        <a href="<?=$v['url'];?>" target="_blank" title="<?=\common\helpers\StringHelper::changeQuotationMark($v['title']);?>"> <?=$v['time'];?>  <?=$v['title'];?></a>
                    </li>
                    <?php } ?>
                </ul>
                <?php endif; ?>
                <?php if ($recommendFour[0]): ?>
                <ul class="textbreak">
                    <?php foreach ($recommendFour as  $k=>$v ) { ?>
                    <li>
                        <a href="<?=$v['url'];?>" target="_blank" title="<?=\common\helpers\StringHelper::changeQuotationMark($v['title']);?>"> <?=$v['time'];?>  <?=$v['title'];?></a>
                    </li>
                    <?php } ?>
                </ul>
                <?php endif; ?>
                <?php if ($recommendFive[0]): ?>
                <ul class="textbreak">
                    <?php foreach ($recommendFive as  $k=>$v ) { ?>
                    <li>
                        <a href="<?=$v['url'];?>" target="_blank" title="<?=\common\helpers\StringHelper::changeQuotationMark($v['title']);?>"> <?=$v['time'];?>  <?=$v['title'];?></a>
                    </li>
                    <?php } ?>
                </ul>
                <?php endif; ?>
            </div>

            <?=$H2_5?>

            <div class="right-siderbar-box hot-information w">
                <div class="common-title">
                    <h2 class="subtitle">热门信息</h2>
                </div>

                <div class="dwm-ranking ">
                    <div class="hb">
                        <ul class="flex">
                            <li class="hot1 on">3 日热门</li>
                            <li class="hot2">1 周热门</li>
                            <li class="hot3">1 月热门</li>
                        </ul>
                    </div>
                    <div class="dwm-ranking ">

                        <ul class="item" style="display: block;">
                            <?php foreach ($hotTag['threeDay'] as  $k=>$v ) { ?>
                            <li>
                                <?php if ($k<3): ?>
                                <span class="one"><?=$k+1?></span>
                                <?php else:?>
                                <span><?=$k+1?></span>
                                <?php endif; ?>
                                <a href="<?=$v['url']?>" target="_blank" title="<?=\common\helpers\StringHelper::changeQuotationMark($v['title']);?>">
                                    <?=$v['title']?>
                                </a>
                            </li>
                            <?php } ?>
                        </ul>
                        <ul class="item">
                            <?php foreach ($hotTag['week'] as  $k=>$v ) { ?>
                            <li>
                                <?php if ($k<3): ?>
                                <span class="one"><?=$k+1?></span>
                                <?php else:?>
                                <span><?=$k+1?></span>
                                <?php endif; ?>
                                <a href="<?=$v['url']?>" target="_blank" title="<?=common\helpers\StringHelper::changeQuotationMark($v['title']);?>">
                                    <?=$v['title']?>
                                </a>
                            </li>
                            <?php } ?>
                        </ul>
                        <ul class="item">
                            <?php foreach ($hotTag['month'] as  $k=>$v ) { ?>
                            <li>
                                <?php if ($k<3): ?>
                                <span class="one"><?=$k+1?></span>
                                <?php else:?>
                                <span><?=$k+1?></span>
                                <?php endif; ?>
                                <a href="<?=$v['url']?>" target="_blank" title="<?=common\helpers\StringHelper::changeQuotationMark($v['title']);?>">
                                    <?=$v['title']?>
                                </a>
                            </li>
                            <?php } ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>


    </div>


    <div class="job-container flex w">
        <div class="left-content">

            <?= frontendPc\components\JobNavWidget::widget() ?>


            <div class="disseminate">
                <?=$L06?>
                <?=$L07?>
            </div>
        </div>

        <div class="right-content job">
            <?= frontendPc\components\HomeSelectJobWidget::widget() ?>

            <?= frontendPc\components\HomeHotCompanyWidget::widget() ?>


        </div>
    </div>
</div>

<div class="slide-swiper-container">
    <?=$A4?>
    <?=$A5?>
</div>

<div class="fixed-tool">
    <ul>
        <li class="current">A-热招</li>
        <li>B-焦点</li>
        <li>C-头条</li>
        <li>D-地区</li>
        <li>E-长招</li>
        <li>F-推荐</li>
        <li class="special">G-最新</li>
        <li>H-热门</li>
        <li>I-职位</li>
        <li>J-精选</li>
    </ul>
</div>

<?= frontendPc\components\FriendLinkWidget::widget() ?>



<script>
    const searchComponent = {
        data() {
            return {
                type: '<?= common\helpers\UrlHelper::getPathBelongType(Yii::$app->request->pathInfo)?>',
                keyword: '',
            }
        },
        methods: {
            search() {
                switch (this.type) {
                    case '1':
                        window.open('/job?keyword=' + this.keyword);
                        break;
                    case '2':
                        window.open('/search?keyword=' + this.keyword);
                        break;
                    case '3':
                        window.open('/company?keyword=' + this.keyword);
                        break;
                    case '4':
                        window.open('/search?keyword=' + this.keyword + '&type=2');
                        break;
                }
            }
        }
    }

    Vue.createApp(searchComponent).use(ElementPlus).mount('#searchComponent')
</script>

