<div class="latest-job w">
    <div class="common-title component">
        <h2>最新职位</h2>
        <div class="data">
            <div class="amount">
                共<span>
                    {{latestJobTotal}}
                </span>条职位
            </div>
        </div>
    </div>

    <div class="type-select">
        <el-form :model="formData" inline>
            <?php  foreach ($tapList as $key => $item) { ?>
            <el-form-item>
                <el-select @change="() => handleLatestJob()" filterable placeholder="<?php echo $item[0]['v'];?>" clearable v-model="<?php echo 'latestJobForm.'.$key?>" class="m-2"
                           size="small">
                    <?php foreach ($item as $i => $value) { ?>
                    <el-option label="<?php echo $value['v'];?>" value="<?php echo $value['k'];?>"></el-option>
                    <?php } ?>
                </el-select>
            </el-form-item>
            <?php }?>
        </el-form>

        <div class="superior-filter-global" :class="{active: hasSuperiorJobFilter}" @click="handleSuperior(1)">
            高级筛选
            <div class="superior-tips">含编制、热度等会员专属筛选特权</div>
        </div>

        <div class="type-select-tips">
            <span>说明：通过自定义上方职位筛选框内容可匹配更精准的职位信息。</span>
        </div>
    </div>

    <?= frontendPc\components\LatestJobClassificationWidget::widget(['columnId'=>$columnId]) ?>

    <el-pagination background :layout="paginationLayout" :current-page="latestJobForm.page"
                   :total="latestJobTotal" :page-size="latestJobPageSize" @current-change="(val) => handleLatestJob(val)">
    </el-pagination>
</div>