<?php

use common\base\models\BaseMemberMessage;
use frontendPc\components;
use frontendPc\models\Resume;

// 定义一个头部常量
define('HEAD_COMPONENT_LOADED', true);
$memberId = Yii::$app->user->id;
if ($memberId) {
    $unreadCountArr = BaseMemberMessage::getUnreadData($memberId);

    $totalMessageAmount = $unreadCountArr['totalAmount'] > 99 ? '99+' : $unreadCountArr['totalAmount'];
    $jobTrendAmount     = $unreadCountArr['jobTrendAmount'] > 99 ? '99+' : $unreadCountArr['jobTrendAmount'];
    $systemAmount       = $unreadCountArr['systemAmount'] > 99 ? '99+' : $unreadCountArr['systemAmount'];
    $chatAmount         = $unreadCountArr['chatAmount'] > 99 ? '99+' : $unreadCountArr['chatAmount'];
    $vipCardInfo        = Resume::getVipCardInfo($memberId);
}
?>
<header class="page-header-container">
    <div class="pagetop is-login" id="headerContent">
        <?php if (Yii::$app->params['user']['type'] == '1'): ?>
            <div class="page-login-container">
                <?= components\HomeCitySwitchWidget::widget() ?>
                <div class="right-content">
                    <div class="search">
                        <el-form class="main-part">
                            <el-form-item>
                                <el-select autocomplete="off" v-model="type" class="m-2" size="small">
                                    <el-option label="职位" value="1"></el-option>
                                    <el-option label="公告" value="2"></el-option>
                                    <el-option label="单位" value="3"></el-option>
                                    <el-option label="资讯" value="4"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-form>
                        <input @keyup.enter="search" type="text" autocomplete="off" v-model="keyword" placeholder="搜索"/>
                        <a @click="search" target="_blank"></a>
                    </div>
                    <a href="/member/company/applyCooperation" target="_blank" class="application-release">合作申请</a>
                    <a href="/member/company/home" target="_blank" class="recruit">单位登录丨注册</a>

                    <div class="header-account-template" id="headerAccountTemplate" v-cloak>
                        <el-popover popper-class="header-popover-message" trigger="hover" :width="180">
                            <template #reference>
                                <span class="message">
                                    <el-badge :value="total" :hidden="total === 0">
                                        <i class="el-icon-bell"></i>
                                    </el-badge>
                                </span>
                            </template>

                            <ul class="message-options">
                                <li v-for="(item, index) in messageOptions" :key="index" class="item pointer"
                                    @click="handleClick(item)">
                                    <span class="label">{{ item.label }}</span>

                                    <span v-if="item.value" class="value"> <span class="num"> {{ item.value }} </span>条未读 </span>
                                </li>
                            </ul>
                        </el-popover>

                        <el-dropdown popper-class="header-dropdown-popper">
                            <div class="header-account-dropdown">
                                <el-avatar :size="28" :src="avatar"></el-avatar>
                                <div class="vip-logo" v-if="isVip"></div>
                                <!-- <span>木子</span> -->
                                <!-- <i class="el-icon-arrow-down el-icon--right"></i> -->
                            </div>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item
                                            @click="() => ( !Boolean(<?= $vipCardInfo['isServiceUrl']?>) ? openVip('/vip.html') : handleRoute('/job?tab=service'))">
                                        <div class="dropdown-item__card">
                                            <div class="dropdown-item__card-title">
                                                <span><?= $vipCardInfo['type'] ?></span>
                                                <span><?= $vipCardInfo['btnText'] ?></span>
                                            </div>

                                            <div class="dropdown-item__card-desc"><?=$vipCardInfo['describe']?></div>

                                            <div class="dropdown-item__card-tips"><?=$vipCardInfo['content']?></div>
                                        </div>
                                    </el-dropdown-item>

                                    <el-dropdown-item @click="() => handleRoute('/home')">
                                        <div class="dropdown-item-cell">
                                            <span class="name person">个人中心<i class="icon"></i></span>
                                            <span class="tips">智能匹配职位、求职管理</span>
                                        </div>
                                    </el-dropdown-item>

                                    <el-dropdown-item @click="() => handleRoute('/resume')">
                                        <div class="dropdown-item-cell">
                                            <span class="name">
                                                我的简历
                                                <span class="complete" :class="{ 'is-special': resumeComplete >= 75 }"> {{ resumeComplete }}% </span>
                                            </span>
                                            <span class="tips">完整度达75%可投全站职位</span>
                                        </div>
                                    </el-dropdown-item>

                                    <el-dropdown-item @click="() => handleRoute('/delivery')">
                                        <div class="dropdown-item-cell">
                                            <span class="name">投递反馈</span>
                                        </div>
                                    </el-dropdown-item>

                                    <el-dropdown-item @click="() => handleRoute('/view')">
                                        <div class="dropdown-item-cell">
                                            <span class="name">谁看过我</span>
                                        </div>
                                    </el-dropdown-item>

                                    <el-dropdown-item @click="() => handleRoute('/job?tab=tool')">
                                        <div class="dropdown-item-cell">
                                            <span class="name">
                                                求职工具
                                                <span class="complete"> NEW </span>
                                            </span>
                                            <span class="tips">求职无压力，实用工具助你赢在起跑线</span>
                                        </div>
                                    </el-dropdown-item>

                                    <el-dropdown-item>
                                        <div class="dropdown-item-cell">
                                            <div class="nav-free-vip-enter" @click="fetchShareInfo">
                                                <div class="nav-free-vip-header">
                                                    <div class="title">免费薅羊毛</div>
                                                    <div class="regulate" @click.stop="showRegulate">活动规则</div>
                                                </div>
                                                <div class="nav-free-vip-footer">点击<span class="invite">立即邀请>></span>,扫码获取专属分享海报</div>
                                            </div>
                                        </div>
                                    </el-dropdown-item>

                                    <el-dropdown-item @click="() => handleRoute('/setting')">
                                        <div class="dropdown-item-cell">
                                            <span class="name">账号设置</span>
                                            <span class="tips">管理账号、屏蔽单位和简历公开程度</span>
                                        </div>
                                    </el-dropdown-item>

                                    <el-dropdown-item @click="handleLogout">
                                        <div class="dropdown-item-cell is-logout">
                                            <span class="name">退出登录</span>
                                        </div>
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>

                        <el-dialog custom-class="free-vip-share-dialog" append-to-body v-model="shareDialogVisible">
                            <div class="main-container">
                                <div class="title">扫码获取专属分享海报</div>
                                <img class="qr-code" :src="qrCodeUrl" alt="" />
                                <div class="step">
                                    <div class="item scan">扫描二维码</div>
                                    <div class="item save">保存海报</div>
                                    <div class="item send">发送给好友</div>
                                </div>
                            </div>
                        </el-dialog>

                        <el-dialog custom-class="free-vip-regulate-dialog" append-to-body v-model="regulateDialogVisible">
                            <div class="main-container">
                                <div class="title">规则说明</div>

                                <div class="wrapper">
                                    <div class="name">活动内容</div>
                                    <div class="content">向好友发送分享海报/分享链接，邀请好友注册并完善简历，双方均可获得黄金VIP会员 * 3天。</div>
                                </div>

                                <div class="wrapper">
                                    <div class="name">邀请流程</div>
                                    <div class="content">
                                        1、点击“<span class="special">立即邀请>></span>”，扫描二维码获得专属邀请海报，分享给硕博好友。 <br />
                                        2、好友在分享页面正确填写手机号完成注册，并于注册后<span class="special">7日内</span>登录该账号，完善在线简历至65%或以上，即视为邀请成功。<br />
                                        *
                                        若好友注册后，未于7日内将简历完整度完善至65%或以上，则视为邀请不成功。用户可在手机移动端网页&高才优聘小程序：【我的】-【免费领VIP会员】-【我的成就】模块查看好友邀请进度。<br />
                                        3、每成功邀请一位好友，邀请方及被邀请方均可获得高校人才网黄金VIP会员*3天权益。<br />
                                        4、邀请成功，奖励会于当天24:00前发放。奖励发放情况可在手机移动端网页&高才优聘小程序：【我的】-【免费领VIP会员】-【我的成就】模块查看。<br />
                                        5、若发放奖励时，用户有生效中的钻石VIP套餐，奖励将发放失败，请务必知悉。<br />
                                        6、同一用户最多可获取15次奖励，累计45天。超过上限后，邀请成功不再发放相应奖励。<br />
                                        <span class="bold">7、本活动最终解释权归高校人才网所有。</span>
                                    </div>
                                </div>
                            </div>
                        </el-dialog>
                    </div>

                </div>
            </div>
        <?php else: ?>
            <div class="page-login-container">
                <?= components\HomeCitySwitchWidget::widget() ?>
                <div class="right-content">
                    <div class="search">
                        <el-form class="main-part">
                            <el-form-item>
                                <el-select autocomplete="off" v-model="type" class="m-2" size="small">
                                    <el-option label="职位" value="1"></el-option>
                                    <el-option label="公告" value="2"></el-option>
                                    <el-option label="单位" value="3"></el-option>
                                    <el-option label="资讯" value="4"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-form>
                        <input @keyup.enter="search" autocomplete="off" type="text" autocomplete="off" v-model="keyword"
                               placeholder="搜索"/>
                        <a @click="search" target="_blank"></a>
                    </div>
                    <a href="/member/company/applyCooperation" target="_blank" class="application-release">合作申请</a>
                    <a href="/member/company/home" target="_blank" class="recruit">单位登录丨注册</a>
                    
                    <div class="login-register-container" style="display: flex;">
                        <a href="/member/person/home" target="_blank" class="job-seekers">求职者登录</a>
                        <span class="line">|</span>
                        <a href="/member/person/registry" target="_blank">注册</a>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
    <?= components\HeadNavWidget::widget() ?>

</header>

<script>
    const Header = {
        data() {
            return {
                name: '<?= Yii::$app->params['user']['showName']; ?>',
                isLogin: false,
                type: "<?= common\helpers\UrlHelper::getPathBelongType(Yii::$app->request->pathInfo)?>",
                keyword: "",
                avatar: '<?=Yii::$app->params['user']['avatar']?>',
                resumeComplete: '<?=Yii::$app->params['user']['resumePercent']?>',
                isVip: "<?= \frontendPc\models\Resume::checkVip(Yii::$app->user->id)?>",
                vipInfo: <?= json_encode(\frontendPc\models\Resume::getVipInfo(Yii::$app->user->id))?>,

                total: "<?=$totalMessageAmount ?: ''?>",
                messageOptions: [
                    {
                        label: '我的直聊',
                        value: "<?=$chatAmount ?: ''?>",
                        path: '/chat'
                    },
                    {
                        label: '求职动态',
                        value: "<?=$jobTrendAmount ?: ''?>",
                        path: '/message?read=2&type=0'
                    },
                    {
                        label: '系统通知',
                        value: "<?=$systemAmount ?: ''?>",
                        path: '/message?read=2&type=3'
                    }
                ],

                shareDialogVisible: false,
                regulateDialogVisible: false,
                qrCodeUrl: ''
            }
        },
        methods: {
            onMounted() {
                this.type = "1"
                this.keyword = ""
            },
            handleLogout: function () {
                this.$confirm('确定退出登录?', '提示', {
                    buttonSize: 'large',
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    // type: 'warning',
                })
                    .then(() => {
                        this.loginOut()
                    }).catch(_ => {
                })

            },
            search() {
                switch (this.type) {
                    case '1':
                        window.open('/job?keyword=' + this.keyword);
                        break;
                    case '2':
                        window.open('/search?keyword=' + this.keyword);
                        break;
                    case '3':
                        window.open('/company?keyword=' + this.keyword);
                        break;
                    case '4':
                        window.open('/search?keyword=' + this.keyword + '&type=2');
                        break;
                }
            },
            removeToken() {
                window.localStorage.clear()
                window.sessionStorage.clear()
                removeToken()
            },

            openVip(path) {
                window.location.href = path
            },

            handleRoute(path) {
                window.location.href = '/member/person' + path
            },

            handleClick(data) {
                this.handleRoute(data.path)
            },

            loginOut: function () {
                httpGet('/api/member/logout').then(response => {
                    this.name = ''
                    this.removeToken()
                    window.location.reload()
                }).catch(error => console.log(error));
            },

            showRegulate() {
                this.regulateDialogVisible = true
            },

            async fetchShareInfo() {
                const { codeUrl } = await httpGet('/api/person/new-resume/create')

                this.qrCodeUrl = codeUrl
                this.shareDialogVisible = true
            }
        },
    }
    Vue.createApp(Header).use(ElementPlus).mount('#headerContent')
</script>
<style>
    [v-clock] {
        display: none !important;
    }
</style>
<?= frontendPc\components\HomeSideBarWidget::widget() ?>
