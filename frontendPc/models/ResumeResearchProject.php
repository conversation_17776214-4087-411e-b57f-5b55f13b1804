<?php

namespace frontendPc\models;

use common\base\models\BaseDictionary;
use common\base\models\BaseMember;
use common\base\models\BaseMemberActionLog;
use common\base\models\BaseResumeResearchProject;
use common\helpers\TimeHelper;
use common\libs\Cache;
use yii\base\BaseObject;
use yii\base\Exception;

class ResumeResearchProject extends BaseResumeResearchProject
{
    /**
     * 获取科研方向信息
     * @param $memberId
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function getLastProject($memberId)
    {
        $info              = self::find()
            ->where([
                'member_id' => $memberId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->orderBy('id DESC')
            ->select([
                'name',
                'begin_date as beginDate',
                'end_date as endDate',
                'role',
                'company',
                'category',
                'description',
                'is_close as isClose',
            ])
            ->asArray()
            ->one();
        $info['beginDate'] = TimeHelper::formatToYearMonth($info['beginDate']);
        if ($info['endDate'] == TimeHelper::ZERO_DATE) {
            $info['endDate'] = '0000-00';
        } else {
            $info['endDate'] = TimeHelper::formatToYearMonth($info['endDate']);
        }

        return $info;
    }

    /**
     * 获取科研信息列表
     * @param $memberId
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getInfoList($memberId)
    {
        $list = self::find()
            ->where([
                'member_id' => $memberId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->select([
                'id',
                'name',
                'begin_date as beginDate',
                'end_date as endDate',
                'role',
                'company',
                'category',
                'description',
                'is_close as isClose',
            ])
            //            ->limit(self::LIMIT_NUM)
            ->orderBy('end_date desc,id desc')
            ->asArray()
            ->all();
        foreach ($list as $k => &$v) {
            $v['categoryName'] = BaseDictionary::getProjectCateName($v['category']);
            $v['beginDate']    = TimeHelper::formatToYearMonth($v['beginDate']);

            //处理一下回显
            if ($v['endDate'] == TimeHelper::ZERO_DATE) {
                $v['endDate'] = '0000-00';
            } else {
                $v['endDate'] = TimeHelper::formatToYearMonth($v['endDate']);
            }
        }

        return $list;
    }

    /**
     * @throws Exception
     */
    public static function saveInfo($data)
    {
        //判断参数
        if (strlen($data['name']) < 1 || strlen($data['beginDate']) < 1 || strlen($data['endDate']) < 1 || strlen($data['role']) < 1 || strlen($data['description']) < 1 || mb_strlen($data['description'],
                'UFT-8') > 500) {
            throw new Exception('缺失必填参数');
        }

        if (strtotime($data['beginDate']) > time()) {
            throw new Exception('项目周期开始时间不得晚于当前时间');
        }
        //判断参数是否正确
        if (!empty($data['beginDate']) && !empty($data['endDate'])) {
            $endDate = strtotime($data['endDate']);
            if ($endDate > 0 && strtotime($data['beginDate']) >= $endDate) {
                throw new Exception('项目周期时间不得早于或者等于开始时间');
            }
        }
        //判断参数是否正确--这里endDate 0000-00-00为至今
        //这里处理一下
        if ($data['endDate'] == '0000-00') {
            $endDate = $data['endDate'] . '-00';
        } else {
            $endDate = TimeHelper::formatAddDay($data['endDate']);
        }
        if (!empty($data['id'])) {
            $model    = self::findOne($data['id']);
            $memberId = $model->member_id;
            if (empty($model) || $model->status != self::STATUS_ACTIVE) {
                throw new Exception('该项目经历记录不存在');
            }
        } else {
            $model = new ResumeResearchProject();
            if (!empty($data['memberId']) && !empty($data['resumeId'])) {
                $memberId         = $data['memberId'];
                $resumeId         = $data['resumeId'];
                $model->member_id = $memberId;
                $model->resume_id = $resumeId;
            } else {
                $memberId = \Yii::$app->user->id;
                $resumeId = BaseMember::getMainId($memberId);

                $model->member_id = $memberId;
                $model->resume_id = $resumeId;
            }
        }

        $model->name        = $data['name'];
        $model->category    = $data['category'];
        $model->role        = $data['role'];
        $model->begin_date  = TimeHelper::formatAddDay($data['beginDate']);
        $model->end_date    = $endDate;
        $model->company     = $data['company'];
        $model->description = $data['description'];
        $model->is_close    = $data['isClose'];

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        //修改简历完成度
        ResumeComplete::updateResumeCompleteInfo($memberId);
        //新增操作日志
        $log_data = [
            'content' => '保存简历项目经历信息，memberId：' . $memberId,
        ];
        // 写日志
        BaseMemberActionLog::log($log_data);
    }

    public static function delProject($id, $memberId)
    {

        // 先判断是否最后一条了
        $count = self::find()
            ->where([
                'member_id' => $memberId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->count();

        if ($count <= 1) {
            throw new Exception('至少保留一条项目经历');
        }

        $model = self::findOne($id);
        if ($model->member_id != $memberId) {
            throw new Exception('该记录与当前用户信息不匹配');
        }

        $model->status = self::STATUS_DELETE;

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        //更新简历完成度表
        ResumeComplete::updateResumeCompleteInfo($memberId);

        //新增操作日志
        $data = [
            'content' => '删除研究方向id：' . $id,
        ];
        // 写登录日志
        BaseMemberActionLog::log($data);
    }

}