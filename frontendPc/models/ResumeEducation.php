<?php

namespace frontendPc\models;

use common\base\models\BaseDictionary;
use common\base\models\BaseMajor;
use common\base\models\BaseMemberActionLog;
use common\base\models\BaseResumeEducation;
use common\helpers\ArrayHelper;
use common\helpers\TimeHelper;
use common\libs\Cache;
use yii\base\BaseObject;
use yii\base\Exception;

class ResumeEducation extends BaseResumeEducation
{

    /**
     * 获取用户多条教育信息
     * @param $memberId
     * @return array|\yii\db\ActiveRecord[]
     * @throws \Exception
     */
    public static function getInfoList($memberId)
    {
        $list = self::find()
            ->where([
                'member_id' => $memberId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->select([
                'id',
                'begin_date as studyBeginDate',
                'end_date as studyEndDate',
                'is_abroad as isOverseasStudy',
                'school',
                'college',
                'is_recruitment as isRecruitment',
                'education_id as educationId',
                'major_id as majorId',
                'is_project_school',
                'major_custom as majorCustom',
                'mentor',
            ])
            //            ->limit(self::LIMIT_NUM)
            ->orderBy('end_date desc,id desc')
            ->asArray()
            ->all();
        foreach ($list as $k => $v) {
            $list[$k]['educationName']  = Dictionary::getEducationName($v['educationId']);
            $list[$k]['majorName']      = $v['majorId'] > 0 ? Major::getMajorName($v['majorId']) : $v['majorCustom'];
            $list[$k]['studyBeginDate'] = TimeHelper::formatToYearMonth($v['studyBeginDate']);
            $list[$k]['studyEndDate']   = TimeHelper::formatToYearMonth($v['studyEndDate']);
        }

        return $list;
    }

    public static function delEducation($id, $memberId)
    {
        // 先判断是否最后一条了
        $count = self::find()
            ->where([
                'member_id' => $memberId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->count();

        if ($count <= 1) {
            throw new Exception('至少保留一条教育经历');
        }

        $model = self::findOne($id);

        if ($model->member_id != $memberId) {
            throw new Exception('该记录与当前用户信息不匹配');
        }

        $model->status = self::STATUS_DELETE;

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        //更新简历完成度表
        ResumeComplete::updateResumeCompleteInfo($memberId);

        //新增操作日志
        $data = [
            'content' => '删除教育经历id：' . $id,
        ];
        //保存完，要重新获取一次最高学历的id，保存到简历表
        // $lastEducationInfo = self::find()
        //     ->where(['member_id' => $memberId])
        //     ->andWhere(['status' => self::STATUS_ACTIVE])
        //     ->orderBy('end_date desc')
        //     ->select('id')
        //     ->one();
        // if (!empty($lastEducationInfo)) {
        //     $lastEducationId = ArrayHelper::getValue($lastEducationInfo, 'id');
        // } else {
        //     $lastEducationId = $model->id;
        // }
        $resumeModel                     = Resume::findOne(['member_id' => $memberId]);
        $resumeModel->last_education_id  = BaseResumeEducation::getTopEducationId($resumeModel->id);
        $resumeModel->top_education_code = BaseResumeEducation::getTopEducationCode($resumeModel->id);

        $resumeModel->save();

        // 写登录日志
        BaseMemberActionLog::log($data);
    }

    /**
     * 废弃了不要用了
     * @param $memberId
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function getHighestSchoolName($memberId)
    {
        $resumeModel     = Resume::findOne(['member_id' => $memberId]);
        $lastEducationId = self::getTopEducationId($resumeModel->id);

        $info = self::find()
            ->where([
                'status' => self::STATUS_ACTIVE,
                'id'     => $lastEducationId,
            ])
            ->select([
                'school',
            ])
            ->asArray()
            ->one();

        return ArrayHelper::getValue($info, 'school');
    }

}