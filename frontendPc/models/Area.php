<?php

namespace frontendPc\models;

use common\base\models\BaseArea;
use Yii;

class Area extends BaseArea
{
    /**
     * PC端职位列表和企业列表的地区(热词)
     * @return array
     */
    public static function getSearchHotList()
    {
        $list = Yii::$app->params['jobAndCompanyListHotArea'];

        return $list;


    }    /**
     * PC端职位列表和企业列表的地区(热词)
     * @return array
     */
    public static function getNewSearchHotList()
    {
        $list = Yii::$app->params['newAndCompanyListHotArea'];

        return $list;
    }
}