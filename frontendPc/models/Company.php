<?php

namespace frontendPc\models;

use admin\models\Dictionary;
use common\base\BaseActiveRecord;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyChildUnit;
use common\base\models\BaseCompanyCollect;
use common\base\models\BaseCompanyContact;
use common\base\models\BaseCompanyInfoAuth;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseDictionary;
use common\base\models\BaseFile;
use common\base\models\BaseHwActivity;
use common\base\models\BaseHwActivityAnnouncement;
use common\base\models\BaseHwActivitySession;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseMember;
use common\base\models\BaseMemberAddress;
use common\base\models\BaseResume;
use common\base\models\BaseTrade;
use common\base\models\BaseWelfareLabel;
use common\helpers\ArrayHelper;
use common\helpers\FileHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use common\helpers\ValidateHelper;
use common\libs\BaiduTimeFactor;
use common\libs\ToutiaoTimeFactor;
use common\service\search\CommonSearchApplication;
use queue\MeilisearchJob;
use queue\Producer;
use Yii;
use yii\base\Exception;
use yii\db\ActiveRecord;
use common\libs\Cache;
use yii\db\Expression;

class Company extends BaseCompany
{

    /**
     * 提交第一阶段审核.
     *
     * // 这里其实是有三种情况,一种是被驳回了,那么按理就应该重新生成数据
     * // 还有一种情况,是自己修改的,这种情况这个认证还没被审核并且是在阶段1才允许的
     * // 还有一种情况,就是没有提交过审核的
     * // 那么先找看看之前有没有审核信息
     *
     * @param $data
     *
     * @throws Exception
     */
    public function submitAuthPhaseOne($data)
    {
        $memberId = Yii::$app->user->id;
        $company  = Company::findOne(['member_id' => $memberId]);

        $auth = CompanyInfoAuth::find()
            ->where(['company_id' => $company->id])
            ->orderBy('id desc')
            ->one();
        if ($auth['audit_status'] == BaseCompanyInfoAuth::AUDIT_STATUS_NO || $auth['audit_status'] == BaseCompanyInfoAuth::AUDIT_STATUS_SECOND_REJECT) {
            // 在这里开始做一些前置判断
            if ($auth) {
                // 看看是否已经通过审核了,通过就不给提交了
                if ($auth['audit_status'] == BaseCompanyInfoAuth::AUDIT_STATUS_PASS) {
                    throw new Exception('你已通过审核,无需再提交信息');
                }
                switch ($auth['phase']) {
                    case BaseCompanyInfoAuth::PHASE_ONE:
                    case BaseCompanyInfoAuth::PHASE_NO:
                    case BaseCompanyInfoAuth::PHASE_TWO:
                        $authModel = CompanyInfoAuth::findOne($auth['id']);
                        break;
                    default:
                        throw new Exception('不存在的阶段');
                }
            }

            if (!$auth) {
                // 第一次提交审核,是可以提交成功的
                $authModel = new CompanyInfoAuth();
            }

            // 做一些基本的信息判断
            if (empty($data['fullName'])) {
                throw new Exception('单位名称不能为空');
            }
            if (empty($data['type'])) {
                throw new Exception('请选择单位类型');
            }
            if (empty($data['nature'])) {
                throw new Exception('请选择行业性质');
            }
            if (empty($data['industryId'])) {
                throw new Exception('请选择所属行业');
            }
            if (empty($data['areaId'])) {
                throw new Exception('请选择地区');
            }
            if (empty($data['address'])) {
                throw new Exception('请填写详细地址');
            }
            if (empty($data['contact'])) {
                throw new Exception('请填写联系人');
            }
            if (empty($data['department'])) {
                throw new Exception('请填写所在部门');
            }
            if (empty($data['email'])) {
                throw new Exception('请填写邮箱');
            }
            if (empty($data['mobile'])) {
                throw new Exception('请填联系人手机号');
            }

            // 检查一下合法性
            if (!ValidateHelper::isEmail($data['email'])) {
                throw new Exception('邮箱格式错误');
            }
            // 检查一下合法性
            if (!ValidateHelper::isMobileCN($data['mobile'])) {
                throw new Exception('联系人手机号格式错误');
            }

            if (Company::checkNameOnly($company->id, trim($data['fullName']))) {
                throw new Exception('该单位名称已存在');
            }
            //创建人
            $company->create_admin_id = $memberId;
            $company->contact         = $data['contact'];
            $company->department      = $data['department'];
            $company->address         = $data['address'];
            $company->status          = BaseCompany::AUDIT_STATUS_NO;
            if (!$company->save()) {
                throw new Exception($company->getFirstErrorsMessage());
            }
            // 不采用load方法,因为不能确保前端会不会多传字段过来
            $authModel->member_id    = $memberId;
            $authModel->company_id   = $company->id;
            $authModel->phase        = BaseCompanyInfoAuth::PHASE_ONE;
            $authModel->audit_status = BaseCompanyInfoAuth::AUDIT_STATUS_NO;
            $authModel->full_name    = trim($data['fullName']);
            $authModel->type         = $data['type'];
            $authModel->nature       = $data['nature'];
            $authModel->industry_id  = $data['industryId'];
            //            $authModel->area_id      = $data['areaId'];
            $authModel->province_id = $data['areaId'][0];
            $authModel->city_id     = $data['areaId'][1];
            $authModel->district_id = $data['areaId'][2];
            $authModel->address     = $data['address'];
            $authModel->contact     = $data['contact'];
            $authModel->department  = $data['department'];
            $authModel->mobile      = $data['mobile'];
            //            $authModel->submit_audit_time = CUR_DATETIME; // 修改添加时间
            if ($data['telephone']) {
                $authModel->telephone = $data['telephone'];
            }
            $authModel->email = $data['email'];

            if (!$authModel->save()) {
                throw new Exception($authModel->getFirstErrorsMessage());
            }

            // 【修复】同步更新Company状态，确保数据一致性
            // 当重新提交第一阶段时，需要重置Company状态为未提交状态
            $company->status = BaseCompany::AUDIT_STATUS_NO; // 重置为审核未提交状态
            if (!$company->save()) {
                throw new Exception($company->getFirstErrorsMessage());
            }

            // 会员地址
            $memberAddress = new MemberAddress();

            $memberAddress->member_id   = $memberId;
            $memberAddress->province_id = $data['areaId'][0];
            $memberAddress->city_id     = $data['areaId'][1];
            $memberAddress->district_id = $data['areaId'][2];
            $memberAddress->detail      = $data['address'];
            if (!$memberAddress->save()) {
                throw new Exception($memberAddress->getFirstErrorsMessage());
            }
            //更新单位信息表
            $company_member_info             = BaseCompanyMemberInfo::findOne(['member_id' => $memberId]);
            $company_member_info->contact    = $data['contact'];
            $company_member_info->department = $data['department'];
            if (!$company_member_info->save()) {
                throw new Exception($company_member_info->getFirstErrorsMessage());
            }
        } else {
            throw new Exception('在此阶段无法再次提交基本信息');
        }
    }

    /**
     * 提交第二阶段审核信息.
     *
     * @param $data
     *
     * @throws Exception
     */
    public function submitAuthPhaseTwo($data)
    {
        $memberId  = Yii::$app->user->id;
        $companyId = BaseCompany::findOneVal(['member_id' => $memberId], 'id');
        $auth      = CompanyInfoAuth::find()
            ->where(['company_id' => $companyId])
            ->orderBy('id desc')
            ->one();

        if (!$auth) {
            throw new Exception('非法操作');
        }

        if ($auth['audit_status'] == BaseCompanyInfoAuth::AUDIT_STATUS_NO || $auth['audit_status'] == BaseCompanyInfoAuth::AUDIT_STATUS_SECOND_REJECT) {
            if (!$data['licensePath']) {
                throw new Exception('请上传单位资质认证');
            }

            $auth->license_path = $data['licensePath'];
            if ($data['personInfoPath']) {
                // 如果有个人资质认证,就进入第二阶段,要不就需要进入第三阶段
                $auth->person_info_path = $data['personInfoPath'];
            }
            $auth->phase             = BaseCompanyInfoAuth::PHASE_TWO;
            $auth->audit_status      = BaseCompanyInfoAuth::AUDIT_STATUS_SECOND_WAIT;
            $auth->submit_audit_time = CUR_DATETIME;
            if (!$auth->save()) {
                throw new Exception($auth->getFirstErrorsMessage());
            }

            // 同步更新单位表的主状态
            $company = Company::findOne(['member_id' => Yii::$app->user->id]);
            if ($company) {
                $company->status = BaseCompany::STATUS_WAIT_FIRST_AUDIT;
                if (!$company->save()) {
                    throw new Exception($company->getFirstErrorsMessage());
                }
            }
        } else {
            throw new Exception('在此阶段无法再次提交基本信息');
        }
    }

    /**
     * 提交第三阶段审核.
     *
     * @param $data
     *
     * @throws Exception
     */
    public function submitAuthPhaseThree($data)
    {
        $memberId = Yii::$app->user->id;
        //这里一定是主账号，先获取一下member信息
        $member_info = Member::findOne($memberId);
        //如果不是主账号提交的就直接报错
        if ($member_info->company_member_type != BaseMember::COMPANY_MEMBER_TYPE_MAIN) {
            throw new Exception('当前账号无权提交单位审核资质');
        }
        $company = Company::findOne(['member_id' => $memberId]);
        $auth    = CompanyInfoAuth::find()
            ->where(['company_id' => $company->id])
            ->orderBy('id desc')
            ->one();
        if ($auth['audit_status'] == BaseCompanyInfoAuth::AUDIT_STATUS_FIRST_REJECT || $auth['audit_status'] == BaseCompanyInfoAuth::AUDIT_STATUS_IDENTITY_AUTH || BaseCompanyInfoAuth::AUDIT_STATUS_SECOND_PASS_IDENTITY_REJECT) {
            if (!$auth) {
                throw new Exception('非法操作');
            }
            if (!in_array($auth['audit_status'], [
                BaseCompanyInfoAuth::AUDIT_STATUS_IDENTITY_AUTH,
                BaseCompanyInfoAuth::AUDIT_STATUS_FIRST_REJECT,
                BaseCompanyInfoAuth::AUDIT_STATUS_SECOND_PASS_IDENTITY_REJECT,
            ])) {
                throw new Exception('非法状态');
            }
            if (!$data['personInfoPath']) {
                throw new Exception('请上传经办人身份认证');
            }
            //这里修改企业状态 等待复审
            $company->status = BaseCompany::STATUS_WAIT_SECOND_AUDIT;
            $company->save();

            $auth->person_info_path  = $data['personInfoPath'];
            $auth->phase             = BaseCompanyInfoAuth::PHASE_THREE;
            $auth->audit_status      = BaseCompanyInfoAuth::AUDIT_STATUS_FIRST_WAIT;
            $auth->submit_audit_time = CUR_DATETIME;
            if (!$auth->save()) {
                throw new Exception($auth->getFirstErrorsMessage());
            }
        }
    }

    /**
     * 获取审核的信息.
     *
     * @return array|ActiveRecord
     */
    public static function getAuthInfo()
    {
        $memberId = Yii::$app->user->id;

        $auth = CompanyInfoAuth::find()
            ->alias('c')
            ->select([
                'phase',
                'audit_status',
                'reason',
                'full_name',
                'c.type',
                'nature',
                'industry_id',
                'province_id',
                'city_id',
                'district_id',
                'address',
                'contact',
                'mMobile' => 'm.mobile',
                'cMobile' => 'c.mobile',
                'mEmail'  => 'm.email',
                'cEmail'  => 'c.email',
                'department',
                'telephone',
                'license_path',
                'person_info_path',
            ])
            ->innerJoin(['m' => Member::tableName()], 'c.member_id=m.id')
            ->where(['member_id' => $memberId])
            ->asArray()
            ->one();

        if (!$auth) {
            return [];
        } else {
            // 首选是审核资料里面的邮箱和手机号
            $auth['email']   = $auth['cEmail'] ?: $auth['mEmail'];
            $auth['mobile']  = $auth['cMobile'] ?: $auth['mMobile'];
            $auth['area_id'] = [
                $auth['province_id'],
                $auth['city_id'],
            ];
            // 拼接一下全路径
            $auth['license_url']     = FileHelper::getFullUrl($auth['license_path']);
            $auth['person_info_url'] = FileHelper::getFullUrl($auth['person_info_path']);

            //返回值处理
            if ($auth['type'] == 0) {
                $auth['type'] = '';
            }
            if ($auth['nature'] == 0) {
                $auth['nature'] = '';
            }
        }

        return $auth;
    }

    /**
     * 获取公司联系信息
     * @return array
     */
    public function getContactInfo(): array
    {
        $memberId      = Yii::$app->user->id;
        $companyInfo   = self::getCompanyInfo($memberId);
        $areaCache     = BaseArea::setAreaCache();
        $provinceTitle = $areaCache[$companyInfo['province_id']]['name'];
        $cityTitle     = $areaCache[$companyInfo['city_id']]['name'];

        return [
            'fullName'  => $companyInfo['full_name'],
            'contact'   => $companyInfo['contact'],
            'telephone' => $companyInfo['telephone'],
            'address'   => $provinceTitle . '-' . $cityTitle . $companyInfo['address'],
        ];
    }

    /**
     * 获取某个企业在账号页面的详细设置
     */
    public static function getFullInfo($companyId, $memberId)
    {
        // 首先找到基本的信息
        $baseInfo = self::find()
            ->select([
                'full_name',
                'short_name',
                'english_name',
                'type',
                'nature',
                'scale',
                'industry_id',
                'welfare_label_ids',
                'website',
                'introduce',
                'logo_url',
                'label_ids',
                'head_banner_url',
                'district_id',
                'province_id',
                'city_id',
                'address',
                'style_atlas',
                'mobile_head_banner_url',
            ])
            ->where(['id' => $companyId])
            ->asArray()
            ->one();

        $baseInfo['logo_full_url']          = FileHelper::getFullUrl($baseInfo['logo_url']);
        $baseInfo['head_banner_url']        = FileHelper::getFullUrl($baseInfo['head_banner_url']);
        $baseInfo['mobile_head_banner_url'] = FileHelper::getFullUrl($baseInfo['mobile_head_banner_url']);

        //企业风采图集
        $styleAtlas                 = array_filter(explode(',', $baseInfo['style_atlas']));
        $baseInfo['styleAtlasList'] = [];

        foreach ($styleAtlas as $key => $item) {
            $file                         = BaseFile::findOne(['id' => $item]);
            $temp                         = [];
            $temp['id']                   = $item;
            $temp['url']                  = FileHelper::getFullUrl($file['path'], $file['platform']);
            $baseInfo['styleAtlasList'][] = $temp;
        }

        // 福利标签
        $welfareLabelWhere       = [
            'id' => explode(',', $baseInfo['welfare_label_ids']),
        ];
        $welfareLabelSelect      = [
            'id',
            'name',
        ];
        $welfareLabelList        = WelfareLabel::findList($welfareLabelWhere, $welfareLabelSelect);
        $baseInfo['welfareTage'] = [];
        foreach ($welfareLabelList as $k => $welfareLabel) {
            $baseInfo['welfareTage'][$k]['k'] = $welfareLabel['id'];
            $baseInfo['welfareTage'][$k]['v'] = $welfareLabel['name'];
        }

        // 单位标签
        $companyLabelWhere       = [
            'code' => explode(',', $baseInfo['label_ids']),
            'type' => Dictionary::TYPE_26,
        ];
        $companyLabelSelect      = [
            'code',
            'name',
        ];
        $companyLabelList        = Dictionary::findList($companyLabelWhere, $companyLabelSelect);
        $baseInfo['companyTage'] = [];
        foreach ($companyLabelList as $k => $companyLabel) {
            $baseInfo['companyTage'][$k]['k'] = $companyLabel['code'];
            $baseInfo['companyTage'][$k]['v'] = $companyLabel['name'];
        }

        // 所属行业数组格式返回
        $parentId = Trade::findOneVal(['id' => $baseInfo['industry_id']], 'parent_id');;
        if ($baseInfo['industry_id'] === '0') {
            $baseInfo['industry_id'] = '';
        }
        $baseInfo['tradeArr'] = [
            $parentId,
            $baseInfo['industry_id'],
        ];

        // 所属地区数组格式返回
        $areaParentId = Area::findOneVal(['id' => $baseInfo['city_id']], 'parent_id');
        if ($baseInfo['city_id'] === '0') {
            $baseInfo['city_id'] = '';
        }
        $baseInfo['areaArr'] = [
            $areaParentId,
            $baseInfo['city_id'],
        ];

        $keyBaseInfo = [];
        foreach ($baseInfo as $k => $v) {
            $keyBaseInfo['base_' . $k] = $v;
        }

        /**
         * 联系人信息
         */
        $contactInfo = CompanyContact::find()
            ->select([
                'name',
                'mobile',
                'email',
                'telephone',
                'department',
                'fax',
                'name_is_public',
                'mobile_is_public',
                'email_is_public',
            ])
            ->where(['company_id' => $companyId])
            ->asArray()
            ->one() ?: [
            'name'             => '',
            'mobile'           => '',
            'email'            => '',
            'telephone'        => '',
            'department'       => '',
            'fax'              => '',
            'name_is_public'   => '',
            'mobile_is_public' => '',
            'email_is_public'  => '',
        ];

        $keyContactInfo = [];
        foreach ($contactInfo as $k => $v) {
            $keyContactInfo['contact_' . $k] = $v;
        }

        // 二级单位(二级院校)
        $childInfo = CompanyChildUnit::find()
            ->select([
                'id',
                'name',
                'contact',
                'telephone',
                'fax',
                'status',
            ])
            ->where([
                'company_id' => $companyId,
                'status'     => self::STATUS_ACTIVE,
            ])
            ->asArray()
            ->all();

        $keyChildInfo['childColleges'] = [];
        foreach ($childInfo as $k => $v) {
            $keyChildInfo['childColleges'][$k] = $v;
        }

        // 拿地址信息
        $addressInfo = MemberAddress::find()
            ->select([
                'lat',
                'lng',
                'district_id',
                'province_id',
                'city_id',
                'detail',
            ])
            ->where(['member_id' => $memberId])
            ->asArray()
            ->one() ?: [
            'lat'         => '',
            'lng'         => '',
            'district_id' => '',
            'province_id' => '',
            'city_id'     => '',
            'detail'      => '',
        ];

        $keyAddressInfo = [];
        foreach ($addressInfo as $k => $v) {
            $keyAddressInfo['address_' . $k] = $v;
        }

        return array_merge($keyBaseInfo, $keyContactInfo, $keyChildInfo, $keyAddressInfo);
    }

    /**
     * @param $memberId
     * @param $data
     *              baseFullName": "",
     *              "baseShortName": "",
     *              "baseEnglishName": "",
     *              "baseType": "0",
     *              "baseNature": "0",
     *              "baseScale": "0",
     *              "baseIndustryId": "0",
     *              "baseWelfareLabelIds": "",
     *              "baseWebsite": "",
     *              "baseIntroduce": "",
     *              "baseLogoUrl": "",
     *              "baseLogoFullUrl": "",
     *              "contactName": "",
     *              "contactMobile": "",
     *              "contactEmail": "",
     *              "contactTelephone": "",
     *              "contactDepartment": "",
     *              "contactFax": "",
     *              "contactNameIsPublic": "",
     *              "contactMobileIsPublic": "",
     *              "contactEmailIsPublic": "",
     *              "childName": "",
     *              "childContact": "",
     *              "childTelephone": "",
     *              "childFax": "",
     *              "addressLat": "",
     *              "addressLng": "",
     *              "addressDistrictId": "",
     *              "addressProvinceId": "",
     *              "addressCityId": "",
     *              "addressDetail": ""
     */
    public function updateFullInfo($memberId, $data)
    {
        $companyId = BaseCompanyMemberInfo::findOneVal(['member_id' => $memberId], 'company_id');
        $company   = self::findOne(['id' => $companyId]);

        if (!$company) {
            throw new Exception('非法操作');
        }
        if (empty($data['baseNature'])) {
            throw new Exception('请选择所单位性质');
        }
        if (empty($data['baseScale']) && $data['baseScale'] != 0) {
            throw new Exception('请选择单位规模');
        }
        if (empty($data['baseIndustryId'])) {
            throw new Exception('请选择所属行业');
        }
        if (empty($data['baseIntroduce'])) {
            throw new Exception('请填写单位介绍');
        }
        if (empty($data['baseLogoUrl'])) {
            throw new Exception('请上传单位Logo');
        }
        if (empty($data['contactName'])) {
            throw new Exception('请填写联系人');
        }
        if (empty($data['contactMobile'])) {
            throw new Exception('请填写联系电话');
        }
        if (empty($data['contactEmail'])) {
            throw new Exception('请填写联系邮箱');
        }
        if (empty($data['contactDepartment'])) {
            throw new Exception('请填写所在部门');
        }
        if (empty($data['baseAreaArr'])) {
            throw new Exception('请选择地区');
        }
        if (empty($data['addressDetail'])) {
            throw new Exception('请填写联系地址');
        }

        $companyId = $company->id;

        /**
         * 更新基础信息部分
         */
        $company->english_name           = $data['baseEnglishName'];
        $company->type                   = $data['baseType'];
        $company->nature                 = $data['baseNature'];
        $company->scale                  = $data['baseScale'];
        $company->industry_id            = $data['baseIndustryId'];
        $company->welfare_label_ids      = $data['baseWelfareLabelIds'] ?: '';
        $company->label_ids              = $data['baseLabelIds'] ?: '';
        $company->website                = $data['baseWebsite'];
        $company->introduce              = $data['baseIntroduce'];
        $company->logo_url               = $data['baseLogoUrl'];
        $company->head_banner_url        = $data['baseHeadBannerUrl'] ?: '';
        $company->province_id            = $data['baseAreaArr'][0];
        $company->city_id                = $data['baseAreaArr'][1];
        $company->address                = $data['addressDetail'];
        $company->contact                = $data['contactName'];
        $company->fax                    = $data['contactFax'];
        $company->telephone              = $data['contactMobile'];
        $company->style_atlas            = $data['styleAtlas'] ?: '';
        $company->mobile_head_banner_url = $data['baseMobileHeadBannerUrl'] ?: '';
        if (!$company->save()) {
            throw new Exception($company->getFirstErrorsMessage());
        }

        /**
         * 更新联系信息部分
         */
        $companyContact = CompanyContact::findOne(['company_id' => $companyId]) ?: new CompanyContact();

        $companyContact->company_id       = $companyId;
        $companyContact->name             = $data['contactName'];
        $companyContact->mobile           = $data['contactMobile'];
        $companyContact->email            = $data['contactEmail'];
        $companyContact->telephone        = $data['contactTelephone'];
        $companyContact->department       = $data['contactDepartment'];
        $companyContact->fax              = $data['contactFax'];
        $companyContact->name_is_public   = $data['contactNameIsPublic'];
        $companyContact->mobile_is_public = $data['contactMobileIsPublic'];
        $companyContact->email_is_public  = $data['contactEmailIsPublic'];
        if (!$companyContact->save()) {
            throw new Exception($companyContact->getFirstErrorsMessage());
        }

        if (!empty($data['childColleges'])) {
            foreach ($data['childColleges'] as $item) {
                // 批量更新二级院校
                $companyChildUnit = CompanyChildUnit::findOne(['id' => $item['id']]) ?: new CompanyChildUnit();
                if (empty($item['name'])) {
                    throw new Exception('院系名称不能为空');
                }

                if (empty($item['contact'])) {
                    throw new Exception('院校联系人不能为空');
                }

                $companyChildUnit->company_id = $companyId;
                $companyChildUnit->member_id  = $memberId;
                $companyChildUnit->name       = $item['name'];
                $companyChildUnit->contact    = $item['contact'];
                $companyChildUnit->telephone  = $item['telephone'] ?: '';
                $companyChildUnit->fax        = $item['fax'] ?: '';
                if (!$companyChildUnit->save()) {
                    throw new Exception($companyChildUnit->getFirstErrorsMessage());
                }
            }
        }

        $memberAddress = MemberAddress::findOne(['member_id' => $memberId]) ?: new MemberAddress();

        $memberAddress->member_id   = $memberId;
        $memberAddress->lng         = $data['addressLng'] ?: 0;
        $memberAddress->lat         = $data['addressLat'] ?: 0;
        $memberAddress->province_id = $data['baseAreaArr'][0];
        $memberAddress->city_id     = $data['baseAreaArr'][1];
        $memberAddress->detail      = $data['addressDetail'];
        if (!$memberAddress->save()) {
            throw new Exception($memberAddress->getFirstErrorsMessage());
        }
        // 最后还要处理,如果地址信息更新了,那么需要同步到对应的职位

        //同步修改职位地址
        if ($data['isSynContact'] == Company::COMPANY_SYN_CONTACT_YES) {
            $job = Job::findOne(['company_id' => $companyId]) ?: new Job();

            $job->province_id = $data['baseAreaArr'][0];
            $job->city_id     = $data['baseAreaArr'][1];
            $job->address     = $data['addressDetail'];
            if (!$job->save()) {
                throw new Exception($job->getFirstErrorsMessage());
            }
        }

        // 入队meilisearch
        Producer::meilisearch($companyId, MeilisearchJob::TYPE_COMPANY);
    }

    /**
     * 获取单位详情页面banner模块
     * @param $id
     * @return array|ActiveRecord|null
     */
    public static function getDetailBannerInfo($id)
    {
        $info = self::find()
            ->where(['id' => $id])
            ->select([
                'id as companyId',
                'full_name as companyName',
                'status',
                'logo_url as logoUrl',
                'english_name as englishName',
                'label_ids',
                'is_cooperation as isCooperation',
                'head_banner_url as headBannerUrl',
                'welfare_label_ids as welfareLabelIds',
            ])
            ->asArray()
            ->one();
        //获取单位标签
        $info['labelArr'] = [];
        if (!empty($info['label_ids'])) {
            $labelIdsArr = explode(',', $info['label_ids']);
            foreach ($labelIdsArr as $k => $v) {
                array_push($info['labelArr'], BaseDictionary::getCompanyLabelName($v));
            }
        }
        //获取单位福利
        $info['welfareArr'] = [];
        if (!empty($info['welfareLabelIds'])) {
            $welfareIdsArr = explode(',', $info['welfareLabelIds']);
            $welfareList   = BaseWelfareLabel::find()
                ->select([
                    'name',
                ])
                ->where([
                    'id'     => $welfareIdsArr,
                    'status' => BaseCompany::STATUS_ACTIVE,
                ])
                ->asArray()
                ->all();

            $info['welfareArr'] = array_column($welfareList, 'name');
        }
        //融合单位福利、标签
        $info['fuseArr'] = array_merge($info['labelArr'], $info['welfareArr']);
        //获取单位logo
        $info['logo'] = self::getLogoFullUrl($info['logoUrl']);
        //单位背景图
        $info['headBannerUrl'] = FileHelper::getFullUrl($info['headBannerUrl']);
        if ($info['headBannerUrl']) {
            $info['headBannerUrl'] = $info['headBannerUrl'] . '?imageView2/1/w/1920/h/370/q/75';
        }
        //获取单位发布公告数量
        $info['onlineAnnouncementCount'] = BaseAnnouncement::getCompanyOnLineAnnouncementAmount($info['companyId']);

        //获取单位在招职位数量
        $info['onlineJobCount'] = BaseJob::getCompanyJobAmount($info['companyId']);
        // 获取活动总数
        $activityCount         = BaseCompany::getActivityList(['companyId' => $info['companyId']])['page']['count'];
        $activityCount         = $activityCount > 99 ? '99+' : $activityCount;
        $info['activityCount'] = $activityCount;

        return $info;
    }

    /**
     * 获取详情页面单位介绍信息
     * @param $id
     * @return array|ActiveRecord|null
     * @throws \Exception
     */
    public static function getDetailIntroduceInfo($id)
    {
        $info = self::find()
            ->alias('c')
            ->leftJoin(['cc' => BaseCompanyContact::tableName()], 'cc.company_id = c.id')
            ->where(['c.id' => $id])
            ->select([
                'c.id as companyId',
                'c.full_name as companyName',
                'c.introduce',
                'cc.name_is_public as nameIsPublic',
                'cc.email_is_public as emailIsPublic',
                'cc.mobile_is_public as mobileIsPublic',
                'cc.name as contact',
                'cc.telephone',
                'cc.email',
                'cc.fax',
                'c.address',
                'c.website',
                'c.province_id',
                'c.city_id',
                'c.district_id',
                'c.add_time',
                'c.type',
                'c.nature',
            ])
            ->asArray()
            ->one();

        //热招公告
        $announcementHotDate         = [
            'companyId' => $info['companyId'],
            'pageSize'  => 8,
        ];
        $announcementHotList         = BaseAnnouncement::getCompanyDetailList($announcementHotDate,
            self::NEED_PAGE_INFO_NO);
        $info['announcementHotList'] = $announcementHotList;
        //最新职位
        $jobHotDate         = [
            'companyId' => $info['companyId'],
            'pageSize'  => 9,
        ];
        $jobHotList         = BaseJob::getCompanyHotJobList($jobHotDate);
        $info['jobHotList'] = $jobHotList;
        //判断职位联系方式是否显示
        if ($info['nameIsPublic'] == self::NAME_PUBLIC_NO) {
            $info['contact'] = '';
        }
        if ($info['emailIsPublic'] == self::EMAIL_PUBLIC_NO) {
            $info['email'] = '';
        }
        if ($info['mobileIsPublic'] == self::MOBILE_PUBLIC_NO) {
            $info['telephone'] = '';
        }
        //拼接单位地址
        $provinceName = BaseArea::getAreaName($info['province_id']);
        $cityName     = BaseArea::getAreaName($info['city_id']);
        if ($provinceName == $cityName) {
            $info['address'] = BaseArea::getAreaName($info['city_id']) . BaseArea::getAreaName($info['district_id']) . $info['address'];
        } else {
            $info['address'] = BaseArea::getAreaName($info['province_id']) . BaseArea::getAreaName($info['city_id']) . BaseArea::getAreaName($info['district_id']) . $info['address'];
        }

        //获取单位经纬度
        $info['lat'] = BaseMemberAddress::findOneVal(['member_id' => $info['companyMemberId']], 'lat');
        $info['lng'] = BaseMemberAddress::findOneVal(['member_id' => $info['companyMemberId']], 'lng');
        //获取单位二级院校
        $info['childUnitList'] = BaseCompanyChildUnit::getList($info['companyId']);
        if (count($info['childUnitList']) > 0) {
            $info['hasChildUnitList'] = true;
        } else {
            $info['hasChildUnitList'] = false;
        }
        //获取单位类型
        $info['type'] = BaseDictionary::getCompanyTypeName($info['type']);
        //获取单位性质
        $info['nature'] = BaseDictionary::getCompanyNatureName($info['nature']);

        return $info;
    }

    /**
     * 获取单位详情页面信息
     * @param $id
     * @return array|ActiveRecord|null|bool
     * @throws \Exception
     */
    public static function getUnCooperationDetailInfo($id)
    {
        // 先找缓存里面是否有
        $cacheKey  = Cache::ALL_COMPANY_DETAIL_KEY . ':' . $id;
        $cacheData = Cache::get($cacheKey);
        if ($cacheData) {
            $companyDetail = json_decode($cacheData, true);
        } else {
            $companyDetail = self::find()
                ->alias('c')
                ->leftJoin(['cc' => BaseCompanyContact::tableName()], 'cc.company_id = c.id')
                ->where(['c.id' => $id])
                ->select([
                    'c.id as companyId',
                    'c.full_name as companyName',
                    'c.member_id as companyMemberId',
                    'c.introduce',
                    'c.website',
                    'cc.name as contact',
                    'cc.telephone',
                    'cc.fax',
                    'c.label_ids',
                    'cc.name_is_public as nameIsPublic',
                    'cc.email_is_public as emailIsPublic',
                    'cc.mobile_is_public as mobileIsPublic',
                    'c.head_banner_url as headBannerUrl',
                    'c.add_time',
                ])
                ->asArray()
                ->one();

            if (empty($companyDetail)) {
                return false;
            }

            //判断职位联系方式是否显示
            if ($companyDetail['nameIsPublic'] == self::NAME_PUBLIC_NO) {
                $companyDetail['contact'] = '';
            }
            if ($companyDetail['emailIsPublic'] == self::EMAIL_PUBLIC_NO) {
                $companyDetail['email'] = '';
            }
            if ($companyDetail['mobileIsPublic'] == self::MOBILE_PUBLIC_NO) {
                $companyDetail['telephone'] = '';
            }
            //拼接单位地址
            $companyDetail['address'] = self::getAddress($id);
            //获取单位标签
            $companyDetail['labelArr'] = [];
            if (!empty($companyDetail['label_ids'])) {
                $labelIdsArr = explode(',', $companyDetail['label_ids']);
                foreach ($labelIdsArr as $k => $v) {
                    array_push($companyDetail['labelArr'], BaseDictionary::getCompanyLabelName($v));
                }
            }
            //获取单位二级院校
            $companyDetail['childUnitList'] = BaseCompanyChildUnit::getList($companyDetail['companyId']);
            if (count($companyDetail['childUnitList']) > 0) {
                $companyDetail['hasChildUnitList'] = true;
            } else {
                $companyDetail['hasChildUnitList'] = false;
            }

            //获取招聘公告
            $jobSearchData                     = [
                'companyId' => $companyDetail['companyId'],
            ];
            $announcementListInfo              = BaseAnnouncement::getCompanyDetailList($jobSearchData,
                self::NEED_PAGE_INFO_YES);
            $companyDetail['announcementList'] = $announcementListInfo['list'];
            //获取单位公告总数量
            $companyDetail['announcementAmount'] = $announcementListInfo['totalNum'];
            //获取单位公告总数量
            $companyDetail['onlineAnnouncementCount'] = $announcementListInfo['onlineTotalNum'];
            //获取在招职位
            $commonSearchApp          = new CommonSearchApplication();
            $jobListInfo              = $commonSearchApp->companyJobListSearch($jobSearchData);
            $companyDetail['jobList'] = $jobListInfo['list'];
            //获取单位在招职位数量
            $companyDetail['jobAmount']      = $jobListInfo['totalNum'];
            $companyDetail['onlineJobCount'] = $jobListInfo['onlineTotalNum'];
            //获取单位下公告的职能筛选列表
            $companyDetail['announceCityList'] = self::getJobAreaList($companyDetail['companyId']);
            //获取单位下职位的职能筛选列表
            $companyDetail['jobCityList'] = self::getJobAreaList($companyDetail['companyId']);
            //获取单位下公告的职能筛选列表
            $companyDetail['announceJobCategoryList'] = self::getJobCategoryList($companyDetail['companyId'], true);
            //获取单位下职位的职能筛选列表
            $companyDetail['jobCategoryList'] = self::getJobCategoryList($companyDetail['companyId']);
            //获取单位下的薪资筛选列表（2.4弃用）
            //$companyDetail['wageList'] = ArrayHelper::obj2Arr(BaseDictionary::getWageRangeList());
            //获取单位下的需求专业筛选列表
            $companyDetail['majorList'] = self::getJobMajorList($companyDetail['companyId']);
            //单位介绍换行格式
            $companyDetail['introduce'] = StringHelper::changeLineFeed($companyDetail['introduce']);
            // 2.4追加学历
            $companyDetail['announceEducationList'] = self::getJobEducationList($companyDetail['companyId']);
            //单位背景图
            $companyDetail['headBannerUrl'] = FileHelper::getFullUrl($companyDetail['headBannerUrl']);
            if ($companyDetail['headBannerUrl']) {
                $companyDetail['headBannerUrl'] = $companyDetail['headBannerUrl'] . '?imageView2/1/w/1920/h/370/q/75';
            }

            Cache::set($cacheKey, json_encode($companyDetail), self::CACHE_TIME);
        }

        return $companyDetail;
    }

    public static function getDetailAnnouncementListInfo($id)
    {
        $info              = [];
        $info['companyId'] = $id;
        //获取单位下公告的职能筛选列表
        $info['announceCityList'] = self::getJobAreaList($info['companyId']);
        //获取单位下公告的职能筛选列表
        $info['announceJobCategoryList'] = self::getJobCategoryList($info['companyId'], true);
        // 2.4追加专业跟学历
        $info['announceEducationList'] = self::getJobEducationList($info['companyId'], true);
        $info['announceMajorList']     = self::getJobMajorList($info['companyId'], true);
        // 2.4弃用薪资
        //获取单位下的薪资筛选列表
        //$info['wageList'] = ArrayHelper::obj2Arr(BaseDictionary::getWageRangeList());

        $info['add_time'] = self::findOneVal(['id' => $id], 'add_time');

        //获取招聘公告
        $jobSearchData            = [
            'companyId' => $info['companyId'],
        ];
        $announcementListInfo     = BaseAnnouncement::getCompanyDetailList($jobSearchData, self::NEED_PAGE_INFO_YES);
        $info['announcementList'] = $announcementListInfo['list'];
        //获取单位公告总数量
        $info['announcementAmount'] = $announcementListInfo['totalNum'];
        $info['companyName']        = self::findOneVal(['id' => $id], 'full_name');

        return $info;
    }

    /**
     * 获取合作单位详情页面职位列表页面信息
     * @param $id
     * @return array|ActiveRecord|null
     * @throws \Exception
     */
    public static function getDetailJobListInfo($id)
    {
        $info              = [];
        $info['companyId'] = $id;
        //获取单位下职位的职能筛选列表
        $info['jobCityList'] = self::getJobAreaList($info['companyId']);
        //获取单位下职位的职能筛选列表
        $info['jobCategoryList'] = self::getJobCategoryList($info['companyId']);
        //获取单位下的需求专业筛选列表
        $info['majorList'] = self::getJobMajorList($info['companyId']);
        //2.4追加学历
        $info['educationList'] = self::getJobEducationList($info['companyId']);
        //获取在招职位
        $commonSearchApp = new CommonSearchApplication();
        $jobSearchData   = [
            'companyId' => $info['companyId'],
        ];
        $jobListInfo     = $commonSearchApp->companyJobListSearch($jobSearchData);
        $info['jobList'] = $jobListInfo['list'];
        //获取单位在招职位数量
        $info['jobAmount']   = $jobListInfo['totalNum'];
        $info['add_time']    = self::findOneVal(['id' => $id], 'add_time');
        $info['companyName'] = self::findOneVal(['id' => $id], 'full_name');

        return $info;
    }
}
