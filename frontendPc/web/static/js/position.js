var _maqData = {}
_maq.push(['_setCookie', getCookie('gaoxiaojob')])

if (_maq) {
    for (var i in _maq) {
        switch (_maq[i][0]) {
            case '_setAccount':
                _maqData.token = _maq[i][1]
                break
            case '_setCookie':
                _maqData.cookies = _maq[i][1]
                break
            default:
                break
        }
    }
}

function setCookie(name, value) {
    var exp = new Date()
    // 24小时过期,认为是新访客了
    exp.setTime(exp.getTime() + 24 * 60 * 60 * 1000)
    document.cookie =
        name + '=' + escape(value) + ';expires=' + exp.toGMTString()
}

function getCookie(name) {
    var arr,
        reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)')

    if ((arr = document.cookie.match(reg))) return unescape(arr[2])
    var rnd = ''
    for (var i = 0; i < 32; i++) rnd += Math.floor(Math.random() * 10)
    setCookie(name, rnd)

    return rnd
}

$(function () {
    $('body').on('click', 'a', function () {
        let stream = $(this).hasClass('showcase-browse')
        if (!stream) {
            return
        }
        let number = $(this).data('showcase-number')
        let id = $(this).data('showcase-id')
        if (!number) {
            return
        }
        let data = {}
        let baseUrl = "/1.gif"
        let args = ''
        for (var i in _maqData) {
            if (args != '') {
                args += '&'
            }
            args += i + '=' + encodeURIComponent(_maqData[i])
        }
        args += '&number=' + number
        args += '&id=' + id
        let img = new Image();

        img.src = baseUrl + '?' + args

    })

    $(document).on('click', function (e) {

        let module = ''
        let action = ''

        //判断是否是下面的操作
        let allClassList = [
            {'class': 'send-code', 'type': 3, 'module': 14},//底部登录框发送验证码
            {'class': 'account-tips', 'type': 1, 'module': 3},//首页切换账号登录
            {'class': 'scan-tips', 'type': 1, 'module': 1},//首页切换二维码登录
            {'class': 'mobile-login-confirm', 'type': 2, 'module': 0},//首页\栏目底部手机登录
            {'class': 'account-login-confirm', 'type': 2, 'module': 2},//首页账号登录
            {'class': 'send-code-button', 'type': 3, 'module': 1},//首页发送验证码
        ]

        let isClass = false
        for (let i = 0; i < allClassList.length; i++) {
            if ($(e.target).hasClass(allClassList[i]['class'])) {
                action = allClassList[i]['type']
                module = allClassList[i]['module']
                if (module === 0) {
                    module = getModule(allClassList[i]['class'])

                }
                isClass = true
            }
        }
        let isId = false
        let allIdList = [
            {'id': 'tab-account', 'type': 1, 'module': 2},//首页切换账号登录tab-account
            {'id': 'tab-mobile', 'type': 1, 'module': 1},//首页切换手机登录tab-mobile
            {'id': 'tab-email', 'type': 1, 'module': 5},//注册页面切换邮箱注册tab-email
        ]
        for (let i = 0; i < allIdList.length; i++) {
            if ($(e.target).attr('id') === allIdList[i]['id']) {
                action = allIdList[i]['type']
                module = allIdList[i]['module']
                if (module === 0) {
                    module = getModule(allIdList[i]['id'])
                }
                isId = true
            }
        }

        if (!isId && !isClass) {
            //都不属于，则返回
            return
        }

        let baseUrl = "/2.gif"
        let args = ''
        for (var i in _maqData) {
            if (args != '') {
                args += '&'
            }
            args += i + '=' + encodeURIComponent(_maqData[i])
        }
        args += '&actionType=' + action
        args += '&actionModule=' + module
        let img = new Image();

        img.src = baseUrl + '?' + args
    })

    function getModule(name) {
        let module = 0
        let path = window.location.pathname
        let attrModuleList = [
            {'name': 'mobile-login-confirm', 'indexModuleNum': 1, 'columnModuleNum': 14,'detailModuleNum':13},//首页、登录页面切换手机登录
        ]

        for (let i = 0; i < attrModuleList.length; i++) {
            if (name === attrModuleList[i]['name']) {
                if (path === '/') {
                    module = attrModuleList[i]['indexModuleNum']
                }else if(path.indexOf('column') >= 0){
                    //栏目页面
                    module = attrModuleList[i]['columnModuleNum']
                } else {
                    //侧边栏
                    module = attrModuleList[i]['detailModuleNum']
                }
            }
        }
        return module
    }


})
