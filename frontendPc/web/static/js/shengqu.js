window.onload = function () {
    var time = null;
    var index = 0;
    var picCon = document.getElementsByClassName('slider_con');
    var buttonBox = document.getElementById('button_box');
    var buttonList = buttonBox.getElementsByTagName('span');

    function byId(id) {
        return typeof (id) === 'string' ? document.getElementById(id) : id;
    }

    function slideChange() {
        var adSlider = byId('ad_slider');
        adSlider.onmouseover = function () {
            stopAutoPlay();
        }
        adSlider.onmouseout = function () {
            startAutoPlay();
        }
        adSlider.onmouseout();
        for (var i = 0; i < picCon.length; i++) {
            buttonList[i].id = i;
            buttonList[i].onclick = function () {
                index = this.id;
                changeSlider();
            }
        }
    }

    function startAutoPlay() {
        time = setInterval(function () {
            index++;
            if (index > 2) {
                index = 0;
            }
            changeSlider();
        }, 3000);
    }

    function stopAutoPlay() {
        if (time) {
            clearInterval(time);
        }
    }

    function changeSlider() {
        for (var i = 0; i < picCon.length; i++) {
            picCon[i].style.display = 'none';
            buttonList[i].className = '';
        }
        picCon[index].style.display = 'block';
        buttonList[index].className = 'cli_change';
    }
    slideChange();

    var tabBox = document.getElementById('tab_box');
    var adtabLi = tabBox.getElementsByTagName('li');
    var adtabCon = document.getElementById('tab_con');
    var adCon = adtabCon.getElementsByClassName('ad_con');
    for (var i = 0; i < adtabLi.length; i++) {
        adtabLi[i].index = i;
        adtabLi[i].onclick = function () {
            for (var i = 0; i < adtabLi.length; i++) {
                adtabLi[i].className = '';
                adCon[i].style.display = 'none';
            }
            this.className = 'tab_act';
            adCon[this.index].style.display = 'block';
        }
        for (var g = 1; g < adtabLi.length; g++) {
            adtabLi[g].className = '';
            adCon[g].style.display = 'none';
        }
    }

    var offSide = document.getElementById('offside');
    var collageSide = offSide.getElementsByClassName('offside_collage');
    var sideText = offSide.getElementsByClassName('mask');
    for (var i = 0; i < collageSide.length; i++) {
        collageSide[i].index = i;
        collageSide[i].onmouseover = function () {
            for (i = 0; i < collageSide.length; i++) {
                sideText[this.index].style.display = 'block';
            }
        }
        collageSide[i].onmouseout = function () {
            for (i = 0; i < collageSide.length; i++) {
                sideText[this.index].style.display = 'none';
            }
        }
    }
}