!function (e, t, n, g, i) {
    e[i] = e[i] || function () {
        (e[i].q = e[i].q || []).push(arguments)
    }, n = t.createElement("script"), tag = t.getElementsByTagName("script")[0], n.async = 1, n.src = ('https:' == document.location.protocol ? 'https://' : 'http://') + g, tag.parentNode.insertBefore(n, tag)
}(window, document, "script", "assets.giocdn.com/2.1/gio.js", "gio");
gio('init', '8e5e4b80a514d362', {});

//custom page code begin here

//custom page code end here

gio('send');