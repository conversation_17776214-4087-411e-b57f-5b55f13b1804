body{color:#fff;font-size:14px}.container{background:url("../assets/application/picture.webp") no-repeat center/cover}.container .header{display:flex;justify-content:space-between;padding:40px 60px 24px}.container .header a{display:block;width:154px;height:42px}.container .header a img{display:block;max-width:100%}.container .main{display:flex;padding-bottom:106px;margin:0 auto;width:1160px}.container .main .tips{border-radius:10px 0 0 10px;background-color:rgba(92,92,92,.9)}.container .main .tips .tips-application .tips-title{margin:80px 0 41px 34px;height:38px;font-size:20px;font-weight:bold;background:url("../assets/application/background.webp") no-repeat left center/209px}.container .main .tips .tips-application .tips-title span{padding-left:21px;line-height:38px}.container .main .tips .tips-application .tips-news{width:378px;margin:0 52px 0 40px}.container .main .tips .tips-application .tips-news p{padding-bottom:30px;line-height:2em}.container .main .tips .tips-application .tips-news p:last-child{padding-bottom:80px;border-bottom:1px dashed rgba(255,255,255,.6)}.container .main .tips .tips-contact{padding-left:38px}.container .main .tips .tips-contact .tips-more{padding:39px 0 39px 0}.container .main .tips .tips-contact p{padding-bottom:20px}.container .main .tips .tips-contact p img{display:inline-block;margin-right:12px;vertical-align:middle;width:24px;height:24px}.container .main .tips .tips-contact p:last-child{padding-bottom:40px}.container #component{background:#fff;border-radius:0px 10px 10px 0px}.container #component .form-container label{color:#333}.container #component .form-container .el-form{padding:73px 75px 5px 77px;width:690px}.container #component .form-container .el-input__inner::placeholder,.container #component .form-container .el-textarea__inner::placeholder{font-size:12px}.container #component .form-container .el-textarea__inner{height:94px;resize:none}.container #component .form-container .el-button--primary{width:280px;height:44px;font-size:16px}.container #component .form-container .el-button+.el-button{width:160px;height:44px;color:rgba(51,51,51,.4);font-size:16px}.container #component .explain .el-form-item__content{display:flex;justify-content:space-between;color:#ffa000}