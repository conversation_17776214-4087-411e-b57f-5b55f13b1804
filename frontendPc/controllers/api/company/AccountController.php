<?php

namespace frontendPc\controllers\api\company;

use common\base\models\BaseCompanyMemberInfo;
use common\helpers\ArrayHelper;
use frontendPc\models\Company;
use frontendPc\models\CompanyChildUnit;
use frontendPc\models\Dictionary;
use frontendPc\models\Member;
use frontendPc\models\WelfareLabel;
use frontendPc\models\Trade;
use Yii;

/**
 * 账号设置类
 */
class AccountController extends BaseFrontPcApiCompanyController
{
    /**
     * 单位设置里面获取全部信息
     * 原型
     * https://lanhuapp.com/web/#/item/project/product?tid=8d951de4-aefb-40f7-954e-366683d68331&pid=8308ab1e-91a7-4dde-bc6e-b1d6568eef07&versionId=e71520cb-6b2b-4801-bf1b-339f8f157e92&docId=********-adea-41d5-a3e8-82c2238437ee&docType=axure&pageId=bcc9d8605f5f45cda2b3495590ec299e&image_id=********-adea-41d5-a3e8-82c2238437ee&parentId=62db03ce-57c5-4e4a-94f1-216424d0e337
     */
    public function actionGetFullInfo()
    {
        $companyId = Member::getMainId();
        $memberId  = Yii::$app->user->id;

        if (!$companyId) {
            $this->loginExpire();
        }

        return $this->success([
            'companyId'        => $companyId,
            'info'             => Company::getFullInfo($companyId, $memberId),
            'natureList'       => ArrayHelper::obj2Arr(Company::getNatureList()),
            'typeList'         => ArrayHelper::obj2Arr(Dictionary::getCompanyTypeList()),
            'scaleList'        => ArrayHelper::obj2Arr(Company::getScaleList()),
            'welfareLabelList' => WelfareLabel::getCompanyWelfareLabelList(),
            'tagList'          => ArrayHelper::obj2Arr(Dictionary::getCompanyLabelList()),
            'industryList'     => Trade::getTradeList(),
        ]);
    }

    /**
     * 修改所有基本信息
     *
     **/
    public function actionUpdateFullInfo()
    {
        $model       = new Company();
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $transaction->commit();
            $model->updateFullInfo(Yii::$app->user->id, Yii::$app->request->post());

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 单位二级院校删除
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionChildUnitDelete()
    {
        $id = Yii::$app->request->post('id');
        if (!$id) {
            return $this->fail();
        }

        return $this->success(CompanyChildUnit::setChildUnitDelete($id));
    }

    /**
     * 单位端账号设置，获取个人资料
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetCompanyMemberInfo()
    {
        try {
            return $this->success(BaseCompanyMemberInfo::getCompanyMemberInfo(Yii::$app->user->id));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 编辑个人资料
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditCompanyMemberInfo()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $transaction->commit();

            Member::editCompanyMemberInfo(Yii::$app->request->post());

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取单位账户列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetCompanyMemberInfoList()
    {
        try {
            return $this->success(BaseCompanyMemberInfo::getCompanyMemberInfoList(Yii::$app->request->get()));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

}