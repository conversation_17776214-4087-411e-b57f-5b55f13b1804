<?php

namespace frontendPc\controllers\api;

use common\base\models\BaseCompany;
use common\base\models\BaseCompanyResumePvTotal;
use common\base\models\BaseMember;
use common\base\models\BaseMemberLoginForm;
use common\base\models\BaseResume;
use common\base\models\BaseResumeShare;
use common\helpers\FileHelper;
use common\helpers\MaskHelper;
use \Yii;

class ResumeController extends BaseFrontPcApiController
{

    public function actionGetShareResumeInfo()
    {
        $code     = Yii::$app->request->post('code');
        $password = Yii::$app->request->post('password');

        // 在这里校验一下,然后再去拿detail的信息,并且,过滤掉手机号和邮箱就可以了
        if (!$code || !$password) {
            return $this->fail('缺少参数');
        }
        // 找到对应的信息
        $model = BaseResumeShare::find()
            ->select([
                'id',
                'expire_time',
                'password',
                'resume_id',
                'company_id',
            ])
            ->where(['code' => $code])
            ->asArray()
            ->one();

        if (!$model) {
            return $this->fail('数据不存在');
        }

        //判断密码
        if ($password != $model['password']) {
            return $this->fail('密码输入错误');
        }
        $memberId = BaseResume::findOneVal(['id' => $model['resume_id']], 'member_id');

        //获取数据
        $resumeInfo = BaseResume::getInfo($memberId);
        //人才库这边要做特殊处理
        //手机、邮箱脱敏
        $resumeInfo['userInfo']['mobile'] = MaskHelper::getPhone($resumeInfo['userInfo']['mobile']);

        $resumeInfo['userInfo']['fullMobile'] = BaseMember::getFormatFullMobile($resumeInfo['userInfo']['mobile'],
            $resumeInfo['userInfo']['mobileCode']);

        $resumeInfo['userInfo']['email'] = MaskHelper::getEmail($resumeInfo['userInfo']['email']);
        //姓名部分
        if ($resumeInfo['userInfo']['memberStatus'] != BaseMember::STATUS_RESUME_CANCELED) {
            $resumeInfo['userInfo']['name'] = MaskHelper::getName($resumeInfo['userInfo']['name'],
                $resumeInfo['userInfo']['gender']);
        }

        if (!$resumeInfo['userInfo']['isDefaultAvatar']) {
            //不是默认头像，给个模糊处理
            $resumeInfo['userInfo']['avatar'] = MaskHelper::getImage($resumeInfo['userInfo']['avatar']);
        }
        //        个人身份经验获取
        //        $resumeInfo['userInfo']['identityExperience'] = BaseResume::getIdentityExperienceText($model['resume_id']);
        //获取活跃度
        $resumeInfo['userInfo']['activeTime'] = BaseMember::getUserActiveTime($memberId);

        $resumeInfo['companyName'] = BaseCompany::findOneVal(['id' => $model['company_id']], 'full_name');
        //做一个PV统计
        BaseCompanyResumePvTotal::updateDailyTotalPv($resumeInfo['userInfo']['resumeId']);

        return $this->success($resumeInfo);
    }
}