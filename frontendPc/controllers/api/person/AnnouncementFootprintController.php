<?php

namespace frontendPc\controllers\api\person;

use common\base\models\BaseResumeAnnouncementFootprint;

class AnnouncementFootprintController extends BaseFrontPcApiPersonController
{
    public function actionGetList()
    {
        $params   = \Yii::$app->request->get();
        $resumeId = $this->getResumeId();

        return $this->success(BaseResumeAnnouncementFootprint::getListForResume($params, $resumeId));
    }
}