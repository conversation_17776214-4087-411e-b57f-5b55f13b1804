<?php

namespace frontendPc\controllers\api\person;

use frontendPc\models\ResumeCertificate;
use yii\base\BaseObject;
use yii\base\Exception;

class ResumeCertificateController extends BaseFrontPcApiPersonController
{
    public function actionSave()
    {
        $data = \Yii::$app->request->post();

        $transaction = \Yii::$app->db->beginTransaction();

        try {
            $data['memberId'] = \Yii::$app->user->id;

            ResumeCertificate::saveInfo($data);
            $transaction->commit();

            return $this->success(ResumeCertificate::getCertificateList($data['memberId']));
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    public function actionDel()
    {
        $id       = \Yii::$app->request->post('id');
        $memberId = \Yii::$app->user->id;

        $transaction = \Yii::$app->db->beginTransaction();

        try {
            ResumeCertificate::delCertificate($id, $memberId);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

}