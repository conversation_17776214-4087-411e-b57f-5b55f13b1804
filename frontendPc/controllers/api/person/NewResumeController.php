<?php

namespace frontendPc\controllers\api\person;

class NewResumeController extends BaseFrontPcApiPersonController
{
    public function actionCreate()
    {
        $service = new \common\service\newResumeActivity\CreateService();

        $resumeId = $this->getResumeId();

        $data = $service->run($resumeId);

        return $this->success($data);
    }

    public function actionRegister()
    {
        // $service = new \common\service\newResumeActivity\RegisterService();
        // $service->run();
        //
        // return $this->success();
    }
}