<?php

namespace frontendPc\controllers\api\person;

use common\base\models\BaseResumeEquityPackage;
use common\libs\Cache;
use Yii;
use frontendPc\models\ResumeEquity;

class ResumeEquityController extends BaseFrontPcApiPersonController
{
    /**
     * 查询我的服务-顶部提示栏
     */
    public function actionGetMyServicesTopTips()
    {
        try {
            $resumeId = $this->getResumeId();
            if (empty($resumeId)) {
                throw new \Exception('系统异常');
            }

            $res = ResumeEquity::getMyServicesTopTips($resumeId);

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 查询我的服务
     */
    public function actionGetMyServices()
    {
        try {
            $resumeId = $this->getResumeId();
            if (empty($resumeId)) {
                throw new \Exception('系统异常');
            }

            $res = ResumeEquity::getMyServices($resumeId);

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取服务到期提醒弹窗
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetServicesExpirePopInfo()
    {
        try {
            $memberId        = Yii::$app->user->id;

            $dailyFirstClick = ResumeEquity::getDailyMyServiceExpirePop($memberId);
            if (!$dailyFirstClick) {
                return $this->success();
            }
            $resumeId = $this->getResumeId();
            if (empty($resumeId)) {
                throw new \Exception('系统异常');
            }

            $res = ResumeEquity::getServicesExpirePopInfo($resumeId);
            ResumeEquity::setDailyMyServiceExpirePop($memberId);

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 查询求职工具
     */
    public function actionGetJobTools()
    {
        try {
            $resumeId = $this->getResumeId();
            if (empty($resumeId)) {
                throw new \Exception('系统异常');
            }

            $res = ResumeEquity::getJobTools($resumeId);

            return $this->success($res);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取求职资源
     */
    public function actionGetJobResources()
    {
        try {
            $resumeId = $this->getResumeId();
            if (empty($resumeId)) {
                throw new \Exception('系统异常');
            }

            $code = ResumeEquity::getJobResources($resumeId);

            $tips = [
                'title'          => '求职资料包领取',
                'successContent' => '请扫码关注【高校人才网服务号】，回复“求职”，领取VIP专属求职学习资料包！',
            ];

            return $this->success($code + $tips);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取是否有某个包生效
     */
    public function actionIsPackageEffect()
    {
        $package_id = Yii::$app->request->get('packageId');
        if ($package_id <= 0) {
            return $this->fail('参数错误');
        }
        $resume_id = $this->getResumeId();
        $isEquity  = BaseResumeEquityPackage::isPackageEffect($package_id, $resume_id);

        return $this->success(['isEquity' => $isEquity]);
    }
}