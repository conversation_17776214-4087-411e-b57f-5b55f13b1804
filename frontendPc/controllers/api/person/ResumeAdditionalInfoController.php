<?php

namespace frontendPc\controllers\api\person;

use frontendPc\models\ResumeAdditionalInfo;

class ResumeAdditionalInfoController extends BaseFrontPcApiPersonController
{
    public function actionSave()
    {
        $data = \Yii::$app->request->post();

        try {
            $data['memberId'] = \Yii::$app->user->id;

            ResumeAdditionalInfo::saveInfo($data);

            return $this->success(ResumeAdditionalInfo::getInfoList($data['memberId']));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionDel()
    {
        $id       = \Yii::$app->request->post('id');
        $memberId = \Yii::$app->user->id;

        $transaction = \Yii::$app->db->beginTransaction();

        try {
            ResumeAdditionalInfo::delInfo($id, $memberId);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

}