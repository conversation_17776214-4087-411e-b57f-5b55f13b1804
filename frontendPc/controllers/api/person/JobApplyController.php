<?php

namespace frontendPc\controllers\api\person;

use frontendPc\models\JobApply;
use Yii;

class JobApplyController extends BaseFrontPcApiPersonController
{
    /**
     * 获取站内投递的列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetOnSiteList()
    {
        try {
            $searchData             = Yii::$app->request->get();
            $searchData['memberId'] = Yii::$app->user->id;

            return $this->success(JobApply::getApplyList($searchData, JobApply::NEED_PAGE_INFO_YES));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 保存站内投递的备注
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSaveNote()
    {
        $data     = Yii::$app->request->post();
        $memberId = Yii::$app->user->id;
        //判断参数
        if (empty($data['id']) ) {
            return $this->fail('缺失必填参数');
        }
        //判断数据是否正确
        $model = JobApply::find()
            ->where(['id' => $data['id']])
            ->select([
                'resume_member_id',
                'status',
            ])
            ->asArray()
            ->one();
        if (empty($model)) {
            return $this->fail('数据不存在');
        }
        if ($model['resume_member_id'] != $memberId) {
            return $this->fail('该投递数据不属于当前用户');
        }
        try {
            JobApply::saveNote($data);

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}