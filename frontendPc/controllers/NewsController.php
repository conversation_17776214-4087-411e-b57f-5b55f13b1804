<?php

namespace frontendPc\controllers;

use common\base\models\BaseArticleClickLog;
use common\base\models\BaseNewsCollect;
use common\libs\BaiduTimeFactor;
use common\libs\ToutiaoTimeFactor;
use frontendPc\models\Article;
use frontendPc\models\HomeColumn;
use frontendPc\models\News;
use Yii;
use yii\base\Exception;

class NewsController extends BaseFrontendPcController
{
    /**
     * 收藏资讯文章
     */
    public function actionNewsCollect()
    {
        $id       = Yii::$app->request->get('id');
        $memberId = Yii::$app->user->id;
        if (!$memberId) {
            return $this->fail('未登录，无法收藏文章！');
        }

        try {
            return $this->success(BaseNewsCollect::saveOne($id, $memberId));
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 获取详情页面信息
     */
    public function actionDetail()
    {
        $this->layout = 'column_main';
        $id           = Yii::$app->request->get('id');

        $recommendList = News::getRecommendNewsList();

        $info = News::getDetailInfo($id);

        if ($info['status'] != News::STATUS_ACTIVE) {
            $this->notFound();
        }

        $memberId = Yii::$app->user->id ?? 0;
        BaseArticleClickLog::create($info['article_id'], $memberId);

        $info['content'] = Article::replaceIllegalStyle($info['content']);
        $column          = Article::getHomeColumnInfo($info['article_id']);

        $info['isCollect'] = BaseNewsCollect::checkIsCollect(Yii::$app->user->id, $id) ? '1' : '0';
        $backUrl           = HomeColumn::getDetailUrl(HomeColumn::NEWS_TOP_ID);
        if (!$info) {
            $this->notFound();
        }

        $seoConfig = Yii::$app->params['seo']['newsDetail'];
        // 先把公告的原本的找出来
        $keywords    = $info['seo_keywords'];
        $description = $info['seo_description'];

        if (!$description) {
            // 去掉html 标签并且截取100个字符
            $description = $info['abstract'];
        }
        if (!$keywords) {
            $keywords = str_replace('【资讯标题】', $info['title'], $seoConfig['keywords']);
        }

        $this->setSeo([
            'title'       => str_replace('【资讯标题】', $info['title'], $seoConfig['title']),
            'keywords'    => $keywords,
            'description' => $description,
        ]);

        BaiduTimeFactor::create($info['fullRefreshTime'], $info['update_time']);
        ToutiaoTimeFactor::create($info['fullRefreshTime'], $info['update_time']);

        return $this->render('detail.html', [
            'info'          => $info,
            'recommendList' => $recommendList,
            'backUrl'       => $backUrl,
            'name'          => $column['name'],
            'url'           => $column['url'],
        ]);
    }

    /**
     * 获取详情页面信息
     */
    public function actionPreview()
    {
        $this->layout = 'column_main';
        $id           = Yii::$app->request->get('id');

        $recommendList = News::getRecommendNewsList();

        $info = News::getDetailInfo($id, true);

        $info['isCollect'] = BaseNewsCollect::checkIsCollect(Yii::$app->user->id, $id) ? '1' : '0';
        if (!$info) {
            $this->notFound();
        }

        return $this->render('detail.html', [
            'info'          => $info,
            'recommendList' => $recommendList,
        ]);
    }

}
