<?php

namespace haiWai\controllers;

use common\service\abroadColumn\CompanyService;
use common\service\abroadColumn\HomeService;
use Yii;

class CompanyController extends BaseHaiWaiController
{
    // 获取全部信息
    public function actionGetAll()
    {
        $data = (new CompanyService())->getAll();

        return $this->success($data);
    }

    public function actionGetSearchList()
    {
        $searchData = Yii::$app->request->get();
        $data       = (new CompanyService())->searchList($searchData);

        return $this->success($data);
    }

}