<?php
namespace haiWai\models;

use common\base\models\BaseArea;
use common\base\models\BaseDictionary;
use common\base\models\BaseHwActivity;
use common\libs\Cache;
use common\service\abroadColumn\BaseService;
use Yii;
use yii\db\Expression;

/**
 * create user：shannon
 * create time：2024/7/3 16:15
 */

/**
 * 处理海外站的页面SEO
 */
class Seo
{

    /** 出海引才配置 start//////////////////////////////////////////////////////////////////////////////////////////////////////// */
    const TYPE_CHUHAI_OVERSEAS_SESSION_KEY  = 'haiwaizhuanchang';
    const TYPE_CHUHAI_GROUP_RECRUITMENT_KEY = 'zutuanzhaopin';
    const TYPE_CHUHAI_OVERSEAS_ACTIVITY_KEY = 'haiwaihuodong';
    const TYPE_CHUHAI_OTHER_ACTIVITY_KEY    = 'chqitahuodong';

    /** 活动类型key */
    const TYPE_CHUHAI_KEY_LIST = [
        self::TYPE_CHUHAI_OVERSEAS_SESSION_KEY,
        self::TYPE_CHUHAI_GROUP_RECRUITMENT_KEY,
        self::TYPE_CHUHAI_OVERSEAS_ACTIVITY_KEY,
        self::TYPE_CHUHAI_OTHER_ACTIVITY_KEY,
    ];
    //活动类型code对应的活动类型拼音
    const TYPE_CHUHAI_CODE_KEY_LIST = [
        BaseHwActivity::TYPE_CHUHAI_OVERSEAS_SESSION  => self::TYPE_CHUHAI_OVERSEAS_SESSION_KEY,
        BaseHwActivity::TYPE_CHUHAI_GROUP_RECRUITMENT => self::TYPE_CHUHAI_GROUP_RECRUITMENT_KEY,
        BaseHwActivity::TYPE_CHUHAI_OVERSEAS_ACTIVITY => self::TYPE_CHUHAI_OVERSEAS_ACTIVITY_KEY,
        BaseHwActivity::TYPE_CHUHAI_OTHER_ACTIVITY    => self::TYPE_CHUHAI_OTHER_ACTIVITY_KEY,
    ];

    /** 活动类型 */
    const TYPE_CHUHAI_LIST = [
        [
            'id'     => BaseHwActivity::TYPE_CHUHAI_OVERSEAS_SESSION,
            'name'   => BaseHwActivity::TYPE_CHUHAI_OVERSEAS_SESSION_NAME,
            'key'    => self::TYPE_CHUHAI_OVERSEAS_SESSION_KEY,
            'url'    => '',
            'active' => false,
        ],
        [
            'id'     => BaseHwActivity::TYPE_CHUHAI_GROUP_RECRUITMENT,
            'name'   => BaseHwActivity::TYPE_CHUHAI_GROUP_RECRUITMENT_NAME,
            'key'    => self::TYPE_CHUHAI_GROUP_RECRUITMENT_KEY,
            'url'    => '',
            'active' => false,
        ],
        [
            'id'     => BaseHwActivity::TYPE_CHUHAI_OVERSEAS_ACTIVITY,
            'name'   => BaseHwActivity::TYPE_CHUHAI_OVERSEAS_ACTIVITY_NAME,
            'key'    => self::TYPE_CHUHAI_OVERSEAS_ACTIVITY_KEY,
            'url'    => '',
            'active' => false,
        ],
        [
            'id'     => BaseHwActivity::TYPE_CHUHAI_OTHER_ACTIVITY,
            'name'   => BaseHwActivity::TYPE_CHUHAI_OTHER_ACTIVITY_NAME,
            'key'    => self::TYPE_CHUHAI_OTHER_ACTIVITY_KEY,
            'url'    => '',
            'active' => false,
        ],
    ];

    // 特殊地区----港澳结构定义
    const SPECIAL_CHUHAI_AREA     = [
        'id'       => BaseArea::HONG_KONG_AND_MACAO_ID,
        'name'     => '港澳',
        'level'    => '3',
        'key'      => 'gangao',
        'url'      => '',
        'active'   => false,
        'children' => [
            //            [
            //                'id'     => '3716',
            //                'name'   => '香港',
            //                'level'  => '4',
            //                'key'    => 'xianggang',
            //                'url'    => '',
            //                'active' => false,
            //            ],
            //            [
            //                'id'     => '3738',
            //                'name'   => '澳门',
            //                'level'  => '4',
            //                'key'    => 'aomen',
            //                'url'    => '',
            //                'active' => false,
            //            ],
        ],
    ];
    const SPECIAL_CHUHAI_AREA_KEY = [
        'gangao',
        //        'xianggang',
        //        'aomen',
    ];
    /** @var string[] 港澳对应的ID */
    const SPECIAL_CHUHAI_AREA_IDS = [
        '3716',
        '3738',
    ];

    /**
     * 海外地区直接从表里面获取
     *        $areaList = [
     *            [
     *                'id'=>1,
     *                'name'=>'日本',
     *                'level'=>'3',
     *                'key'=>'riben',
     *                'children'=>[
     *                    [
     *                        'id'=>2,
     *                        'name'=>'东京',
     *                        'level'=>'4',
     *                        'key'=>'dongjing',
     *                    ]
     *                ]
     *            ]
     *        ];
     * 缓存直接那缓存,里业务逻辑是必须要有城市的海外地区的3的数据，如果没有，则返回空数组
     */
    private static function getAbroadArea()
    {
        //缓存
        $areaList       = Cache::get(Cache::HAIWAI_CHUHAI_AREA_KEY);
        $areaAllKeyList = Cache::get(Cache::HAIWAI_CHUHAI_AREA_ALL_KEY);
        if (!$areaList || !$areaAllKeyList) {
            //先查出海外四级的三级ID
            $areaIds = BaseArea::find()
                ->select(['parent_id'])
                ->where([
                    'level'    => 4,
                    'is_china' => BaseArea::IS_CHINA_NO,
                ])
                ->groupBy('parent_id')
                ->column();
            //查出海外四级的三级数据
            $areaList       = BaseArea::find()
                ->select([
                    'id',
                    'name',
                    'level',
                    'spell as key',
                ])
                ->where([
                    'id' => $areaIds,
                ])
                ->asArray()
                ->all();
            $areaAllKeyList = array_column($areaList, 'key');
            foreach ($areaList as &$area) {
                $area             = array_merge($area, self::COMMON_ITEM);
                $area['children'] = BaseArea::find()
                    ->select([
                        'id',
                        'name',
                        'level',
                        'spell as key',
                    ])
                    ->where(['parent_id' => $area['id']])
                    ->asArray()
                    ->all();
                $areaAllKeyList   = array_merge($areaAllKeyList, array_column($area['children'], 'key'));
                foreach ($area['children'] as &$child) {
                    $child = array_merge($child, self::COMMON_ITEM);
                }
            }

            //处理港澳
            $gangaoData = BaseArea::find()
                ->select([
                    'id',
                    'name',
                    'level',
                    'spell as key',
                ])
                ->where(['id' => self::SPECIAL_CHUHAI_AREA_IDS])
                ->asArray()
                ->all();
            foreach ($gangaoData as &$area) {
                $area = array_merge($area, self::COMMON_ITEM);
            }
            $gangao             = self::SPECIAL_CHUHAI_AREA;
            $gangao['children'] = $gangaoData;
            $gangaoKey          = array_merge(self::SPECIAL_CHUHAI_AREA_KEY, array_column($gangaoData, 'key'));
            array_unshift($areaList, $gangao);
            $areaAllKeyList = array_merge($gangaoKey, $areaAllKeyList);
            Cache::set(Cache::HAIWAI_CHUHAI_AREA_KEY, json_encode($areaList));
            Cache::set(Cache::HAIWAI_CHUHAI_AREA_ALL_KEY, json_encode($areaAllKeyList));
        } else {
            $areaList       = json_decode($areaList, true);
            $areaAllKeyList = json_decode($areaAllKeyList, true);
        }

        return [
            'areaList'       => $areaList,
            'areaAllKeyList' => $areaAllKeyList,
        ];
    }

    /** 出海引才配置 end////////////////////////////////////////////////////////////////////////////////////////////////////////// */

    /** 归国活动配置 start//////////////////////////////////////////////////////////////////////////////////////////////////////// */
    const TYPE_GUIGUO_SCHOLARS_FORUM_KEY         = 'xuezheluntan';
    const TYPE_GUIGUO_STUDENTS_RETURNED_HOME_KEY = 'xueziguiguoxing';
    const TYPE_GUIGUO_VENTURE_CONTEST_KEY        = 'chuangyedasai';
    const TYPE_GUIGUO_TALENT_CONFERENCE_KEY      = 'rencaidahui';
    const TYPE_GUIGUO_OTHER_ACTIVITY_KEY         = 'qitahuodong';
    /** 活动类型key */
    const TYPE_GUIGUO_KEY_LIST = [
        self::TYPE_GUIGUO_SCHOLARS_FORUM_KEY,
        self::TYPE_GUIGUO_STUDENTS_RETURNED_HOME_KEY,
        self::TYPE_GUIGUO_VENTURE_CONTEST_KEY,
        self::TYPE_GUIGUO_TALENT_CONFERENCE_KEY,
        self::TYPE_GUIGUO_OTHER_ACTIVITY_KEY,
    ];
    //活动类型code对应的活动类型拼音
    const TYPE_GUIGUO_CODE_KEY_LIST = [
        BaseHwActivity::TYPE_GUIGUO_SCHOLARS_FORUM         => self::TYPE_GUIGUO_SCHOLARS_FORUM_KEY,
        BaseHwActivity::TYPE_GUIGUO_STUDENTS_RETURNED_HOME => self::TYPE_GUIGUO_STUDENTS_RETURNED_HOME_KEY,
        BaseHwActivity::TYPE_GUIGUO_VENTURE_CONTEST        => self::TYPE_GUIGUO_VENTURE_CONTEST_KEY,
        BaseHwActivity::TYPE_GUIGUO_TALENT_CONFERENCE      => self::TYPE_GUIGUO_TALENT_CONFERENCE_KEY,
        BaseHwActivity::TYPE_GUIGUO_OTHER_ACTIVITY         => self::TYPE_GUIGUO_OTHER_ACTIVITY_KEY,
    ];

    /** 活动类型 */
    const TYPE_GUIGUO_LIST = [
        [
            'id'     => BaseHwActivity::TYPE_GUIGUO_SCHOLARS_FORUM,
            'name'   => BaseHwActivity::TYPE_GUIGUO_SCHOLARS_FORUM_NAME,
            'key'    => self::TYPE_GUIGUO_SCHOLARS_FORUM_KEY,
            'url'    => '',
            'active' => false,
        ],
        [
            'id'     => BaseHwActivity::TYPE_GUIGUO_STUDENTS_RETURNED_HOME,
            'name'   => BaseHwActivity::TYPE_GUIGUO_STUDENTS_RETURNED_HOME_NAME,
            'key'    => self::TYPE_GUIGUO_STUDENTS_RETURNED_HOME_KEY,
            'url'    => '',
            'active' => false,
        ],
        [
            'id'     => BaseHwActivity::TYPE_GUIGUO_VENTURE_CONTEST,
            'name'   => BaseHwActivity::TYPE_GUIGUO_VENTURE_CONTEST_NAME,
            'key'    => self::TYPE_GUIGUO_VENTURE_CONTEST_KEY,
            'url'    => '',
            'active' => false,
        ],
        [
            'id'     => BaseHwActivity::TYPE_GUIGUO_TALENT_CONFERENCE,
            'name'   => BaseHwActivity::TYPE_GUIGUO_TALENT_CONFERENCE_NAME,
            'key'    => self::TYPE_GUIGUO_TALENT_CONFERENCE_KEY,
            'url'    => '',
            'active' => false,
        ],
        [
            'id'     => BaseHwActivity::TYPE_GUIGUO_OTHER_ACTIVITY,
            'name'   => BaseHwActivity::TYPE_GUIGUO_OTHER_ACTIVITY_NAME,
            'key'    => self::TYPE_GUIGUO_OTHER_ACTIVITY_KEY,
            'url'    => '',
            'active' => false,
        ],
    ];

    //归国活动地区比较特殊要定义ID进来
    const GUIGUO_AREA_IDS = [
        2,
        802,
        20,
        2324,
        1046,
        1168,
        3022,
        1964,
        2162,
        2572,
        2291,
        25,
        655,
        1532,
        1709,
        1827,
        351,
        820,
        1263,
        585,
        466,
        3178,
        3126,
        2898,
        1375,
        220,
        2367,
        2816,
        3206,
        2670,
        933,
        3716,
        3738,
        3325,
        1965,
        1988,
        1710,
        821,
        2899,
        2368,
        934,
        586,
        1828,
        481,
        1169,
        2573,
        656,
        2292,
        1047,
        352,
        1376,
        2671,
        2817,
        3023,
        1264,
        2163,
        467,
        38,
        861,
        221,
        3207,
        3127,
        1183,
        3179,
        1533,
        1999,
    ];

    private static function getGuiGuoArea()
    {
        //缓存
        $areaList = Cache::get(Cache::HAIWAI_GUIGUO_AREA_KEY);
        if (!$areaList) {
            $areaList = BaseArea::find()
                ->select([
                    'id',
                    'name',
                    'level',
                    'spell as key',
                ])
                ->where([
                    'id' => self::GUIGUO_AREA_IDS,
                ])
                ->orderBy((['FIELD(id,' . implode(',', self::GUIGUO_AREA_IDS) . ')' => SORT_ASC]))
                ->asArray()
                ->all();
            foreach ($areaList as &$area) {
                $area = array_merge($area, self::COMMON_ITEM);
            }
            Cache::set(Cache::HAIWAI_GUIGUO_AREA_KEY, json_encode($areaList));
        } else {
            $areaList = json_decode($areaList, true);
        }

        return [
            'areaList'       => $areaList,
            'areaAllKeyList' => array_column($areaList, 'key'),
        ];
    }

    //归国活动单位类型
    const GUIGUO_COMPANY_CATEGORY_IDS = BaseService::PARAMS_COMPANY_TYPE;

    private static function getGuiGuoCompanyCategory()
    {
        //缓存
        $categoryList = Cache::get(Cache::HAIWAI_GUIGUO_COMPANY_CATEGORY_KEY);
        if (!$categoryList) {
            $categoryList = BaseDictionary::find()
                ->select([
                    'code as id',
                    'name',
                ])
                ->where([
                    'code' => self::GUIGUO_COMPANY_CATEGORY_IDS,
                    'type' => BaseDictionary::TYPE_25,
                ])
                // 按照 GUIGUO_COMPANY_CATEGORY_IDS 排序
                ->orderBy([new Expression('FIELD(code,' . implode(',', self::GUIGUO_COMPANY_CATEGORY_IDS) . ')')])
                ->asArray()
                ->all();
            foreach ($categoryList as &$category) {
                $category = array_merge($category, self::COMMON_ITEM);
            }
            Cache::set(Cache::HAIWAI_GUIGUO_COMPANY_CATEGORY_KEY, json_encode($categoryList));
        } else {
            $categoryList = json_decode($categoryList, true);
        }

        return ['categoryList' => $categoryList];
    }

    /** 归国活动配置 end////////////////////////////////////////////////////////////////////////////////////////////////////////// */

    /** 定义一个公共的空结构 */
    const COMMON_ITEM       = [
        'url'    => '',
        'active' => false,
    ];
    const COMMON_LIMIT_ITEM = [
        'id'     => 0,
        'name'   => '不限',
        'url'    => '',
        'active' => false,
    ];

    const ACTIVITY_TIME_TYPE_OVER_THREE_MONTH = 1;
    const ACTIVITY_TIME_TYPE_THIS_MONTH       = 2;
    const ACTIVITY_TIME_TYPE_NEXT_THREE_MONTH = 3;
    const ACTIVITY_TIME_TYPE_THIS_YEAR        = 4;

    const ACTIVITY_TIME_LIST = [
        [
            'name'   => '不限',
            'id'     => 0,
            'url'    => '',
            'active' => false,
        ],
        [
            'name'   => '过去3个月',
            'id'     => self::ACTIVITY_TIME_TYPE_OVER_THREE_MONTH,
            'url'    => '',
            'active' => false,
        ],
        [
            'name'   => '本月',
            'id'     => self::ACTIVITY_TIME_TYPE_THIS_MONTH,
            'url'    => '',
            'active' => false,
        ],
        [
            'name'   => '接下来3个月',
            'id'     => self::ACTIVITY_TIME_TYPE_NEXT_THREE_MONTH,
            'url'    => '',
            'active' => false,
        ],
        [
            'name'   => '本年度',
            'id'     => self::ACTIVITY_TIME_TYPE_THIS_YEAR,
            'url'    => '',
            'active' => false,
        ],
    ];

    const ACTIVITY_STATUS_LIST = [
        [
            'name'   => '不限',
            'id'     => 0,
            'url'    => '',
            'active' => false,
        ],
        [
            'name'   => '进行中',
            'id'     => BaseHwActivity::ACTIVITY_STATUS_PROGRESS,
            'url'    => '',
            'active' => false,
        ],
        [
            'name'   => '已结束',
            'id'     => BaseHwActivity::ACTIVITY_STATUS_END,
            'url'    => '',
            'active' => false,
        ],
    ];

    const TYPE_CHUHAI            = 1;
    const TYPE_GUIGUO            = 2;
    const TYPE_CHUHAI_URL_PREFIX = '/chuhai';
    const TYPE_GUIGUO_URL_PREFIX = '/guiguo';

    const SEARCH_AREA_KEY                 = 'areaId';
    const SEARCH_ACTIVITY_TYPE_KEY        = 'activityType';
    const SEARCH_ACTIVITY_TIME_KEY        = 'activityTime';
    const SEARCH_ACTIVITY_CUSTOM_TIME_KEY = 'activityCustomTime';
    const SEARCH_ACTIVITY_STATUS_KEY      = 'activityStatus';
    const SEARCH_COMPANY_CATEGORY_KEY     = 'companyCategory';

    private static $oldParams;
    private static $params;
    private static $host;
    private static $hostInfo;

    /**
     * 获取seo
     * @param $params
     * @param $type
     * @return array|false
     */
    public static function getSeoUrl($params, $type)
    {
        //如果含有活动时间参数与自定义活动时间时候，直接清除自定义活动时间
        if (isset($params[self::SEARCH_ACTIVITY_TIME_KEY]) && $params[self::SEARCH_ACTIVITY_TIME_KEY] && isset($params[self::SEARCH_ACTIVITY_CUSTOM_TIME_KEY]) && !empty($params[self::SEARCH_ACTIVITY_CUSTOM_TIME_KEY])) {
            //清除自定义活动时间
            unset($params[self::SEARCH_ACTIVITY_CUSTOM_TIME_KEY]);
        }
        self::$params = self::$oldParams = $params;
        //去掉$oldParams里面的分页参数
        unset(self::$params['page']);
        $config = self::getConfig($type);
        //分情况讨论
        // $params 中level1、level2进行讨论
        if (!isset($params['level1']) && !isset($params['level2'])) {
            //此时就是当前首页没有seo参数的情况-无需验证直接组装
            $paramsType = 0;
        } elseif (!empty($params['level1']) && !isset($params['level2'])) {
            //此时seo只有一个参数，就是level1
            //这个时候，我们需要根据level1来判断是活动类型，还是地区
            if (!in_array($params['level1'], $config['areaKey']) && !in_array($params['level1'],
                    $config['activityTypeKey'])) {
                return false;
            }
            //那看看level1是不是在活动类型里面
            if (in_array($params['level1'], $config['activityTypeKey'])) {
                $paramsType                             = 1;
                $params[self::SEARCH_ACTIVITY_TYPE_KEY] = $params['level1'];
            } else {
                //这里说明level1是地区
                $paramsType = 2;
            }
        } else {
            //此时seo有两个参数 level1肯定是地区，level2肯定是活动类型
            //验证level1在地区key里面 level2在活动类型key里面不在就去404
            if (!in_array($params['level1'], $config['areaKey']) || !in_array($params['level2'],
                    $config['activityTypeKey'])) {
                return false;
            }
            //这时候就是是对应位置替换就好了
            $paramsType = 3;
        }
        unset($config['areaKey'], $config['activityTypeKey']);
        $search = self::structure($config, $paramsType);

        $search['searchList']['activityTime']   = self::getTimeStructure(self::ACTIVITY_TIME_LIST);
        $search['searchList']['activityStatus'] = self::getStatusStructure(self::ACTIVITY_STATUS_LIST);

        if ($type == self::TYPE_GUIGUO) {
            $search['searchList']['companyCategory'] = self::getCompanyCategoryStructure();
        }

        unset(self::$params['level1'], self::$params['level2']);
        $search['params']         = self::$params;
        $search['params']['page'] = self::$oldParams['page'];

        return $search;
    }

    /**
     * 获取配置
     * @param $type
     * @return array
     */
    private static function getConfig($type)
    {
        //构造数据结构
        switch ($type) {
            case self::TYPE_CHUHAI:
                $areaData = self::getAbroadArea();
                $config   = [
                    'area'            => $areaData['areaList'],
                    'areaKey'         => $areaData['areaAllKeyList'],
                    'activityType'    => self::TYPE_CHUHAI_LIST,
                    'activityTypeKey' => self::TYPE_CHUHAI_KEY_LIST,
                ];
                $prefix   = self::TYPE_CHUHAI_URL_PREFIX;
                break;
            case self::TYPE_GUIGUO:
                $areaData = self::getGuiGuoArea();
                $config   = [
                    'area'            => $areaData['areaList'],
                    'areaKey'         => $areaData['areaAllKeyList'],
                    'activityType'    => self::TYPE_GUIGUO_LIST,
                    'activityTypeKey' => self::TYPE_GUIGUO_KEY_LIST,
                ];
                $prefix   = self::TYPE_GUIGUO_URL_PREFIX;
                break;
            default:
                $config = [];
        }
        $host     = $prefix;
        $hostInfo = $host;
        $params   = self::$oldParams;
        if (!empty($params['level1'])) {
            $hostInfo .= '/' . $params['level1'];
        }
        if (!empty($params['level2'])) {
            $hostInfo .= '/' . $params['level2'];
        }
        self::$host     = $host;
        self::$hostInfo = $hostInfo;

        return $config;
    }

    /**
     * 补充url与active
     * @param $params
     */
    private static function structure($config, $paramsType)
    {
        //剔除level1、level2参数
        $httpParams = self::$params;
        unset($httpParams['level1'], $httpParams['level2']);
        $afterPrefixParams = empty($httpParams) ? '' : '?' . http_build_query($httpParams);
        //地区
        foreach ($config['area'] as $k => &$item) {
            $item_data = self::getAreaStructure($item, $paramsType, $afterPrefixParams);
            $item      = $item_data['item'];
            if (!empty($item['children'])) {
                $item['activeDot'] = false;
                $isTrue            = false;
                foreach ($item['children'] as &$child) {
                    $child_data = self::getAreaStructure($child, $paramsType, $afterPrefixParams);
                    $child      = $child_data['item'];
                    if (!$isTrue && $child_data['item']['active']) {
                        $isTrue = true;
                    }
                }
                if ($isTrue) {
                    $item['active']    = true;
                    $item['activeDot'] = true;
                }
            }
        }

        //活动类型
        foreach ($config['activityType'] as &$item) {
            $item_data = self::getActivityStructure($item, $paramsType, $afterPrefixParams);
            $item      = $item_data['item'];
        }

        $areaActive             = false;
        $activityStatusActive   = false;
        $areaLimitUrl           = '';
        $activityStatusLimitUrl = '';
        switch ($paramsType) {
            case 0:
                //当前首页
                $areaLimitUrl           = self::$host . $afterPrefixParams;
                $activityStatusLimitUrl = self::$host . $afterPrefixParams;
                $areaActive             = true;
                $activityStatusActive   = true;
                break;
            case 1:
                //活动类型
                $areaLimitUrl           = self::$host . '/' . self::$oldParams['level1'] . $afterPrefixParams;
                $activityStatusLimitUrl = self::$host . $afterPrefixParams;
                $areaActive             = true;
                break;
            case 2:
                //地区
                $areaLimitUrl           = self::$host . $afterPrefixParams;
                $activityStatusLimitUrl = self::$host . '/' . self::$oldParams['level1'] . $afterPrefixParams;
                $activityStatusActive   = true;

                break;
            case 3:
                //地区+活动类型
                $areaLimitUrl           = self::$host . '/' . self::$oldParams['level2'] . $afterPrefixParams;
                $activityStatusLimitUrl = self::$host . '/' . self::$oldParams['level1'] . $afterPrefixParams;
                break;
        }
        $areaItem           = self::COMMON_LIMIT_ITEM;
        $areaItem['url']    = $areaLimitUrl;
        $areaItem['active'] = $areaActive;
        array_unshift($config['area'], $areaItem);

        $activityStatusItem           = self::COMMON_LIMIT_ITEM;
        $activityStatusItem['url']    = $activityStatusLimitUrl;
        $activityStatusItem['active'] = $activityStatusActive;
        array_unshift($config['activityType'], $activityStatusItem);

        return [
            'searchList' => $config,
        ];
    }

    /**
     * 地区结构处理
     * @param $item
     * @param $paramsType
     * @param $afterPrefix
     * @return array
     */
    private static function getAreaStructure($item, $paramsType, $afterPrefix)
    {
        $params = self::$oldParams;
        $host   = self::$host;
        switch ($paramsType) {
            case 0:
                //当前首页
                $item['url'] = $host . '/' . $item['key'] . $afterPrefix;
                break;
            case 1:
                //活动类型
                $item['url'] = $host . '/' . $item['key'] . '/' . $params['level1'] . $afterPrefix;
                break;
            case 2:
                //地区
                $item['url'] = $host . '/' . $item['key'] . $afterPrefix;
                if ($params['level1'] == $item['key']) {
                    $item['active']                      = true;
                    self::$params[self::SEARCH_AREA_KEY] = $item['id'] == '9999999' ? self::SPECIAL_CHUHAI_AREA_IDS : $item['id'];
                }
                break;
            case 3:
                //地区+活动类型
                $item['url'] = $host . '/' . $item['key'] . '/' . $params['level2'] . $afterPrefix;
                if ($params['level1'] == $item['key']) {
                    $item['active']                      = true;
                    self::$params[self::SEARCH_AREA_KEY] = $item['id'] == '9999999' ? self::SPECIAL_CHUHAI_AREA_IDS : $item['id'];
                }
        }

        return [
            'item' => $item,
        ];
    }

    /**
     * 活动类型结构处理
     * @param $item
     * @param $paramsType
     * @param $afterPrefix
     * @return array
     */
    private static function getActivityStructure($item, $paramsType, $afterPrefix)
    {
        $params = self::$oldParams;
        $host   = self::$host;
        switch ($paramsType) {
            case 0:
                //当前首页
                $item['url'] = $host . '/' . $item['key'] . $afterPrefix;
                break;
            case 1:
                //活动类型
                $item['url'] = $host . '/' . $item['key'] . $afterPrefix;
                if ($params['level1'] == $item['key']) {
                    $item['active']                               = true;
                    self::$params[self::SEARCH_ACTIVITY_TYPE_KEY] = strval($item['id']);
                }
                break;
            case 2:
                //地区
                $item['url'] = $host . '/' . $params['level1'] . '/' . $item['key'] . $afterPrefix;
                break;
            case 3:
                //地区+活动类型
                $item['url'] = $host . '/' . $params['level1'] . '/' . $item['key'] . $afterPrefix;
                if ($params['level2'] == $item['key']) {
                    $item['active']                               = true;
                    self::$params[self::SEARCH_ACTIVITY_TYPE_KEY] = strval($item['id']);
                }
        }

        return [
            'item' => $item,
        ];
    }

    /**
     * 活动时间结构处理
     */
    private static function getTimeStructure($activityTimeList)
    {
        $beforePrefix = self::$hostInfo;
        $params       = self::$oldParams;
        $oldParams    = $params;
        $isCustomTime = isset($oldParams[self::SEARCH_ACTIVITY_CUSTOM_TIME_KEY]) && !empty($oldParams[self::SEARCH_ACTIVITY_CUSTOM_TIME_KEY]);
        unset($params['level1'], $params['level2'], $params[self::SEARCH_ACTIVITY_CUSTOM_TIME_KEY]);
        foreach ($activityTimeList as &$time) {
            if ($time['id'] == 0) {
                // unset($params[self::SEARCH_ACTIVITY_TIME_KEY]);
                $params[self::SEARCH_ACTIVITY_TIME_KEY] = '';
                if (empty($oldParams[self::SEARCH_ACTIVITY_TIME_KEY]) && !$isCustomTime) {
                    $time['active'] = true;
                }
            } else {
                $params[self::SEARCH_ACTIVITY_TIME_KEY] = $time['id'];
                if (isset($oldParams[self::SEARCH_ACTIVITY_TIME_KEY]) && $oldParams[self::SEARCH_ACTIVITY_TIME_KEY] == $time['id']) {
                    $time['active'] = true;
                }
            }
            $time['url'] = $beforePrefix . (empty($params) ? '' : '?' . http_build_query($params));
        }

        return $activityTimeList;
    }

    /**
     * 活动状态结构处理
     */
    private static function getStatusStructure($activityStatusList)
    {
        $beforePrefix = self::$hostInfo;
        $params       = self::$oldParams;
        $oldParams    = $params;
        unset($params['level1'], $params['level2']);
        foreach ($activityStatusList as &$time) {
            if ($time['id'] == 0) {
                unset($params[self::SEARCH_ACTIVITY_STATUS_KEY]);
                if (!isset($oldParams[self::SEARCH_ACTIVITY_STATUS_KEY])) {
                    $time['active'] = true;
                }
            } else {
                $params[self::SEARCH_ACTIVITY_STATUS_KEY] = $time['id'];
                if (isset($oldParams[self::SEARCH_ACTIVITY_STATUS_KEY]) && $oldParams[self::SEARCH_ACTIVITY_STATUS_KEY] == $time['id']) {
                    $time['active'] = true;
                }
            }
            $time['url'] = $beforePrefix . (empty($params) ? '' : '?' . http_build_query($params));
        }

        return $activityStatusList;
    }

    /**
     * 单位分类结构处理
     */
    private static function getCompanyCategoryStructure()
    {
        $list = self::getGuiGuoCompanyCategory()['categoryList'];
        array_unshift($list, self::COMMON_LIMIT_ITEM);
        $beforePrefix = self::$hostInfo;
        $params       = self::$oldParams;
        $oldParams    = $params;
        unset($params['level1'], $params['level2']);
        foreach ($list as &$item) {
            if ($item['id'] == 0) {
                unset($params[self::SEARCH_COMPANY_CATEGORY_KEY]);
                if (!isset($oldParams[self::SEARCH_COMPANY_CATEGORY_KEY])) {
                    $item['active'] = true;
                }
            } else {
                $params[self::SEARCH_COMPANY_CATEGORY_KEY] = $item['id'];
                if (isset($oldParams[self::SEARCH_COMPANY_CATEGORY_KEY]) && $oldParams[self::SEARCH_COMPANY_CATEGORY_KEY] == $item['id']) {
                    $item['active'] = true;
                }
            }
            $item['url'] = $beforePrefix . (empty($params) ? '' : '?' . http_build_query($params));
        }

        return $list;
    }

    private static function getCacheKey()
    {
        $params = self::$oldParams;
        ksort($params);

        return md5(http_build_query($params));
    }
}