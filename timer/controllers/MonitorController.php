<?php

namespace timer\controllers;

use common\helpers\IpHelper;
use common\libs\WxWork;
use itbdw\Ip\IpLocation;

/**
 *
 */
class MonitorController extends BaseTimerController
{
    /**
     * 检查可疑ip
     * php ./timer_yii monitor/check-doubtful-ip
     */
    public function actionCheckDoubtfulIp()
    {
        $sql = "select count(*) as total, ip,controller,user_id
from controller_access_log
where  add_time >= NOW() - INTERVAL 1 HOUR and ip in (SELECT ip
             FROM controller_access_log
             WHERE add_time >= NOW() - INTERVAL 1 HOUR
               and ip != '**************'
             GROUP BY ip
             HAVING COUNT(DISTINCT controller) <= 3
                AND COUNT(*) > 500)
group by controller,ip order by ip,total desc";

        // 上面转成yii的sql

        $rs = \Yii::$app->db->createCommand($sql)
            ->queryAll();

        /**
         * [0] => Array
         * (
         * [total] => 96
         * [ip] => **************
         * [controller] => config/get-emote-list
         * [user_id] => 0
         * )
         */

        $content = '';
        if ($rs) {
            // 组成markdown
            $content = "## 可疑IP访问检测\n\n";
            $content .= "| IP | 访问次数 | 控制器 | 用户ID | 地区 |\n";
            $content .= "| --- | --- | --- | --- | --- |\n";

            foreach ($rs as $item) {
                $area    = $this->ipToArea($item['ip']);
                $content .= "|{$item['ip']}|{$item['total']}|{$item['controller']}|{$item['user_id']}|{$area}|\n";
            }

            $wxWork = WxWork::getInstance();
            $wxWork->robotMessageToCrawler($content, true);
        }

        $this->ipMonitoring();
        $this->controllerFrequency();
    }

    /**
     *监控一下现在队列的一些情况，避免队列堆积
     * php ./timer_yii monitor/queue
     */
    public function actionQueue()
    {
        $list = [
            'clickQueue'                      => [
                'queue' => \Yii::$app->clickQueue,
                'max'   => 10000,
            ],
            // 短信
            'smsQueue'                        => [
                'queue' => \Yii::$app->smsQueue,
                'max'   => 100,
            ],
            // 邮件
            'emailQueue'                      => [
                'queue' => \Yii::$app->emailQueue,
                'max'   => 100,
            ],
            // 邮件2
            'emailQueue2'                     => [
                'queue' => \Yii::$app->emailQueue2,
                'max'   => 100,
            ],
            // 邮件3
            'emailQueue3'                     => [
                'queue' => \Yii::$app->emailQueue3,
                'max'   => 100,
            ],
            // 邮件4
            'emailQueue4'                     => [
                'queue' => \Yii::$app->emailQueue4,
                'max'   => 100,
            ],
            // 邮件5
            'emailQueue5'                     => [
                'queue' => \Yii::$app->emailQueue5,
                'max'   => 100,
            ],
            // 公告更新
            'afterAnnouncementUpdateJobQueue' => [
                'queue' => \Yii::$app->afterAnnouncementUpdateJobQueue,
                'max'   => 100,
            ],
        ];

        $content = '';
        foreach ($list as $k => $item) {
            $queue  = $item['queue'];
            $length = $queue->redis->llen($queue->channel . '.waiting');

            if ($length > $item['max']) {
                $content .= "队列{$k}堆积{$length}个任务\n";
            }
        }

        // 去掉最后一个换行
        $content = rtrim($content, "\n");

        if ($content) {
            $wxWork = WxWork::getInstance();
            $wxWork->robotMessageToSystem($content);
        }
    }

    private function ipMonitoring()
    {
        $h5Path       = '/www/wwwroot/OPS/ip_monitoring/h5';
        $pcPath       = '/www/wwwroot/OPS/ip_monitoring/pc';
        $boshihouPath = '/www/wwwroot/OPS/ip_monitoring/boshihou';
        $time         = date('YmdH');
        $whiteList    = ['**************'];
        $wxWork       = WxWork::getInstance();

        // 准备数据源
        $dataSources = [
            'PC'       => $pcPath . '/' . $time . '.json',
            'H5'       => $h5Path . '/' . $time . '.json',
            'boshihou' => $boshihouPath . '/' . $time . '.json',
        ];

        foreach ($dataSources as $type => $filePath) {
            if (!file_exists($filePath)) {
                continue; // 文件不存在跳过
            }

            // 获取并解析JSON数据
            $content = file_get_contents($filePath);
            $data    = json_decode($content, true);

            // 验证JSON格式
            if (json_last_error() !== JSON_ERROR_NONE) {
                continue; // JSON格式不正确跳过
            }

            // 构建消息内容
            $message       = "## {$time}过去一小时{$type}端访问量统计(超过1000次)\n\n";
            $message       .= "| IP | 访问次数 | 地区 |\n";
            $message       .= "| --- | --- | --- |\n";
            $noticeMessage = false;

            foreach ($data as $item) {
                $ip    = $item['ip'];
                $count = $item['count'];

                // 白名单过滤和访问量判断
                if (!in_array($ip, $whiteList) && $count > 1000) {
                    $area          = $this->ipToArea($ip);
                    $message       .= "|{$ip}|{$count}|{$area}|\n";
                    $noticeMessage = true;
                }
            }

            // 如果有消息需要发送
            if ($noticeMessage) {
                $wxWork->robotMessageToCrawler($message, true);
            }
        }
    }

    // 控制器频率
    private function controllerFrequency()
    {
        $sql = "select count(*) as total, controller
from controller_access_log
where add_time
          between date_sub(now(), interval 1 hour ) and now()
group by controller having total > 25000
order by total desc ;";

        $rs = \Yii::$app->db->createCommand($sql)
            ->queryAll();

        if (!$rs) {
            return;
        }

        $time    = CUR_DATETIME;
        $content = "## {$time}过去一小时控制器访问量统计(超过25000次)：\n\n";
        $content .= "| 控制器 | 访问次数 |\n";
        $content .= "| --- | --- |\n";

        foreach ($rs as $item) {
            $content .= "| {$item['controller']} | {$item['total']} |\n";
        }
        $wxWork = WxWork::getInstance();
        $wxWork->robotMessageToCrawler($content, true);
    }

    private function ipToArea($ip)
    {
        $path = \Yii::getAlias('@common') . IpHelper::DATA_FILE;

        if (!file_exists($path)) {
            return '';
        }
        $data = IpLocation::getLocation($ip, $path);

        //    [country] => 中国
        //     [province] => 北京
        //     [city] =>
        //     [county] =>
        $areaArray = [
            $data['country'],
            $data['province'],
            $data['city'],
            $data['county'],
        ];

        // 合并去重
        $areaArray = array_unique(array_filter($areaArray));

        $area = implode(',', $areaArray);

        return $area;
    }

}
