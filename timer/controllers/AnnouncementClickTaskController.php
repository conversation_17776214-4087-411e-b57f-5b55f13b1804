<?php

namespace timer\controllers;

use common\libs\Cache;
use common\service\announcement\ClickTotalService;
use Yii;
use common\helpers\IpHelper;
use common\base\models\BaseAnnouncementClickUpdateConfig;
use common\base\models\BaseArticleClickLog;
use common\base\models\BaseResume;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArticle;
use common\base\models\BaseDictionary;
use common\base\models\BaseArea;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseAnnouncementClickTotalDaily;
use common\base\models\BaseAnnouncementClickEducationTotal;
use common\base\models\BaseAnnouncementClickEducationTotalDaily;
use common\base\models\BaseAnnouncementClickWorkExperienceTotal;
use common\base\models\BaseAnnouncementClickWorkExperienceTotalDaily;
use common\base\models\BaseAnnouncementClickPoliticalStatusTotal;
use common\base\models\BaseAnnouncementClickPoliticalStatusTotalDaily;
use common\base\models\BaseAnnouncementClickAgeTotal;
use common\base\models\BaseAnnouncementClickAgeTotalDaily;
use common\base\models\BaseAnnouncementClickTitleTotal;
use common\base\models\BaseAnnouncementClickTitleTotalDaily;
use common\base\models\BaseAnnouncementClickSubtitleTotal;
use common\base\models\BaseAnnouncementClickSubtitleTotalDaily;
use common\base\models\BaseAnnouncementClickAreaProvinceTotal;
use common\base\models\BaseAnnouncementClickAreaProvinceTotalDaily;
use common\base\models\BaseAnnouncementClickAreaCityTotal;
use common\base\models\BaseAnnouncementClickAreaCityTotalDaily;
use common\base\models\BaseAnnouncementClickColumnTotal;
use common\base\models\BaseAnnouncementClickColumnTotalDaily;

/**
 * 处理公告点击量的定时任务类
 * php timer_yii announcement-click-task/run
 */
class AnnouncementClickTaskController extends BaseTimerController
{
    // 当前类名称
    private $_className = 'AnnouncementClickTask';

    // 最大limit数量
    private $_defaultLimitMax = 1000000;

    // 日志源数据(实时)
    private $_realLogData;

    // 日志源数据(历史)
    private $_historyLogData;

    // 当前执行数据是否历史数据
    private $_isHistory = false;

    /**
     * 同步统计主程序
     */
    public function actionRun()
    {
        //        return 1;
        //直接获取数据然后执行操作
        //        $id   = Cache::get('AnnouncementClickTask') ?: 0;
        $data = BaseArticleClickLog::find()
            ->select([
                'add_time',
                'member_id',
                'article_id',
                'ip',
            ])
            //            ->where([
            //                '>',
            //                'id',
            //                $id,
            //            ])
            ->offset(10000)
            ->limit(100000)
            // ->andWhere([
            //     'article_id' => [
            //         1229,
            //         719,
            //         201,
            //         974,
            //         1192,
            //         418,
            //         787,
            //         665,
            //         168,
            //         408,
            //     ],
            // ])
            // article_id 在63295944和63300081之间
            //            ->andWhere([
            //                'between',
            //                'id',
            //                1,
            //                1000,
            //            ])
            ->orderBy('id asc')
            ->asArray()
            ->all();
        $i    = 1;
        foreach ($data as $item) {
            $service = new ClickTotalService();
            $service->initData($item)
                ->run();
            self::log('执行：' . $i++);
            //            Cache::set('AnnouncementClickTask', $item['id']);
        }

        self::log('执行完毕');

        //        try {
        //            // 初始化
        //            $this->initData();
        //            // 处理逻辑
        //            // 实时
        //            $this->logic($this->_realLogData);
        //            // 历史
        //            $this->_isHistory = true;
        //            $this->logic($this->_historyLogData);
        //        } catch (\Exception $e) {
        //            self::log($e->getMessage());
        //        }
    }

    /**
     * 初始化
     */
    public function initData()
    {
        // 获取配置信息
        $configRow = BaseAnnouncementClickUpdateConfig::findOne(['class_name' => $this->_className]);
        if (empty($configRow)) { // 第一次执行
            // 查询前一天结束时间的最后一条日志id
            $initDateTime                              = date('Y-m-d H:i:s', strtotime(date('Y-m-d', time())));
            $initId                                    = BaseArticleClickLog::find()
                                                             ->select('id')
                                                             ->where([
                                                                 '<',
                                                                 'add_time',
                                                                 $initDateTime,
                                                             ])
                                                             ->orderBy('id desc')
                                                             ->limit(1)
                                                             ->one()['id'];
            $updateConfig                              = new BaseAnnouncementClickUpdateConfig();
            $updateConfig->update_time                 = CUR_DATETIME;
            $updateConfig->start_id                    = $initId + 1;
            $updateConfig->end_id                      = $initId;
            $updateConfig->limit_max                   = $this->_defaultLimitMax;
            $updateConfig->class_name                  = $this->_className;
            $updateConfig->real_execute_status         = BaseAnnouncementClickUpdateConfig::STATUS_EXECUTE_COMPLETE;
            $updateConfig->history_execute_status      = BaseAnnouncementClickUpdateConfig::STATUS_EXECUTE_COMPLETE;
            $updateConfig->history_log_complete_status = BaseAnnouncementClickUpdateConfig::STATUS_HISTORY_INCOMPLETE;
            $updateConfig->real_fail_msg               = '';
            $updateConfig->history_fail_msg            = '';
            $updateConfig->save();
            $configRow = $updateConfig;
        }
        // 上次运行结果未完成
        if ($configRow['real_execute_status'] == BaseAnnouncementClickUpdateConfig::STATUS_EXECUTE_INCOMPLETE || $configRow['history_execute_status'] == BaseAnnouncementClickUpdateConfig::STATUS_EXECUTE_INCOMPLETE) {
            throw new \Exception('上次脚本运行异常');
        }
        $startId  = $configRow['start_id'];
        $endId    = $configRow['end_id'];
        $limitMax = $configRow['limit_max'];
        // 实时日志数据
        $this->_realLogData = $realLogData = $this->setLogData([
            '>',
            'l.id',
            $endId,
        ], 'l.id asc', $limitMax);
        if (empty($this->_realLogData)) {
            self::log('暂无实时日志');
        } else {
            $logIds     = array_column($realLogData, 'id');
            $logStartId = array_shift($logIds);
            $logEndId   = array_pop($logIds) ?: $logStartId;
            $logCount   = count($realLogData);
            unset($realLogData);

            self::log('日志源数据-实时待处理-日志id区间:' . $logStartId . '---' . $logEndId);
            self::log('日志源数据-实时待处理-条数:' . $logCount);
        }

        // 上次历史数据未完成
        if ($configRow['history_log_complete_status'] == BaseAnnouncementClickUpdateConfig::STATUS_HISTORY_INCOMPLETE) {
            $this->_historyLogData = $historyLogData = $this->setLogData([
                '<',
                'l.id',
                $startId,
            ], 'l.id desc', $limitMax);
            // 无历史数据，修改历史数据为已完成
            if (empty($historyLogData)) {
                $configRow->history_log_complete_status = BaseAnnouncementClickUpdateConfig::STATUS_HISTORY_COMPLETE;
                $configRow->save();
            } else {
                $logIds     = array_column($historyLogData, 'id');
                $logStartId = array_pop($logIds);
                $logEndId   = array_shift($logIds) ?: $logStartId;
                $logCount   = count($historyLogData);
                unset($historyLogData);

                self::log('日志源数据-历史待处理-日志id区间:' . $logStartId . '---' . $logEndId);
                self::log('日志源数据-历史待处理-条数:' . $logCount);
            }
        }

        return $this;
    }

    /**
     * 设置日志源数据
     */
    private function setLogData($where, $orderBy, $limit)
    {
        $logData = BaseArticleClickLog::find()
            ->alias('l')
            ->select([
                'l.id',
                'l.article_id',
                'l.member_id',
                'l.ip',
                'l.add_time',
                "DATE_FORMAT(l.add_time, '%Y-%m-%d') AS add_date",
                'IFNULL(r.id, 0) AS resume_id',
                'IFNULL(an.id, 0) AS announcement_id',
                'IFNULL(ar.home_column_id, 0) AS home_column_id',
            ])
            ->leftJoin(['r' => BaseResume::tableName()], 'l.member_id = r.member_id')
            ->leftJoin(['an' => BaseAnnouncement::tableName()], 'l.article_id = an.article_id')
            ->leftJoin(['ar' => BaseArticle::tableName()], 'an.article_id = ar.id')
            ->where($where)
            // 排序不要改
            ->orderBy($orderBy)
            ->limit($limit)
            ->asArray()
            ->all();

        return $logData;
    }

    /**
     * 处理日志
     */
    private function logic($logData)
    {
        if (empty($logData)) {
            return;
        }
        // 简历id
        $resumeIds  = array_unique(array_column($logData, 'resume_id'));
        $resumeData = BaseResume::find()
            ->select('id, top_education_code, work_experience, political_status_id, age, title_id')
            ->where(['id' => $resumeIds])
            ->indexBy('id')
            ->asArray()
            ->all();

        // 职称字典
        $titleDictionary = BaseDictionary::find()
            ->alias('d')
            ->select([
                'd.code',
                'IFNULL(dd.code, 0) AS parent_code',
            ])
            ->where([
                '<>',
                'd.parent_id',
                0,
            ]) // 只要二级
            ->andWhere(['d.type' => BaseDictionary::TYPE_13])
            ->leftJoin(['dd' => BaseDictionary::tableName()], 'dd.id = d.parent_id')
            ->indexBy('code')
            ->asArray()
            ->all();

        // 地区字典(省份)
        $areaProvinceDictionary = BaseArea::find()
            ->select([
                'id',
                'full_name',
            ])
            ->where(['level' => BaseArea::PROVINCE_LEVEL]) // 只要省份
            ->indexBy('id')
            ->asArray()
            ->all();
        // 地区字典(城市)
        $areaCityDictionary = BaseArea::find()
            ->alias('a')
            ->select([
                'a.id',
                'a.full_name',
                'IFNULL(aa.id, 0) AS parent_id',
            ])
            ->where(['a.level' => BaseArea::CITY_LEVEL]) // 只要城市
            ->leftJoin(['aa' => BaseArea::tableName()], 'aa.id = a.parent_id')
            ->indexBy('id')
            ->asArray()
            ->all();

        // 栏目数据
        $homeColumn = BaseHomeColumn::find()
            ->alias('h')
            ->select([
                'h.id',
                'h.level',
                'IFNULL(hh.id, 0) AS parent_id',
            ])
            ->leftJoin(['hh' => BaseHomeColumn::tableName()], 'hh.id = h.parent_id')
            ->indexBy('id')
            ->asArray()
            ->all();

        unset($resumeIds);

        //统计
        foreach ($logData as $log) {
            $announcementId = $log['announcement_id'];
            $resumeId       = $log['resume_id'];
            $addDate        = $log['add_date'];
            $homeColumnId   = $log['home_column_id'];
            $dailyKey       = "{$announcementId}_{$addDate}";

            // 日阅读量
            $dailyClick = ($dailyClickRows[$dailyKey]['total'] ?? 0) + 1;
            // 日
            $dailyClickRows[$dailyKey]['announcement_id'] = $announcementId;
            $dailyClickRows[$dailyKey]['add_date']        = $addDate;
            $dailyClickRows[$dailyKey]['total']           = $dailyClick;

            // 学历
            $totalRests    = $totalEducationRows[$announcementId]['rests'] ?? 0;
            $dailyRests    = $dailyEducationRows[$dailyKey]['rests'] ?? 0;
            $totalCollege  = $totalEducationRows[$announcementId]['college'] ?? 0;
            $dailyCollege  = $dailyEducationRows[$dailyKey]['college'] ?? 0;
            $totalPoaceae  = $totalEducationRows[$announcementId]['poaceae'] ?? 0;
            $dailyPoaceae  = $dailyEducationRows[$dailyKey]['poaceae'] ?? 0;
            $totalMaster   = $totalEducationRows[$announcementId]['master'] ?? 0;
            $dailyMaster   = $dailyEducationRows[$dailyKey]['master'] ?? 0;
            $totalDoctor   = $totalEducationRows[$announcementId]['doctor'] ?? 0;
            $dailyDoctor   = $dailyEducationRows[$dailyKey]['doctor'] ?? 0;
            $totalEduGuest = $totalEducationRows[$announcementId]['guest'] ?? 0;
            $dailyEduGuest = $dailyEducationRows[$dailyKey]['guest'] ?? 0;

            $topEducationCode = $resumeData[$resumeId]['top_education_code'] ?? 0;
            switch ($topEducationCode) {
                case BaseDictionary::EDUCATION_RESTS_ID:
                    $totalRests += 1;
                    $dailyRests += 1;
                    break;
                case BaseDictionary::EDUCATION_COLLEGE_ID:
                    $totalCollege += 1;
                    $dailyCollege += 1;
                    break;
                case BaseDictionary::EDUCATION_POACEAE_ID:
                    $totalPoaceae += 1;
                    $dailyPoaceae += 1;
                    break;
                case BaseDictionary::EDUCATION_MASTER_ID:
                    $totalMaster += 1;
                    $dailyMaster += 1;
                    break;
                case BaseDictionary::EDUCATION_DOCTOR_ID:
                    $totalDoctor += 1;
                    $dailyDoctor += 1;
                    break;
                default:
                    $totalEduGuest += 1;
                    $dailyEduGuest += 1;
                    break;
            }
            // 总
            $totalEducationRows[$announcementId]['announcement_id'] = $announcementId;
            $totalEducationRows[$announcementId]['rests']           = $totalRests;
            $totalEducationRows[$announcementId]['college']         = $totalCollege;
            $totalEducationRows[$announcementId]['poaceae']         = $totalPoaceae;
            $totalEducationRows[$announcementId]['master']          = $totalMaster;
            $totalEducationRows[$announcementId]['doctor']          = $totalDoctor;
            $totalEducationRows[$announcementId]['guest']           = $totalEduGuest;
            // 日
            $dailyEducationRows[$dailyKey]['announcement_id'] = $announcementId;
            $dailyEducationRows[$dailyKey]['add_date']        = $addDate;
            $dailyEducationRows[$dailyKey]['rests']           = $dailyRests;
            $dailyEducationRows[$dailyKey]['college']         = $dailyCollege;
            $dailyEducationRows[$dailyKey]['poaceae']         = $dailyPoaceae;
            $dailyEducationRows[$dailyKey]['master']          = $dailyMaster;
            $dailyEducationRows[$dailyKey]['doctor']          = $dailyDoctor;
            $dailyEducationRows[$dailyKey]['guest']           = $dailyEduGuest;

            // 工作经验
            $totalRange_1_3    = $totalWorkExperienceRows[$announcementId]['range_1_3'] ?? 0;
            $dailyRange_1_3    = $dailyWorkExperienceRows[$dailyKey]['range_1_3'] ?? 0;
            $totalRange_3_5    = $totalWorkExperienceRows[$announcementId]['range_3_5'] ?? 0;
            $dailyRange_3_5    = $dailyWorkExperienceRows[$dailyKey]['range_3_5'] ?? 0;
            $totalRange_5_10   = $totalWorkExperienceRows[$announcementId]['range_5_10'] ?? 0;
            $dailyRange_5_10   = $dailyWorkExperienceRows[$dailyKey]['range_5_10'] ?? 0;
            $totalRange_10     = $totalWorkExperienceRows[$announcementId]['range_10'] ?? 0;
            $dailyRange_10     = $dailyWorkExperienceRows[$dailyKey]['range_10'] ?? 0;
            $totalWxRangeGuest = $totalWorkExperienceRows[$announcementId]['range_guest'] ?? 0;
            $dailyWxRangeGuest = $dailyWorkExperienceRows[$dailyKey]['range_guest'] ?? 0;

            $workExperience = $resumeData[$resumeId]['work_experience'] ?? 0;
            switch (true) {
                case $workExperience >= 1 && $workExperience < 3:
                    $totalRange_1_3 += 1;
                    $dailyRange_1_3 += 1;
                    break;
                case $workExperience >= 3 && $workExperience < 5:
                    $totalRange_3_5 += 1;
                    $dailyRange_3_5 += 1;
                    break;
                case $workExperience >= 5 && $workExperience < 10:
                    $totalRange_5_10 += 1;
                    $dailyRange_5_10 += 1;
                    break;
                case $workExperience >= 10:
                    $totalRange_10 += 1;
                    $dailyRange_10 += 1;
                    break;
                default:
                    $totalWxRangeGuest += 1;
                    $dailyWxRangeGuest += 1;
                    break;
            }
            // 总
            $totalWorkExperienceRows[$announcementId]['announcement_id'] = $announcementId;
            $totalWorkExperienceRows[$announcementId]['range_1_3']       = $totalRange_1_3;
            $totalWorkExperienceRows[$announcementId]['range_3_5']       = $totalRange_3_5;
            $totalWorkExperienceRows[$announcementId]['range_5_10']      = $totalRange_5_10;
            $totalWorkExperienceRows[$announcementId]['range_10']        = $totalRange_10;
            $totalWorkExperienceRows[$announcementId]['range_guest']     = $totalWxRangeGuest;
            // 日
            $dailyWorkExperienceRows[$dailyKey]['announcement_id'] = $announcementId;
            $dailyWorkExperienceRows[$dailyKey]['add_date']        = $addDate;
            $dailyWorkExperienceRows[$dailyKey]['range_1_3']       = $dailyRange_1_3;
            $dailyWorkExperienceRows[$dailyKey]['range_3_5']       = $dailyRange_3_5;
            $dailyWorkExperienceRows[$dailyKey]['range_5_10']      = $dailyRange_5_10;
            $dailyWorkExperienceRows[$dailyKey]['range_10']        = $dailyRange_10;
            $dailyWorkExperienceRows[$dailyKey]['range_guest']     = $dailyWxRangeGuest;

            // 政治面貌
            $totalPartyMember             = $totalPoliticalStatusRows[$announcementId]['party_member'] ?? 0;
            $dailyPartyMember             = $dailyPoliticalStatusRows[$dailyKey]['party_member'] ?? 0;
            $totalDemocraticParty         = $totalPoliticalStatusRows[$announcementId]['democratic_party'] ?? 0;
            $dailyDemocraticParty         = $dailyPoliticalStatusRows[$dailyKey]['democratic_party'] ?? 0;
            $totalNoDemocraticParty       = $totalPoliticalStatusRows[$announcementId]['no_democratic_party'] ?? 0;
            $dailyNoDemocraticParty       = $dailyPoliticalStatusRows[$dailyKey]['no_democratic_party'] ?? 0;
            $totalLeagueMember            = $totalPoliticalStatusRows[$announcementId]['league_member'] ?? 0;
            $dailyLeagueMember            = $dailyPoliticalStatusRows[$dailyKey]['league_member'] ?? 0;
            $totalMasses                  = $totalPoliticalStatusRows[$announcementId]['masses'] ?? 0;
            $dailyMasses                  = $dailyPoliticalStatusRows[$dailyKey]['masses'] ?? 0;
            $totalProbationaryPartyMember = $totalPoliticalStatusRows[$announcementId]['probationary_party_member'] ?? 0;
            $dailyProbationaryPartyMember = $dailyPoliticalStatusRows[$dailyKey]['probationary_party_member'] ?? 0;
            $totalPsGuest                 = $totalPoliticalStatusRows[$announcementId]['guest'] ?? 0;
            $dailyPsGuest                 = $dailyPoliticalStatusRows[$dailyKey]['guest'] ?? 0;

            $politicalStatusId = $resumeData[$resumeId]['political_status_id'] ?? 0;
            switch ($politicalStatusId) {
                case BaseDictionary::POLITICAL_STATUS_PARTY_MEMBER_ID:
                    $totalPartyMember += 1;
                    $dailyPartyMember += 1;
                    break;
                case BaseDictionary::POLITICAL_STATUS_DEMOCRATIC_PARTY_ID:
                    $totalDemocraticParty += 1;
                    $dailyDemocraticParty += 1;
                    break;
                case BaseDictionary::POLITICAL_STATUS_NO_DEMOCRATIC_PARTY_ID:
                    $totalNoDemocraticParty += 1;
                    $dailyNoDemocraticParty += 1;
                case BaseDictionary::POLITICAL_STATUS_LEAGUE_MEMBER_ID:
                    $totalLeagueMember += 1;
                    $dailyLeagueMember += 1;
                    break;
                case BaseDictionary::POLITICAL_STATUS_MASSES_ID:
                    $totalMasses += 1;
                    $dailyMasses += 1;
                    break;
                case BaseDictionary::POLITICAL_STATUS_PROBATIONARY_PARTY_MEMBER_ID:
                    $totalProbationaryPartyMember += 1;
                    $dailyProbationaryPartyMember += 1;
                    break;
                default:
                    $totalPsGuest += 1;
                    $dailyPsGuest += 1;
                    break;
            }
            // 总
            $totalPoliticalStatusRows[$announcementId]['announcement_id']           = $announcementId;
            $totalPoliticalStatusRows[$announcementId]['party_member']              = $totalPartyMember;
            $totalPoliticalStatusRows[$announcementId]['democratic_party']          = $totalDemocraticParty;
            $totalPoliticalStatusRows[$announcementId]['no_democratic_party']       = $totalNoDemocraticParty;
            $totalPoliticalStatusRows[$announcementId]['league_member']             = $totalLeagueMember;
            $totalPoliticalStatusRows[$announcementId]['masses']                    = $totalMasses;
            $totalPoliticalStatusRows[$announcementId]['probationary_party_member'] = $totalProbationaryPartyMember;
            $totalPoliticalStatusRows[$announcementId]['guest']                     = $totalPsGuest;
            // 日
            $dailyPoliticalStatusRows[$dailyKey]['announcement_id']           = $announcementId;
            $dailyPoliticalStatusRows[$dailyKey]['add_date']                  = $addDate;
            $dailyPoliticalStatusRows[$dailyKey]['party_member']              = $dailyPartyMember;
            $dailyPoliticalStatusRows[$dailyKey]['democratic_party']          = $dailyDemocraticParty;
            $dailyPoliticalStatusRows[$dailyKey]['no_democratic_party']       = $dailyNoDemocraticParty;
            $dailyPoliticalStatusRows[$dailyKey]['league_member']             = $dailyLeagueMember;
            $dailyPoliticalStatusRows[$dailyKey]['masses']                    = $dailyMasses;
            $dailyPoliticalStatusRows[$dailyKey]['probationary_party_member'] = $dailyProbationaryPartyMember;
            $dailyPoliticalStatusRows[$dailyKey]['guest']                     = $dailyPsGuest;

            // 年龄
            $totalRange_1_17    = $totalAgeRows[$announcementId]['range_1_17'] ?? 0;
            $dailyRange_1_17    = $dailyAgeRows[$dailyKey]['range_1_17'] ?? 0;
            $totalRange_18_25   = $totalAgeRows[$announcementId]['range_18_25'] ?? 0;
            $dailyRange_18_25   = $dailyAgeRows[$dailyKey]['range_18_25'] ?? 0;
            $totalRange_26_30   = $totalAgeRows[$announcementId]['range_26_30'] ?? 0;
            $dailyRange_26_30   = $dailyAgeRows[$dailyKey]['range_26_30'] ?? 0;
            $totalRange_31_35   = $totalAgeRows[$announcementId]['range_31_35'] ?? 0;
            $dailyRange_31_35   = $dailyAgeRows[$dailyKey]['range_31_35'] ?? 0;
            $totalRange_36_40   = $totalAgeRows[$announcementId]['range_36_40'] ?? 0;
            $dailyRange_36_40   = $dailyAgeRows[$dailyKey]['range_36_40'] ?? 0;
            $totalRange_41      = $totalAgeRows[$announcementId]['range_41'] ?? 0;
            $dailyRange_41      = $dailyAgeRows[$dailyKey]['range_41'] ?? 0;
            $totalAgeRangeGuest = $totalAgeRows[$announcementId]['range_guest'] ?? 0;
            $dailyAgeRangeGuest = $dailyAgeRows[$dailyKey]['range_guest'] ?? 0;

            $age = $resumeData[$resumeId]['age'] ?? 0;
            switch (true) {
                case $age >= 1 && $age <= 17:
                    $totalRange_1_17 += 1;
                    $dailyRange_1_17 += 1;
                    break;
                case $age >= 18 && $age <= 25:
                    $totalRange_18_25 += 1;
                    $dailyRange_18_25 += 1;
                    break;
                case $age >= 26 && $age <= 30:
                    $totalRange_26_30 += 1;
                    $dailyRange_26_30 += 1;
                    break;
                case $age >= 31 && $age <= 35:
                    $totalRange_31_35 += 1;
                    $dailyRange_31_35 += 1;
                    break;
                case $age >= 36 && $age <= 40:
                    $totalRange_36_40 += 1;
                    $dailyRange_36_40 += 1;
                    break;
                case $age >= 41:
                    $totalRange_41 += 1;
                    $dailyRange_41 += 1;
                    break;
                default:
                    $totalAgeRangeGuest += 1;
                    $dailyAgeRangeGuest += 1;
                    break;
            }
            // 总
            $totalAgeRows[$announcementId]['announcement_id'] = $announcementId;
            $totalAgeRows[$announcementId]['range_1_17']      = $totalRange_1_17;
            $totalAgeRows[$announcementId]['range_18_25']     = $totalRange_18_25;
            $totalAgeRows[$announcementId]['range_26_30']     = $totalRange_26_30;
            $totalAgeRows[$announcementId]['range_31_35']     = $totalRange_31_35;
            $totalAgeRows[$announcementId]['range_36_40']     = $totalRange_36_40;
            $totalAgeRows[$announcementId]['range_41']        = $totalRange_41;
            $totalAgeRows[$announcementId]['range_guest']     = $totalAgeRangeGuest;
            // 日
            $dailyAgeRows[$dailyKey]['announcement_id'] = $announcementId;
            $dailyAgeRows[$dailyKey]['add_date']        = $addDate;
            $dailyAgeRows[$dailyKey]['range_1_17']      = $dailyRange_1_17;
            $dailyAgeRows[$dailyKey]['range_18_25']     = $dailyRange_18_25;
            $dailyAgeRows[$dailyKey]['range_26_30']     = $dailyRange_26_30;
            $dailyAgeRows[$dailyKey]['range_31_35']     = $dailyRange_31_35;
            $dailyAgeRows[$dailyKey]['range_36_40']     = $dailyRange_36_40;
            $dailyAgeRows[$dailyKey]['range_41']        = $dailyRange_41;
            $dailyAgeRows[$dailyKey]['range_guest']     = $dailyAgeRangeGuest;

            // 职称(一级)
            $totalTitleHigh     = $totalTitleRows[$announcementId]['title_high'] ?? 0;
            $dailyTitleHigh     = $dailyTitleRows[$dailyKey]['title_high'] ?? 0;
            $totalTitleViceHigh = $totalTitleRows[$announcementId]['title_vice_high'] ?? 0;
            $dailyTitleViceHigh = $dailyTitleRows[$dailyKey]['title_vice_high'] ?? 0;
            $totalTitleMiddle   = $totalTitleRows[$announcementId]['title_middle'] ?? 0;
            $dailyTitleMiddle   = $dailyTitleRows[$dailyKey]['title_middle'] ?? 0;
            $totalTitlePrimary  = $totalTitleRows[$announcementId]['title_primary'] ?? 0;
            $dailyTitlePrimary  = $dailyTitleRows[$dailyKey]['title_primary'] ?? 0;
            $totalTitleGuest    = $totalTitleRows[$announcementId]['title_guest'] ?? 0;
            $dailyTitleGuest    = $dailyTitleRows[$dailyKey]['title_guest'] ?? 0;

            $titleIds       = $resumeData[$resumeId]['title_id'] ?? 0;
            $titleIdsArr    = explode(',', $titleIds);
            $faTitleCodeTmp = [];
            foreach ($titleIdsArr as $titleId) {
                $faTitleCode = $titleDictionary[$titleId]['parent_code'] ?? 0;
                if (in_array($faTitleCode, $faTitleCodeTmp)) { // 去重
                    continue;
                } else {
                    $faTitleCodeTmp[] = $faTitleCode;
                }
                switch ($faTitleCode) {
                    case BaseDictionary::TITLE_HIGH_ID:
                        $totalTitleHigh += 1;
                        $dailyTitleHigh += 1;
                        break;
                    case BaseDictionary::TITLE_VICE_HIGH_ID:
                        $totalTitleViceHigh += 1;
                        $dailyTitleViceHigh += 1;
                        break;
                    case BaseDictionary::TITLE_MIDDLE_ID:
                        $totalTitleMiddle += 1;
                        $dailyTitleMiddle += 1;
                        break;
                    case BaseDictionary::TITLE_PRIMARY_ID:
                        $totalTitlePrimary += 1;
                        $dailyTitlePrimary += 1;
                        break;
                    default: // 游客或者缺失
                        $totalTitleGuest += 1;
                        $dailyTitleGuest += 1;
                        break;
                }
            }
            // 总(一级)
            $totalTitleRows[$announcementId]['announcement_id'] = $announcementId;
            $totalTitleRows[$announcementId]['title_high']      = $totalTitleHigh;
            $totalTitleRows[$announcementId]['title_vice_high'] = $totalTitleViceHigh;
            $totalTitleRows[$announcementId]['title_middle']    = $totalTitleMiddle;
            $totalTitleRows[$announcementId]['title_primary']   = $totalTitlePrimary;
            $totalTitleRows[$announcementId]['title_guest']     = $totalTitleGuest;
            // 日(一级)
            $dailyTitleRows[$dailyKey]['announcement_id'] = $announcementId;
            $dailyTitleRows[$dailyKey]['add_date']        = $addDate;
            $dailyTitleRows[$dailyKey]['title_high']      = $dailyTitleHigh;
            $dailyTitleRows[$dailyKey]['title_vice_high'] = $dailyTitleViceHigh;
            $dailyTitleRows[$dailyKey]['title_middle']    = $dailyTitleMiddle;
            $dailyTitleRows[$dailyKey]['title_primary']   = $dailyTitlePrimary;
            $dailyTitleRows[$dailyKey]['title_guest']     = $dailyTitleGuest;

            // 职称(二级)
            foreach ($titleIdsArr as $titleId) {
                $faTitleCode = $titleDictionary[$titleId]['parent_code'] ?? 0;
                if ($faTitleCode) {
                    $titleLevel_1 = $faTitleCode;
                    $titleLevel_2 = $titleId;
                } else {// 游客或者缺失
                    $titleLevel_1 = $titleLevel_2 = 0;
                }
                $totalSubtitleKey   = "{$announcementId}_{$titleLevel_1}_{$titleLevel_2}";
                $dailySubtitleKey   = "{$dailyKey}_{$titleLevel_1}_{$titleLevel_2}";
                $totalSubtitleClick = ($totalSubtitleRows[$totalSubtitleKey]['total'] ?? 0) + 1;
                $dailySubtitleClick = ($dailySubtitleRows[$dailySubtitleKey]['total'] ?? 0) + 1;
                // 总
                $totalSubtitleRows[$totalSubtitleKey]['announcement_id'] = $announcementId;
                $totalSubtitleRows[$totalSubtitleKey]['title_level_1']   = $titleLevel_1;
                $totalSubtitleRows[$totalSubtitleKey]['title_level_2']   = $titleLevel_2;
                $totalSubtitleRows[$totalSubtitleKey]['total']           = $totalSubtitleClick;
                // 日
                $dailySubtitleRows[$dailySubtitleKey]['announcement_id'] = $announcementId;
                $dailySubtitleRows[$dailySubtitleKey]['add_date']        = $addDate;
                $dailySubtitleRows[$dailySubtitleKey]['title_level_1']   = $titleLevel_1;
                $dailySubtitleRows[$dailySubtitleKey]['title_level_2']   = $titleLevel_2;
                $dailySubtitleRows[$dailySubtitleKey]['total']           = $dailySubtitleClick;
            }

            // 地区
            $ip           = long2ip($log['ip']);
            $areaInfo     = IpHelper::getArea($ip);
            $provinceName = $areaInfo['province'] ?: '';
            $cityName     = $areaInfo['city'] ?: '';
            $cityName     = $cityName ?: $provinceName;
            // 省份
            $matchProvinceArr = $this->searchArray($areaProvinceDictionary, $provinceName);
            if ($matchProvinceArr) {
                $areaProvinceId = $matchProvinceArr['id'];
            } else { // 缺失
                $areaProvinceId = 0;
            }
            // 省份
            $totalProvinceKey   = "{$announcementId}_{$areaProvinceId}";
            $dailyProvinceKey   = "{$dailyKey}_{$areaProvinceId}";
            $totalProvinceClick = ($totalProvinceRows[$totalProvinceKey]['total'] ?? 0) + 1;
            $dailyProvinceClick = ($dailyProvinceRows[$dailyProvinceKey]['total'] ?? 0) + 1;
            // 总
            $totalProvinceRows[$totalProvinceKey]['announcement_id']  = $announcementId;
            $totalProvinceRows[$totalProvinceKey]['area_province_id'] = $areaProvinceId;
            $totalProvinceRows[$totalProvinceKey]['total']            = $totalProvinceClick;
            // 日
            $dailyProvinceRows[$dailyProvinceKey]['announcement_id']  = $announcementId;
            $dailyProvinceRows[$dailyProvinceKey]['add_date']         = $addDate;
            $dailyProvinceRows[$dailyProvinceKey]['area_province_id'] = $areaProvinceId;
            $dailyProvinceRows[$dailyProvinceKey]['total']            = $dailyProvinceClick;

            // 城市
            $matchCityArr = $this->searchArray($areaCityDictionary, $cityName);
            if ($matchCityArr) {
                $areaProvinceId = $matchCityArr['parent_id'];
                $areaCityId     = $matchCityArr['id'];
            } else { // 缺失
                $areaProvinceId = $areaCityId = 0;
            }
            // 城市
            $totalCityKey   = "{$announcementId}_{$areaProvinceId}_{$areaCityId}";
            $dailyCityKey   = "{$dailyKey}_{$areaProvinceId}_{$areaCityId}";
            $totalCityClick = ($totalCityRows[$totalCityKey]['total'] ?? 0) + 1;
            $dailyCityClick = ($dailyCityRows[$dailyCityKey]['total'] ?? 0) + 1;
            // 总
            $totalCityRows[$totalCityKey]['announcement_id']  = $announcementId;
            $totalCityRows[$totalCityKey]['area_province_id'] = $areaProvinceId;
            $totalCityRows[$totalCityKey]['area_city_id']     = $areaCityId;
            $totalCityRows[$totalCityKey]['total']            = $totalCityClick;
            // 日
            $dailyCityRows[$dailyCityKey]['announcement_id']  = $announcementId;
            $dailyCityRows[$dailyCityKey]['add_date']         = $addDate;
            $dailyCityRows[$dailyCityKey]['area_province_id'] = $areaProvinceId;
            $dailyCityRows[$dailyCityKey]['area_city_id']     = $areaCityId;
            $dailyCityRows[$dailyCityKey]['total']            = $dailyCityClick;

            // 栏目
            $columnLevel = $homeColumn[$homeColumnId]['level'] ?? 0;
            if ($columnLevel == BaseHomeColumn::LEVEL_FIRST) { // 一级
                $columnId_1 = $homeColumnId;
                $columnId_2 = 0;
            } elseif ($columnLevel == BaseHomeColumn::LEVEL_SECOND) {
                // 找上级id
                $faColumnId = $homeColumn[$homeColumnId]['parent_id'] ?? 0;
                $columnId_1 = $faColumnId;
                $columnId_2 = $homeColumnId;
            } else { // 缺失
                $columnId_1 = $columnId_2 = 0;
            }
            $totalColumnKey   = "{$announcementId}_{$columnId_1}_{$columnId_2}";
            $dailyColumnKey   = "{$dailyKey}_{$columnId_1}_{$columnId_2}";
            $totalColumnClick = ($totalColumnRows[$totalColumnKey]['total'] ?? 0) + 1;
            $dailyColumnClick = ($dailyColumnRows[$dailyColumnKey]['total'] ?? 0) + 1;
            // 总
            $totalColumnRows[$totalColumnKey]['announcement_id'] = $announcementId;
            $totalColumnRows[$totalColumnKey]['column_id_1']     = $columnId_1;
            $totalColumnRows[$totalColumnKey]['column_id_2']     = $columnId_2;
            $totalColumnRows[$totalColumnKey]['total']           = $totalColumnClick;
            // 日
            $dailyColumnRows[$dailyColumnKey]['announcement_id'] = $announcementId;
            $dailyColumnRows[$dailyColumnKey]['add_date']        = $addDate;
            $dailyColumnRows[$dailyColumnKey]['column_id_1']     = $columnId_1;
            $dailyColumnRows[$dailyColumnKey]['column_id_2']     = $columnId_2;
            $dailyColumnRows[$dailyColumnKey]['total']           = $dailyColumnClick;
        }

        // bb($logData);
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $this->executeDaily($dailyClickRows ?? [], BaseAnnouncementClickTotalDaily::instance());

            $this->executeTotal($totalEducationRows ?? [], BaseAnnouncementClickEducationTotal::instance());
            $this->executeDaily($dailyEducationRows ?? [], BaseAnnouncementClickEducationTotalDaily::instance());

            $this->executeTotal($totalWorkExperienceRows ?? [], BaseAnnouncementClickWorkExperienceTotal::instance());
            $this->executeDaily($dailyWorkExperienceRows ?? [],
                BaseAnnouncementClickWorkExperienceTotalDaily::instance());

            $this->executeTotal($totalPoliticalStatusRows ?? [], BaseAnnouncementClickPoliticalStatusTotal::instance());
            $this->executeDaily($dailyPoliticalStatusRows ?? [],
                BaseAnnouncementClickPoliticalStatusTotalDaily::instance());

            $this->executeTotal($totalAgeRows ?? [], BaseAnnouncementClickAgeTotal::instance());
            $this->executeDaily($dailyAgeRows ?? [], BaseAnnouncementClickAgeTotalDaily::instance());

            $this->executeTotal($totalTitleRows ?? [], BaseAnnouncementClickTitleTotal::instance());
            $this->executeDaily($dailyTitleRows ?? [], BaseAnnouncementClickTitleTotalDaily::instance());

            $this->executeTotal($totalSubtitleRows ?? [], BaseAnnouncementClickSubtitleTotal::instance(), [
                'title_level_1',
                'title_level_2',
            ]);
            $this->executeDaily($dailySubtitleRows ?? [], BaseAnnouncementClickSubtitleTotalDaily::instance(), [
                'title_level_1',
                'title_level_2',
            ]);

            $this->executeTotal($totalProvinceRows ?? [], BaseAnnouncementClickAreaProvinceTotal::instance(),
                ['area_province_id']);
            $this->executeDaily($dailyProvinceRows ?? [], BaseAnnouncementClickAreaProvinceTotalDaily::instance(),
                ['area_province_id']);

            $this->executeTotal($totalCityRows ?? [], BaseAnnouncementClickAreaCityTotal::instance(), [
                'area_province_id',
                'area_city_id',
            ]);
            $this->executeDaily($dailyCityRows ?? [], BaseAnnouncementClickAreaCityTotalDaily::instance(), [
                'area_province_id',
                'area_city_id',
            ]);

            $this->executeTotal($totalColumnRows ?? [], BaseAnnouncementClickColumnTotal::instance(), [
                'column_id_1',
                'column_id_2',
            ]);
            $this->executeDaily($dailyColumnRows ?? [], BaseAnnouncementClickColumnTotalDaily::instance(), [
                'column_id_1',
                'column_id_2',
            ]);

            // 修改配置
            $this->updateConfig(BaseAnnouncementClickUpdateConfig::STATUS_EXECUTE_COMPLETE);

            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            self::log('异常回滚:' . $e->getMessage());
            $this->updateConfig(BaseAnnouncementClickUpdateConfig::STATUS_EXECUTE_INCOMPLETE, $e->getMessage());
        }
    }

    /**
     * 修改配置
     */
    private function updateConfig($status, $failMsg = '')
    {
        $configRow = BaseAnnouncementClickUpdateConfig::findOne(['class_name' => $this->_className]);
        if (!$this->_isHistory) { // 实时
            $configRow->real_execute_status = $status;
            $configRow->real_fail_msg       = $failMsg;
            if ($status == BaseAnnouncementClickUpdateConfig::STATUS_EXECUTE_COMPLETE) {
                $configRow->end_id = end($this->_realLogData)['id'];
            }
        } else { // 历史
            $configRow->history_execute_status = $status;
            $configRow->history_fail_msg       = $failMsg;
            if ($status == BaseAnnouncementClickUpdateConfig::STATUS_EXECUTE_COMPLETE) {
                $configRow->start_id = end($this->_historyLogData)['id'];
            }
        }
        if (!$configRow->save()) {
            throw new \Exception($configRow->getFirstErrorsMessage());
        }
    }

    /**
     * 执行
     * 总
     * fields 注意数组元素顺序
     */
    private function executeTotal($curRows, $model, $fields = [])
    {
        return;
        if (empty($curRows)) {
            return;
        }
        // 默认字段(不要随便改)
        $default = ['announcement_id'];
        // 检索字段
        $fields = array_merge($default, $fields);
        // 当前时间
        $updateTime = CUR_DATETIME;
        // 当前数据key
        $curIds = array_keys($curRows);
        // 循环where条件
        foreach ($fields as $field) {
            $where[$field] = array_unique(array_column($curRows, $field));
        }
        // 已统计数据
        $statsRows = $model::find()
            ->where($where)
            ->asArray()
            ->all();
        // 待更新id
        $updateIds = [];
        if ($statsRows) {
            // 待更新数据
            $updateRows = [];
            foreach ($statsRows as $stats) {
                // 关联id
                $linkId = [];
                // 循环拼接
                foreach ($fields as $field) {
                    $linkId[] = $stats[$field];
                }
                $linkId      = implode('_', $linkId);
                $updateIds[] = $linkId;
                // 累计值
                foreach ($stats as $k => $v) {
                    // 检索字段不累计
                    if (in_array($k, $fields)) {
                        continue;
                    }
                    if (!isset($curRows[$linkId][$k])) {
                        // id
                        $updateRow[$k] = $v;
                    } else {
                        $updateRow[$k] = $curRows[$linkId][$k] + $v;
                    }
                    // 修改更新时间
                    $updateRow['update_time'] = $updateTime;
                }
                // 加入批量更新
                $updateRows[] = $updateRow;
            }
            try {
                $res = $this->batchUpdate('id', $updateRows, $model::tableName());
                // self::log('批量更新成功-' . $model::tableName() . '-更新ids:' . json_encode($updateIds));
                self::log('批量更新成功-' . $model::tableName() . '-条数:' . $res);
            } catch (\Exception $e) {
                throw new \Exception('批量更新异常-' . $model::tableName() . ':' . $e->getMessage());
            }
        }
        // 待新增id(当前数据与已统计数据的差集)
        $insertIds = array_diff($curIds, $updateIds);
        if ($insertIds) {
            // 当前数据按照新增数据id取交集
            $insertRows = array_intersect_key($curRows, array_flip($insertIds));
            // 批量写入
            $insertKeys = array_keys(current($insertRows));
            try {
                $res = Yii::$app->db->createCommand()
                    ->batchInsert($model::tableName(), $insertKeys, $insertRows)
                    ->execute();
                // self::log('批量新增成功-' . $model::tableName() . '-新增ids:' . json_encode($insertIds));
                self::log('批量新增成功-' . $model::tableName() . '-条数:' . $res);
            } catch (\Exception $e) {
                throw new \Exception('批量新增异常-' . $model::tableName() . ':' . $e->getMessage());
            }
        }
    }

    /**
     * 执行
     * 日
     * fields 注意数组元素顺序
     */
    private function executeDaily($curRows, $model, $fields = [])
    {
        if (empty($curRows)) {
            return;
        }
        // 默认字段(不要随便改)
        $default = [
            'announcement_id',
            'add_date',
        ];
        // 检索字段
        $fields = array_merge($default, $fields);
        // 当前数据key
        $curIds = array_keys($curRows);
        // 循环where条件
        foreach ($fields as $field) {
            $where[$field] = array_unique(array_column($curRows, $field));
        }
        // 已统计数据
        $statsRows = $model::find()
            ->where($where)
            ->asArray()
            ->all();
        // 待更新id
        $updateIds = [];
        if ($statsRows) {
            // 待更新数据
            $updateRows = [];
            foreach ($statsRows as &$stats) {
                // 关联id
                $linkId = [];
                // 循环拼接
                foreach ($fields as $field) {
                    $linkId[] = $stats[$field];
                }
                $linkId      = implode('_', $linkId);
                $updateIds[] = $linkId;
                // 累计值
                foreach ($stats as $k => $v) {
                    // 检索字段不累计
                    if (in_array($k, $fields)) {
                        continue;
                    }
                    if (!isset($curRows[$linkId][$k])) {
                        // id
                        $updateRow[$k] = $v;
                    } else {
                        $updateRow[$k] = $curRows[$linkId][$k] + $v;
                    }
                }
                // 加入批量更新
                $updateRows[] = $updateRow;
            }
            try {
                $res = $this->batchUpdate('id', $updateRows, $model::tableName());
                // self::log('批量更新成功-' . $model::tableName() . '-更新ids:' . json_encode($updateIds));
                self::log('批量更新成功-' . $model::tableName() . '-条数:' . $res);
            } catch (\Exception $e) {
                throw new \Exception('批量更新异常-' . $model::tableName() . ':' . $e->getMessage());
            }
        }
        // 待新增id(当前数据与已统计数据的差集)
        $insertIds = array_diff($curIds, $updateIds);
        if ($insertIds) {
            // 当前数据按照新增数据id取交集
            $insertRows = array_intersect_key($curRows, array_flip($insertIds));
            // 批量写入
            $insertKeys = array_keys(current($insertRows));
            try {
                $res = Yii::$app->db->createCommand()
                    ->batchInsert($model::tableName(), $insertKeys, $insertRows)
                    ->execute();
                // self::log('批量新增成功-' . $model::tableName() . '-新增ids:' . json_encode($insertIds));
                self::log('批量新增成功-' . $model::tableName() . '-条数:' . $res);
            } catch (\Exception $e) {
                throw new \Exception('批量新增异常-' . $model::tableName() . ':' . $e->getMessage());
            }
        }
    }

    /**
     * 在数组中模糊搜索给定的值
     */
    private function searchArray($data, $keyword, $queryField = 'full_name')
    {
        foreach ($data as $values) {
            if (strstr($values[$queryField], $keyword) !== false) {
                return $values;
            }
        }

        return [];
    }
}