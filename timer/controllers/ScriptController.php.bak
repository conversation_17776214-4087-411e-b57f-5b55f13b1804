<?php

namespace timer\controllers;

use admin\models\Area;
use admin\models\Article;
use admin\models\HomePosition;
use admin\models\RuleAnnouncement;
use admin\models\RuleCompany;
use admin\models\RuleJob;
use admin\models\Showcase;
use common\base\BaseActiveRecord;
use Codeception\Lib\Driver\Db;
use common\base\models\BaseActivityForm;
use common\base\models\BaseActivityFormRegistrationForm;
use common\base\models\BaseAdmin;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseArticleClickLog;
use common\base\models\BaseChatMessage;
use common\base\models\BaseChatRoomSession;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyContact;
use common\base\models\BaseCompanyDeliveryChangeLog;
use common\base\models\BaseCompanyMemberConfig;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyMemberMessageConfig;
use common\base\models\BaseCompanyPackageChangeLog;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseCompanyPackageSystemConfig;
use common\base\models\BaseCompanyResumeLibrary;
use common\base\models\BaseCompanyWelfareLabelRelationship;
use common\base\models\BaseDictionary;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomePosition;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyHandleLog;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobApplyRecordExtra;
use common\base\models\BaseJobClickLog;
use common\base\models\BaseJobContact;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseMajor;
use common\base\models\BaseMajorAiDictionary;
use common\base\models\BaseMember;
use common\base\models\BaseMemberActionLog;
use common\base\models\BaseMemberMessage;
use common\base\models\BaseOffSiteJobApply;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAnnouncementFootprint;
use common\base\models\BaseResumeAttachment;
use common\base\models\BaseResumeComplete;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeEquity;
use common\base\models\BaseResumeEquityActionRecord;
use common\base\models\BaseResumeEquityPackage;
use common\base\models\BaseResumeEquityPackageCategorySetting;
use common\base\models\BaseResumeEquityPackageDetail;
use common\base\models\BaseResumeEquityPackageRelationSetting;
use common\base\models\BaseResumeEquityPackageSetting;
use common\base\models\BaseResumeJobFootprint;
use common\base\models\BaseResumeLibraryCollect;
use common\base\models\BaseResumeLibraryInviteLog;
use common\base\models\BaseResumeOrder;
use common\base\models\BaseResumeOrderSnapshot;
use common\base\models\BaseResumeResearchDirection;
use common\base\models\BaseResumeResearchProject;
use common\base\models\BaseResumeStatData;
use common\base\models\BaseResumeWork;
use common\base\models\BaseShowcase;
use common\base\models\BaseShowcasePacking;
use common\base\models\BaseShowcasePackingRelationship;
use common\base\models\BaseWelfareLabel;
use common\helpers\PingYinHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\helpers\UUIDHelper;
use common\helpers\ValidateHelper;
use common\libs\BadWordCheck;
use common\libs\Cache;
use common\libs\ColumnAuto\JobAutoClassify;
use common\libs\EmailQueue;
use common\models\MajorAiDictionaryRelationship;
use common\models\ResumeLibraryDownloadLog;
use common\models\ShowcasePackingRelationship;
use common\service\match\MatchCompleteService;
use common\service\resume\ResumeAfterService;
use common\service\stat\StateApplication;
use common\service\stat\Tmp;
use GatewayWorker\Lib\Gateway;
use queue\Producer;
use Yii;
use yii\base\Exception;

/**
 *
 */
class ScriptControllerbak extends BaseTimerController
{

    public function actionCheckIllegalCharacter()
    {
        $path = Yii::getAlias('@frontendPc') . '/web/uploads';
        $path = '/www/wwwroot/gaoxiaojob.com/web/zhaopin/zhuanti';
        $rs   = $this->myScandir($path);
        var_dump($rs);
    }

    private function myScandir($dir)
    {
        $files = [];
        $model = new BadWordCheck();
        if (is_dir($dir)) {
            if ($handle = opendir($dir)) {
                while (($file = readdir($handle)) !== false) {
                    if ($file != '.' && $file != "..") {
                        if (is_dir($dir . "/" . $file)) {
                            $files[$file] = $this->myScandir($dir . "/" . $file);
                        } else {
                            $path = $dir . "/" . $file;
                            // 判断如果是html文件
                            if (strpos($file, '.html') !== false) {
                                $files[] = $path;
                                // $r = file_get_contents($dir . "/" . $file);
                                // // 去html标签
                                // $text = strip_tags($r);
                                //
                                // if ($model->check($text)) {
                                //     self::log($path);
                                //     self::log($model->getErrorMessage());
                                //     exit;
                                // }
                            }
                        }
                    }
                }
            }
        }
        closedir($handle);

        return $files;
    }

    public function actionZeroTimeInAnnouncement()
    {
        // 找到公告status=1并且没有发布时间的公告
        $announcement = Article::find()
            ->select('id,first_release_time,add_time,release_time,refresh_time')
            ->where(['status' => 1])
            ->andWhere(['refresh_time' => TimeHelper::ZERO_TIME])
            ->asArray()
            ->all();

        self::log(var_export($announcement, true));

        foreach ($announcement as $item) {
            $model = Article::findOne($item['id']);
            if ($item['release_time'] != TimeHelper::ZERO_TIME) {
                $model->refresh_time = $item['release_time'];
                $model->save();
                continue;
            }

            if ($item['first_release_time'] != TimeHelper::ZERO_TIME) {
                $model->refresh_time = $item['first_release_time'];
                $model->save();
                continue;
            }

            if ($item['add_time'] != TimeHelper::ZERO_TIME) {
                $model->refresh_time = $item['add_time'];
                $model->save();
            }
        }
    }

    public function actionUpdateColumnDictionary()
    {
        try {
            $columns = BaseHomeColumn::find()
                ->select('id,name')
                ->asArray()
                ->all();

            foreach ($columns as $column) {
                $name = $column['name'];

                // 找到地区字典里面是否有
                $area = Area::find()
                    ->select('id,name,level')
                    ->where([
                        'name'  => $name,
                        'level' => [
                            1,
                            2,
                        ],
                    ])
                    ->asArray()
                    ->all();

                // $major = Major::find()
                //     ->select('id,name,level')
                //     ->where([
                //         'name'  => $name,
                //         'level' => [
                //             1,
                //             2,
                //         ],
                //     ])
                //     ->asArray()
                //     ->all();

                // if (count($area) > 1) {
                //     aa($name);
                // }

                if (count($area) > 1) {
                    aa($area);
                }
            }
        } catch (\Exception $e) {
            bb($e);
        }
    }

    // php ./timer_yii script/update-job-apply-address
    public function actionUpdateJobApplyAddress()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $data = json_decode(Cache::get('tmp_excel'));

            foreach ($data as $item) {
                // 去除左右空格
                $item         = array_map('trim', $item);
                $jobId        = trim($item[0]);
                $jobName      = trim($item[1]);
                $applyType    = trim($item[7]);
                $applyAddress = trim($item[8]);
                $jobStatus    = trim($item[2]);
                if ($jobStatus != '在线') {
                    continue;
                }
                // 找到职位
                $job = BaseJob::findOne(['id' => $jobId]);

                if (!$job) {
                    throw new Exception("找不到职位:$jobId");
                }

                if ($job->name != $jobName) {
                    aa("职位匹配错误:$jobId");
                }

                if (!$applyType) {
                    continue;
                }

                self::log("找到了职位信息:$job->id  ---- $job->name --- $job->apply_type");

                switch ($applyType) {
                    case '电子邮件':
                    case '电子邮箱':
                        // 检查邮箱合法性
                        if (self::isEmail($applyAddress)) {
                            $job->apply_type    = (string)BaseJob::ATTRIBUTE_APPLY_EMAIL;
                            $job->apply_address = $applyAddress;
                            if ($job->save()) {
                                self::log("成功更新:$job->id -- 更新数据为投递方式:$applyType ---  地址:$job->apply_address");
                            } else {
                                throw new Exception("失败更新:$job->id ,原因 {$job->getFirstErrorsMessage()}");
                            }
                        } else {
                            throw new Exception("邮箱格式不正确 --- :$jobId --- $applyAddress");
                        }
                        break;
                    case '其他':
                        $job->apply_type = (string)BaseJob::ATTRIBUTE_APPLY_OTHER;
                        if ($job->save()) {
                            self::log("成功更新:$job->id -- 更新数据为投递方式:$applyType ---  地址:$job->apply_address");
                        } else {
                            // 抛出异常
                            throw new Exception("失败更新:$job->id ,原因 {$job->getFirstErrorsMessage()}");
                        }
                        break;

                    case '网上系统':
                        $job->apply_type    = (string)BaseJob::ATTRIBUTE_APPLY_ONLINE;
                        $job->apply_address = $applyAddress;
                        if ($job->save()) {
                            self::log("成功更新:$job->id -- 更新数据为投递方式:$applyType ---  地址:$job->apply_address");
                        } else {
                            // 抛出异常
                            throw new Exception("失败更新:$job->id ,原因 {$job->getFirstErrorsMessage()}");
                        }
                        break;
                    case '现场报名':
                        $job->apply_type    = (string)BaseJob::ATTRIBUTE_APPLY_SCENE;
                        $job->apply_address = $applyAddress;
                        if ($job->save()) {
                            self::log("成功更新:$job->id -- 更新数据为投递方式:$applyType ---  地址:$job->apply_address");
                        } else {
                            // 抛出异常
                            throw new Exception("失败更新:$job->id ,原因 {$job->getFirstErrorsMessage()}");
                        }
                        break;
                    case '电话报名':
                        $job->apply_type    = (string)BaseJob::ATTRIBUTE_APPLY_TELEPHONE;
                        $job->apply_address = $applyAddress;
                        if ($job->save()) {
                            self::log("成功更新:$job->id -- 更新数据为投递方式:$applyType ---  地址:$job->apply_address");
                        } else {
                            // 抛出异常
                            throw new Exception("失败更新:$job->id ,原因 {$job->getFirstErrorsMessage()}");
                        }
                        break;
                    default:

                        throw new Exception("投递方式不正确:$jobId");
                }
            }

            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();

            bb($e->getMessage());
        }
    }

    // 主要是初始化一下字典和学科的关联关系,只执行一遍
    // ./timer_yii script/update-major-ai-relationship
    public function actionUpdateMajorAiRelationship()
    {
        // 首先找到全部字典和学科匹配
        $list = BaseMajorAiDictionary::find()
            ->select('a.id as aid,b.id bid,a.name')
            ->alias('a')
            ->innerJoin(['b' => BaseMajor::tableName()], 'a.name=b.name')
            ->where(['b.level' => 2])
            ->asArray()
            ->all();

        // 循环写到关系表里面
        $transaction = Yii::$app->db->beginTransaction();
        try {
            foreach ($list as $item) {
                $rel                = new MajorAiDictionaryRelationship();
                $rel->major_id      = $item['bid'];
                $rel->dictionary_id = $item['aid'];
                $rel->save();
            }
            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            $message = $e->getMessage();
            bb($message);
        }
    }

    public function actionUpdateMajorAiRelationshipLevel3()
    {
        // 首先找到全部字典和学科匹配
        $list = BaseMajor::find()
            ->select('id,name,parent_id')
            ->where(['level' => 3])
            ->asArray()
            ->all();

        foreach ($list as $item) {
            // 找到字典
            $dictionary = BaseMajorAiDictionary::find()
                ->select('id')
                ->where(['name' => $item['name']])
                ->asArray()
                ->one();
            if ($dictionary) {
                continue;
            }

            $dictionary       = new BaseMajorAiDictionary();
            $dictionary->name = $item['name'];
            $dictionary->save();

            $rel                = new MajorAiDictionaryRelationship();
            $rel->major_id      = $item['parent_id'];
            $rel->dictionary_id = $dictionary->id;
            $rel->save();
        }
        // 循环写到关系表里面
        // $transaction = Yii::$app->db->beginTransaction();
        // try {
        //     foreach ($list as $item) {
        //         $rel                = new MajorAiDictionaryRelationship();
        //         $rel->major_id      = $item['bid'];
        //         $rel->dictionary_id = $item['aid'];
        //         $rel->save();
        //     }
        //     $transaction->commit();
        // } catch (\Exception $e) {
        //     $transaction->rollBack();
        //     $message = $e->getMessage();
        //     bb($message);
        // }
    }

    /**
     * ./timer_yii script/delete-repeat-announcement-attribute
     */
    public function actionDeleteRepeatAnnouncementAttribute()
    {
        $sql = "SELECT CONCAT ( article_id, '-',type ) AS only_key,count( * ) AS total ,
	article_id,id,type
FROM
	article_attribute
GROUP BY
	only_key
	HAVING total > 1";

        $connection = Yii::$app->db;
        $list       = $connection->createCommand($sql)
            ->queryAll();

        $transaction = Yii::$app->db->beginTransaction();
        try {
            // 首先把数据给必须放一份
            foreach ($list as $item) {
                // 先检查一下是否真的有重复的
                $sql   = "SELECT * FROM article_attribute WHERE article_id = {$item['article_id']} AND type = {$item['type']}";
                $list2 = $connection->createCommand($sql)
                    ->queryAll();
                if (count($list2) < 2) {
                    bb($item);
                } else {
                    $model = BaseArticleAttribute::findOne($item['id']);
                    $data  = [
                        'article_id'  => $model->article_id,
                        'type'        => $model->type,
                        'add_time'    => $model->add_time,
                        'sort_time'   => $model->sort_time,
                        'expire_time' => $model->expire_time,
                    ];
                    BaseArticleAttribute::deleteAll([
                        'article_id' => $model->article_id,
                        'type'       => $model->type,
                    ]);
                    $model = new BaseArticleAttribute();
                    $model->load($data, '');
                    $model->save();
                }
                // $model = BaseArticleAttribute::findOne($item['id']);
                // aa($model->attributes);
                // bb($item);
            }
            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            $message = $e->getMessage();
        }
    }

    private static function isEmail($email)
    {
        // 转数组
        $emailArr = explode(',', $email);
        foreach ($emailArr as $item) {
            if ($item == '<EMAIL>') {
                return true;
            }
            if (!ValidateHelper::isEmail($item)) {
                return false;
            }
        }

        return true;
    }

    /**
     *  更新简历完成度
     *  PHP timer_yii script/update-resume-complete
     */
    public function actionUpdateResumeComplete()
    {
        //所有简历

        $list = BaseResume::find()
            ->select('id,member_id')
            ->asArray()
            ->all();

        foreach ($list as $item) {
            $complete        = BaseResume::updateComplete($item['member_id']);
            $model           = BaseResume::findOne($item['id']);
            $model->complete = $complete;
            if (!$model->save()) {
                bb($model->getFirstErrorsMessage());
            }
            self::log("{$item['member_id']}:{$complete}");
        }

        // foreach (BaseResume::find()
        //     ->batch(5000) as $val) {
        //     //处理500条
        //     foreach ($val as $item) {
        //         self::log($item->member_id . PHP_EOL);
        //         $memberId       = $item->member_id;
        //         $item->complete =
        //         $item->save();
        //     }
        //     //休眠1S
        // }
    }

    /**
     * @return void
     * @throws Exception
     *  hotfix/二级栏目_公告_资讯搜索页添加广告位
     *
     * PHP timer_yii script/add-job-and-announcement-ad
     */
    public function actionAddJobAndAnnouncementAd()
    {
        BaseHomePosition::carryColumnSecondHotToPosition();
        BaseHomePosition::carryColumnAnnouncementSearchToPosition();
    }

    /**
     * @return bool
     * @throws Exception
     * hotfix/0622_广告管理编辑关联位置保存问题
     * 将没有包的广告数据给个包(单个)
     * PHP timer_yii script/add-showcase-packing
     */
    public function actionAddShowcasePacking(): bool
    {
        $showcasePackingIds = BaseShowcasePackingRelationship::find()
            ->select('showcase_id')
            ->asArray()
            ->column();

        $checkShowcasePackingList = BaseShowcase::find()
            ->select('id,title')
            ->where([
                'not in',
                'id',
                $showcasePackingIds,
            ])
            ->andWhere([
                'status' => [
                    BaseShowcase::STATUS_ONLINE,
                    BaseShowcase::STATUS_OFFLINE,
                    BaseShowcase::STATUS_WAIT,
                ],
            ])
            ->asArray()
            ->all();

        // 找到没有包的广告
        self::log('没有包的广告数量:' . count($checkShowcasePackingList));

        foreach ($checkShowcasePackingList as $key => $item) {
            self::log('没有包的广告:' . $item['id']);
            $showcasePackingRelationship = BaseShowcasePackingRelationship::findOne(['showcase_id' => $item['id']]);
            if (!$showcasePackingRelationship) {
                $packModel         = new BaseShowcasePacking();
                $packModel->remark = $item['title'];
                if (!$packModel->save()) {
                    throw new Exception('打包失败');
                }
                $relModel                      = new ShowcasePackingRelationship();
                $relModel->showcase_id         = $item['id'];
                $relModel->showcase_packing_id = $packModel->id;
                if (!$relModel->save()) {
                    throw new Exception('打包关系失败');
                }
            }
        }

        return true;
    }

    /**
     * @return bool
     * @throws Exception
     * hotfix/104_单位职位公告资讯热门搜索广告位
     * PHP timer_yii script/add-hot-search-position
     */
    public function actionAddHotSearchPosition(): bool
    {
        $nameList = [
            [
                'number' => 'zhiweiliebiao_remensousuo',
                'name'   => '职位列表',
            ],
            [
                'number' => 'danweiliebiao_remensousuo',
                'name'   => '单位列表',
            ],
            [
                'number' => 'gonggao_remensousuo',
                'name'   => '公告',
            ],
            [
                'number' => 'zixun_remensousuo',
                'name'   => '资讯',
            ],
            [
                'number'        => 'gonggao_remensousuo_m',
                'platform_type' => BaseHomePosition::PLATFORM_H5,
                'name'          => '公告',
                'suffix'        => '_m',
            ],
            [
                'number'        => 'zixun_remensousuo_m',
                'platform_type' => BaseHomePosition::PLATFORM_H5,
                'name'          => '资讯',
                'suffix'        => '_m',
            ],
            [
                'number'        => 'zhiweiliebiao_remensousuo_m',
                'platform_type' => BaseHomePosition::PLATFORM_H5,
                'name'          => '职位列表',
                'suffix'        => '_m',
            ],
            [
                'number'        => 'danweiliebiao_remensousuo_m',
                'platform_type' => BaseHomePosition::PLATFORM_H5,
                'name'          => '单位列表',
                'suffix'        => '_m',
            ],
        ];

        $temp = [
            'add_time'     => CUR_DATETIME,
            'status'       => 1,
            'creator_type' => 1,
            'creator'      => 'admin',
            'creator_id'   => 1,
            'width'        => '0',
            'height'       => '0',
            'describe'     => '',
        ];

        $list = [];

        foreach ($nameList as $k => $v) {
            self::log('新增广告位:' . $v['number']);
            $temp['name']          = $v['name'] . '_热门搜索' . $v['suffix'];
            $temp['chinese_name']  = '热门搜索';
            $temp['platform_type'] = $v['platform_type'] ?: BaseHomePosition::PLATFORM_PC_COLUMN;
            $temp['number']        = $v['number'];
            $list[]                = $temp;
        }

        foreach ($list as $kk => $vv) {
            if (!BaseHomePosition::checkPositionByNumber($vv['number'])) {
                BaseHomePosition::createHomePosition($vv);
            }
        }

        return true;
    }

    /*
     * 更新单位简历库(主要是上线后补充那些用于投递申请的)
     * PHP timer_yii script/update-company-resume-apply-source
     */
    public function actionUpdateCompanyResumeApplySource()
    {
        try {
            // 找到所有在线简历对于某个单位的第一次投递
            $list = BaseJobApply::find()
                ->asArray()
                ->all();

            self::log('一共有投递数据:' . count($list));
            foreach ($list as $item) {
                // 首先找有没有这个数据
                // $rs = BaseCompanyResumeLibrary::findOneVal([
                //     'company_id' => $item['company_id'],
                //     'resume_id'  => $item['resume_id'],
                // ], 'id');

                $rs = BaseCompanyResumeLibrary::find()
                    ->where([
                        'company_id' => $item['company_id'],
                        'resume_id'  => $item['resume_id'],
                    ])
                    ->one();

                if ($rs) {
                    //判断记录是否是下载的，如果是，要做处理，不是就跳过
                    if ($rs->source_type == BaseCompanyResumeLibrary::SOURCE_TYPE_DOWNLOAD) {
                        $rs->source_type = BaseCompanyResumeLibrary::SOURCE_TYPE_BOTH;
                        $rs->apply_time  = CUR_DATETIME;
                        if (!$rs->save()) {
                            throw new Exception('添加失败' . $rs->getFirstErrorsMessage());
                        }
                    }
                    self::log('已经有了:' . $item['id']);
                    continue;
                }

                $model              = new BaseCompanyResumeLibrary();
                $model->company_id  = $item['company_id'];
                $model->resume_id   = $item['resume_id'];
                $model->source_type = BaseCompanyResumeLibrary::SOURCE_TYPE_APPLY;
                $model->add_time    = $item['add_time'];
                $model->apply_time  = $item['add_time'];
                if ($model->save()) {
                    self::log('添加成功:' . $item['id']);
                } else {
                    self::log('添加失败:' . $model->getFirstErrorsMessage());
                }
            }
        } catch (Exception $e) {
            self::log($e->getMessage());
        }
    }

    /*
    * 更新求职在的最高学历
    * PHP timer_yii script/update-resume-top-education-code
    */
    public function actionUpdateResumeTopEducationCode()
    {
        // 找到所有的简历
        $list = BaseResume::find()
            ->select(['id'])
            ->asArray()
            ->all();

        foreach ($list as $item) {
            self::log('开始处理简历:' . $item['id']);
            $model                     = BaseResume::findOne($item['id']);
            $topEducationCode          = BaseResumeEducation::getTopEducationCode($item['id']);
            $model->top_education_code = $topEducationCode;
            if ($model->save()) {
                self::log('更新成功:' . $item['id'] . ':' . $topEducationCode);
            } else {
                self::log('更新失败:' . $model->getFirstErrorsMessage());
            }
        }
    }

    /*
    * 更新简历统计数据
    */
    public function actionFixResumeStatsLibrary()
    {
        // 找到下次次数和收藏总量
        $downloadAmount = ResumeLibraryDownloadLog::find()
            ->select('resume_id,count(*) as total')
            ->groupBy('resume_id')
            ->asArray()
            ->all();

        $collectAmount = BaseResumeLibraryCollect::find()
            ->select('resume_id,count(*) as total')
            ->groupBy('resume_id')
            ->asArray()
            ->all();

        foreach ($downloadAmount as $item) {
            $model                                 = BaseResumeStatData::findOne([
                'resume_id' => $item['resume_id'],
            ]);
            $model->resume_library_download_amount = $item['total'];
            $model->save();
        }

        foreach ($collectAmount as $item) {
            $model                                = BaseResumeStatData::findOne([
                'resume_id' => $item['resume_id'],
            ]);
            $model->resume_library_collect_amount = $item['total'];
            $model->save();
        }
    }

    public function actionUpdateTopEducationId()
    {
        try {
            $list = BaseResume::find()
                ->select('id,last_education_id')
                ->asArray()
                ->all();
            foreach ($list as $item) {
                $topEducationId = BaseResumeEducation::getTopEducationId($item['id']);
                // if ($topEducationId == $item['last_education_id']) {
                //     self::log('不需要更新:' . $item['id']);
                //     continue;
                // }

                $resumeEducation = BaseResumeEducation::findOne($topEducationId);

                //教育经历是否还有海外经历,更新简历表的is_project_school
                $workInfo = BaseResumeEducation::find()
                    ->where([
                        'resume_id'         => $item['id'],
                        'status'            => BaseResumeEducation::STATUS_ACTIVE,
                        'is_project_school' => BaseResumeEducation::IS_PROJECT_SCHOOL_YES,
                    ])
                    ->one();

                $model                     = BaseResume::findOne($item['id']);
                $model->last_education_id  = $topEducationId;
                $model->top_education_code = $resumeEducation->education_id;

                if (!empty($workInfo)) {
                    $model->is_project_school = BaseResumeEducation::IS_PROJECT_SCHOOL_YES;
                } else {
                    $model->is_project_school = BaseResumeEducation::IS_PROJECT_SCHOOL_NO;
                }
                if ($model->save()) {
                    self::log('更新成功:' . $item['id']);
                } else {
                    self::log('更新失败:' . $item['id']);
                }
            }
        } catch (Exception $e) {
            self::log($e->getMessage());
        }
    }

    /*
    * 套餐记录变更添加操作类型
    */
    public function actionAddPackageLogHandleType()
    {
        exit;
        try {
            //更新历史职位发布操作类型
            $jobReleaseList = BaseCompanyPackageChangeLog::find()
                ->select(['id'])
                ->where(['handle_type' => 0])
                ->andWhere([
                    'like',
                    'content',
                    '职位发布',
                ])
                ->asArray()
                ->all();

            foreach ($jobReleaseList as $item) {
                self::log('更新职位发布变更记录id:' . $item['id']);
                $model              = BaseCompanyPackageChangeLog::findOne($item['id']);
                $model->handle_type = BaseCompanyPackageChangeLog::HANDLE_TYPE_JOB_RELEASE;
                if ($model->save()) {
                    self::log('更新成功:' . $item['id']);
                } else {
                    self::log('更新失败:' . $item['id']);
                }
            }

            //更新历史职位刷新操作类型
            $jobRefreshList = BaseCompanyPackageChangeLog::find()
                ->select(['id'])
                ->where(['handle_type' => 0])
                ->andWhere([
                    'like',
                    'content',
                    '职位刷新',
                ])
                ->asArray()
                ->all();

            foreach ($jobRefreshList as $item) {
                self::log('更新职位刷新变更记录id:' . $item['id']);
                $model              = BaseCompanyPackageChangeLog::findOne($item['id']);
                $model->handle_type = BaseCompanyPackageChangeLog::HANDLE_TYPE_JOB_REFRESH;
                if ($model->save()) {
                    self::log('更新成功:' . $item['id']);
                } else {
                    self::log('更新失败:' . $item['id']);
                }
            }

            //更新历史公告发布操作类型
            $announcementReleaseList = BaseCompanyPackageChangeLog::find()
                ->select(['id'])
                ->where(['handle_type' => 0])
                ->andWhere([
                    'like',
                    'content',
                    '简章发布',
                ])
                ->asArray()
                ->all();

            foreach ($announcementReleaseList as $item) {
                self::log('更新公告发布变更记录id:' . $item['id']);
                $model              = BaseCompanyPackageChangeLog::findOne($item['id']);
                $model->handle_type = BaseCompanyPackageChangeLog::HANDLE_TYPE_ANNOUNCEMENT_RELEASE;
                if ($model->save()) {
                    self::log('更新成功:' . $item['id']);
                } else {
                    self::log('更新失败:' . $item['id']);
                }
            }

            //更新历史公告刷新操作类型
            $announcementRefreshList = BaseCompanyPackageChangeLog::find()
                ->select(['id'])
                ->where(['handle_type' => 0])
                ->andWhere([
                    'like',
                    'content',
                    '简章刷新',
                ])
                ->asArray()
                ->all();

            foreach ($announcementRefreshList as $item) {
                self::log('更新公告刷新变更记录id:' . $item['id']);
                $model              = BaseCompanyPackageChangeLog::findOne($item['id']);
                $model->handle_type = BaseCompanyPackageChangeLog::HANDLE_TYPE_ANNOUNCEMENT_REFRESH;
                if ($model->save()) {
                    self::log('更新成功:' . $item['id']);
                } else {
                    self::log('更新失败:' . $item['id']);
                }
            }

            //更新历史配置免费会员操作类型
            $configureFreeList = BaseCompanyPackageChangeLog::find()
                ->select(['id'])
                ->where(['handle_type' => 0])
                ->andWhere([
                    'like',
                    'content',
                    '免费会员',
                ])
                ->asArray()
                ->all();

            foreach ($configureFreeList as $item) {
                self::log('更新免费会员变更记录id:' . $item['id']);
                $model              = BaseCompanyPackageChangeLog::findOne($item['id']);
                $model->handle_type = BaseCompanyPackageChangeLog::HANDLE_TYPE_CONFIGURE_FREE;
                if ($model->save()) {
                    self::log('更新成功:' . $item['id']);
                } else {
                    self::log('更新失败:' . $item['id']);
                }
            }

            //更新历史配置会员操作类型
            $configureFreeList = BaseCompanyPackageChangeLog::find()
                ->select(['id'])
                ->where(['handle_type' => 0])
                ->andWhere([
                    'like',
                    'content',
                    '高级会员',
                ])
                ->asArray()
                ->all();

            foreach ($configureFreeList as $item) {
                self::log('更新高级会员变更记录id:' . $item['id']);
                $model              = BaseCompanyPackageChangeLog::findOne($item['id']);
                $model->handle_type = BaseCompanyPackageChangeLog::HANDLE_TYPE_CONFIGURE_SENIOR;
                if ($model->save()) {
                    self::log('更新成功:' . $item['id']);
                } else {
                    self::log('更新失败:' . $item['id']);
                }
            }
        } catch (Exception $e) {
            self::log($e->getMessage());
        }
    }

    /*
    * 部分企业配置套餐下载点数
    */
    public function actionAddCompanyDownloadAmount()
    {
        exit;
        try {
            //这里提供具体的单位id
            $companyIds = [16555];

            //这里做多一层过滤
            $configList = BaseCompanyPackageConfig::find()
                ->select([
                    'company_id',
                ])
                ->where([
                    'company_id' => $companyIds,
                    'status'     => BaseCompanyPackageConfig::STATUS_ACTIVE,
                ])
                ->andWhere([
                    '>',
                    'expire_time',
                    CUR_DATETIME,
                ])
                ->asArray()
                ->all();

            $effectCompanyIds = array_column($configList, 'company_id');

            $adminModel = BaseAdmin::findOne(['username' => 'admin']);

            foreach ($effectCompanyIds as $item) {
                self::log('更新单位会员变更记录id:' . $item);
                $model                = BaseCompanyPackageConfig::findOne(['company_id' => $item]);
                $systemModel          = BaseCompanyPackageSystemConfig::findOne(['code' => $model->code]);
                $base                 = $systemModel->base_resume_download_amount;
                $packageAmount        = $model->package_amount ?: 1;
                $resumeDownloadAmount = $systemModel->resume_download_amount;
                $amount               = $base + $packageAmount * $resumeDownloadAmount;
                self::log('单位下载点数新增数量:' . $amount);
                $model->resume_download_amount = $amount;

                if ($model->save()) {
                    self::log('更新成功:' . $item);
                    //套餐服务项目变更记录
                    $companyModel                = BaseCompany::findOne(['id' => $item]);
                    $memberName                  = BaseMember::findOneVal(['id' => $companyModel->member_id],
                        'username');
                    $packageConfigName           = BaseCompanyPackageSystemConfig::PACKAGE_CONFIG_NAME;
                    $packageSurplus              = [
                        $packageConfigName['job_amount']                  => $model->job_amount,
                        $packageConfigName['announcement_amount']         => $model->announcement_amount,
                        $packageConfigName['job_refresh_amount']          => $model->job_refresh_amount,
                        $packageConfigName['announcement_refresh_amount'] => $model->announcement_refresh_amount,
                        $packageConfigName['resume_download_amount']      => $amount,
                    ];
                    $packageSurplus              = json_encode($packageSurplus);
                    $companyPackageChangeLogData = [
                        'add_time'        => CUR_DATETIME,
                        'type'            => BaseCompanyPackageChangeLog::TYPE_SYSTEM_CONFIG,
                        'identify'        => BaseCompanyPackageChangeLog::IDENTIFY_ADD,
                        'package_surplus' => $packageSurplus,
                        'member_id'       => $model->member_id,
                        'member_name'     => $memberName,
                        'company_id'      => $model->company_id,
                        'company_name'    => $companyModel->full_name,
                        'handler_type'    => BaseCompanyPackageChangeLog::HANDLER_TYPE_PLATFORM,
                        'handler'         => $adminModel->name,
                        'handler_id'      => $adminModel->id,
                        'content'         => BaseCompanyPackageChangeLog::CONTENT_CONFIGURE_TITLE,
                        'remark'          => '系统配置套餐内容：下载点数',
                        'handle_type'     => BaseCompanyPackageChangeLog::HANDLE_TYPE_RESUME_DOWNLOAD,
                        'change_amount'   => $amount,
                        'surplus'         => $amount,
                        'handle_before'   => (string)$model->resume_download_amount,
                        'handle_after'    => (string)$amount,
                    ];
                    BaseCompanyPackageChangeLog::createCompanyPackageChangeLog($companyPackageChangeLogData);
                } else {
                    self::log('更新失败:' . $item);
                }
            }
        } catch (Exception $e) {
            self::log($e->getMessage());
        }
    }

    public function actionSendResumeDate()
    {
        try {
            $app    = StateApplication::getInstance();
            $params = ['complete' => 40];
            $app->resumeList($params);
        } catch (Exception $e) {
            self::log($e->getMessage());
        }
    }

    public function actionTmp()
    {
        try {
            $app = new Tmp();
            $app->getResume20231204();
        } catch (Exception $e) {
            self::log($e->getMessage());
        }
    }

    /**
     * 通过线下招聘会报名的简历，没有加入到简历库中，这里做个更新
     * php timer_yii script/update-company-resume-library
     * @return void
     */
    public function actionUpdateCompanyResumeLibrary()
    {
        exit;
        //目前有这些线下招聘的职位id
        $arr          = [
            240463,
            240469,
            240470,
            240471,
            240472,
            240473,
            240474,
            240475,
            240476,
        ];
        $resumeIdList = BaseJobApply::find()
            ->where([
                'in',
                'job_id',
                $arr,
            ])
            ->select([
                'resume_id',
                'company_id',
                'add_time',
            ])
            ->asArray()
            ->all();
        foreach ($resumeIdList as $item) {
            self::log('开始处理简历:' . $item['resume_id']);
            //查询是否在简历库
            $model = BaseCompanyResumeLibrary::findOne([
                'resume_id'  => $item['resume_id'],
                'company_id' => $item['company_id'],
            ]);
            if (empty($model)) {
                //如果不存在记录，新增一条记录
                $model              = new  BaseCompanyResumeLibrary();
                $model->resume_id   = $item['resume_id'];
                $model->company_id  = $item['company_id'];
                $model->source_type = BaseCompanyResumeLibrary::SOURCE_TYPE_APPLY;
                $model->apply_time  = $item['add_time'];
                if ($model->save()) {
                    self::log('更新成功:' . $item['resume_id']);
                } else {
                    self::log('更新失败:' . $model->getFirstErrorsMessage());
                }
            }
        }
    }

    public function actionBuildAllJobAuto()
    {
        exit;
        // 跑所有职位的自动匹配
        $jobList = BaseJob::find()
            // ->where([
            //     '<>',
            //     'status',
            //     BaseJob::STATUS_ONLINE,
            // ])
            ->select([
                'id',
            ])
            ->where([
                '>',
                'id',
                285445,
            ])
            ->asArray()
            ->all();
        $count   = count($jobList);
        self::log('一共' . $count . '个职位');
        foreach ($jobList as $k => $item) {
            try {
                self::log('开始处理职位:' . $item['id']);
                $model = new JobAutoClassify($item['id']);
                $model->run();
                self::log('结束处理职位:' . $item['id']);
                self::log('进度: ' . $k . '/' . $count . '  ' . $k / $count * 100 . '%');
            } catch (Exception $e) {
                self::log($e->getMessage());
            }
        }
    }

    /**
     * 同步站内+站外投递数据去投递总表
     * 只需要同步一次 所有写数据位置都已修改会写入总表（一次性同步）
     * php timer_yii script/update-apply-data
     */
    public function actionUpdateApplyData()
    {
        exit;
        self::log('开始同步站内投递数据表');
        //获取站内数据
        $applyData = BaseJobApply::find()
            ->alias('ja')
            ->leftJoin(['j' => BaseJob::tableName()], 'ja.job_id=j.id')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'j.announcement_id=a.id')
            ->select([
                'ja.id',
                'ja.add_time',
                'ja.company_id',
                'ja.resume_id',
                'ja.job_id',
                'ja.source',
                'j.apply_type as job_apply_type',
                'j.apply_address as job_apply_address',
                'a.id as aid',
                'a.apply_type as announcement_apply_type',
                'a.apply_address as announcement_apply_address',
            ])
            ->asArray()
            ->all();

        $count = count($applyData);
        self::log('站内投递数据表共有' . $count . '条数据');

        $totalApply = 0;
        foreach ($applyData as $k => $item) {
            self::log('Start同步站内投递ID：' . $item['id']);
            // 处理的百分比
            $percent = round(($k + 1) / $count, 2) * 100;
            self::log('同步站内投递数据表进度：' . $percent . '%');

            //数据处理
            if (empty($item['job_apply_type'])) {
                if (empty($item['announcement_apply_type'])) {
                    $delivery_way = 1;
                } else {
                    $ann_apply_type_arr = explode(',', $item['announcement_apply_type']);
                    $isEmail            = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $ann_apply_type_arr);
                    if ($isEmail) {
                        $delivery_way = 2;
                    } else {
                        $delivery_way = 3;
                    }
                }
            } else {
                $job_apply_type_arr = explode(',', $item['job_apply_type']);
                $isEmail            = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $job_apply_type_arr);
                if ($isEmail) {
                    $delivery_way = 2;
                } else {
                    $delivery_way = 3;
                }
            }

            //写入数据表job_apply_record
            $insert = [
                'delivery_type'   => 2,
                'delivery_way'    => $delivery_way,
                'add_time'        => $item['add_time'],
                'update_time'     => CUR_DATETIME,
                'apply_id'        => $item['id'],
                'company_id'      => $item['company_id'],
                'resume_id'       => $item['resume_id'],
                'announcement_id' => empty($item['aid']) ? 0 : $item['aid'],
                'job_id'          => $item['job_id'],
                'source'          => $item['source'],
            ];
            Yii::$app->db->createCommand()
                ->insert(BaseJobApplyRecord::tableName(), $insert)
                ->execute();
            self::log('End同步站内投递ID：' . $item['id']);
            $totalApply++;
        }
        self::log('结束同步站内投递数据表');
        self::log('=================');
        self::log('=================');
        self::log('开始同步站外投递数据表');
        //获取站内数据
        $offApplyData = BaseOffSiteJobApply::find()
            ->alias('ja')
            ->leftJoin(['j' => BaseJob::tableName()], 'ja.job_id=j.id')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'j.announcement_id=a.id')
            ->select([
                'ja.id',
                'ja.add_time',
                'ja.resume_id',
                'ja.job_id',
                'ja.source',
                'j.apply_type as job_apply_type',
                'j.apply_address as job_apply_address',
                'j.company_id',
                'a.id as aid',
                'a.apply_type as announcement_apply_type',
                'a.apply_address as announcement_apply_address',
            ])
            ->asArray()
            ->all();
        $offCount     = count($offApplyData);
        self::log('站外投递数据表共有' . $offCount . '条数据');
        $totalOffApply = 0;
        foreach ($offApplyData as $k1 => $itemOff) {
            self::log('Start同步站外投递ID' . $itemOff['id']);
            // 处理的百分比
            $percent = round(($k1 + 1) / $offCount, 2) * 100;
            self::log('同步站外投递数据表进度：' . $percent . '%');

            //数据处理
            if (empty($itemOff['job_apply_type'])) {
                if (empty($itemOff['announcement_apply_type'])) {
                    $delivery_way_off = 1;
                } else {
                    $ann_apply_type_arr = explode(',', $itemOff['announcement_apply_type']);
                    $isEmail            = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $ann_apply_type_arr);
                    if ($isEmail) {
                        $delivery_way_off = 2;
                    } else {
                        $delivery_way_off = 3;
                    }
                }
            } else {
                $job_apply_type_arr = explode(',', $itemOff['job_apply_type']);
                $isEmail            = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $job_apply_type_arr);
                if ($isEmail) {
                    $delivery_way_off = 2;
                } else {
                    $delivery_way_off = 3;
                }
            }
            if ($itemOff['source'] == 2) {
                $source = 1;
            } else {
                $source = 3;
            }

            //写入数据表job_apply_record
            $insert = [
                'delivery_type'   => 1,
                'delivery_way'    => $delivery_way_off,
                'add_time'        => $itemOff['add_time'],
                'update_time'     => CUR_DATETIME,
                'apply_site_id'   => $itemOff['id'],
                'company_id'      => $itemOff['company_id'] ?: 0,
                'resume_id'       => $itemOff['resume_id'] ?: 0,
                'announcement_id' => empty($itemOff['aid']) ? 0 : $itemOff['aid'],
                'job_id'          => $itemOff['job_id'] ?: 0,
                'source'          => $source,
            ];
            Yii::$app->db->createCommand()
                ->insert(BaseJobApplyRecord::tableName(), $insert)
                ->execute();
            self::log('End同步站外投递ID' . $itemOff['id']);
            $totalOffApply++;
        }
        self::log('结束同步站外投递数据表');
        self::log('共计同步站内总数：' . $totalApply);
        self::log('共计同步站外总数：' . $totalOffApply);
    }

    /**
     * 更新单位账号性质
     * 只需要同步一次（一次性同步）
     * php timer_yii script/update-company-data
     */
    public function actionUpdateCompanyData()
    {
        exit;
        //配置指定单位更新成为A账号即站内投递账号 特殊处理14个账号
        $a_ids = [
            893,
            13558,
            7,
            345,
            3,
            636,
            91,
            33,
            130,
            401,
            585,
            13846,
            98,
        ];
        foreach ($a_ids as $a_id) {
            //更新A账号的单位性质
            $a_model = BaseCompany::findOne($a_id);
            if ($a_model) {
                $a_model->delivery_type = BaseCompany::DELIVERY_TYPE_INNER;
                if (!$a_model->save()) {
                    self::log('单位A账号性质更新失败；ID为:' . $a_id);
                }
                //插入单位性质变化日志
                $model                     = new BaseCompanyDeliveryChangeLog();
                $model->user_id            = 1;
                $model->user_name          = 'admin';
                $model->description_before = 0;
                $model->description_after  = 2;
                $model->remark             = '系统处理';
                $model->user_type          = 1;
                $model->company_id         = $a_model->id;
                $model->company_name       = $a_model->full_name;
                $model->add_time           = CUR_DATETIME;
                $model->save();
                self::log('单位A账号性质更新成功；ID为:' . $a_id);
            }
        }
        //同步单位B账号的单位性质即站内+站外
        $where = [
            'and',
            [
                'is_cooperation' => 1,
            ],
            [
                'not in',
                'id',
                $a_ids,
            ],
        ];
        BaseCompany::updateAll(['delivery_type' => BaseCompany::DELIVERY_TYPE_OUTER_INNER], $where);
        self::log('单位B账号性质更新完成');
        $companyYesInfo = BaseCompany::find()
            ->select([
                'id',
                'full_name',
            ])
            ->andWhere($where)
            ->asArray()
            ->all();
        foreach ($companyYesInfo as $vi) {
            //插入单位性质变化日志
            $model                     = new BaseCompanyDeliveryChangeLog();
            $model->user_id            = 1;
            $model->user_name          = 'admin';
            $model->description_before = 0;
            $model->description_after  = 3;
            $model->remark             = '系统处理';
            $model->user_type          = 1;
            $model->company_id         = $vi['id'];
            $model->company_name       = $vi['full_name'];
            $model->add_time           = CUR_DATETIME;
            $model->save();
            self::log('单位B账号性质更新成功；ID为:' . $vi['id']);
        }
        // 同步非合作单位的账号性质
        BaseCompany::updateAll(['delivery_type' => BaseCompany::DELIVERY_TYPE_INSIDE], ['is_cooperation' => 2]);
        self::log('非合作单位性质更新完成');
    }

    /**
     * 更新合作单位单位公告职位属性
     * 只需要同步一次（一次性同步）
     * php timer_yii script/update-announcement-job-data-yes
     */
    public function actionUpdateAnnouncementJobDataYes()
    {
        exit;
        //A账号
        //四个单位的职位走新A逻辑
        $a_id_1 = [
            893,
            13558,
            7,
            345,
            3,
            636,
            91,
            33,
            130,
            401,
            585,
        ];
        //拿这四家单位的职位与公共数据
        $new_data_1 = BaseJob::find()
            ->alias('j')
            ->select([
                'j.id as job_id',
                'j.apply_type',
                'j.apply_address',
                'an.id as an_id',
                'an.apply_type as an_apply_type',
                'an.apply_address as an_apply_address',
            ])
            ->leftJoin(['an' => BaseAnnouncement::tableName()], 'j.announcement_id=an.id')
            ->andWhere(['j.company_id' => $a_id_1])
            ->asArray()
            ->all();

        self::log('A账号公告职位数据更新开始');
        foreach ($new_data_1 as $new_item) {
            self::log('A账号公告职位数据更新开始：' . $new_item['job_id']);
            if ($new_item['apply_type'] && $new_item['apply_address']) {
                $isMail = in_array(BaseAnnouncement::ATTRIBUTE_APPLY_EMAIL, explode(',', $new_item['apply_type']));
                if ($isMail) {
                    //是邮件则更新到通知邮箱
                    $update_data  = [
                        'apply_type'           => '',
                        'apply_address'        => '',
                        'extra_notify_address' => $new_item['apply_address'],
                        'delivery_way'         => BaseJob::DELIVERY_WAY_PLATFORM,
                        'delivery_type'        => BaseJob::DELIVERY_TYPE_INSIDE,
                    ];
                    $update_where = ['id' => $new_item['job_id']];
                    BaseJob::updateAll($update_data, $update_where);
                    self::log('A账号职位数据更新成功：' . $new_item['job_id'] . '源数据内容json格式：' . json_encode($new_item));
                } else {
                    //是网址
                    $update_data  = [
                        'delivery_way'  => BaseJob::DELIVERY_WAY_LINK,
                        'delivery_type' => BaseJob::DELIVERY_TYPE_INSIDE,
                    ];
                    $update_where = ['id' => $new_item['job_id']];
                    BaseJob::updateAll($update_data, $update_where);
                    self::log('A账号职位数据更新成功：' . $new_item['job_id'] . '源数据内容json格式：' . json_encode($new_item));
                }
            }

            if ($new_item['an_apply_type'] && $new_item['an_apply_address']) {
                if (!$new_item['an_id']) {
                    self::log('A账号公告数据更新失败：' . $new_item['an_id']);
                    continue;
                }
                $isAnMail = in_array(BaseAnnouncement::ATTRIBUTE_APPLY_EMAIL, explode(',', $new_item['an_apply_type']));
                if ($isAnMail) {
                    //是邮件则更新到通知邮箱
                    $an_update_data  = [
                        'apply_type'           => '',
                        'apply_address'        => '',
                        'extra_notify_address' => $new_item['apply_address'],
                        'delivery_way'         => BaseAnnouncement::DELIVERY_WAY_PLATFORM,
                        'delivery_type'        => BaseAnnouncement::DELIVERY_TYPE_INSIDE,
                    ];
                    $an_update_where = ['id' => $new_item['an_id']];
                    BaseAnnouncement::updateAll($an_update_data, $an_update_where);
                    self::log('A账号公告数据更新成功：' . $new_item['an_id'] . '源数据内容json格式：' . json_encode($new_item));
                } else {
                    //是网址
                    $an_update_data  = [
                        'delivery_way'  => BaseAnnouncement::DELIVERY_WAY_LINK,
                        'delivery_type' => BaseAnnouncement::DELIVERY_TYPE_INSIDE,
                    ];
                    $an_update_where = ['id' => $new_item['an_id']];
                    BaseAnnouncement::updateAll($an_update_data, $an_update_where);
                    self::log('A账号公告数据更新成功：' . $new_item['an_id'] . '源数据内容json格式：' . json_encode($new_item));
                }
            }

            if (!$new_item['an_apply_type'] && !$new_item['an_apply_address'] && !$new_item['apply_type'] && !$new_item['apply_address']) {
                //全部没有 更新职位为平台投递
                $update_data  = [
                    'delivery_way'  => BaseJob::DELIVERY_WAY_PLATFORM,
                    'delivery_type' => BaseJob::DELIVERY_TYPE_INSIDE,
                ];
                $update_where = ['id' => $new_item['job_id']];
                BaseJob::updateAll($update_data, $update_where);
                self::log('A账号公告职位数据更新成功：' . $new_item['job_id'] . '源数据内容json格式：' . json_encode($new_item));
            }
            self::log('A账号公告职位数据更新结束：' . $new_item['job_id']);
        }
        self::log('A账号公告职位数据更新结束');
        //B账号
        //10个单位的职位走B逻辑+剩下的合作单位全部走B
        // $a_id_2 = [
        //     13846,
        //     98,
        // ];
        //拿这四家单位的职位与公共数据
        $new_data_2 = BaseJob::find()
            ->alias('j')
            ->select([
                'j.id as job_id',
                'j.apply_type',
                'j.apply_address',
                'an.id as an_id',
                'an.apply_type as an_apply_type',
                'an.apply_address as an_apply_address',
            ])
            ->leftJoin(['an' => BaseAnnouncement::tableName()], 'j.announcement_id=an.id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'j.company_id=c.id')
            ->andWhere([
                'not in',
                'j.company_id',
                $a_id_1,
            ])
            ->andWhere(['c.is_cooperation' => BaseAnnouncement::IS_COOPERATION_YES])
            ->asArray()
            ->all();

        self::log('B账号公告职位数据更新开始');
        foreach ($new_data_2 as $new_item) {
            self::log('B账号公告职位数据更新开始：' . $new_item['job_id']);
            if (!$new_item['job_id']) {
                self::log('B账号公告职位数据更新失败：' . $new_item['job_id']);
                continue;
            }
            if ($new_item['apply_type'] && $new_item['apply_address']) {
                $isMail = in_array(BaseAnnouncement::ATTRIBUTE_APPLY_EMAIL, explode(',', $new_item['apply_type']));
                if ($isMail) {
                    //是邮件则更新到通知邮箱
                    $update_data  = [
                        'delivery_way'  => BaseJob::DELIVERY_WAY_EMAIL,
                        'delivery_type' => BaseJob::DELIVERY_TYPE_INSIDE,
                    ];
                    $update_where = ['id' => $new_item['job_id']];
                    BaseJob::updateAll($update_data, $update_where);
                    self::log('B账号职位数据更新成功：' . $new_item['job_id'] . '源数据内容json格式：' . json_encode($new_item));
                } else {
                    //是网址
                    $update_data  = [
                        'delivery_way'  => BaseJob::DELIVERY_WAY_LINK,
                        'delivery_type' => BaseJob::DELIVERY_TYPE_INSIDE,
                    ];
                    $update_where = ['id' => $new_item['job_id']];
                    BaseJob::updateAll($update_data, $update_where);
                    self::log('B账号职位数据更新成功：' . $new_item['job_id'] . '源数据内容json格式：' . json_encode($new_item));
                }
            }
            if ($new_item['an_apply_type'] && $new_item['an_apply_address']) {
                if (!$new_item['an_id']) {
                    self::log('B账号公告数据更新失败：' . $new_item['an_id']);
                    continue;
                }
                $isAnMail = in_array(BaseAnnouncement::ATTRIBUTE_APPLY_EMAIL, explode(',', $new_item['an_apply_type']));
                if ($isAnMail) {
                    //是邮件则更新到通知邮箱
                    $an_update_data  = [
                        'delivery_way'  => BaseAnnouncement::DELIVERY_WAY_EMAIL,
                        'delivery_type' => BaseAnnouncement::DELIVERY_TYPE_INSIDE,
                    ];
                    $an_update_where = ['id' => $new_item['an_id']];
                    BaseAnnouncement::updateAll($an_update_data, $an_update_where);
                    self::log('B账号公告数据更新成功：' . $new_item['an_id'] . '源数据内容json格式：' . json_encode($new_item));
                } else {
                    //是网址
                    $an_update_data  = [
                        'delivery_way'  => BaseAnnouncement::DELIVERY_WAY_LINK,
                        'delivery_type' => BaseAnnouncement::DELIVERY_TYPE_INSIDE,
                    ];
                    $an_update_where = ['id' => $new_item['an_id']];
                    BaseAnnouncement::updateAll($an_update_data, $an_update_where);
                    self::log('B账号公告数据更新成功：' . $new_item['an_id'] . '源数据内容json格式：' . json_encode($new_item));
                }
            }
            if ((!$new_item['apply_type'] && !$new_item['apply_address']) && (!$new_item['an_apply_type'] && !$new_item['an_apply_address'])) {
                //全部没有 更新职位为平台投递
                $update_data  = [
                    'delivery_way'  => BaseJob::DELIVERY_WAY_PLATFORM,
                    'delivery_type' => BaseJob::DELIVERY_TYPE_INSIDE,
                ];
                $update_where = ['id' => $new_item['job_id']];
                BaseJob::updateAll($update_data, $update_where);
                self::log('B账号职位数据更新成功：' . $new_item['job_id'] . '源数据内容json格式：' . json_encode($new_item));
            }
        }
        self::log('B账号公告职位数据更新结束');
    }

    /**
     * 更新非合作单位单位公告职位属性
     * 只需要同步一次（一次性同步）
     * php timer_yii script/update-announcement-job-data-no
     */
    public function actionUpdateAnnouncementJobDataNo()
    {
        exit;
        self::log('非合作单位公告职位数据更新开始');
        $new_data = BaseJob::find()
            ->alias('j')
            ->select([
                'j.id as job_id',
                'j.apply_type',
                'j.apply_address',
                'an.id as an_id',
                'an.apply_type as an_apply_type',
                'an.apply_address as an_apply_address',
            ])
            ->leftJoin(['an' => BaseAnnouncement::tableName()], 'j.announcement_id=an.id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'j.company_id=c.id')
            ->andWhere(['c.is_cooperation' => BaseAnnouncement::IS_COOPERATION_NO])
            ->asArray()
            ->all();
        foreach ($new_data as $new_item) {
            if ($new_item['apply_type'] && $new_item['apply_address']) {
                $isMail                       = in_array(BaseAnnouncement::ATTRIBUTE_APPLY_EMAIL,
                    explode(',', $new_item['apply_type']));
                $update_data                  = [];
                $update_data['delivery_type'] = BaseJob::DELIVERY_TYPE_INSIDE;
                if ($isMail) {
                    //是邮件
                    $update_data['delivery_way'] = BaseJob::DELIVERY_WAY_EMAIL;
                } else {
                    //是网址
                    $update_data['delivery_way'] = BaseJob::DELIVERY_WAY_LINK;
                }
                $update_where = ['id' => $new_item['job_id']];
                BaseJob::updateAll($update_data, $update_where);
                self::log('非合作单位职位数据更新成功：' . $new_item['job_id'] . '源数据内容json格式：' . json_encode($new_item));
            }
            if ($new_item['an_apply_type'] && $new_item['an_apply_address']) {
                $isAnMail                        = in_array(BaseAnnouncement::ATTRIBUTE_APPLY_EMAIL,
                    explode(',', $new_item['an_apply_type']));
                $an_update_data                  = [];
                $an_update_data['delivery_type'] = BaseJob::DELIVERY_TYPE_INSIDE;
                if ($isAnMail) {
                    //是邮件
                    $an_update_data['delivery_way'] = BaseJob::DELIVERY_WAY_EMAIL;
                } else {
                    //是网址
                    $an_update_data['delivery_way'] = BaseJob::DELIVERY_WAY_LINK;
                }
                $an_update_where = ['id' => $new_item['an_id']];
                BaseAnnouncement::updateAll($an_update_data, $an_update_where);
                self::log('非合作单位职位数据更新成功：' . $new_item['an_id'] . '源数据内容json格式：' . json_encode($new_item));
            }
            if ((!$new_item['apply_type'] && !$new_item['apply_address']) && (!$new_item['an_apply_type'] && !$new_item['an_apply_address'])) {
                self::log('站外属性更新时职位ID为' . $new_item['job_id'] . ',公告ID为' . $new_item['an_id'] . ',属性值为空请核对。');
            }
        }
        self::log('非合作单位公告职位数据更新开结束');
    }

    /**
     * 合作单位的数据出现问题修复脚本
     * php timer_yii script/update-no-company-data
     */
    public function actionUpdateNoCompanyData()
    {
        exit;
        self::log('非合作单位公告下职位数据更新开始');
        $new_data = BaseJob::find()
            ->alias('j')
            ->select([
                'j.id',
                'j.apply_type',
                'j.apply_address',
                'j.delivery_type',
                'j.delivery_way',
                'an.id as an_id',
                'an.apply_type as an_apply_type',
                'an.apply_address as an_apply_address',
            ])
            ->leftJoin(['an' => BaseAnnouncement::tableName()], 'j.announcement_id=an.id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'j.company_id=c.id')
            ->andWhere(['c.is_cooperation' => BaseAnnouncement::IS_COOPERATION_NO])
            ->andWhere(['j.delivery_type' => 1])
            ->andWhere(['j.delivery_way' => 0])
            ->asArray()
            ->all();

        $count = count($new_data);

        foreach ($new_data as $k => $new_item) {
            self::log('公告ID为' . $new_item['an_id'] . '的职位ID为' . $new_item['id'] . '的数据为：' . json_encode($new_item));
            self::log('进度: ' . $k . '/' . $count . '  ' . $k / $count * 100 . '%');
            $model                = BaseJob::findOne($new_item['id']);
            $model->delivery_type = 0;
            if ($model->save()) {
                self::log('公告ID为' . $new_item['an_id'] . '的职位ID为' . $new_item['id'] . '的数据delivery_type由1变为0,成功！');
            } else {
                self::log('公告ID为' . $new_item['an_id'] . '的职位ID为' . $new_item['id'] . '的数据delivery_type由1变为0,失败！');
            }
        }

        self::log('非合作单位公告下职位数据更新开结束');
    }

    /**
     * 更新历史遗留错误数据
     * php timer_yii script/update-error-data
     */
    public function actionUpdateErrorData()
    {
        exit;
        //更新公告数据----跟职位数据
        $announcement_id = [
            8376,
            10244,
            12969,
            14150,
            15605,
            10244,
        ];
        foreach ($announcement_id as $item) {
            $announcementInfo = BaseAnnouncement::findOne($item);
            if (!$announcementInfo) {
                self::log('------------------公告数据未获取到：' . $item);
                continue;
            }
            self::log('更新公告原始数据：' . json_encode($announcementInfo));
            $announcementInfo->apply_type    = '';
            $announcementInfo->apply_address = '';
            $announcementInfo->delivery_type = 0;
            $announcementInfo->delivery_way  = 0;
            if ($announcementInfo->save()) {
                self::log('更新公告数据成功：' . json_encode($announcementInfo));
            } else {
                self::log('更新公告数据失败：' . json_encode($announcementInfo));
            }
        }

        //更新职位数据----跟公告数据
        $job_id = [
            //13005
            134720,
            134721,
            134723,
            172125,
            172126,
            172127,
            172128,
            172129,
            172130,
            172131,
            172132,
            172133,
            172134,
            172135,
            172136,
            172137,
            172138,
            172139,
            172140,
            172141,
            172142,
            172143,
            172144,
            172145,
            172146,
            172147,
            172148,
            172149,
            172150,
            172151,
            172152,
            172153,
            172154,
            172155,
            172156,
            172157,
            172158,
            172159,
            172160,
            172161,
            172162,
            172163,
            172164,
            172165,
            172166,
            172167,
            172168,
            172169,
        ];
        foreach ($job_id as $item) {
            $jobInfo = BaseJob::findOne($item);
            if (!$jobInfo) {
                self::log('------------------职位数据未获取到：' . $item);
                continue;
            }
            self::log('更新职位原始数据：' . json_encode($jobInfo));
            $jobInfo->apply_type    = '';
            $jobInfo->apply_address = '';
            $jobInfo->delivery_type = 0;
            $jobInfo->delivery_way  = 0;
            if ($jobInfo->save()) {
                self::log('更新职位数据成功：' . json_encode($jobInfo));
            } else {
                self::log('更新职位数据失败：' . json_encode($jobInfo));
            }
        }

        //特殊处理数据
        self::log('特殊处理数据开始');
        self::log('特殊处理单位ID为140，公告ID为273的数据开始,职位更新网上系统');
        $job_273 = [
            //140-273
            3339,
            3340,
            3341,
            3342,
            3343,
            3344,
            3345,
            3346,
            3347,
            3348,
            3349,
            3350,
            3351,
            3352,
            3353,
            3354,
            3355,
            3356,
            3357,
            3358,
            3359,
            3360,
            3361,
            3362,
            3363,
            3364,
            3365,
            3366,
            3367,
            3368,
            3369,
            3370,
            3371,
            3372,
            3373,
            3374,
            3375,
            3376,
            3377,
            3378,
        ];
        foreach ($job_273 as $item) {
            $jobInfo = BaseJob::findOne($item);
            if (!$jobInfo) {
                self::log('------------------职位数据未获取到：' . $item);
                continue;
            }
            self::log('更新职位原始数据：' . json_encode($jobInfo));
            $jobInfo->apply_type    = '2';
            $jobInfo->apply_address = 'https://www.wjx.top/m/34134152.aspx';
            $jobInfo->delivery_type = 1;
            $jobInfo->delivery_way  = 3;
            if ($jobInfo->save()) {
                self::log('更新职位数据成功：' . json_encode($jobInfo));
            } else {
                self::log('更新职位数据失败：' . json_encode($jobInfo));
            }
        }
        self::log('特殊处理单位ID为140，公告ID为273的数据结束,职位更新网上系统');
        self::log('特殊处理单位ID为336，公告ID为521的数据开始,职位更新网上系统');
        $job_521 = [
            6477,
            6480,
        ];
        foreach ($job_521 as $item) {
            $jobInfo = BaseJob::findOne($item);
            if (!$jobInfo) {
                self::log('------------------职位数据未获取到：' . $item);
                continue;
            }
            self::log('更新职位原始数据：' . json_encode($jobInfo));
            $jobInfo->apply_type    = '2';
            $jobInfo->apply_address = 'https://rszp.scou.cn/index.php?r=survey/index&sid=889652';
            $jobInfo->delivery_type = 1;
            $jobInfo->delivery_way  = 3;
            if ($jobInfo->save()) {
                self::log('更新职位数据成功：' . json_encode($jobInfo));
            } else {
                self::log('更新职位数据失败：' . json_encode($jobInfo));
            }
        }
        self::log('特殊处理单位ID为336，公告ID为521的数据开始,职位更新网上系统');
        self::log('特殊处理的数据开始,职位更新平台投递');
        $job_platform = [
            //384
            5259,
            5260,
            5267,
            5268,
            5269,
            5270,
            //547
            6747,
            6748,
            6749,
            //1257
            13785,
            13786,
            //3741
            38265,
            38266,
            38267,
            //7736
            68193,
            //7737
            68199,
            //7745
            68263,
            //111795
            94860,
            94861,
            94862,
            94863,
            94864,
            94865,
            94866,
            94867,
            //12597
            100087,
            //13011
            102737,
            102738,
            102739,
            //17562
            131899,
            131900,
            //20448
            150262,
            //30602
            197297,
            197298,
            197299,
            //44664
            255436,
            255437,
            //45608
            259298,
            259299,
            259300,
            259301,
            259302,
            259303,
            259304,
            259305,
            259306,
            259307,
            //47829
            268000,
            268001,
            //49109
            272594,
            272595,
            272596,
            272597,
            272598,
            //--
            137465,
            137495,
        ];
        foreach ($job_platform as $item) {
            $jobInfo = BaseJob::findOne($item);
            if (!$jobInfo) {
                self::log('------------------职位数据未获取到：' . $item);
                continue;
            }
            self::log('更新职位原始数据：' . json_encode($jobInfo));
            $jobInfo->apply_type    = '';
            $jobInfo->apply_address = '';
            $jobInfo->delivery_type = 2;
            $jobInfo->delivery_way  = 1;
            if ($jobInfo->save()) {
                self::log('更新职位数据成功：' . json_encode($jobInfo));
            } else {
                self::log('更新职位数据失败：' . json_encode($jobInfo));
            }
        }
        self::log('特殊处理的数据结束,职位更新平台投递');
        self::log('特殊处理单位ID为495，公告ID为654的数据开始,职位更新电子邮件');
        $job_654 = [7951];
        foreach ($job_654 as $item) {
            $jobInfo = BaseJob::findOne($item);
            if (!$jobInfo) {
                self::log('------------------职位数据未获取到：' . $item);
                continue;
            }
            self::log('更新职位原始数据：' . json_encode($jobInfo));
            $jobInfo->apply_type    = '1';
            $jobInfo->apply_address = '<EMAIL>';
            $jobInfo->delivery_type = 2;
            $jobInfo->delivery_way  = 2;
            if ($jobInfo->save()) {
                self::log('更新职位数据成功：' . json_encode($jobInfo));
            } else {
                self::log('更新职位数据失败：' . json_encode($jobInfo));
            }
        }
        self::log('特殊处理单位ID为495，公告ID为654的数据结束,职位更新电子邮件');
        self::log('特殊处理单位ID为976，公告ID为1783的数据开始,职位更新电子邮件');
        $job_1783 = [
            18635,
            18636,
            18637,
            18638,
            18639,
            18640,
            18641,
            18642,
            18643,
            18644,
            18645,
            38265,
            38266,
            38267,
        ];
        foreach ($job_1783 as $item) {
            $jobInfo = BaseJob::findOne($item);
            if (!$jobInfo) {
                self::log('------------------职位数据未获取到：' . $item);
                continue;
            }
            self::log('更新职位原始数据：' . json_encode($jobInfo));
            $jobInfo->apply_type    = '1';
            $jobInfo->apply_address = '<EMAIL>';
            $jobInfo->delivery_type = 2;
            $jobInfo->delivery_way  = 2;
            if ($jobInfo->save()) {
                self::log('更新职位数据成功：' . json_encode($jobInfo));
            } else {
                self::log('更新职位数据失败：' . json_encode($jobInfo));
            }
        }
        self::log('特殊处理单位ID为976，公告ID为1783的数据结束,职位更新电子邮件');
        self::log('特殊处理单位ID为233，公告ID为6924的数据开始,职位更新电子邮件');
        $job_6924 = [
            61698,
            61699,
        ];
        foreach ($job_6924 as $item) {
            $jobInfo = BaseJob::findOne($item);
            if (!$jobInfo) {
                self::log('------------------职位数据未获取到：' . $item);
                continue;
            }
            self::log('更新职位原始数据：' . json_encode($jobInfo));
            $jobInfo->apply_type    = '1';
            $jobInfo->apply_address = '<EMAIL>';
            $jobInfo->delivery_type = 2;
            $jobInfo->delivery_way  = 2;
            if ($jobInfo->save()) {
                self::log('更新职位数据成功：' . json_encode($jobInfo));
            } else {
                self::log('更新职位数据失败：' . json_encode($jobInfo));
            }
        }
        self::log('特殊处理单位ID为233，公告ID为6924的数据结束,职位更新电子邮件');
        self::log('特殊处理单位ID为6911，公告ID为10265的数据结束,职位更新平台投递');
        $job_10265 = [
            85902,
            85903,
        ];
        foreach ($job_10265 as $item) {
            $jobInfo = BaseJob::findOne($item);
            if (!$jobInfo) {
                self::log('------------------职位数据未获取到：' . $item);
                continue;
            }
            if ($jobInfo->announcement_id > 0) {
                $announcementInfo                = BaseAnnouncement::findOne($jobInfo->announcement_id);
                $announcementInfo->apply_type    = '';
                $announcementInfo->apply_address = '';
                $announcementInfo->delivery_type = 0;
                $announcementInfo->delivery_way  = 0;
                $announcementInfo->save();
            }
            self::log('更新职位原始数据：' . json_encode($jobInfo));
            $jobInfo->apply_type    = '';
            $jobInfo->apply_address = '';
            $jobInfo->delivery_type = 2;
            $jobInfo->delivery_way  = 1;
            if ($jobInfo->save()) {
                self::log('更新职位数据成功：' . json_encode($jobInfo));
            } else {
                self::log('更新职位数据失败：' . json_encode($jobInfo));
            }
        }
        self::log('特殊处理单位ID为6911，公告ID为10265的数据结束,职位更新平台投递');
        self::log('特殊处理单位ID为935，公告ID为11818的数据开始,职位更新电子邮件');
        $job_11818 = [
            95019,
        ];
        foreach ($job_11818 as $item) {
            $jobInfo = BaseJob::findOne($item);
            if (!$jobInfo) {
                self::log('------------------职位数据未获取到：' . $item);
                continue;
            }
            self::log('更新职位原始数据：' . json_encode($jobInfo));
            $jobInfo->apply_type    = '1';
            $jobInfo->apply_address = '<EMAIL>';
            $jobInfo->delivery_type = 2;
            $jobInfo->delivery_way  = 2;
            if ($jobInfo->save()) {
                self::log('更新职位数据成功：' . json_encode($jobInfo));
            } else {
                self::log('更新职位数据失败：' . json_encode($jobInfo));
            }
        }
        self::log('特殊处理单位ID为935，公告ID为11818的数据结束,职位更新电子邮件');
        self::log('特殊处理单位ID为10195，开始，职位更新电子邮件');
        $job_10195 = [
            110781,
            110787,
            110788,
        ];
        foreach ($job_10195 as $item) {
            $jobInfo = BaseJob::findOne($item);
            if (!$jobInfo) {
                self::log('------------------职位数据未获取到：' . $item);
                continue;
            }
            self::log('更新职位原始数据：' . json_encode($jobInfo));
            $jobInfo->apply_type    = '1';
            $jobInfo->apply_address = '<EMAIL>';
            $jobInfo->delivery_type = 2;
            $jobInfo->delivery_way  = 2;
            if ($jobInfo->save()) {
                self::log('更新职位数据成功：' . json_encode($jobInfo));
            } else {
                self::log('更新职位数据失败：' . json_encode($jobInfo));
            }
        }
        self::log('特殊处理单位ID为10195，结束,职位更新电子邮件');
        self::log('特殊处理单位ID为580，公告ID为42619的数据开始,职位更新网上系统');
        $job_42619 = [247488];
        foreach ($job_42619 as $item) {
            $jobInfo = BaseJob::findOne($item);
            if (!$jobInfo) {
                self::log('------------------职位数据未获取到：' . $item);
                continue;
            }
            self::log('更新职位原始数据：' . json_encode($jobInfo));
            $jobInfo->apply_type    = '2';
            $jobInfo->apply_address = 'http://t.jugaocai.com/Um4L';
            $jobInfo->delivery_type = 1;
            $jobInfo->delivery_way  = 3;
            if ($jobInfo->save()) {
                self::log('更新职位数据成功：' . json_encode($jobInfo));
            } else {
                self::log('更新职位数据失败：' . json_encode($jobInfo));
            }
        }
        self::log('特殊处理单位ID为580，公告ID为42619的数据开始,职位更新网上系统');
        self::log('特殊处理单位ID为24，开始，职位更新电子邮件');
        $job_198889 = [
            198889,
        ];
        foreach ($job_198889 as $item) {
            $jobInfo = BaseJob::findOne($item);
            if (!$jobInfo) {
                self::log('------------------职位数据未获取到：' . $item);
                continue;
            }
            self::log('更新职位原始数据：' . json_encode($jobInfo));
            $jobInfo->apply_type    = '1';
            $jobInfo->apply_address = '<EMAIL>';
            $jobInfo->delivery_type = 2;
            $jobInfo->delivery_way  = 2;
            if ($jobInfo->save()) {
                self::log('更新职位数据成功：' . json_encode($jobInfo));
            } else {
                self::log('更新职位数据失败：' . json_encode($jobInfo));
            }
        }
        self::log('特殊处理单位ID为24，结束,职位更新电子邮件');
        self::log('特殊处理单位ID为156，开始，职位更新电子邮件');
        $job_225598 = [
            225598,
        ];
        foreach ($job_225598 as $item) {
            $jobInfo = BaseJob::findOne($item);
            if (!$jobInfo) {
                self::log('------------------职位数据未获取到：' . $item);
                continue;
            }
            self::log('更新职位原始数据：' . json_encode($jobInfo));
            $jobInfo->apply_type    = '1';
            $jobInfo->apply_address = '<EMAIL>';
            $jobInfo->delivery_type = 2;
            $jobInfo->delivery_way  = 2;
            if ($jobInfo->save()) {
                self::log('更新职位数据成功：' . json_encode($jobInfo));
            } else {
                self::log('更新职位数据失败：' . json_encode($jobInfo));
            }
        }
        self::log('特殊处理单位ID为156，结束,职位更新电子邮件');
        self::log('特殊处理单位ID为572，开始，职位更新电子邮件');
        $job_251418 = [
            251418,
        ];
        foreach ($job_251418 as $item) {
            $jobInfo = BaseJob::findOne($item);
            if (!$jobInfo) {
                self::log('------------------职位数据未获取到：' . $item);
                continue;
            }
            self::log('更新职位原始数据：' . json_encode($jobInfo));
            $jobInfo->apply_type    = '1';
            $jobInfo->apply_address = '<EMAIL>';
            $jobInfo->delivery_type = 2;
            $jobInfo->delivery_way  = 2;
            if ($jobInfo->save()) {
                self::log('更新职位数据成功：' . json_encode($jobInfo));
            } else {
                self::log('更新职位数据失败：' . json_encode($jobInfo));
            }
        }
        self::log('特殊处理单位ID为572，结束,职位更新电子邮件');
        self::log('特殊处理单位ID为572，开始，职位更新电子邮件');
        $job_251952 = [
            251952,
        ];
        foreach ($job_251952 as $item) {
            $jobInfo = BaseJob::findOne($item);
            if (!$jobInfo) {
                self::log('------------------职位数据未获取到：' . $item);
                continue;
            }
            self::log('更新职位原始数据：' . json_encode($jobInfo));
            $jobInfo->apply_type    = '1';
            $jobInfo->apply_address = '<EMAIL>,<EMAIL>';
            $jobInfo->delivery_type = 2;
            $jobInfo->delivery_way  = 2;
            if ($jobInfo->save()) {
                self::log('更新职位数据成功：' . json_encode($jobInfo));
            } else {
                self::log('更新职位数据失败：' . json_encode($jobInfo));
            }
        }
        self::log('特殊处理单位ID为572，结束,职位更新电子邮件');
        self::log('特殊处理单位ID为105，开始，职位更新电子邮件');
        $job_263894 = [
            263894,
            267942,
            268119,
        ];
        foreach ($job_263894 as $item) {
            $jobInfo = BaseJob::findOne($item);
            if (!$jobInfo) {
                self::log('------------------职位数据未获取到：' . $item);
                continue;
            }
            self::log('更新职位原始数据：' . json_encode($jobInfo));
            $jobInfo->apply_type    = '1';
            $jobInfo->apply_address = '<EMAIL>';
            $jobInfo->delivery_type = 2;
            $jobInfo->delivery_way  = 2;
            if ($jobInfo->save()) {
                self::log('更新职位数据成功：' . json_encode($jobInfo));
            } else {
                self::log('更新职位数据失败：' . json_encode($jobInfo));
            }
        }
        self::log('特殊处理单位ID为105，结束,职位更新电子邮件');
        self::log('特殊处理单位ID为105，开始，职位更新电子邮件');
        $job_273897 = [
            273897,
        ];
        foreach ($job_273897 as $item) {
            $jobInfo = BaseJob::findOne($item);
            if (!$jobInfo) {
                self::log('------------------职位数据未获取到：' . $item);
                continue;
            }
            self::log('更新职位原始数据：' . json_encode($jobInfo));
            $jobInfo->apply_type    = '1';
            $jobInfo->apply_address = '<EMAIL>';
            $jobInfo->delivery_type = 2;
            $jobInfo->delivery_way  = 2;
            if ($jobInfo->save()) {
                self::log('更新职位数据成功：' . json_encode($jobInfo));
            } else {
                self::log('更新职位数据失败：' . json_encode($jobInfo));
            }
        }
        self::log('特殊处理单位ID为105，结束,职位更新电子邮件');
    }

    /**
     * 修复投递数据
     * php timer_yii script/update-apply-record-data
     */
    public function actionUpdateApplyRecordData()
    {
        self::log('更新职位数据开始');
        $new_data = [
            298777,
            241756,
            298511,
        ];
        foreach ($new_data as $item) {
            $jobInfo = BaseJob::findOne($item);
            if (!$jobInfo) {
                self::log('跳过职位ID：' . $item);
                continue;
            }
            self::log('更新职位原始数据：' . json_encode($jobInfo->attributes));
            $jobInfo->apply_type    = '';
            $jobInfo->apply_address = '';
            $jobInfo->delivery_type = 2;
            $jobInfo->delivery_way  = 1;
            if ($jobInfo->save()) {
                self::log('更新职位数据成功：' . json_encode($jobInfo->attributes));
            } else {
                self::log('更新职位数据失败：' . json_encode($jobInfo->attributes));
            }
        }
        self::log('更新职位数据结束');
        self::log('更新公告数据开始');
        //56297
        $announcementInfo = BaseAnnouncement::findOne(56297);
        if ($announcementInfo) {
            self::log('更新公告原始数据：' . json_encode($announcementInfo->attributes));
            $announcementInfo->apply_type    = '';
            $announcementInfo->apply_address = '';
            $announcementInfo->delivery_type = 2;
            $announcementInfo->delivery_way  = 1;
            if ($announcementInfo->save()) {
                self::log('更新公告数据成功：' . json_encode($announcementInfo->attributes));
            } else {
                self::log('更新公告数据失败：' . json_encode($announcementInfo->attributes));
            }
        }
        self::log('更新公告数据结束');

        //数据修复  自主录入
        $data1 = BaseJobApplyRecord::find()
            ->where(['source' => 3])
            ->asArray()
            ->all();
        foreach ($data1 as $item) {
            $info1 = BaseJobApplyRecord::findOne($item['id']);
            if (!$info1) {
                self::log('跳过ID：' . $item['id']);
                continue;
            }
            self::log('更新投递1原始数据：' . json_encode($item));
            $info1->delivery_way = 99;
            if ($info1->save()) {
                self::log('更新投递1数据成功：' . json_encode($item));
            } else {
                self::log('更新投递1数据失败：' . json_encode($item));
            }
        }
        //数据修复 站内+网址
        $data2 = BaseJobApplyRecord::find()
            ->where([
                'delivery_way'  => 3,
                'delivery_type' => 2,
            ])
            ->asArray()
            ->all();

        foreach ($data2 as $item) {
            $info2 = BaseJobApplyRecord::findOne($item['id']);
            if (!$info2) {
                self::log('跳过ID：' . $item['id']);
                continue;
            }
            self::log('更新投递2原始数据：' . json_encode($item));
            $info2->delivery_way = 1;
            if ($info2->save()) {
                self::log('更新投递2原始数据成功：' . json_encode($item));
            } else {
                self::log('更新投递2原始数据出错：' . json_encode($item));
            }
        }
    }

    /**
     * http://zentao.jugaocai.com/index.php?m=story&f=view&id=269&tid=dx91zmig
     *
     * 需求描述
     * 现有职位类型无法满足常规内容输出，需对【职位类型】、【福利待遇】字典内容修改、增加。
     *
     * 1. 表中【职位类型】涉及1、2级职位修改，2级职位新增。
     *
     * 2. 修改字典表时，前后端涉及职位类型展示、筛选页面也许同步调整展示；
     *
     * 3. 修改完字典表后请即时反馈，因为后续还涉及栏目公告职位调用规则以及职位批量录入模板调整。
     *
     * 这个脚本主要是用于更新福利标签的
     */
    public function actionZentao269()
    {
        $addList = [
            '租房补贴',
            '五险二金',
            '六险一金',
            '六险二金',
            '生活补贴',
            '随迁入户',
            '配偶工作安排',
            '子女入学安排',
            '社保',
            '科研资金',
            '带薪寒暑假',
            '安家补贴',
            '降温补贴',
            '年金',
            '育儿假',
        ];

        $updateList = [
            [
                'oldName' => '定期体检',
                'newName' => '定期/免费体检',
            ],
        ];

        // 循环新增的
        foreach ($addList as $item) {
            $name = $item;
            // 找是否在福利表

            // 开启事务
            $transaction = Yii::$app->db->beginTransaction();
            try {
                $rs = BaseWelfareLabel::findOne(['name' => $item]);
                if ($rs) {
                    if ($rs->is_system == 1) {
                        continue;
                    }
                    //这里需要删除和单位的联系?然后把福利标签升级为系统标签
                    BaseCompanyWelfareLabelRelationship::deleteAll(['welfare_label_id' => $rs->id]);
                    $rs->is_system = 1;
                    if ($rs->save()) {
                        self::log('更新福利标签成功：' . $name);
                    } else {
                        self::log('更新福利标签失败：' . $rs->getFirstErrorsMessage());
                    }
                } else {
                    $model            = new BaseWelfareLabel();
                    $model->name      = $name;
                    $model->status    = 1;
                    $model->is_delete = 0;
                    $model->is_system = 1;
                    if ($model->save()) {
                        self::log('新增福利标签成功：' . $name);
                    } else {
                        self::log('新增福利标签失败：' . $model->getFirstErrorsMessage());
                    }
                }
                $transaction->commit();
            } catch (Exception $e) {
                $transaction->rollBack();
                self::log($e->getMessage());
            }
        }

        // 循环更新的
        foreach ($updateList as $item) {
            $rs = BaseWelfareLabel::findOne(['name' => $item['oldName']]);
            if ($rs) {
                $rs->name = $item['newName'];
                if ($rs->save()) {
                    self::log('修改福利标签成功：' . $rs->id);
                }
            }
        }
    }

    /**
     * 修改栏目的地区属性
     * php timer_yii script/update-home-column-attribute
     */
    public function actionUpdateHomeColumnAttribute()
    {
        //首先拿一下地区一级的数据
        $regionList = BaseHomeColumn::find()
            ->select([
                'name',
            ])
            ->where([
                '>',
                'id',
                10,
            ])
            ->andWhere([
                '<',
                'id',
                119,
            ])
            ->asArray()
            ->all();

        $suffixList = [
            '高校',
            '科研',
            '机关事业',
            '中小学',
            '医学',
            '企业',
        ];

        $allList = [];
        foreach ($regionList as $value) {
            foreach ($suffixList as $item) {
                $allList[] = $value['name'] . $item;
            }
        }

        foreach ($allList as $item) {
            $rs = BaseHomeColumn::findOne(['name' => $item]);
            if ($rs) {
                $rs->operate_attribute = BaseHomeColumn::OPERATE_ATTRIBUTE_AREA_SECOND;
                if ($rs->save()) {
                    self::log('修改地区栏目属性成功：' . $item);
                }
            }
        }
    }

    /**
     * 同步中间表数据s
     * @throws Exception
     * php timer_yii script/update-migration-data
     */
    public function actionUpdateMigrationData()
    {
        //获取所有职位
        $jobList = BaseJob::find()
            ->select(['id'])
            ->asArray()
            ->all();
        foreach ($jobList as $jobItem) {
            $jobId = $jobItem['id'];
            $model = new JobAutoClassify($jobId);
            self::log('开始职位ID：' . $jobId . ',同步职位类型');
            $model->updateJobCategoryTable();
            self::log('结束职位ID：' . $jobId . ',同步职位类型');
            self::log('开始职位ID：' . $jobId . ',同步职位专业');
            $model->updateJobMajorTable();
            self::log('结束职位ID：' . $jobId . ',同步职位专业');
            self::log('开始职位ID：' . $jobId . ',同步职位福利');
            $model->updateJobWelfareTable();
            self::log('结束职位ID：' . $jobId . ',同步职位福利');
        }
    }

    /**
     * 由于简历服务类的错误录入，导致1.1.6上线后，学术成果统计错误，这里需要一个方法，来更新简历完成模块统计表的数据
     */
    public function actionUpdateResumeCompleteRecord()
    {
        $allRecordAmount = BaseResumeComplete::find()
            ->count();
        $loopAmount      = ceil($allRecordAmount / 100);
        self::log('更新总数据量：' . $allRecordAmount . ',循环次数：' . $loopAmount);

        for ($i = 1; $i < $loopAmount + 1; $i++) {
            self::log("取出第{$i}组数据");
            $memberIdArr = BaseResumeComplete::find()
                ->select(['member_id'])
                ->asArray()
                ->offset(($i - 1) * 100)
                ->limit(100)
                ->orderBy('id asc')
                ->all();
            self::log("开始第{$i}组数据更新");
            foreach ($memberIdArr as $data) {
                $memberId = $data['member_id'];
                self::log('memberId = ' . $memberId . '开始执行');
                $oldRecord = BaseResumeComplete::find()
                    ->where(['member_id' => $memberId])
                    ->asArray()
                    ->one();
                self::log("memberId:{$oldRecord['member_id']},旧纪录内容：basic:{$oldRecord['basic']},education:{$oldRecord['education']},research_direction:{$oldRecord['research_direction']},work:{$oldRecord['work']},intention:{$oldRecord['intention']},academic_page:{$oldRecord['academic_page']},academic_patent:{$oldRecord['academic_patent']},academic_book:{$oldRecord['academic_book']},research_project:{$oldRecord['research_project']},academic_reward:{$oldRecord['academic_reward']},other_reward:{$oldRecord['other_reward']},certificate:{$oldRecord['certificate']},skill:{$oldRecord['skill']},advantage:{$oldRecord['advantage']},other_skill:{$oldRecord['other_skill']}");
                if (!empty($memberId)) {
                    $completeRecordList = BaseResumeComplete::getResumeCompleteRecordInfo($memberId);

                    $model = BaseResumeComplete::findOne(['member_id' => $memberId]);
                    foreach ($completeRecordList as $field => $amount) {
                        $model->$field = $amount;
                    }
                    self::log("memberId:{$oldRecord['member_id']},新纪录内容：basic:{$completeRecordList['basic']},education:{$completeRecordList['education']},research_direction:{$completeRecordList['research_direction']},work:{$completeRecordList['work']},intention:{$completeRecordList['intention']},academic_page:{$completeRecordList['academic_page']},academic_patent:{$completeRecordList['academic_patent']},academic_book:{$completeRecordList['academic_book']},research_project:{$completeRecordList['research_project']},academic_reward:{$completeRecordList['academic_reward']},other_reward:{$completeRecordList['other_reward']},certificate:{$completeRecordList['certificate']},skill:{$completeRecordList['skill']},advantage:{$completeRecordList['advantage']},other_skill:{$completeRecordList['other_skill']}");
                    if (!$model->save()) {
                        throw new \yii\db\Exception('更新简历完成表失败' . $model->getFirstErrorsMessage());
                    }
                    self::log('memberId = ' . $memberId . '执行完毕');
                }
            }
        }
    }

    /**
     * 修复简历重复记录完成度表记录问题
     * @return void
     * @throws \yii\db\Exception
     * php timer_yii script/update-resume-complete-repeat-record
     */
    public function actionUpdateResumeCompleteRepeatRecord()
    {
        $allRecord = BaseResumeComplete::find()
            ->where(['member_id' => 0])
            ->select(['resume_id'])
            ->column();
        self::log('需要更新数量：' . count($allRecord) . '条');
        foreach ($allRecord as $resumeId) {
            //判断当前记录是否有2条，如果是，执行更新，删除member_id为0的记录，如果不是，特殊情况，进行记录
            $memberId            = BaseResume::findOneVal(['id' => $resumeId], 'member_id');
            $resumeCompleteCount = BaseResumeComplete::find()
                ->where(['resume_id' => $resumeId])
                ->count();
            if ($resumeCompleteCount > 1) {
                //删除旧记录，重新统计
                BaseResumeComplete::deleteAll([
                    'resume_id' => $resumeId,
                    'member_id' => 0,
                ]);
                self::log('删除简历id：' . $resumeId . '，多余的memberId=0记录');
                //统计更新
                $oldRecord = BaseResumeComplete::find()
                    ->where(['resume_id' => $resumeId])
                    ->asArray()
                    ->one();
                self::log("resumeId:{$resumeId},旧纪录内容：basic:{$oldRecord['basic']},education:{$oldRecord['education']},research_direction:{$oldRecord['research_direction']},work:{$oldRecord['work']},intention:{$oldRecord['intention']},academic_page:{$oldRecord['academic_page']},academic_patent:{$oldRecord['academic_patent']},academic_book:{$oldRecord['academic_book']},research_project:{$oldRecord['research_project']},academic_reward:{$oldRecord['academic_reward']},other_reward:{$oldRecord['other_reward']},certificate:{$oldRecord['certificate']},skill:{$oldRecord['skill']},advantage:{$oldRecord['advantage']},other_skill:{$oldRecord['other_skill']}");
                if (!empty($memberId)) {
                    $completeRecordList = BaseResumeComplete::getResumeCompleteRecordInfo($memberId);

                    $model = BaseResumeComplete::findOne([
                        'member_id' => $memberId,
                        'resume_id' => $resumeId,
                    ]);
                    if (!empty($model)) {
                        foreach ($completeRecordList as $field => $amount) {
                            $model->$field = $amount;
                        }
                        self::log("resumeId:{$resumeId},新纪录内容：basic:{$completeRecordList['basic']},education:{$completeRecordList['education']},research_direction:{$completeRecordList['research_direction']},work:{$completeRecordList['work']},intention:{$completeRecordList['intention']},academic_page:{$completeRecordList['academic_page']},academic_patent:{$completeRecordList['academic_patent']},academic_book:{$completeRecordList['academic_book']},research_project:{$completeRecordList['research_project']},academic_reward:{$completeRecordList['academic_reward']},other_reward:{$completeRecordList['other_reward']},certificate:{$completeRecordList['certificate']},skill:{$completeRecordList['skill']},advantage:{$completeRecordList['advantage']},other_skill:{$completeRecordList['other_skill']}");
                        if (!$model->save()) {
                            throw new \yii\db\Exception('更新简历完成表失败' . $model->getFirstErrorsMessage());
                        }
                        //更新简历完整度
                        $resumeInfo           = BaseResume::findOne($resumeId);
                        $resumeInfo->complete = BaseResume::updateComplete($memberId);
                        if (!$resumeInfo->save()) {
                            throw new \yii\db\Exception('更新简历表完成度失败' . $model->getFirstErrorsMessage());
                        }
                        self::log('resumeId = ' . $resumeId . '，更新简历完整度');
                    }

                    self::log('resumeId = ' . $resumeId . '执行完毕');
                }
            } else {
                self::log('简历id：' . $resumeId . '，仅有一条记录，且memberId=0');
            }
        }
    }

    /**
     * 删除没有内容的研究方向记录
     * @return void
     */
    public function actionDelEmptyContentDirection()
    {
        $recordList = BaseResumeResearchDirection::find()
            ->where(['content' => ''])
            ->orWhere(['content' => null])
            ->asArray()
            ->all();
        $amount     = count($recordList);
        self::log('需要处理数据条数：' . $amount);
        foreach ($recordList as $record) {
            self::log('开始处理id：' . $record['id']);
            self::log("数据内容id：{$record['id']},resume_id:{$record['resume_id']},member_id:{$record['member_id']}");
            BaseResumeResearchDirection::deleteAll(['id' => $record['id']]);
            self::log('删除完毕，id：' . $record['id']);
        }
    }

    /**
     * 修复中间表job_major_relation
     * PHP timer_yii script/insert-level-data
     */
    public function actionInsertLevelData()
    {
        //获取所有二三级数据
        $data = BaseJobMajorRelation::find()
            ->andWhere([
                'level' => [
                    2,
                    3,
                ],
            ])
            ->asArray()
            ->all();

        foreach ($data as $item) {
            self::log('开始处理id：' . $item['id'] . '，数据源：' . json_encode($item));
            if ($item['level'] == 2) {
                //获取上级major
                $parent_id   = BaseMajor::findOneVal(['id' => $item['major_id']], 'parent_id');
                $parent_info = BaseJobMajorRelation::findOne([
                    'job_id'   => $item['job_id'],
                    'major_id' => $parent_id,
                    'level'    => 1,
                ]);
                if (!$parent_info) {
                    $jobMajorModel                  = new BaseJobMajorRelation();
                    $jobMajorModel->announcement_id = $item['announcement_id'];
                    $jobMajorModel->job_id          = $item['job_id'];
                    $jobMajorModel->major_id        = $parent_id;
                    $jobMajorModel->level           = 1;
                    $jobMajorModel->save();
                }
            } elseif ($item['level'] == 3) {
                //获取上级major
                $parent_id     = BaseMajor::findOneVal(['id' => $item['major_id']], 'parent_id');
                $parent_info_1 = BaseJobMajorRelation::findOne([
                    'job_id'   => $item['job_id'],
                    'major_id' => $parent_id,
                    'level'    => 2,
                ]);
                if (!$parent_info_1) {
                    $jobMajorModel                  = new BaseJobMajorRelation();
                    $jobMajorModel->announcement_id = $item['announcement_id'];
                    $jobMajorModel->job_id          = $item['job_id'];
                    $jobMajorModel->major_id        = $parent_id;
                    $jobMajorModel->level           = 2;
                    $jobMajorModel->save();
                }
                $parent_parent_id = BaseMajor::findOneVal(['id' => $parent_id], 'parent_id');
                $parent_info_1    = BaseJobMajorRelation::findOne([
                    'job_id'   => $item['job_id'],
                    'major_id' => $parent_parent_id,
                    'level'    => 1,
                ]);
                if (!$parent_info_1) {
                    $jobMajorModel                  = new BaseJobMajorRelation();
                    $jobMajorModel->announcement_id = $item['announcement_id'];
                    $jobMajorModel->job_id          = $item['job_id'];
                    $jobMajorModel->major_id        = $parent_parent_id;
                    $jobMajorModel->level           = 1;
                    $jobMajorModel->save();
                }
            }
            self::log('结束处理id：' . $item['id']);
        }
    }

    /**
     * 更新地区表大区信息
     * php timer_yii script/update-area-region
     */
    public function actionUpdateAreaRegion()
    {
        /**
         *
         * 华东  江苏、浙江、上海、福建、安徽、江西、山东、台湾
         * 华中  湖北、河南、湖南
         * 华北  北京、天津、河北、山西、内蒙古
         * 西南  四川、贵州、云南、重庆、西藏
         * 华南  广东、海南、广西、香港、澳门
         * 东北  黑龙江、吉林、辽宁
         * 西北  陕西、甘肃、宁夏、新疆、青海
         */
        // 首先从地区表找到上面对应的
        $config = [
            ['华东' => '江苏、浙江、上海、福建、安徽、江西、山东、台湾'],
            ['华中' => '湖北、河南、湖南'],
            ['华北' => '北京、天津、河北、山西、内蒙古'],
            ['西南' => '四川、贵州、云南、重庆、西藏'],
            ['华南' => '广东、海南、广西、香港、澳门'],
            ['东北' => '黑龙江、吉林、辽宁'],
            ['西北' => '陕西、甘肃、宁夏、新疆、青海'],
        ];

        foreach ($config as $item) {
            foreach ($item as $key => $value) {
                // 找到key真的值
                $regionList     = BaseArea::REGION_LIST;
                $region         = array_search($key, $regionList);
                $areaLevelArray = explode('、', $value);
                $area           = BaseArea::find()
                    ->where([
                        'name'  => $areaLevelArray,
                        'level' => 1,
                    ])
                    ->all();
                if ($area) {
                    foreach ($area as $item1) {
                        $item1->region = $region;
                        $item1->save();
                        ob_flush();
                        flush();
                        echo $item1->name . '更新成功' . PHP_EOL;
                        $area2 = BaseArea::find()
                            ->where([
                                'parent_id' => $item1->id,
                                'level'     => 2,
                            ])
                            ->all();
                        foreach ($area2 as $item2) {
                            $item2->region = $region;
                            $item2->save();
                            // 实时输出一个更新成功
                            ob_flush();
                            flush();
                            echo $item2->name . '更新成功' . PHP_EOL;
                            // 找到这些level2的level3,也更新为对应的region
                            $area3 = BaseArea::find()
                                ->where([
                                    'parent_id' => $item2->id,
                                    'level'     => 3,
                                ])
                                ->all();
                            foreach ($area3 as $item3) {
                                $item3->region = $region;
                                $item3->save();
                                // 实时输出一个更新成功
                                ob_flush();
                                flush();
                                echo $item3->name . '更新成功' . PHP_EOL;
                            }
                        }
                    }
                }
            }
        }

        exit;
    }

    /**
     * 扩充简历业务表
     * 更新简历意向地区中间表
     * 更新教育经历表的专业字段
     * php timer_yii script/update-resume-expansion
     */
    public function actionUpdateResumeExpansion()
    {
        $data = BaseResume::find()
            ->select(['id'])
            ->andWhere(['status' => BaseResume::STATUS_ACTIVE])
            ->asArray()
            ->all();
        foreach ($data as $item) {
            //处理中间表
            self::log('开始处理简历ID：' . $item['id']);
            $service = new ResumeAfterService();
            $service->run($item['id']);
            self::log('结束处理简历ID：' . $item['id'] . PHP_EOL);
        }
    }

    /**
     * 补发投递邮件
     * php timer_yii script/sub-send-email
     */
    public function actionSubSendEmail()
    {
        exit('已执行');
        //投递ID数组
        //
        $apply_arr_id = [
            247230,
            247228,
            247127,
            246975,
            246950,
            246775,
            246763,
            246748,
            246724,
            246699,
            246406,
            246315,
            246164,
            246159,
            245972,
            245868,
            245854,
            245770,
            245744,
            245568,
            245484,
            245458,
            245457,
            245441,
            245398,
            245393,
            245361,
            245306,
            245201,
            244955,
            244932,
            244911,
            244893,
            244805,
            244779,
            244770,
            244533,
            244500,
            244473,
            244373,
            244311,
            244271,
            244219,
            244217,
            244154,
            244149,
            244089,
            244042,
            243976,
            243917,
            243890,
            243837,
            243796,
            243735,
            243719,
            243701,
            243671,
            243607,
            243379,
            241591,
            241578,
            241168,
            240520,
            240454,
            240409,
            240362,
            240040,
            240026,
            239905,
            239574,
            239415,
            239293,
            238071,
            238059,
            237794,
            237756,
            237742,
            237606,
            237190,
            236993,
            236630,
            236299,
            236264,
            236232,
            236226,
            235850,
            235667,
            235622,
            235085,
            234490,
            234291,
            234132,
            234069,
            233872,
            233869,
            233830,
            233788,
            233732,
            233731,
            233730,
            233724,
            233380,
            233368,
            233364,
            233329,
            233010,
            232225,
            232221,
            232099,
            231614,
            231441,
            231351,
            230756,
            230098,
            229897,
            229740,
            229292,
            228893,
            228892,
            228565,
            228264,
            228124,
            227291,
            227223,
            227163,
            227009,
            226786,
            226769,
            226726,
            226336,
            226317,
            226293,
            226292,
            226099,
            225836,
            225708,
            225184,
            224785,
            224783,
            224719,
            224626,
            224409,
            224096,
            224081,
            224041,
            224040,
            223406,
            223374,
            223105,
            222903,
            222692,
            222677,
            222522,
            222377,
            222355,
            222327,
            222195,
            222036,
            221873,
            221828,
            221511,
            221436,
            221239,
            221186,
            221050,
            221010,
            220984,
            220932,
            220846,
            220844,
            220841,
            220819,
            220803,
            220760,
            220513,
            220345,
            220273,
            220259,
            220048,
            219813,
            219809,
            219807,
            219629,
            219624,
            219592,
            219086,
            219072,
            218980,
            218957,
            218923,
            218803,
            218789,
            218783,
            218762,
            218728,
            218513,
            218512,
            218510,
            218501,
            218131,
            217702,
            217530,
            217528,
            217114,
            217095,
            216936,
            216193,
            215793,
            215765,
            215764,
            213882,
            213871,
            213867,
            213771,
            213649,
            213372,
            213048,
            212911,
            212910,
            211874,
            211645,
            211621,
            211197,
            211093,
            210789,
            210197,
            209275,
            209164,
            209163,
            209162,
            209161,
            208282,
        ];
        foreach ($apply_arr_id as $apply_id) {
            //获取投递数据
            $all_apply_info = BaseJobApplyRecord::findOne(['apply_id' => $apply_id]);
            //获取投递数据
            $apply_info = BaseJobApply::findOne($apply_id);
            //简历信息
            $resume_info = BaseResume::findOne($all_apply_info->resume_id);
            //单位信息
            $company_info = BaseCompany::findOne($all_apply_info->company_id);
            //职位信息
            $job_info = BaseJob::findOne($all_apply_info->job_id);
            //公告信息
            $email = $job_info->apply_address;
            if ($job_info->announcement_id > 0 && $job_info->delivery_type == 0) {
                $announcement_info = BaseAnnouncement::findOne($job_info->announcement_id);
                $email             = $announcement_info->apply_address;
            }
            $data = [
                'resumeId'      => $resume_info->id,
                'name'          => $company_info->full_name,
                'resumeName'    => $resume_info->name,
                'jobId'         => $all_apply_info->job_id,
                'jobName'       => $job_info->name,
                'department'    => $job_info->department,
                'cityName'      => BaseArea::getAreaName($job_info->city_id),
                'educationName' => BaseDictionary::getEducationName($job_info->education_type),
                'jobApplyId'    => $apply_id,
            ];
            if ($apply_info->resume_attachment_id) {
                $data['resumeAttachmentId'] = $apply_info->resume_attachment_id;
            }
            if ($apply_info->stuff_file_id) {
                $data['stuffFileId'] = $apply_info->stuff_file_id;
            }
            if ($email) {
                self::log('投递ID：' . $apply_id . ',组装数据为:' . json_encode($data));
                Producer::email($email, BaseMember::TYPE_COMPANY, EmailQueue::EMAIL_POST_DELIVERY, $data);
                self::log('投递ID：' . $apply_id . ',推送队列成功');
            }
        }
    }

    /**
     * 批量跑一下近30天活跃的人的求职意向
     * php timer_yii script/active-person-match
     */
    public function actionActivePersonMatch()
    {
        //获取所有近30天活跃的用户
        $data = BaseMember::find()
            ->alias('m')
            ->select(['r.id'])
            ->leftJoin(['r' => BaseResume::tableName()], 'm.id=r.member_id')
            ->andWhere(['r.status' => BaseResume::STATUS_ACTIVE])
            ->andWhere([
                '>',
                'm.last_active_time',
                date('Y-m-d H:i:s', strtotime('-1 day')),
            ])
            ->asArray()
            ->all();
        foreach ($data as $item) {
            self::log('正在准备推送简历ID为：' . $item['id'] . '去到队列中');
            Producer::personResumeIntentionMatchJob($item['id']);
            self::log('推送简历ID为：' . $item['id'] . '已推送完成');
        }
    }

    /**
     * 初始化
     * php timer_yii script/init-meilisearch-announcement
     */
    public function actionInitMeilisearchAnnouncement()
    {
        self::log('开始查询全部公告');
        //
        // $ms   = \Yii::$app->meilisearch;
        // $msci = $ms->createCommand();
        //
        // $index = $msci->index('announcement');
        // $index->deleteAllDocuments();
        // return;

        BaseArticle::openDb2();
        $allList = BaseAnnouncement::find()
            ->alias('a')
            ->select([
                'a.id',
                'a.title',
                'b.content',
            ])
            ->innerJoin(['b' => BaseArticle::tableName()], 'b.id = a.article_id')
            ->asArray()
            ->all();

        self::log('总共有' . count($allList) . '个公告');
        // 每1000个写一次
        $allList = array_chunk($allList, 1000);

        self::log('总共有' . count($allList) . '个分组');

        $ms = \Yii::$app->meilisearch;

        foreach ($allList as $k => $list) {
            // 百分比
            foreach ($list as &$item) {
                // content去掉html信息
                $item['content'] = strip_tags($item['content']);
                // 去掉空格等符号
                $item['content'] = preg_replace('/\s/', '', $item['content']);
            }

            // Set API keys From your master Key
            // $ms->apiKey = 'xxxx';

            try {
                $msci = $ms->createCommand();

                $index = $msci->index('announcement');
                // 全部删除
                $taskId = $index->addDocuments($list, 'id');

                // 成功就sleep(3)
                sleep(3);
            } catch (\Exception $e) {
                self::log($e->getMessage());
            }

            self::log(var_export($taskId, true));

            self::log('当前进度' . (($k + 1) / count($allList)) * 100 . '%');
        }
    }

    /**
     * 初始化
     * php timer_yii script/upload-resume-to-oss
     */
    public function actionUploadResumeToOss()
    {
        $list = BaseResumeAttachment::find()
            ->select('id,file_url')
            ->where([
                'is_upload_oss' => 2,
            ])
            ->asArray()
            ->limit(1000)
            ->all();

        // 每次1000个算了
        self::log('总共有' . count($list) . '个简历');

        foreach ($list as $k => $item) {
            $realPath = BaseResumeAttachment::getRealPath($item['file_url']);
            if (!file_exists($realPath)) {
                self::log('文件不存在:' . $realPath);
                continue;
            }

            BaseResumeAttachment::uploadToOss($item['id']);
            self::log('上传成功:' . $item['id']);
            // 报一下进度百分比
            self::log('当前进度' . (($k + 1) / count($list)) * 100 . '%');
        }
    }

    /**
     * 邮箱绑定字段【email_register_status】修复
     * php timer_yii script/update-member-email-register-status
     */
    public function actionUpdateMemberEmailRegisterStatus()
    {
        // (source_type = 1, create_admin_id = 0) create_admin_id => member_id
        $companyMemberData = BaseCompany::find()
            ->where([
                '=',
                'source_type',
                1,
            ])
            ->andWhere([
                '=',
                'create_admin_id',
                0,
            ])
            ->all();

        foreach ($companyMemberData as $item) {
            self::log('1创建账号-待修复-数据id：' . $item['member_id']);
            $item->create_admin_id = $item['member_id'];
            $item->save();
        }

        $companyMemberCount = count($companyMemberData);
        self::log('1创建账号-待修复-数量：' . "{$companyMemberCount}个");

        // (email != '', is_cooperation = 1, type = 2) email_register_status = 1
        $companyMemberIds = BaseCompany::find()
            ->alias('c')
            ->select('m.id')
            ->leftJoin(['m' => BaseMember::tableName()], 'c.member_id = m.id')
            ->where([
                '!=',
                'm.email',
                '',
            ])
            ->andWhere([
                '=',
                'm.type',
                2,
            ])
            ->andWhere([
                '=',
                'c.is_cooperation',
                1,
            ])
            ->asArray()
            ->all();

        $companyMemberIds   = array_column($companyMemberIds, 'id');
        $companyMemberCount = count($companyMemberIds);

        self::log('2创建账号-待修复-数量：' . "{$companyMemberCount}个");
        self::log('2创建账号-待修复-数据：' . json_encode($companyMemberIds));

        $res = BaseMember::updateAll(['email_register_status' => 1], [
            'in',
            'id',
            $companyMemberIds,
        ]);

        self::log('2创建账号已修复-数量：' . "{$res}个");

        // (email_register_status = 0) email_register_status => -1
        $memberIds = BaseMember::find()
            ->select('id')
            ->where([
                '=',
                'email_register_status',
                0,
            ])
            ->asArray()
            ->all();

        $memberIds   = array_column($memberIds, 'id');
        $memberCount = count($memberIds);

        self::log('3创建账号-待修复-数量：' . "{$memberCount}个");
        self::log('3创建账号-待修复-数据：' . json_encode($memberIds));

        $res = BaseMember::updateAll(['email_register_status' => -1], [
            'in',
            'id',
            $memberIds,
        ]);

        self::log('3创建账号-已修复-数量：' . "{$res}个");
    }

    /**
     * 地区栏目新增小程广告位
     *  php timer_yii script/carry-home-column-area-to-position
     */
    public function actionCarryHomeColumnAreaToPosition()
    {
        $needData = BaseHomeColumn::find()
            ->select([
                'name',
                'id',
            ])
            ->where([
                '>=',
                'id',
                11,
            ])
            ->andWhere([
                '<=',
                'id',
                118,
            ])
            ->asArray()
            ->all();

        //这里与原来栏目不一致的，产品要特殊
        array_push($needData, [
            'id'   => 0,
            'name' => '海外',
        ]);
        foreach ($needData as $item) {
            if ($item['id'] == 110) {
                $item['name'] = $item['name'] . '市';
            }
            $pinyin = PingYinHelper::get($item['name']);
            if (substr_count($item['name'], '陕西') > 0) {
                $pinyin = str_replace('shan', 'shaan', $pinyin);
            }
            if (substr_count($item['name'], '台州') > 0) {
                $pinyin = str_replace('zhou', 'jiu', $pinyin);
            }

            $number1 = 'mini_diqu_' . $pinyin . '_HF';
            $number2 = 'mini_diqu_' . $pinyin . '_toutiao';
            $adminId = 1;

            $temp1 = [
                'platform_type' => BaseHomePosition::PLATFORM_MINI_APP,
                'number'        => $number1,
                'name'          => '地区_' . $item['name'] . '_HF',
                'width'         => '',
                'height'        => '',
                'status'        => BaseActiveRecord::STATUS_ACTIVE,
                'describe'      => '',
            ];

            $temp2 = [
                'platform_type' => BaseHomePosition::PLATFORM_MINI_APP,
                'number'        => $number2,
                'name'          => '地区_' . $item['name'] . '_头条',
                'width'         => '',
                'height'        => '',
                'status'        => BaseActiveRecord::STATUS_ACTIVE,
                'describe'      => '',
            ];

            if (BaseHomePosition::findOne(['number' => $number1])) {
                self::log($number1 . '该广告位已存在');
            } else {
                HomePosition::addHomePosition($temp1, $adminId);
                self::log('新增' . $item['name'] . '顶部banner广告位:' . $number1);
            }

            if (BaseHomePosition::findOne(['number' => $number2])) {
                self::log($number2 . '该广告位已存在');
            } else {
                HomePosition::addHomePosition($temp2, $adminId);
                self::log('新增' . $item['name'] . '通知栏广告位:' . $number2);
            }
        }
    }

    /***
     * 修复公告发布时间refresh_date字段脚本
     * php timer_yii script/fix-announcement-refresh-date
     */
    public function actionFixAnnouncementRefreshDate()
    {
        //Announcement表连article表

        $list = BaseAnnouncement::find()
            ->alias('a')
            ->select('b.id,b.refresh_date,b.refresh_time')
            ->leftJoin(['b' => BaseArticle::tableName()], 'a.id = b.id')
            ->andWhere('b.refresh_date!=DATE_FORMAT(b.refresh_time,"%Y-%m-%d")')
            ->asArray()
            ->all();

        foreach ($list as $k => $item) {
            $article = BaseArticle::findOne($item['id']);
            if (!$article) {
                self::log('公告不存在:' . $item['id']);
                continue;
            }

            $article->refresh_date = date('Y-m-d', strtotime($item['refresh_time']));
            $article->save();
            self::log('更新成功:' . $item['id'] . '-' . $item['refresh_time'] . '-' . $item['refresh_date'] . '-' . $article->refresh_date);
        }
    }

    /***
     * 修复数据-单位是否被小程序调用
     * php timer_yii script/mini-company-tag
     */
    public function actionMiniCompanyTag()
    {
        //获取所有单位列表
        $companyList = BaseCompany::find()
            ->select([
                'id',
            ])
            ->asArray()
            ->all();
        foreach ($companyList as $k => $item) {
            self::log('开始小程序规则调用单位ID：' . $item['id']);
            //调用小程序单位规则
            $model         = new RuleCompany();
            $res           = $model->exec($item['id']);
            $company_model = BaseCompany::findOne($item['id']);
            if ($res) {
                $company_model->is_miniapp = BaseCompany::IS_MINIAPP_YES;
            } else {
                $company_model->is_miniapp = BaseCompany::IS_MINIAPP_NO;
            }
            //写入表
            $company_model->save();
            self::log('结束小程序规则调用单位ID：' . $item['id'] . '，单位is_miniapp变成：' . $company_model->is_miniapp);
        }
    }

    /***
     * 修复数据-职位是否被小程序调用
     * php timer_yii script/mini-job-tag
     */
    public function actionMiniJobTag()
    {
        //获取所有职位列表
        $key     = 'SCRIPT:MINIJOBTAG:ID';
        $id      = Cache::get($key);
        $jobList = BaseJob::find()
            ->select([
                'id',
            ])
            ->andFilterWhere([
                '>',
                'id',
                $id,
            ])
            ->limit(10000)
            ->orderBy('id')
            ->asArray()
            ->all();
        foreach ($jobList as $k => $item) {
            self::log('开始小程序规则调用职位ID：' . $item['id']);
            //调用小程序职位规则
            $model     = new RuleJob();
            $res       = $model->exec($item['id']);
            $job_model = BaseJob::findOne($item['id']);
            if ($res) {
                $job_model->is_miniapp = BaseJob::IS_MINIAPP_YES;
            } else {
                $job_model->is_miniapp = BaseJob::IS_MINIAPP_NO;
            }
            //写入表
            $job_model->save();
            Cache::set($key, $item['id']);
            self::log('结束小程序规则调用职位ID：' . $item['id'] . '，职位is_miniapp变成：' . $job_model->is_miniapp);
        }
    }

    /*
     * 修复职位足迹的数据
     * php timer_yii script/update-job-footprint
     */
    public function actionUpdateJobFootprint()
    {
        $key = 'SCRIPT:JOB:FOOTPRINT:ID';
        // 找到职位点击日志里面全部含有memberId的
        $value = Cache::get($key);
        $id    = $value ?: 0;
        $list  = BaseJobClickLog::find()
            ->select([
                'id',
                'add_time',
                'member_id',
                'job_id',
            ])
            ->where([
                '>',
                'member_id',
                0,
            ])
            ->andWhere([
                '>',
                'id',
                $id,
            ])
            ->asArray()
            ->limit(10000)
            ->all();

        self::log('开始更新:' . count($list) . '条数据');
        foreach ($list as $k => $item) {
            // 更新的百分比
            $percent = round(($k + 1) / count($list) * 100, 2);
            self::log('更新进度:' . $percent . '%');
            $date     = date('Y-m-d', strtotime($item['add_time']));
            $resumeId = BaseResume::findOneVal([
                'member_id' => $item['member_id'],
            ], 'id');
            if (!$resumeId) {
                self::log('简历不存在:' . $item['member_id']);
                Cache::set($key, $item['id']);
                continue;
            }
            $model = BaseResumeJobFootprint::findOne([
                'resume_id' => $resumeId,
                'date'      => $date,
                'job_id'    => $item['job_id'],
            ]);

            if (!$model) {
                $model            = new BaseResumeJobFootprint();
                $model->resume_id = $resumeId;
                $model->date      = $date;
                $model->last_time = $item['add_time'];
                $model->job_id    = $item['job_id'];
                $model->save();
                self::log('新增成功:' . $item['id'] . '-' . $item['member_id'] . '-' . $item['job_id']);
            } else {
                // 对比一下时间是否比旧的时间大
                $oldTime = strtotime($model->last_time);
                $newTime = strtotime($item['add_time']);
                if ($newTime <= $oldTime) {
                    self::log('时间不对:' . $item['id'] . '-' . $item['member_id'] . '-' . $item['job_id']);
                } else {
                    $model->last_time = $item['add_time'];
                    $model->save();
                    self::log('更新成功:' . $item['id'] . '-' . $item['member_id'] . '-' . $item['job_id']);
                }
                Cache::set($key, $item['id']);
            }
        }
    }

    /***
     * 修复数据-公告是否被小程序调用
     * php timer_yii script/mini-announcement-tag
     */
    public function actionMiniAnnouncementTag()
    {
        $key = 'SCRIPT:MINIANNOUNCEMENTTAG:ID';
        $id  = Cache::get($key);
        //获取所有公告列表
        $announcementList = BaseAnnouncement::find()
            ->select([
                'id',
            ])
            ->andFilterWhere([
                '>',
                'id',
                $id,
            ])
            ->limit(10000)
            ->orderBy('id')
            ->asArray()
            ->all();
        foreach ($announcementList as $k => $item) {
            self::log('开始小程序规则调用公告ID：' . $item['id']);
            //调用小程序公告规则
            $model              = new RuleAnnouncement();
            $res                = $model->exec($item['id']);
            $announcement_model = BaseAnnouncement::findOne($item['id']);
            if ($res) {
                $announcement_model->is_miniapp = BaseAnnouncement::IS_MINIAPP_YES;
            } else {
                $announcement_model->is_miniapp = BaseAnnouncement::IS_MINIAPP_NO;
            }
            //写入表
            $announcement_model->save();
            Cache::set($key, $item['id']);
            self::log('结束小程序规则调用公告ID：' . $item['id'] . '，公告is_miniapp变成：' . $announcement_model->is_miniapp);
        }
    }

    /*
     * 修复公告足迹的数据
     * php timer_yii script/update-announcement-footprint
     */
    public function actionUpdateAnnouncementFootprint()
    {
        $key = 'SCRIPT:ANNOUNCEMENT:FOOTPRINT:ID';
        // 找到职位点击日志里面全部含有memberId的
        $value = Cache::get($key);
        $id    = $value ?: 0;
        $list  = BaseArticleClickLog::find()
            ->select([
                'id',
                'add_time',
                'member_id',
                'article_id',
            ])
            ->where([
                '>',
                'member_id',
                0,
            ])
            ->andWhere([
                '>',
                'id',
                $id,
            ])
            ->asArray()
            ->limit(10000)
            ->all();

        self::log('开始更新:' . count($list) . '条数据');
        foreach ($list as $k => $item) {
            // 更新的百分比
            $percent = round(($k + 1) / count($list) * 100, 2);
            self::log('更新进度:' . $percent . '%');
            $announcementId = BaseAnnouncement::findOneVal([
                'article_id' => $item['article_id'],
            ], 'id');
            if (!$announcementId) {
                self::log('公告不存在:' . $item['article_id']);
                Cache::set($key, $item['id']);
                continue;
            }
            $date     = date('Y-m-d', strtotime($item['add_time']));
            $resumeId = BaseResume::findOneVal([
                'member_id' => $item['member_id'],
            ], 'id');
            if (!$resumeId) {
                self::log('简历不存在:' . $item['member_id']);
                Cache::set($key, $item['id']);
                continue;
            }
            $model = BaseResumeAnnouncementFootprint::findOne([
                'resume_id'       => $resumeId,
                'date'            => $date,
                'announcement_id' => $announcementId,
            ]);

            if (!$model) {
                $model                  = new BaseResumeAnnouncementFootprint();
                $model->resume_id       = $resumeId;
                $model->date            = $date;
                $model->last_time       = $item['add_time'];
                $model->announcement_id = $announcementId;
                $model->save();
                self::log('新增成功:' . $item['id'] . '-' . $item['member_id'] . '-' . $announcementId);
            } else {
                // 对比一下时间是否比旧的时间大
                $oldTime = strtotime($model->last_time);
                $newTime = strtotime($item['add_time']);
                if ($newTime <= $oldTime) {
                    self::log('时间不对:' . $item['id'] . '-' . $item['member_id'] . '-' . $announcementId);
                } else {
                    $model->last_time = $item['add_time'];
                    $model->save();
                    self::log('更新成功:' . $item['id'] . '-' . $item['member_id'] . '-' . $announcementId);
                }
                Cache::set($key, $item['id']);
            }
        }
    }

    /***
     * 修复双一流高校
     * php timer_yii script/fix-is-project-school
     */
    public function actionFixIsProjectSchool()
    {
        $sql = "SELECT
                r.id,
                r.`name`,
                r.is_project_school,
                re.resume_id,
                re.is_project_school 
                FROM
                `resume` r
                LEFT JOIN resume_education re ON r.id = re.resume_id 
                WHERE
                r.is_project_school = 2 
                AND re.is_project_school = 1 
                AND re.`status` = 1 
                GROUP BY
                r.id";

        // 找到对应的数据
        $list = Yii::$app->db->createCommand($sql)
            ->queryAll();

        self::log('一共找到' . count($list) . '条数据');

        foreach ($list as $item) {
            // 开始修复
            self::log('开始修复:' . $item['id'] . '-' . $item['name']);
            // 找到该求职者是否存在is_project_school
            $rs = BaseResumeEducation::find()
                ->where([
                    'resume_id' => $item['resume_id'],
                    'status'    => 1,
                ])
                ->andWhere([
                    'is_project_school' => 1,
                ])
                ->exists();

            if (!$rs) {
                self::log('修复失败:' . $item['id'] . '-' . $item['name']);
                continue;
            }

            $model = BaseResume::findOne($item['resume_id']);
            if (!$model) {
                self::log('修复失败:' . $item['id'] . '-' . $item['name']);
                continue;
            }

            $model->is_project_school = 1;
            $model->save();
            self::log('修复成功:' . $item['id'] . '-' . $item['name']);
        }
    }

    /***
     * 修复投递记录的平台字段
     * php timer_yii script/update-apply-platform
     */
    public function actionUpdateApplyPlatform()
    {
        //获取平台为0的投递记录
        $key  = Cache::get('update_apply_platform');
        $data = BaseJobApplyRecord::find()
            ->select([
                'id',
                'platform',
                'announcement_id',
                'job_id',
                'resume_id',
                'add_time',
            ])
            ->where(['platform' => 0])
            ->andFilterWhere([
                '>',
                'id',
                $key,
            ])
            ->limit(10000)
            ->asArray()
            ->all();
        //循环处理
        foreach ($data as $item) {
            $resume_info = BaseResume::findOne($item['resume_id']);
            //获取点击职位的最后一条记录
            $click_info = BaseJobClickLog::find()
                ->andWhere(['member_id' => $resume_info['member_id']])
                ->andWhere(['job_id' => $item['job_id']])
                ->andWhere([
                    '<',
                    'add_time',
                    $item['add_time'],
                ])
                ->orderBy('add_time desc')
                ->asArray()
                ->one();
            $model      = BaseJobApplyRecord::findOne($item['id']);
            if ($click_info) {
                $model->platform = $click_info['source'];
            } else {
                $click_info_p = BaseJobClickLog::find()
                    ->andWhere(['member_id' => $resume_info['member_id']])
                    ->andWhere([
                        '<',
                        'add_time',
                        $item['add_time'],
                    ])
                    ->orderBy('add_time desc')
                    ->asArray()
                    ->one();
                if ($click_info_p) {
                    $model->platform = $click_info_p['source'];
                } else {
                    //找一下公告
                    $announcement_info = BaseArticleClickLog::find()
                        ->andWhere(['member_id' => $resume_info['member_id']])
                        ->andWhere([
                            '<',
                            'add_time',
                            $item['add_time'],
                        ])
                        ->orderBy('add_time desc')
                        ->asArray()
                        ->one();
                    if ($announcement_info) {
                        $model->platform = $announcement_info['source'];
                    }
                }
            }
            $model->save();
            self::log('简历ID：' . $resume_info['id'] . '投递记录ID：' . $item['id'] . '平台：' . $model->platform);
            Cache::set('update_apply_platform', $item['id']);
        }
        echo '执行结束';
    }

    /***
     * 修复投递记录的平台字段
     * php timer_yii script/update-apply-platform-two
     */
    public function actionUpdateApplyPlatformTwo()
    {
        //获取平台为0的投递记录
        $data = BaseJobApplyRecord::find()
            ->select([
                'id',
                'platform',
                'announcement_id',
                'job_id',
                'resume_id',
                'add_time',
            ])
            ->where(['platform' => 0])
            ->asArray()
            ->all();
        //循环处理
        $i = 1;
        foreach ($data as $item) {
            $resume_info = BaseResume::findOne($item['resume_id']);
            $info        = BaseMemberActionLog::find()
                ->andWhere(['member_id' => $resume_info['member_id']])
                ->andWhere(['is_login' => 1])
                ->andWhere([
                    '<',
                    'add_time',
                    $item['add_time'],
                ])
                ->orderBy('add_time desc')
                ->asArray()
                ->one();
            $model       = BaseJobApplyRecord::findOne($item['id']);
            if ($info) {
                if ($info['platform'] == 'PC') {
                    $model->platform = 1;
                } else {
                    $model->platform = 2;
                }
                $model->save();
                self::log('序号' . $i++ . '简历ID：' . $resume_info['id'] . '投递记录ID：' . $item['id'] . '平台：' . $model->platform);
            }
            echo '执行结束';
        }
    }

    /***
     * 修复科研项目项目周期时间字段问题
     * php timer_yii script/update-resume-research-project
     */
    public function actionUpdateResumeResearchProject()
    {
        $data = BaseResumeResearchProject::find()
            ->select([
                'id',
                'end_date',
                'begin_date',
            ])
            ->andWhere(['status' => 1])
            ->andWhere([
                '!=',
                'end_date',
                '0000-00-00',
            ])
            ->andWhere('begin_date > end_date')
            ->asArray()
            ->all();
        $i    = 0;
        foreach ($data as $item) {
            self::log('修复数据：' . json_encode($item));
            $model = BaseResumeResearchProject::findOne($item['id']);
            if ($model) {
                $model->begin_date = $item['end_date'];
                $model->end_date   = $item['begin_date'];
                $model->save();
                self::log('修复成功：' . $item['id'] . ',第' . $i++ . '条');
            }
        }
        echo '执行结束';
    }

    /**
     * 修复历史数据-投递总表的匹配度字段
     * php timer_yii script/update-apply-record-match
     */
    public function actionUpdateApplyRecordMatch()
    {
        $key    = 'UpdateApplyRecordMatch';
        $id     = Cache::get($key) ?: 0;
        $limit  = 10000;
        $data   = BaseJobApplyRecord::find()
            ->select([
                'id',
                'job_id',
                'resume_id',
                'match_complete',
            ])
            ->andWhere(['match_complete' => 0])
            ->andWhere([
                '>',
                'id',
                $id,
            ])
            ->andWhere([
                '>',
                'job_id',
                0,
            ])
            ->limit($limit)
            ->asArray()
            ->all();
        $lastId = 0;
        foreach ($data as $item) {
            $service = new MatchCompleteService();
            $init    = [
                'job_id'    => $item['job_id'],
                'resume_id' => $item['resume_id'],
            ];

            $service_result = $service->setRuleKey()
                ->setProject(MatchCompleteService::PROJECT_TYPE_2)
                ->init($init)
                ->run();
            if (isset($service_result['rule_id']) && $service_result['rule_id'] > 0) {
                $model = BaseJobApplyRecord::findOne($item['id']);
                if ($model) {
                    $model->match_complete = $service_result['rule_id'];
                    $model->save();
                    self::log('投递记录ID：' . $item['id'] . '匹配度：' . $service_result['rule_id']);
                }
            }

            $lastId = $item['id'];
        }

        Cache::set($key, $lastId);
    }

    /***
     * 更新表单link字段http问题
     * php timer_yii script/update-link-http
     */
    public function actionUpdateLinkHttp()
    {
        $data = BaseActivityForm::find()
            ->asArray()
            ->all();
        foreach ($data as $item) {
            self::log('修复数据：' . json_encode($item));
            $model = BaseActivityForm::findOne($item['id']);
            if ($model && !empty($item['link'])) {
                //含http://www.则替换成https://www.，含http://则替换成https://www
                $model->link = str_replace('http://www.', 'https://www.', $item['link']);
                $model->link = str_replace('http://', 'https://www.', $model->link);
                $model->save();
                self::log('修复成功：' . $item['id'] . ',第' . $i++ . '条');
            }
        }
        echo '执行结束';
    }

    /***
     * 修复表resume_stat_data投递统计字段数据错误问题
     * php timer_yii script/update-apply-total
     */
    public function actionUpdateApplyTotal()
    {
        //获取平台为0的投递记录
        $key = Cache::get('update_apply_total') ?: 0;
        //查询简历
        $data = BaseResume::find()
            ->select([
                'id',
                'member_id',
            ])
            ->andWhere([
                '>',
                'id',
                $key,
            ])
            ->andWhere(['status' => 1])
            ->orderBy('id asc')
            ->asArray()
            ->all();
        //循环处理
        foreach ($data as $item) {
            $model = BaseResumeStatData::findOne($item['id']);
            if ($model) {
                $model->on_site_apply_amount  = BaseJobApplyRecord::find()
                    ->where([
                        'resume_id'     => $item['id'],
                        'delivery_type' => 2,
                    ])
                    ->count();
                $model->off_site_apply_amount = BaseJobApplyRecord::find()
                    ->where([
                        'resume_id'     => $item['id'],
                        'delivery_type' => 1,
                        'delivery_way'  => [
                            1,
                            2,
                            3,
                        ],
                    ])
                    ->count();
                $model->save();
                self::log('简历ID：' . $item['id'] . '站内投递总数：' . $model->on_site_apply_amount . ';站外投递总数：' . $model->off_site_apply_amount);
                Cache::set('update_apply_total', $item['id']);
            }
        }
    }

    /***
     * 修复表职位表省份字段数据错误问题
     * php timer_yii script/fix-job-error-province-id
     */
    public function actionFixJobErrorProvinceId()
    {
        // 某些职位里面包含了一些不存在的省份id,需要做一次修复,主要就是海外的
        $id = [
            3750,
            3751,
        ];

        $list = BaseJob::find()
            ->select('id,province_id')
            ->where([
                'province_id' => $id,
            ])
            ->asArray()
            ->all();

        foreach ($list as $item) {
            $jobId = $item['id'];
            // 开启事务
            $transaction = Yii::$app->db->beginTransaction();
            try {
                $job = BaseJob::findOne($jobId);
                // 先把省份信息保留下来
                self::log('职位ID：' . $jobId . '省份ID：' . $item['province_id'] . '修复开始');
                if ($job) {
                    $job->province_id = 3870;
                    $job->save();
                    self::log('职位ID：' . $jobId . '省份ID：' . $item['province_id'] . '修复成功');
                }

                // 走自动匹配规则
                $jobMatch = new JobAutoClassify($job->id);
                $jobMatch->run();

                $transaction->commit();
            } catch (\Exception $e) {
                $transaction->rollBack();
                self::log('职位ID：' . $jobId . '省份ID：' . $item['province_id'] . '修复失败');
            }
        }
    }

    /***
     * 更新账号信息表
     * php timer_yii script/update-company-member-info
     */
    public function actionUpdateCompanyMemberInfo()
    {
        //获取平台为0的投递记录
        $key = Cache::get('update_company_member_info') ?: 0;
        //获取所有审核通过的合作单位信息
        $data = BaseCompany::find()
            ->andWhere(['status' => 1])
            ->andWhere(['is_cooperation' => 1])
            ->andWhere([
                '>',
                'id',
                $key,
            ])
            ->asArray()
            ->all();
        foreach ($data as $item) {
            self::log('单位ID：' . $item['id'] . '开始插入账号信息表');
            $insert = [
                'member_id'           => $item['member_id'],
                'company_id'          => $item['id'],
                'contact'             => $item['contact'] ?: BaseMember::findOneVal(['id' => $item['member_id']],
                    'username'),
                'department'          => $item['department'] ?: '',
                'member_rule'         => BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_SUPER,
                'company_member_type' => BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN,
                'create_id'           => $item['create_admin_id'] ?: 1,
                'source_type'         => $item['source_type'] ?: 2,
                'is_wx_bind'          => BaseCompanyMemberInfo::IS_WX_BIND_NO,
            ];
            BaseCompanyMemberInfo::add($insert);
            self::log('单位ID：' . $item['id'] . '插入账号信息表成功;插入信息为' . json_encode($insert));
            self::log('单位ID：' . $item['id'] . '开始插入子账号配置表初始化');
            BaseCompanyMemberConfig::initAdd($item['id']);
            self::log('单位ID：' . $item['id'] . '插入子账号配置表初始化成功');
            Cache::set('update_company_member_info', $item['id']);
        }
    }

    /***
     * 更新账号信息表
     * php timer_yii script/update-company-message-config
     */
    public function actionUpdateCompanyMessageConfig()
    {
        //获取平台为0的投递记录
        $key = Cache::get('update-company-message-config') ?: 0;
        //获取所有审核通过的合作单位信息
        $data = BaseCompany::find()
            ->andWhere(['status' => 1])
            ->andWhere(['is_cooperation' => 1])
            ->andWhere([
                '>',
                'id',
                $key,
            ])
            ->orderBy('id')
            ->asArray()
            ->all();
        foreach ($data as $item) {
            self::log('单位ID：' . $item['id'] . '消息配置表初始化');
            $res = BaseCompanyMemberMessageConfig::initAdd($item['member_id'], $item['id']);
            self::log('单位ID：' . $item['id'] . '消息配置表初始化成功:' . $res);
            Cache::set('update-company-message-config', $item['id']);
        }
    }

    /**
     * 修复表职位表省份字段数据错误问题
     * php timer_yii script/ fix-job-error-city-id
     */
    public function actionFixJobErrorCityId()
    {
        // 某些职位里面包含了一些不存在的省份id,需要做一次修复,主要就是澳门的
        $id = [
            3739,
        ];

        $list = BaseJob::find()
            ->select('id,city_id')
            ->where([
                'city_id' => $id,
            ])
            ->asArray()
            ->all();

        foreach ($list as $item) {
            $jobId = $item['id'];
            // 开启事务
            $transaction = Yii::$app->db->beginTransaction();
            try {
                $job = BaseJob::findOne($jobId);
                // 走自动匹配规则
                $jobMatch = new JobAutoClassify($job->id);
                $jobMatch->run();
                self::log('职位ID：' . $jobId . '城市ID：' . $item['city_id'] . '修复成功');
                $transaction->commit();
            } catch (\Exception $e) {
                $transaction->rollBack();
                self::log('职位ID：' . $jobId . '城市ID：' . $item['city_id'] . '修复失败');
            }
        }
    }

    /**
     * 处理历史投递数据去到统计表
     * php timer_yii script/update-job-apply-count
     */
    public function actionUpdateJobApplyCount()
    {
        //获取最大的ID
        //获取投递数据进行处理
        $key = Cache::get('update_job_apply_count');
        if ($key == 1) {
            exit('已经处理完毕');
        }
        if ($key <= 0) {
            $max_id = BaseJobApplyRecord::find()
                ->asArray()
                ->max('id');
            Cache::set('update_job_apply_count', $max_id);
            $key = $max_id;
        }
        $data = BaseJobApplyRecord::find()
            ->select([
                'id',
                'job_id',
                'resume_id',
                'platform',
            ])
            ->andWhere([
                'delivery_way' => [
                    1,
                    2,
                    3,
                ],
            ])
            ->andWhere([
                '<',
                'id',
                $key,
            ])
            ->orderBy('id desc')
            ->limit(5000)
            ->asArray()
            ->all();

        foreach ($data as $item) {
            self::log('开始处理ID：' . $item['id']);
            BaseJobApplyRecordExtra::JobApplyCount($item['job_id'], $item['resume_id'], $item['platform']);
            Cache::set('update_job_apply_count', $item['id']);
            self::log('处理ID：' . $item['id'] . '成功');
        }
        self::log('结束');
    }

    /**
     * 处理历史投递数据去到统计表
     * php timer_yii script/update-job-apply-interview
     */
    public function actionUpdateJobApplyInterview()
    {
        //获取最大的ID
        //获取投递数据进行处理
        $key = Cache::get('update_job_apply_interview');
        if ($key == 1) {
            exit('已经处理完毕');
        }
        if ($key <= 0) {
            $max_id = BaseJobApply::find()
                ->asArray()
                ->max('id');
            Cache::set('update_job_apply_interview', $max_id);
            $key = $max_id;
        }
        $data = BaseJobApply::find()
            ->select([
                'is_invitation',
                'job_id',
                'id',
            ])
            ->andWhere([
                '<',
                'id',
                $key,
            ])
            ->orderBy('id desc')
            ->limit(100000)
            ->asArray()
            ->all();

        foreach ($data as $item) {
            if ($item['is_invitation'] > 0) {
                self::log('开始处理ID：' . $item['id']);
                BaseJobApplyRecordExtra::jobApplyUpdateInterview($item['job_id']);
                self::log('处理ID：' . $item['id'] . '成功');
            }
            Cache::set('update_job_apply_interview', $item['id']);
        }
        self::log('结束');
    }

    /**
     * 修复职位历史数据的联系人去到联系人表
     * php timer_yii script/fix-job-contact
     */
    public function actionFixJobContact()
    {
        //获取投递数据进行处理
        $key  = Cache::get('fix_job_contact') ?: 0;
        $data = BaseJob::find()
            ->alias('j')
            ->select('j.id,j.announcement_id,j.company_id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = j.company_id')
            ->andWhere([
                '>',
                'j.id',
                $key,
            ])
            ->andWhere(['c.is_cooperation' => 1])
            ->orderBy('j.id asc')
            ->asArray()
            ->all();

        foreach ($data as $item) {
            self::log('开始处理jobID：' . $item['id']);
            //获取单位主账号ID
            $company_member_info_id = BaseCompanyMemberInfo::findOneVal([
                'company_id'          => $item['company_id'],
                'company_member_type' => BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN,
            ], 'id') ?: 0;
            $insert                 = [
                'job_id'                 => $item['id'],
                'company_id'             => $item['company_id'],
                'company_member_info_id' => $company_member_info_id,
                'announcement_id'        => $item['announcement_id'],
            ];
            try {
                BaseJobContact::add($insert);
            } catch (\Exception $e) {
                self::log('处理jobID：' . $item['id'] . '失败');
                continue;
            }
            self::log('处理jobID：' . $item['id'] . '成功');
            Cache::set('fix_job_contact', $item['id']);
        }
        self::log('结束');
    }

    /**
     * 工作年限、求职身份存量数据更新
     * @return void
     */
    public function actionUpdateIdentityType()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $resumeList  = BaseResumeWork::find()
                ->where([
                    'status' => BaseResume::STATUS_ACTIVE,
                ])
                ->select([
                    'resume_id',
                    'min(begin_date) as begin_date',
                ])
                ->groupBy('resume_id')
                ->asArray()
                ->all();
            $resumeCount = count($resumeList);
            self::log("数据共{
        $resumeCount}条\n");

            foreach ($resumeList as $item) {
                $resume = BaseResume::findOne($item['resume_id']);
                if (!$resume || $resume['identity_type'] != BaseResume::IDENTITY_TYPE_DELETION) {
                    //只有缺失的才需要更新
                    self::log("resumeId:{$resume['id']}无需更新，跳过\n");
                    continue;
                }
                self::log("开始处理resumeId:{$resume['id']}\n");

                $workExperience = TimeHelper::countDifferYears(CUR_DATE, $item['begin_date']);

                $resume->work_experience = $workExperience;
                $resume->begin_work_date = $item['begin_date'];
                $resume->identity_type   = BaseResume::IDENTITY_TYPE_WORKER;
                if (!$resume->save()) {
                    throw new Exception($resume->getFirstErrorsMessage());
                }
                self::log("更新resumeId:{$resume['id']}成功\n");
            }

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            var_dump($e->getMessage());
        }
    }

    /**
     * 同步更新职位投递流程处理人账号历史数据
     * php timer_yii script/update-job-apply-handle-log
     */
    public function actionUpdateJobApplyHandleLog()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $jobApplyHandleList = BaseJobApplyHandleLog::find()
                ->alias('l')
                ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = l.company_id')
                ->select([
                    'l.id',
                    'l.company_id',
                    'c.member_id',
                ])
                ->where([
                    'l.handle_id' => 0,
                ])
                ->asArray()
                ->all();
            $count              = count($jobApplyHandleList);
            self::log("数据共{$count}条\n");

            foreach ($jobApplyHandleList as $item) {
                $model = BaseJobApplyHandleLog::findOne($item['id']);
                self::log("开始处理id:{$model['id']}\n");

                $model->handle_id = $item['member_id'];
                if (!$model->save()) {
                    throw new Exception($model->getFirstErrorsMessage());
                }
                self::log("更新Id:{$model['id']}成功\n");
            }

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            var_dump($e->getMessage());
        }
    }

    /**
     * 同步更新人才库邀约日志处理人账号历史数据
     * php timer_yii script/update-resume-library-invite-log
     */
    public function actionUpdateResumeLibraryInviteLog()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $resumeLibraryInviteLogList = BaseResumeLibraryInviteLog::find()
                ->alias('l')
                ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = l.company_id')
                ->select([
                    'l.id',
                    'l.company_id',
                    'c.member_id',
                ])
                ->where([
                    'l.company_member_id' => 0,
                ])
                ->asArray()
                ->all();
            $count                      = count($resumeLibraryInviteLogList);
            self::log("数据共{$count}条\n");

            foreach ($resumeLibraryInviteLogList as $item) {
                $model = BaseResumeLibraryInviteLog::findOne($item['id']);
                self::log("开始处理id:{$model['id']}\n");

                $model->company_member_id = $item['member_id'];
                if (!$model->save()) {
                    throw new Exception($model->getFirstErrorsMessage());
                }
                self::log("更新Id:{$model['id']}成功\n");
            }

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            var_dump($e->getMessage());
        }
    }

    /***
     * 修复表职位表省份字段数据错误问题
     * php timer_yii script/change-activity-registration-form-dirty-data
     */
    public function actionChangeActivityRegistrationFormDirtyData()
    {
        $activityFormIdList = BaseActivityForm::find()
            ->select(['id'])
            ->where(['status' => BaseActivityForm::STATUS_ACTIVE])
            ->column();

        foreach ($activityFormIdList as $formId) {
            $dirtyDataIds = BaseActivityFormRegistrationForm::getRegistrationFormIds($formId);

            foreach ($dirtyDataIds as $item) {
                // 开启事务
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    $activityFormRegistrationForm         = BaseActivityFormRegistrationForm::findOne($item);
                    $activityFormRegistrationForm->status = BaseActivityFormRegistrationForm::STATUS_DELETE;
                    $activityFormRegistrationForm->save();
                    self::log('报名表单ID：' . $item . '修复成功');
                    $transaction->commit();
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    self::log('报名表单ID：' . $item . '修复失败');
                }
            }
        }
    }

    /**
     * 同步更新人才库收藏处理人账号历史数据
     * php timer_yii script/update-resume-library-collect
     */
    public function actionUpdateResumeLibraryCollect()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $resumeLibraryCollectList = BaseResumeLibraryCollect::find()
                ->alias('l')
                ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = l.company_id')
                ->select([
                    'l.id',
                    'l.company_id',
                    'c.member_id',
                ])
                ->where([
                    'l.member_id' => 0,
                ])
                ->asArray()
                ->all();
            $count                    = count($resumeLibraryCollectList);
            self::log("数据共{$count}条\n");

            foreach ($resumeLibraryCollectList as $item) {
                $model = BaseResumeLibraryCollect::findOne($item['id']);
                self::log("开始处理id:{$model['id']}\n");

                $model->member_id = $item['member_id'];
                if (!$model->save()) {
                    throw new Exception($model->getFirstErrorsMessage());
                }
                self::log("更新Id:{$model['id']}成功\n");
            }

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            var_dump($e->getMessage());
        }
    }

    /**
     * 同步更新单位简历库账号历史数据
     * php timer_yii script/update-company-resume-library-member
     */
    public function actionUpdateCompanyResumeLibraryMember()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $resumeLibraryCollectList = BaseCompanyResumeLibrary::find()
                ->alias('l')
                ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = l.company_id')
                ->select([
                    'l.id',
                    'l.company_id',
                    'c.member_id',
                ])
                ->where([
                    'l.company_member_id' => 0,
                ])
                ->asArray()
                ->all();
            $count                    = count($resumeLibraryCollectList);
            self::log("数据共{$count}条\n");

            foreach ($resumeLibraryCollectList as $item) {
                $model = BaseCompanyResumeLibrary::findOne($item['id']);
                self::log("开始处理id:{$model['id']}\n");

                $model->company_member_id = $item['member_id'];
                if (!$model->save()) {
                    throw new Exception($model->getFirstErrorsMessage());
                }
                self::log("更新Id:{$model['id']}成功\n");
            }

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            var_dump($e->getMessage());
        }
    }

    /**
     * 将历史订单写入快照
     * @return void
     * @throws Exception
     */
    public function actionUpdateOrderSnapshot()
    {
        $orderList = BaseResumeOrder::find()
            ->select([
                'id',
                'snapshot_data',
                'resume_id',
                'equity_package_id',
                'payway',
                'platform',
                'pay_channel',
                'ip',
            ])
            ->asArray()
            ->all();
        $totalNum  = count($orderList);
        self::log('共有' . $totalNum . '订单');
        foreach ($orderList as $orderInfo) {
            //判断是否存在快照了
            $snapshotInfo = BaseResumeOrderSnapshot::findOne(['order_id' => $orderInfo['id']]);
            if (!empty($snapshotInfo) && !empty($orderInfo['snapshot_data'])) {
                self::log('订单id:' . $orderInfo['id'] . '已经存在快照，跳过！');
            } else {
                self::log('订单id:' . $orderInfo['id'] . '，开始新增快照！');
                //如果不存在，就写入
                $orderInfo        = BaseResumeOrder::findOne($orderInfo['id']);
                $equityPackageRow = BaseResumeEquityPackageSetting::findOne(['id' => $orderInfo['equity_package_id']]);
                $equityText       = BaseResumeEquityPackageRelationSetting::getEquityInfoText($orderInfo['equity_package_id']);
                //新增快照记录
                if (empty($snapshotInfo)) {
                    $snapshotModel                      = new BaseResumeOrderSnapshot();
                    $snapshotModel->order_id            = $orderInfo['id'];
                    $snapshotModel->equity_package_name = $equityPackageRow['name'];
                    $snapshotModel->service_days        = $equityPackageRow['days'];
                    $snapshotModel->equity_content      = $equityText;
                    if (!$snapshotModel->save()) {
                        throw new Exception($snapshotModel->getFirstErrorsMessage());
                    }
                }
                //更新快照信息
                if (empty($orderInfo['snapshot_data'])) {
                    $orderModel = BaseResumeOrder::findOne($orderInfo['id']);
                    //大类名称
                    $equityPackageCategoryName = BaseResumeEquityPackageCategorySetting::findOneVal(['id' => $equityPackageRow['equity_package_category_id']],
                        'name');
                    //获取权益列表
                    $equityText = BaseResumeEquityPackageRelationSetting::getEquityInfoText($orderInfo['equity_package_id']);

                    //保存快照信息
                    $snapshotData = [
                        'resume_id'                    => $orderInfo['resume_id'],
                        'equity_package_id'            => $orderInfo['equity_package_id'],
                        //支付方式
                        'payway'                       => $orderInfo['payway'],
                        //平台
                        'platform'                     => $orderInfo['platform'],
                        //支付通道
                        'pay_channel'                  => $orderInfo['pay_channel'],
                        //用户ip
                        'ip'                           => $orderInfo['ip'],
                        //原始金额
                        'original_amount'              => $orderInfo['original_amount'],
                        //真实金额
                        'real_amount'                  => $orderInfo['real_amount'],
                        //订单号
                        'order_no'                     => $orderInfo['order_no'],
                        //服务名称
                        'equity_package_name'          => $equityPackageRow['name'],
                        //服务天数
                        'service_days'                 => $equityPackageRow['days'],
                        //大类名称
                        'equity_package_category_name' => $equityPackageCategoryName,
                        //权益内容
                        'equity_content'               => $equityText,
                    ];

                    $orderModel->snapshot_data = json_encode($snapshotData);
                    if (!$orderModel->save()) {
                        throw new Exception($orderModel->getFirstErrorsMessage());
                    }
                }

                self::log('订单id:' . $orderInfo['id'] . '，创建快照成功！');
            }
        }
    }

    /***
     * 更新账号信息表
     * php timer_yii script/update-company-member-info-new
     */
    public function actionUpdateCompanyMemberInfoNew()
    {
        //获取平台为0的投递记录
        $key = Cache::get('update_company_member_info_new') ?: 0;
        //获取所有审核通过的合作单位信息
        $data = BaseCompany::find()
            ->andWhere(['is_cooperation' => 1])
            ->andWhere([
                '>',
                'id',
                $key,
            ])
            ->asArray()
            ->all();
        foreach ($data as $item) {
            //先查一下有没有
            $company_member_info = BaseCompanyMemberInfo::findOne(['member_id' => $item['member_id']]);
            if ($company_member_info) {
                continue;
            }
            self::log('单位ID：' . $item['id'] . '开始插入账号信息表');
            $insert = [
                'member_id'           => $item['member_id'],
                'company_id'          => $item['id'],
                'contact'             => $item['contact'] ?: BaseMember::findOneVal(['id' => $item['member_id']],
                    'username'),
                'department'          => $item['department'] ?: '',
                'member_rule'         => BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_SUPER,
                'company_member_type' => BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN,
                'create_id'           => $item['create_admin_id'] ?: 1,
                'source_type'         => $item['source_type'] ?: 2,
                'is_wx_bind'          => BaseCompanyMemberInfo::IS_WX_BIND_NO,
            ];
            BaseCompanyMemberInfo::add($insert);
            self::log('单位ID：' . $item['id'] . '插入账号信息表成功;插入信息为' . json_encode($insert));
            self::log('单位ID：' . $item['id'] . '开始插入子账号配置表初始化');
            BaseCompanyMemberConfig::initAdd($item['id']);
            self::log('单位ID：' . $item['id'] . '插入子账号配置表初始化成功');
            Cache::set('update_company_member_info_new', $item['id']);
        }
    }

    /***
     * 更新简历表的会员等级
     * php timer_yii script/update-resume-member-level
     */
    public function actionUpdateResumeMemberLevel()
    {
        //获取简历表的会员
        $data = BaseResume::find()
            ->select([
                'id',
                'vip_type',
                'vip_level',
            ])
            ->andWhere([
                'status'    => 1,
                'vip_type'  => 1,
                'vip_level' => 0,
            ])
            ->orderBy('id asc')
            ->asArray()
            ->all();

        //更新vip_level=1
        self::log('开始更新vip_type=1的数据');
        foreach ($data as $item) {
            self::log('会员等级更新前数据,ID为：' . $item['id'] . ',原数据为' . json_encode($item));
            $model = BaseResume::findOne($item['id']);
            if ($model) {
                $model->vip_level = BaseResume::VIP_LEVEL_GOLD;
                $model->save();
            }
        }
        self::log('更新vip_type=1的数据结束');
    }

    /***
     * 处理历史的权益数据去到新建两个分表resume_equity_package、resume_equity_package_detail
     * php timer_yii script/update-equity-item-data
     */
    public function actionUpdateEquityItemData()
    {
        //获取订单数据
        $order_data = BaseResumeOrder::find()
            ->andWhere(['status' => 1])
            ->orderBy('resume_id asc,pay_time asc')
            ->asArray()
            ->all();
        //首先数据是按照每个人的支付时间正序，先支付的先处理所以保存一下每个人上一个订单的的过期时间预留给下一个自己订单用
        $resume_time = [];
        $package     = [];
        foreach ($order_data as $value) {
            $resume_id = $value['resume_id'];
            $order_id  = $value['id'];
            $pay_time  = $value['pay_time'];
            $order_no  = $value['order_no'];
            //获取订单快照
            $snapshot = BaseResumeOrderSnapshot::findOne(['order_id' => $order_id]);
            //获取权益包的数据
            $equity_package_info = BaseResumeEquityPackageSetting::findOne($value['equity_package_id']);
            //权益信息
            $equity_ids = BaseResumeEquityPackageRelationSetting::find()
                ->select('equity_id')
                ->andWhere(['equity_package_id' => $equity_package_info->id])
                ->asArray()
                ->column();
            $days       = $snapshot->service_days;
            if ((!isset($resume_time[$resume_id]) && $equity_package_info->equity_package_category_id == 2) || (isset($package[$resume_id]) && $package[$resume_id] == '2_1')) {
                if (!isset($package[$resume_id])) {
                    $package[$resume_id] = $equity_package_info->equity_package_category_id;
                } else {
                    $package[$resume_id] = $package[$resume_id] . '_' . $equity_package_info->equity_package_category_id;
                }
                $eid        = $equity_package_info->equity_package_category_id == 1 ? 8 : 1;
                $start_time = BaseResumeEquity::findOneVal([
                    'resume_id' => $resume_id,
                    'equity_id' => $eid,
                ], 'begin_time');
                $end_time   = BaseResumeEquity::findOneVal([
                    'resume_id' => $resume_id,
                    'equity_id' => $eid,
                ], 'expire_time');
            } else {
                //根据订单支付时间计算服务开始与结束时间
                if (isset($resume_time[$resume_id]) && !empty($resume_time[$resume_id])) {
                    $last_time = $resume_time[$resume_id];
                    if (strtotime($last_time) < strtotime($pay_time)) {
                        $start_time = $pay_time;
                    } else {
                        $start_time = $last_time;
                    }
                } else {
                    $start_time = $pay_time;
                }
                $end_time = date('Y-m-d H:i:s', strtotime('+' . $days . ' day', strtotime($start_time)));
            }
            foreach ($equity_ids as $equity_id) {
                //insert数据
                $info = BaseResumeEquityPackage::findOne([
                    'resume_id'           => $resume_id,
                    'package_category_id' => $equity_package_info->equity_package_category_id,
                    'equity_id'           => $equity_id,
                ]);

                if ($info) {
                    if ($info->expire_time < $start_time) {
                        $info->begin_time = $start_time;
                    }
                    $info->expire_time   = $end_time;
                    $info->update_time   = CUR_DATETIME;
                    $info->expire_status = strtotime($end_time) < time() ? 1 : 0;
                    $info->save();
                } else {
                    $insert = [
                        'resume_id'           => $resume_id,
                        'package_category_id' => $equity_package_info->equity_package_category_id,
                        'equity_id'           => $equity_id,
                        'begin_time'          => $start_time,
                        'expire_time'         => $end_time,
                        'expire_status'       => strtotime($end_time) < time() ? 1 : 0,
                        'add_time'            => CUR_DATETIME,
                        'update_time'         => CUR_DATETIME,
                    ];
                    $model  = new BaseResumeEquityPackage();
                    $model->setAttributes($insert);
                    $model->save();
                }
                $insert_2 = [
                    'resume_id'           => $resume_id,
                    'package_category_id' => $equity_package_info->equity_package_category_id,
                    'equity_id'           => $equity_id,
                    'begin_time'          => $start_time,
                    'expire_time'         => $end_time,
                    'expire_status'       => strtotime($end_time) < time() ? 1 : 0,
                    'add_time'            => CUR_DATETIME,
                    'update_time'         => CUR_DATETIME,
                    'order_id'            => $order_id,
                ];
                $model_2  = new BaseResumeEquityPackageDetail();
                $model_2->setAttributes($insert_2);
                $model_2->save();
            }
            $resume_time[$resume_id] = $end_time;
            self::log('简历ID：' . $resume_id . ';订单ID为' . $order_id . '处理完成');
        }

        self::log('处理完毕');
    }

    /* 批量隐藏单位
     * php timer_yii script/hide-company
     */
    public function actionHideCompany()
    {
        $list = [
            '20000628',
            '20000629',
            '20000644',
            '20000648',
            '20000653',
            '20000663',
            '20000678',
            '20000682',
            '20000684',
            '20000685',
            '20000695',
            '20000703',
            '20000705',
            '20000706',
            '20000709',
            '20000711',
            '20000715',
            '20000725',
            '20000730',
            '20000731',
            '20000736',
            '20000737',
            '20000738',
            '20000739',
            '20000740',
            '20000742',
            '20000749',
            '20000750',
            '20000756',
            '20000758',
            '20000765',
            '20000799',
            '20000805',
            '20000809',
            '20000810',
            '20000811',
            '20000816',
            '20000817',
            '20000821',
            '20000831',
            '20000848',
            '20000849',
            '20000969',
            '20000988',
            '20000996',
            '20000998',
            '20000999',
            '20001008',
            '20001013',
            '20001023',
            '20001042',
            '20001043',
            '20001057',
            '20001076',
            '20001095',
            '20001111',
            '20001134',
            '20001137',
            '20001138',
            '20001147',
            '20001150',
            '20001175',
            '20001177',
            '20001178',
            '20001185',
            '20001186',
            '20001188',
            '20001189',
            '20001190',
            '20001191',
            '20001192',
            '20001193',
            '20001194',
            '20001195',
            '20001196',
            '20001210',
            '20001232',
            '20001394',
            '20001402',
            '20001403',
            '20001404',
            '20001405',
            '20001406',
            '20001415',
            '20001420',
            '20001422',
            '20001424',
            '20001428',
            '20001429',
            '20001430',
            '20001431',
            '20001483',
            '20001511',
            '20001512',
            '20001515',
            '20001523',
            '20001524',
            '20001530',
            '20001532',
            '20001569',
            '20001578',
            '20001586',
            '20001748',
            '20001771',
            '20001772',
            '20001774',
            '20001775',
            '20001779',
            '20001795',
            '20001797',
            '20001799',
            '20001810',
            '20001822',
            '20001975',
            '20001987',
            '20002097',
            '20002098',
            '20002120',
            '20002139',
            '20002145',
            '20002146',
            '20002151',
            '20002172',
            '20002176',
            '20002177',
            '20002187',
            '20002189',
            '20002192',
            '20002193',
            '20002198',
            '20002200',
            '20002219',
            '20002233',
            '20002247',
            '20002433',
            '20002440',
            '20002441',
            '20002444',
            '20002445',
            '20002446',
            '20002447',
            '20002463',
            '20002472',
            '20002483',
            '20002496',
            '20002509',
            '20002512',
            '20002529',
            '20002533',
            '20002548',
            '20002549',
            '20002553',
            '20002556',
            '20002619',
            '20002622',
            '20002653',
            '20002658',
            '20002659',
            '20002660',
            '20002661',
            '20002662',
            '20002664',
            '20002666',
            '20002670',
            '20002686',
            '20002702',
            '20002707',
            '20002710',
            '20002711',
            '20002712',
            '20002713',
            '20002714',
            '20002728',
            '20002750',
            '20002765',
            '20002790',
            '20002800',
            '20002807',
            '20002826',
            '20002829',
            '20002832',
            '20002847',
            '20002848',
            '20002852',
            '20002853',
            '20002854',
            '20002864',
            '20002865',
            '20002866',
            '20002867',
            '20002877',
            '20002885',
            '20002906',
            '20002907',
            '20002908',
            '20002911',
            '20002936',
            '20002968',
            '20003011',
            '20003114',
            '20003124',
            '20003125',
            '20003153',
            '20003154',
            '20003155',
            '20003157',
            '20003160',
            '20003161',
            '20003169',
            '20003180',
            '20003181',
            '20003265',
            '20003400',
            '20003401',
            '20003404',
            '20003408',
            '20003411',
            '20003413',
            '20003422',
            '20003423',
            '20003428',
            '20003429',
            '20003433',
            '20003440',
            '20003443',
            '20003444',
            '20003453',
            '20003454',
            '20003471',
            '20003517',
            '20003518',
            '20003543',
            '20003545',
            '20003555',
            '20003571',
            '20003575',
            '20003576',
            '20003687',
            '20003692',
            '20003736',
            '20003739',
            '20003800',
            '20003801',
            '20003815',
            '20003818',
            '20003821',
            '20003822',
            '20003823',
            '20003824',
            '20003907',
            '20003910',
            '20003915',
            '20003918',
            '20003922',
            '20003925',
            '20003957',
            '20004008',
            '20004010',
            '20004011',
            '20004012',
            '20004026',
            '20004028',
            '20004032',
            '20004033',
            '20004034',
            '20004039',
            '20004043',
            '20004045',
            '20004048',
            '20004058',
            '20004059',
            '20004061',
            '20004099',
            '20004113',
            '20004117',
            '20004122',
            '20004129',
            '20004133',
            '20004137',
            '20004138',
            '20004140',
            '20004141',
            '20004143',
            '20004146',
            '20004147',
            '20004149',
            '20004150',
            '20004151',
            '20004152',
            '20004153',
            '20004155',
            '20004166',
            '20004169',
            '20004201',
            '20004206',
            '20004207',
            '20004214',
            '20004217',
            '20004218',
            '20004222',
            '20004365',
            '20004366',
            '20004367',
            '20004368',
            '20004499',
            '20004563',
            '20004578',
            '20004590',
            '20004605',
            '20004607',
            '20004608',
            '20004615',
            '20004634',
            '20004636',
            '20004637',
            '20004638',
            '20004639',
            '20004640',
            '20004654',
            '20004655',
            '20004656',
            '20004695',
            '20004696',
            '20004704',
            '20004710',
            '20004714',
            '20004782',
            '20004806',
            '20004814',
            '20004829',
            '20004830',
            '20004834',
            '20004835',
            '20004839',
            '20004844',
            '20004860',
            '20004865',
            '20004886',
            '20004887',
            '20004927',
            '20004928',
            '20004933',
            '20004939',
            '20004954',
            '20004972',
            '20004975',
            '20004976',
            '20004977',
            '20004979',
            '20004980',
            '20004985',
            '20004986',
            '20004987',
            '20004988',
            '20005003',
            '20005007',
            '20005008',
            '20005009',
            '20005010',
            '20005011',
            '20005012',
            '20005025',
            '20005042',
            '20005056',
            '20005061',
            '20005119',
            '20005120',
            '20005122',
            '20005123',
            '20005124',
            '20005125',
            '20005126',
            '20005127',
            '20005132',
            '20005136',
            '20005144',
            '20005149',
            '20005151',
            '20005160',
            '20005207',
            '20005214',
            '20005216',
            '20005217',
            '20005237',
            '20005251',
            '20005255',
            '20005256',
            '20005433',
            '20005434',
            '20005438',
            '20005441',
            '20005442',
            '20005458',
            '20005467',
            '20005513',
            '20005581',
            '20005732',
            '20005764',
            '20005810',
            '20005812',
            '20005815',
            '20005846',
            '20005847',
            '20005879',
            '20005880',
            '20005887',
            '20005892',
            '20005897',
            '20005899',
            '20005900',
            '20005901',
            '20005902',
            '20006005',
            '20006014',
            '20006030',
            '20006036',
            '20006037',
            '20006055',
            '20006072',
            '20006102',
            '20006111',
            '20006112',
            '20006128',
            '20006143',
            '20006144',
            '20006145',
            '20006146',
            '20006159',
            '20006160',
            '20006229',
            '20006233',
            '20006234',
            '20006340',
            '20006342',
            '20006381',
            '20006412',
            '20006416',
            '20006417',
            '20006428',
            '20006440',
            '20006451',
            '20006452',
            '20006472',
            '20006495',
            '20006497',
            '20006501',
            '20006503',
            '20006504',
            '20006512',
            '20006533',
            '20006554',
            '20006573',
            '20006600',
            '20006730',
            '20006731',
            '20006732',
            '20006741',
            '20006746',
            '20006750',
            '20006753',
            '20006756',
            '20006758',
            '20006772',
            '20006774',
            '20006776',
            '20006778',
            '20006781',
            '20006784',
            '20006846',
            '20006847',
            '20006849',
            '20006850',
            '20006851',
            '20006852',
            '20006855',
            '20006856',
            '20006862',
            '20006864',
            '20006875',
            '20006881',
            '20006936',
            '20006947',
            '20006949',
            '20006950',
            '20006989',
            '20006990',
            '20007025',
            '20007029',
            '20007053',
            '20007054',
            '20007055',
            '20007056',
            '20007057',
            '20007058',
            '20007059',
            '20007062',
            '20007072',
            '20007073',
            '20007075',
            '20007096',
            '20007102',
            '20007106',
            '20007141',
            '20007142',
            '20007198',
            '20007199',
            '20007200',
            '20007201',
            '20007202',
            '20007204',
            '20007256',
            '20007328',
            '20007329',
            '20007330',
            '20007331',
            '20007354',
            '20007358',
            '20007361',
            '20007366',
            '20007367',
            '20007368',
            '20007369',
            '20007370',
            '20007382',
            '20007383',
            '20007386',
            '20007388',
            '20007389',
            '20007390',
            '20007391',
            '20007392',
            '20007396',
            '20007398',
            '20007438',
            '20007472',
            '20007502',
            '20007518',
            '20007527',
            '20007541',
            '20007543',
            '20007544',
            '20007554',
            '20007555',
            '20007556',
            '20007564',
            '20007682',
            '20007696',
            '20007707',
            '20007715',
            '20007717',
            '20007719',
            '20007723',
            '20007727',
            '20007732',
            '20007741',
            '20007745',
            '20007747',
            '20007748',
            '20007751',
            '20007752',
            '20007753',
            '20007754',
            '20007755',
            '20007756',
            '20007757',
            '20007758',
            '20007762',
            '20007767',
            '20007768',
            '20007794',
            '20007802',
            '20007804',
            '20007806',
            '20007807',
            '20007812',
            '20007815',
            '20007818',
            '20007822',
            '20007823',
            '20007825',
            '20007826',
            '20007828',
            '20007829',
            '20007830',
            '20007834',
            '20007835',
            '20007837',
            '20007838',
            '20007840',
            '20007843',
            '20007845',
            '20007870',
            '20007899',
            '20007900',
            '20007902',
            '20007905',
            '20007954',
            '20007956',
            '20007960',
            '20007963',
            '20007969',
            '20007970',
            '20007971',
            '20007981',
            '20007983',
            '20007986',
            '20007988',
            '20007990',
            '20007991',
            '20007993',
            '20007994',
            '20007996',
            '20008004',
            '20008019',
            '20008031',
            '20008033',
            '20008052',
            '20008053',
            '20008054',
            '20008056',
            '20008057',
            '20008072',
            '20008074',
            '20008237',
            '20008238',
            '20008304',
            '20008306',
            '20008307',
            '20008308',
            '20008309',
            '20008340',
            '20008343',
            '20008351',
            '20008353',
            '20008363',
            '20008366',
            '20008376',
            '20008379',
            '20008393',
            '20008421',
            '20008423',
            '20008424',
            '20008428',
            '20008433',
            '20008436',
            '20008437',
            '20008517',
            '20008571',
            '20008572',
            '20008650',
            '20008653',
            '20008654',
            '20008675',
            '20008681',
            '20008682',
            '20008685',
            '20008705',
            '20008771',
            '20008772',
            '20008773',
            '20008825',
            '20008827',
            '20008831',
            '20008832',
            '20008838',
            '20008839',
            '20008840',
            '20008846',
            '20008849',
            '20008851',
            '20008852',
            '20008853',
            '20008866',
            '20008972',
            '20008979',
            '20008986',
            '20009036',
            '20009038',
            '20009039',
            '20009040',
            '20009041',
            '20009043',
            '20009044',
            '20009049',
            '20009144',
            '20009163',
            '20009165',
            '20009166',
            '20009167',
            '20009168',
            '20009174',
            '20009176',
            '20009266',
            '20009271',
            '20009286',
            '20009287',
            '20009288',
            '20009290',
            '20009291',
            '20009358',
            '20009386',
            '20009387',
            '20009388',
            '20009389',
            '20009392',
            '20009393',
            '20009400',
            '20009405',
            '20009406',
            '20009407',
            '20009422',
            '20009435',
            '20009437',
            '20009438',
            '20009439',
            '20009440',
            '20009442',
            '20009443',
            '20009444',
            '20009445',
            '20009447',
            '20009541',
            '20009543',
            '20009638',
            '20009640',
            '20009662',
            '20009668',
            '20009669',
            '20009673',
            '20009687',
            '20009688',
            '20009689',
            '20009704',
            '20009708',
            '20009710',
            '20009713',
            '20009764',
            '20009789',
            '20009792',
            '20009823',
            '20009831',
            '20009836',
            '20009839',
            '20009992',
            '20009995',
            '20010016',
            '20010043',
            '20010124',
            '20010140',
            '20010171',
            '20010173',
            '20010175',
            '20010177',
            '20010180',
            '20010181',
            '20010182',
            '20010282',
            '20010283',
            '20010284',
            '20010285',
            '20010341',
            '20010344',
            '20010347',
            '20010348',
            '20010382',
            '20010405',
            '20010439',
            '20010441',
            '20010442',
            '20010443',
            '20010445',
            '20010448',
            '20010449',
            '20010452',
            '20010454',
            '20010484',
            '20010485',
            '20010542',
            '20010545',
            '20010548',
            '20010550',
            '20010621',
            '20010641',
            '20010644',
            '20010645',
            '20010649',
            '20010650',
            '20010656',
            '20010663',
            '20010673',
            '20010685',
            '20010687',
            '20010691',
            '20010695',
            '20010697',
            '20010772',
            '20010779',
            '20010823',
            '20010826',
            '20010827',
            '20010861',
            '20010862',
            '20010864',
            '20010865',
            '20010866',
            '20010869',
            '20010883',
            '20010892',
            '20010911',
            '20010912',
            '20010916',
            '20010919',
            '20010935',
            '20010936',
            '20010938',
            '20010948',
            '20010954',
            '20010956',
            '20010964',
            '20010967',
            '20010977',
            '20010992',
            '20010999',
            '20011002',
            '20011007',
            '20011015',
            '20011020',
            '20011021',
            '20011058',
            '20011062',
            '20011063',
            '20011101',
            '20011102',
            '20011169',
            '20011173',
            '20011194',
            '20011263',
            '20011268',
            '20011271',
            '20011367',
            '20011368',
            '20011372',
            '20011375',
            '20011376',
            '20011391',
            '20011443',
            '20011444',
            '20011446',
            '20011450',
            '20011452',
            '20011453',
            '20011454',
            '20011455',
            '20011477',
            '20011493',
            '20011495',
            '20011640',
            '20011643',
            '20011649',
            '20011652',
            '20011653',
            '20011654',
            '20011657',
            '20011658',
            '20011659',
            '20011662',
            '20011777',
            '20011795',
            '20011796',
            '20011842',
            '20011843',
            '20011845',
            '20011883',
            '20011887',
            '20011890',
            '20011896',
            '20011898',
            '20011900',
            '20011901',
            '20011902',
            '20011905',
            '20011906',
            '20011907',
            '20011909',
            '20011910',
            '20011912',
            '20011913',
            '20011916',
            '20011917',
            '20011918',
            '20011919',
            '20011924',
            '20011965',
            '20011969',
            '20011972',
            '20011974',
            '20011978',
            '20011979',
            '20011981',
            '20011984',
            '20012038',
            '20012040',
            '20012041',
            '20012042',
            '20012043',
            '20012044',
            '20012046',
            '20012047',
            '20012049',
            '20012061',
            '20012062',
            '20012064',
            '20012066',
            '20012074',
            '20012075',
            '20012076',
            '20012077',
            '20012078',
            '20012079',
            '20012080',
            '20012086',
            '20012088',
            '20012121',
            '20012122',
            '20012123',
            '20012153',
            '20012178',
            '20012182',
            '20012187',
            '20012188',
            '20012189',
            '20012190',
            '20012191',
            '20012193',
            '20012194',
            '20012195',
            '20012196',
            '20012199',
            '20012203',
            '20012205',
            '20012209',
            '20012244',
            '20012245',
            '20012275',
            '20012276',
            '20012277',
            '20012423',
            '20012456',
            '20012526',
            '20012529',
            '20012530',
            '20012550',
            '20012596',
            '20012598',
            '20012600',
            '20012603',
            '20012607',
            '20012612',
            '20012615',
            '20012626',
            '20012635',
            '20012667',
            '20012673',
            '20012674',
            '20012683',
            '20012730',
            '20012731',
            '20012732',
            '20012734',
            '20012747',
            '20012751',
            '20012753',
            '20012759',
            '20012762',
            '20012763',
            '20012775',
            '20012776',
            '20012778',
            '20012779',
            '20012781',
            '20012805',
            '20012806',
            '20012807',
            '20012808',
            '20012809',
            '20012810',
            '20012816',
            '20012817',
            '20012818',
            '20012819',
            '20012820',
            '20012821',
            '20012822',
            '20012823',
            '20012824',
            '20012825',
            '20012827',
            '20012878',
            '20012882',
            '20012883',
            '20012884',
            '20012885',
            '20012910',
            '20012993',
            '20013002',
            '20013004',
            '20013006',
            '20013007',
            '20013010',
            '20013014',
            '20013023',
            '20013025',
            '20013027',
            '20013031',
            '20013033',
            '20013037',
            '20013093',
            '20013094',
            '20013095',
            '20013096',
            '20013097',
            '20013098',
            '20013099',
            '20013100',
            '20013101',
            '20013109',
            '20013112',
            '20013113',
            '20013114',
            '20013117',
            '20013145',
            '20013150',
            '20013151',
            '20013152',
            '20013156',
            '20013158',
            '20013159',
            '20013160',
            '20013162',
            '20013163',
            '20013167',
            '20013184',
            '20013186',
            '20013187',
            '20013201',
            '20013207',
            '20013215',
            '20013220',
            '20013221',
            '20013250',
            '20013251',
            '20013252',
            '20013253',
            '20013255',
            '20013260',
            '20013261',
            '20013264',
            '20013280',
            '20013281',
            '20013286',
            '20013292',
            '20013362',
            '20013365',
            '20013369',
            '20013372',
            '20013393',
            '20013397',
            '20013402',
            '20013403',
            '20013417',
            '20013418',
            '20013422',
            '20013423',
            '20013424',
            '20013425',
            '20013426',
            '20013427',
            '20013430',
            '20013431',
            '20013432',
            '20013457',
            '20013459',
            '20013463',
            '20013466',
            '20013468',
            '20013470',
            '20013475',
            '20013476',
            '20013480',
            '20013500',
            '20013502',
            '20013504',
            '20013520',
            '20013522',
            '20013523',
            '20013524',
            '20013525',
            '20013526',
            '20013527',
            '20013528',
            '20013529',
            '20013530',
            '20013531',
            '20013532',
            '20013533',
            '20013534',
            '20013535',
            '20013536',
            '20013578',
            '20013581',
            '20013583',
            '20013585',
            '20013586',
            '20013590',
            '20013593',
            '20013595',
            '20013601',
            '20013626',
            '20013651',
            '20013652',
            '20013654',
            '20013655',
            '20013656',
            '20013657',
            '20013659',
            '20013660',
            '20013664',
            '20013667',
            '20013683',
            '20013690',
            '20013691',
            '20013759',
            '20013762',
            '20013764',
            '20013766',
            '20013769',
            '20013770',
            '20013779',
            '20013780',
            '20013786',
            '20013787',
            '20013789',
            '20013797',
            '20013800',
            '20013801',
            '20013804',
            '20013807',
            '20013810',
            '20013811',
            '20013843',
            '20013845',
            '20013852',
            '20013854',
            '20013858',
            '20013859',
            '20013878',
            '20013881',
            '20013892',
            '20013896',
            '20013939',
            '20013940',
            '20013942',
            '20013973',
            '20013985',
            '20013986',
            '20013988',
            '20014035',
            '20014036',
            '20014037',
            '20014042',
            '20014046',
            '20014060',
            '20014061',
            '20014074',
            '20014079',
            '20014080',
            '20014081',
            '20014082',
            '20014083',
            '20014084',
            '20014085',
            '20014092',
            '20014093',
            '20014113',
            '20014138',
            '20014148',
            '20014151',
            '20014153',
            '20014224',
            '20014226',
            '20014227',
            '20014228',
            '20014336',
            '20014337',
            '20014341',
            '20014342',
            '20014343',
            '20014345',
            '20014348',
            '20014349',
            '20014412',
            '20014420',
            '20014427',
            '20014428',
            '20014431',
            '20014433',
            '20014435',
            '20014436',
            '20014437',
            '20014438',
            '20014439',
            '20014440',
            '20014441',
            '20014443',
            '20014450',
            '20014453',
            '20014454',
            '20014462',
            '20014463',
            '20014464',
            '20014483',
            '20014486',
            '20014492',
            '20014532',
            '20014536',
            '20014537',
            '20014582',
            '20014583',
            '20014622',
            '20014625',
            '20014628',
            '20014692',
            '20014698',
            '20014701',
            '20014703',
            '20014705',
            '20014708',
            '20014711',
            '20014723',
            '20014742',
            '20014744',
            '20014745',
            '20014746',
            '20014747',
            '20014748',
            '20014844',
            '20014846',
            '20014848',
            '20014850',
            '20014851',
            '20014852',
            '20014855',
            '20014856',
            '20014857',
            '20014918',
            '20014920',
            '20014923',
            '20014924',
            '20014925',
            '20014926',
            '20014930',
            '20014934',
            '20014938',
            '20014939',
            '20014941',
            '20014984',
            '20014985',
            '20014986',
            '20014987',
            '20014988',
            '20015043',
            '20015051',
            '20015053',
            '20015071',
            '20015075',
            '20015081',
            '20015084',
            '20015086',
            '20015088',
            '20015095',
            '20015105',
            '20015108',
            '20015112',
            '20015114',
            '20015115',
            '20015121',
            '20015125',
            '20015129',
            '20015144',
            '20015152',
            '20015159',
            '20015164',
            '20015166',
            '20015189',
            '20015190',
            '20015209',
            '20015218',
            '20015230',
            '20015298',
            '20015299',
            '20015301',
            '20015311',
            '20015312',
            '20015316',
            '20015317',
            '20015322',
            '20015345',
            '20015349',
            '20015425',
            '20015433',
            '20015436',
            '20015437',
            '20015438',
            '20015441',
            '20015446',
            '20015530',
            '20015531',
            '20015536',
            '20015537',
            '20015543',
            '20015544',
            '20015545',
            '20015546',
            '20015547',
            '20015548',
            '20015549',
            '20015550',
            '20015551',
            '20015552',
            '20015553',
            '20015554',
            '20015555',
            '20015556',
            '20015557',
            '20015562',
            '20015565',
            '20015566',
            '20015568',
            '20015578',
            '20015579',
            '20015580',
            '20015585',
            '20015676',
            '20015685',
            '20015686',
            '20015718',
            '20015720',
            '20015721',
            '20015727',
            '20015728',
            '20015729',
            '20015730',
            '20015731',
            '20015732',
            '20015733',
            '20015734',
            '20015735',
            '20015736',
            '20015737',
            '20015738',
            '20015739',
            '20015751',
            '20015752',
            '20015758',
            '20015760',
            '20015767',
            '20015770',
            '20015771',
            '20015776',
            '20015777',
            '20015778',
            '20015788',
            '20015791',
            '20015794',
            '20015797',
            '20015798',
            '20015802',
            '20015804',
            '20015805',
            '20015806',
            '20015811',
            '20015812',
            '20015813',
            '20015814',
            '20015815',
            '20015816',
            '20015824',
            '20015825',
            '20015826',
            '20015827',
            '20015839',
            '20015845',
            '20015849',
            '20015851',
            '20015852',
            '20015853',
            '20015855',
            '20015857',
            '20015865',
            '20015867',
            '20015868',
            '20015873',
            '20015895',
            '20015897',
            '20015899',
            '20015906',
            '20015947',
            '20016077',
            '20016078',
            '20016081',
            '20016088',
            '20016091',
            '20016092',
            '20016093',
            '20016099',
            '20016104',
            '20016106',
            '20016108',
            '20016109',
            '20016148',
            '20016149',
            '20016243',
            '20016248',
            '20016250',
            '20016255',
            '20016262',
            '20016263',
            '20016290',
            '20016291',
            '20016359',
            '20016372',
            '20016375',
            '20016383',
            '20016386',
            '20016393',
            '20016395',
            '20016396',
            '20016400',
            '20016415',
            '20016424',
            '20016426',
            '20016427',
            '20016465',
            '20016468',
            '20016474',
            '20016475',
            '20016477',
            '20016482',
            '20016484',
            '20016486',
            '20016503',
            '20016505',
            '20016507',
            '20016629',
            '20016630',
            '20016631',
            '20016632',
            '20016634',
            '20016648',
            '20016659',
            '20016660',
            '20016661',
            '20016662',
            '20016664',
            '20016674',
            '20016675',
            '20016677',
            '20016680',
            '20016681',
            '20016684',
            '20016703',
            '20016713',
            '20016714',
            '20016716',
            '20016731',
            '20016744',
            '20016748',
            '20016750',
            '20016761',
            '20016765',
            '20016768',
            '20016772',
            '20016774',
            '20016779',
            '20016811',
            '20016838',
            '20016839',
            '20016840',
            '20016884',
            '20016885',
            '20016897',
            '20016899',
            '20016901',
            '20016907',
            '20016910',
            '20016924',
            '20016935',
            '20016940',
            '20016941',
            '20017013',
            '20017015',
            '20017016',
            '20017017',
            '20017018',
            '20017019',
            '20017020',
            '20017022',
            '20017041',
            '20017045',
            '20017047',
            '20017048',
            '20017049',
            '20017052',
            '20017053',
            '20017054',
            '20017084',
            '20017086',
            '20017137',
            '20017141',
            '20017146',
            '20017151',
            '20017168',
            '20017172',
            '20017173',
            '20017183',
            '20017214',
            '20017218',
            '20017248',
            '20017250',
            '20017251',
            '20017261',
            '20017271',
            '20017274',
            '20017292',
            '20017293',
            '20017352',
            '20017353',
            '20017354',
            '20017358',
            '20017359',
            '20017364',
            '20017368',
            '20017369',
            '20017370',
            '20017371',
            '20017372',
            '20017468',
            '20017469',
            '20017470',
            '20017471',
            '20017472',
            '20017479',
            '20017503',
            '20017504',
            '20017513',
            '20017515',
            '20017549',
            '20017550',
            '20017551',
            '20017552',
            '20017553',
            '20017603',
            '20017605',
            '20017617',
            '20017655',
            '20017657',
            '20017672',
            '20017674',
            '20017675',
            '20017676',
            '20017677',
            '20017682',
            '20017683',
            '20017684',
            '20017685',
            '20017686',
            '20017688',
            '20017689',
            '20017698',
            '20017702',
            '20017703',
            '20017704',
            '20017705',
            '20017706',
            '20017709',
            '20017719',
            '20017723',
            '20017724',
            '20017726',
            '20017728',
            '20017729',
            '20017730',
            '20017809',
            '20017813',
            '20017814',
            '20017815',
            '20017817',
            '20017818',
            '20017837',
            '20017838',
            '20017840',
            '20017848',
            '20017937',
            '20017939',
            '20017940',
            '20017945',
            '20017946',
            '20017956',
            '20017958',
            '20017959',
            '20018072',
            '20018074',
            '20018076',
            '20018095',
            '20018111',
            '20018114',
            '20018134',
            '20018136',
            '20018138',
            '20018143',
            '20018157',
            '20018194',
            '20018197',
            '20018202',
            '20018204',
            '20018211',
            '20018212',
            '20018266',
            '20018277',
            '20018285',
            '20018287',
            '20018353',
            '20018358',
            '20018384',
            '20018388',
            '20018418',
            '20018428',
            '20018468',
            '20018471',
            '20018493',
            '20018584',
            '20018611',
            '20018614',
            '20018619',
            '20018621',
            '20018628',
            '20018650',
            '20018660',
            '20018661',
            '20018668',
            '20018669',
            '20018671',
            '20018672',
            '20018673',
            '20018674',
            '20018675',
            '20018676',
            '20018677',
            '20018679',
            '20018687',
            '20018688',
            '20018689',
            '20018704',
            '20018706',
            '20018707',
            '20018783',
            '20018802',
            '20018813',
            '20018829',
            '20018842',
            '20018888',
            '20018923',
            '20018953',
            '20018954',
            '20018963',
            '20018966',
            '20019014',
            '20019022',
            '20019026',
            '20019036',
            '20019057',
            '20019062',
            '20019065',
            '20019068',
            '20019069',
            '20019070',
            '20019075',
            '20019112',
            '20019138',
            '20019141',
            '20019178',
            '20019179',
            '20019203',
            '20019204',
            '20019237',
            '20019238',
            '20019240',
            '20019241',
            '20019256',
            '20019258',
            '20019276',
            '20019296',
            '20019343',
            '20019366',
            '20019375',
            '20019381',
            '20019392',
            '20019393',
            '20019397',
            '20019484',
            '20019503',
            '20019504',
            '20019506',
            '20019512',
            '20019535',
            '20019547',
            '20019553',
            '20019564',
            '20019566',
            '20019574',
            '20019582',
            '20019625',
            '20019635',
            '20019641',
            '20019670',
            '20019687',
            '20019726',
            '20019734',
            '20019735',
            '20019738',
            '20019740',
            '20019741',
            '20019742',
            '20019743',
            '20019744',
            '20019804',
            '20019805',
            '20019807',
            '20019810',
            '20019812',
            '20019832',
            '20019833',
            '20019836',
            '20019869',
            '20020126',
            '20020181',
            '20020183',
            '20020188',
            '20020224',
            '20020225',
            '20020226',
            '20020229',
            '20020231',
            '20020235',
            '20020237',
            '20020240',
            '20020250',
            '20020254',
            '20020260',
            '20020297',
            '20020298',
            '20020305',
            '20020307',
            '20020329',
            '20020331',
            '20020332',
            '20020431',
            '20020434',
            '20020435',
            '20020436',
            '20020437',
            '20020438',
            '20020445',
            '20020446',
            '20020456',
            '20020457',
            '20020458',
            '20020483',
            '20020491',
            '20020494',
            '20020495',
            '20020496',
            '20020571',
            '20020572',
            '20020574',
            '20020580',
            '20020584',
            '20020647',
            '20020651',
            '20020670',
            '20020671',
            '20020673',
            '20020675',
            '20020676',
            '20020677',
            '20020679',
            '20020682',
            '20020683',
            '20020737',
            '20020740',
            '20020768',
            '20020770',
            '20020771',
            '20020841',
            '20020842',
            '20020844',
            '20020845',
            '20020846',
            '20020848',
            '20020850',
            '20020852',
            '20020855',
            '20020870',
            '20020880',
            '20020886',
            '20020887',
            '20020938',
            '20020941',
            '20020947',
            '20020949',
            '20020956',
            '20020976',
            '20021100',
            '20021106',
            '20021107',
            '20021110',
            '20021112',
            '20021122',
            '20021135',
            '20021142',
            '20021222',
            '20021223',
            '20021231',
            '20021233',
            '20021235',
            '20021237',
            '20021238',
            '20021240',
            '20021241',
            '20021244',
            '20021279',
            '20021297',
            '20021309',
            '20021312',
            '20021320',
            '20021325',
            '20021327',
            '20021328',
            '20021329',
            '20021331',
            '20021334',
            '20021335',
            '20021434',
            '20021513',
            '20021517',
            '20021522',
            '20021624',
            '20021625',
            '20021626',
            '20021650',
            '20021655',
            '20021720',
            '20021727',
            '20021731',
            '20021753',
            '20021782',
            '20021783',
            '20021784',
            '20021862',
            '20021866',
            '20021871',
            '20021882',
            '20021883',
            '20021886',
            '20022001',
            '20022005',
            '20022006',
            '20022009',
            '20022014',
            '20022016',
            '20022026',
            '20022028',
            '20022034',
            '20022060',
            '20022091',
            '20022092',
            '20022094',
            '20022095',
            '20022100',
            '20022260',
            '20022269',
            '20022299',
            '20022302',
            '20022328',
            '20022329',
            '20022367',
            '20022368',
            '20022377',
            '20022433',
            '20022471',
            '20022473',
            '20022480',
            '20022496',
            '20022530',
            '20022532',
            '20022533',
            '20022534',
            '20022538',
            '20022546',
            '20022575',
            '20022576',
            '20022629',
            '20022630',
            '20022632',
            '20022689',
            '20022691',
            '20022692',
            '20022693',
            '20022695',
            '20022697',
            '20022699',
            '20022742',
            '20022745',
            '20022753',
            '20022762',
            '20022772',
            '20022783',
            '20022785',
            '20022873',
            '20022874',
            '20022875',
            '20022876',
            '20022877',
            '20022878',
            '20022879',
            '20022880',
            '20022881',
            '20022882',
            '20022883',
            '20022884',
            '20022887',
            '20022889',
            '20022952',
            '20022953',
            '20022954',
            '20022958',
            '20023052',
            '20023193',
            '20023195',
            '20023200',
            '20023204',
            '20023208',
            '20023211',
            '20023218',
            '20023219',
            '20023228',
            '20023260',
            '20023267',
            '20023268',
            '20023356',
            '20023357',
            '20023361',
            '20023362',
            '20023363',
            '20023366',
            '20023370',
            '20023415',
            '20023421',
            '20023535',
            '20023562',
            '20023637',
            '20023651',
            '20023682',
            '20023684',
            '20023835',
            '20023839',
            '20023878',
            '20023888',
            '20023894',
            '20024010',
            '20024080',
            '20024113',
            '20024121',
            '20024295',
            '20024299',
            '20024375',
            '20024377',
            '20024378',
            '20024381',
            '20024428',
            '20024429',
            '20024431',
            '20024433',
            '20024459',
            '20024490',
            '20024491',
            '20024492',
            '20024494',
            '20024495',
            '20024499',
            '20024500',
            '20024602',
            '20024687',
            '20024688',
            '20024690',
            '20024694',
            '20024737',
            '20024822',
            '20024825',
            '20024861',
            '20024862',
            '20024867',
            '20024970',
            '20024977',
            '20024980',
            '20024983',
            '20024998',
            '20025022',
            '20025028',
            '20025032',
            '20025034',
            '20025085',
            '20025091',
            '20025092',
            '20025097',
            '20025123',
            '20025129',
            '20025134',
            '20025140',
            '20025156',
            '20025158',
            '20025260',
            '20025262',
            '20025267',
            '20025351',
            '20025355',
            '20025362',
            '20025365',
            '20025376',
            '20025398',
            '20025401',
            '20025407',
            '20025513',
            '20025515',
            '20025522',
            '20025633',
            '20025645',
            '20025662',
            '20025690',
            '20025694',
            '20025700',
            '20025701',
            '20025703',
            '20025726',
            '20025727',
            '20025729',
            '20025730',
            '20025732',
            '20025777',
            '20025779',
            '20025782',
            '20025783',
            '20025786',
            '20025787',
            '20025791',
            '20025823',
            '20025825',
            '20025830',
            '20025832',
            '20025834',
            '20025857',
            '20025858',
            '20025859',
            '20025862',
            '20025864',
            '20025869',
            '20025873',
            '20025874',
            '20025878',
            '20025879',
            '20025881',
            '20025883',
            '20025886',
            '20025887',
            '20025906',
            '20025908',
            '20025913',
            '20025916',
            '20025930',
            '20025936',
            '20025971',
            '20025972',
            '20026040',
            '20026041',
            '20026048',
            '20026049',
            '20026050',
            '20026126',
            '20026130',
            '20026131',
            '20026132',
            '20026133',
            '20026144',
            '20026148',
            '20026154',
            '20026158',
            '20026161',
            '20026165',
            '20026166',
            '20026221',
            '20026227',
            '20026228',
            '20026230',
            '20026280',
            '20026286',
            '20026287',
            '20026289',
            '20026312',
            '20026321',
            '20026330',
            '20026355',
            '20026357',
            '20026364',
            '20026366',
            '20026477',
            '20026483',
            '20026556',
            '20026559',
            '20026560',
            '20026561',
            '20026562',
            '20026581',
            '20026582',
            '20026592',
            '20026597',
            '20026735',
            '20026761',
            '20026767',
            '20026770',
            '20026773',
            '20026775',
            '20026778',
            '20026810',
            '20026812',
            '20026815',
            '20026843',
            '20026844',
            '20026893',
            '20026909',
            '20026913',
            '20026914',
            '20026950',
            '20026953',
            '20026958',
            '20026961',
            '20026962',
            '20026963',
            '20026970',
            '20026972',
            '20026973',
            '20027007',
            '20027010',
            '20027013',
            '20027015',
            '20027024',
            '20027048',
            '20027060',
            '20027061',
            '20027062',
            '20027108',
            '20027125',
            '20027134',
            '20027138',
            '20027140',
            '20027143',
            '20027150',
            '20027156',
            '20027216',
            '20027218',
            '20027232',
            '20027246',
            '20027267',
            '20027274',
            '20027305',
            '20027307',
            '20027308',
            '20027309',
            '20027311',
            '20027313',
            '20027314',
            '20027315',
            '20027341',
            '20027344',
            '20027345',
            '20027349',
            '20027353',
            '20027355',
            '20027364',
            '20027366',
            '20027368',
            '20027372',
            '20027396',
            '20027413',
            '20027419',
            '20027423',
            '20027507',
            '20027508',
            '20027569',
            '20027571',
            '20027573',
            '20027580',
            '20027591',
            '20027595',
            '20027607',
            '20027609',
            '20027610',
            '20027612',
            '20027614',
            '20027629',
            '20027632',
            '20027636',
            '20027646',
            '20027647',
            '20027716',
            '20027717',
            '20027724',
            '20027725',
            '20027750',
            '20027751',
            '20027752',
            '20027753',
            '20027762',
            '20027770',
            '20027771',
            '20027775',
            '20027789',
            '20027805',
            '20027807',
            '20027890',
            '20027929',
            '20027931',
            '20027932',
            '20027933',
            '20027934',
            '20027935',
            '20027936',
            '20027978',
            '20027979',
            '20027982',
            '20027984',
            '20027988',
            '20028005',
            '20028006',
            '20028007',
            '20028009',
            '20028012',
            '20028014',
            '20028015',
            '20028017',
            '20028025',
            '20028040',
            '20028041',
            '20028043',
            '20028044',
            '20028061',
            '20028065',
            '20028090',
            '20028123',
            '20028125',
            '20028130',
            '20028131',
            '20028132',
            '20028136',
            '20028137',
            '20028138',
            '20028176',
            '20028180',
            '20028181',
            '20028208',
            '20028214',
            '20028217',
            '20028224',
            '20028225',
            '20028226',
            '20028238',
            '20028249',
            '20028252',
            '20028255',
            '20028256',
            '20028272',
            '20028276',
            '20028278',
            '20028279',
            '20028282',
            '20028284',
            '20028285',
            '20028286',
            '20028291',
            '20028304',
            '20028312',
            '20028317',
            '20028318',
            '20028319',
            '20028320',
            '20028321',
            '20028323',
            '20028324',
            '20028325',
            '20028328',
            '20028360',
            '20028368',
            '20028415',
            '20028418',
            '20028419',
            '20028425',
            '20028427',
            '20028429',
            '20028438',
            '20028439',
            '20028449',
            '20028458',
            '20028494',
            '20028544',
            '20028550',
            '20028583',
            '20028588',
            '20028590',
            '20028592',
            '20028593',
            '20028595',
            '20028596',
            '20028654',
            '20028663',
            '20028671',
            '20028673',
            '20028674',
            '20028680',
            '20028684',
            '20028687',
            '20028689',
            '20028690',
            '20028720',
            '20028721',
            '20028723',
            '20028729',
            '20028731',
            '20028755',
            '20028756',
            '20028757',
            '20028854',
            '20028855',
            '20028856',
            '20028857',
            '20028859',
            '20028860',
            '20028930',
            '20028935',
            '20028937',
            '20028938',
            '20028942',
            '20028943',
            '20028944',
            '20028947',
            '20028950',
            '20028951',
            '20028953',
            '20029001',
            '20029002',
            '20029003',
            '20029012',
            '20029015',
            '20029020',
            '20029029',
            '20029033',
            '20029047',
            '20029048',
            '20029049',
            '20029050',
            '20029053',
            '20029056',
            '20029148',
            '20029181',
            '20029182',
            '20029185',
            '20029186',
            '20029187',
            '20029188',
            '20029204',
            '20029226',
            '20029230',
            '20029233',
            '20029303',
            '20029304',
            '20029305',
            '20029306',
            '20029314',
            '20029317',
            '20029319',
            '20029329',
            '20029330',
            '20029331',
            '20029358',
            '20029384',
            '20029418',
            '20029555',
            '20029560',
            '20029571',
            '20029572',
            '20029574',
            '20029575',
            '20029576',
            '20029580',
            '20029629',
            '20029645',
            '20029646',
            '20029650',
            '20029654',
            '20029656',
            '20029662',
            '20029664',
            '20029670',
            '20029671',
            '20029698',
            '20029699',
            '20029700',
            '20029701',
            '20029734',
            '20029761',
            '20029765',
            '20029771',
            '20029774',
            '20029798',
            '20029799',
            '20029812',
            '20029813',
            '20029823',
            '20029861',
            '20029862',
            '20029863',
            '20029864',
            '20029865',
            '20029868',
            '20029869',
            '20029870',
            '20029871',
            '20029883',
            '20029892',
            '20029985',
            '20029986',
            '20029987',
            '20030006',
            '20030007',
            '20030008',
            '20030010',
            '20030017',
            '20030018',
            '20030053',
            '20030055',
            '20030057',
            '20030058',
            '20030098',
            '20030102',
            '20030103',
            '20030106',
            '20030109',
            '20030120',
            '20030121',
            '20030122',
            '20030131',
            '20030138',
            '20030146',
            '20030147',
            '20030175',
            '20030177',
            '20030191',
            '20030192',
            '20030197',
            '20030200',
            '20030201',
            '20030202',
            '20030204',
            '20030205',
            '20030206',
            '20030207',
            '20030208',
            '20030274',
            '20030283',
            '20030294',
            '20030301',
            '20030305',
            '20030310',
            '20030356',
            '20030360',
            '20030366',
            '20030367',
            '20030368',
            '20030369',
            '20030370',
            '20030376',
            '20030379',
            '20030386',
            '20030397',
            '20030400',
            '20030402',
            '20030403',
            '20030432',
            '20030434',
            '20030435',
            '20030436',
            '20030438',
            '20030443',
            '20030448',
            '20030450',
            '20030452',
            '20030460',
            '20030468',
            '20030471',
            '20030490',
            '20030499',
            '20030500',
            '20030509',
            '20030650',
            '20030670',
            '20030673',
            '20030677',
            '20030679',
            '20030728',
            '20030729',
            '20030732',
            '20030733',
            '20030734',
            '20030735',
            '20030736',
            '20030737',
            '20030740',
            '20030748',
            '20030749',
            '20030751',
            '20030752',
            '20030753',
            '20030762',
            '20030771',
            '20030774',
            '20030794',
            '20030802',
            '20030803',
            '20030809',
            '20030859',
            '20030860',
            '20030890',
            '20030891',
            '20030949',
            '20030959',
            '20030991',
            '20031024',
            '20031035',
            '20031038',
            '20031039',
            '20031040',
            '20031108',
            '20031109',
            '20031112',
            '20031169',
            '20031184',
            '20031213',
            '20031262',
            '20031263',
            '20031264',
            '20031265',
            '20031267',
            '20031268',
            '20031271',
            '20031272',
            '20031277',
            '20031289',
            '20031290',
            '20031292',
            '20031297',
            '20031298',
            '20031310',
            '20031311',
            '20031312',
            '20031313',
            '20031315',
            '20031316',
            '20031317',
            '20031319',
            '20031320',
            '20031342',
            '20031343',
            '20031344',
            '20031348',
            '20031358',
            '20031359',
            '20031402',
            '20031403',
            '20031404',
            '20031405',
            '20031408',
            '20031426',
            '20031447',
            '20031448',
            '20031450',
            '20031452',
            '20031460',
            '20031547',
            '20031548',
            '20031549',
            '20031550',
            '20031551',
            '20031552',
            '20031553',
            '20031554',
            '20031555',
            '20031556',
            '20031557',
            '20031580',
            '20031581',
            '20031587',
            '20031589',
            '20031592',
            '20031594',
            '20031596',
            '20031696',
            '20031697',
            '20031739',
            '20031740',
            '20031741',
            '20031743',
            '20031744',
            '20031749',
            '20031750',
            '20031752',
            '20031753',
            '20031754',
            '20031755',
            '20031819',
            '20031822',
            '20031825',
            '20031827',
            '20031832',
            '20031833',
            '20031835',
            '20031838',
            '20031852',
            '20031853',
            '20031854',
            '20031855',
            '20031866',
            '20031889',
            '20031899',
            '20031993',
            '20031994',
            '20031995',
            '20031999',
            '20032002',
            '20032003',
            '20032008',
            '20032024',
            '20032029',
            '20032033',
            '20032060',
            '20032061',
            '20032062',
            '20032064',
            '20032071',
            '20032151',
            '20032152',
            '20032153',
            '20032204',
            '20032206',
            '20032207',
            '20032208',
            '20032211',
            '20032214',
            '20032217',
            '20032221',
            '20032222',
            '20032275',
            '20032295',
            '20032311',
            '20032314',
            '20032316',
            '20032319',
            '20032320',
            '20032322',
            '20032329',
            '20032330',
            '20032332',
            '20032333',
            '20032348',
            '20032349',
            '20032351',
            '20032353',
            '20032354',
            '20032402',
            '20032404',
            '20032407',
            '20032408',
            '20032409',
            '20032411',
            '20032412',
            '20032413',
            '20032417',
            '20032418',
            '20032420',
            '20032421',
            '20032422',
            '20032423',
            '20032424',
            '20032430',
            '20032432',
            '20032477',
            '20032484',
            '20032486',
            '20032490',
            '20032496',
            '20032539',
            '20032540',
            '20032541',
            '20032545',
            '20032547',
            '20032549',
            '20032591',
            '20032594',
            '20032604',
            '20032606',
            '20032681',
            '20032685',
            '20032700',
            '20032779',
            '20032784',
            '20032785',
            '20032787',
            '20032788',
            '20032789',
            '20032792',
            '20032797',
            '20032835',
            '20032837',
            '20032860',
            '20032862',
            '20032969',
            '20032973',
            '20032974',
            '20032979',
            '20032983',
            '20032984',
            '20032985',
            '20032987',
            '20032995',
            '20032997',
            '20032998',
            '20033002',
            '20033003',
            '20033004',
            '20033060',
            '20033192',
            '20033193',
            '20033198',
            '20033199',
            '20033206',
            '20033208',
            '20033209',
            '20033210',
            '20033211',
            '20033214',
            '20033215',
            '20033222',
            '20033240',
            '20033241',
            '20033283',
            '20033284',
            '20033287',
            '20033321',
            '20033322',
            '20033323',
            '20033325',
            '20033326',
            '20033332',
            '20033335',
            '20033346',
            '20033347',
            '20033354',
            '20033404',
            '20033421',
            '20033424',
            '20033425',
            '20033431',
            '20033434',
            '20033438',
            '20033463',
            '20033464',
            '20033467',
            '20033474',
            '20033611',
            '20033612',
            '20033613',
            '20033615',
            '20033616',
            '20033623',
            '20033625',
            '20033626',
            '20033627',
            '20033629',
            '20033630',
            '20033631',
            '20033647',
            '20033648',
            '20033652',
            '20033656',
            '20033659',
            '20033681',
            '20033683',
            '20033771',
            '20033773',
            '20033776',
            '20033778',
            '20033829',
            '20033846',
            '20033847',
            '20033848',
            '20033877',
            '20033882',
            '20033907',
            '20033913',
            '20033914',
            '20033929',
            '20033965',
            '20033967',
            '20033973',
            '20033974',
            '20033990',
            '20034018',
            '20034019',
            '20034041',
            '20034042',
            '20034045',
            '20034046',
            '20034047',
            '20034052',
            '20034073',
            '20034092',
            '20034110',
            '20034111',
            '20034121',
            '20034123',
            '20034129',
            '20034130',
            '20034132',
            '20034141',
            '20034156',
            '20034166',
            '20034169',
            '20034187',
            '20034230',
            '20034241',
            '20034248',
            '20034274',
            '20034276',
            '20034279',
            '20034280',
            '20034284',
            '20034288',
            '20034289',
            '20034291',
            '20034328',
            '20034329',
            '20034339',
            '20034340',
            '20034342',
            '20034365',
            '20034387',
            '20034389',
            '20034407',
            '20034408',
            '20034443',
            '20034444',
            '20034445',
            '20034473',
            '20034478',
            '20034488',
            '20034493',
            '20034495',
            '20034496',
            '20034498',
            '20034501',
            '20034503',
            '20034520',
            '20034521',
            '20034523',
            '20034524',
            '20034525',
            '20034554',
            '20034555',
            '20034677',
            '20034679',
            '20034684',
            '20034687',
            '20034688',
            '20034730',
            '20034755',
            '20034774',
            '20034793',
            '20034794',
            '20034803',
            '20034804',
            '20034807',
            '20034808',
            '20034809',
            '20034854',
            '20034855',
            '20034865',
            '20034868',
            '20034869',
            '20034870',
            '20034897',
            '20034905',
            '20034925',
            '20034927',
            '20034931',
            '20034932',
            '20034935',
            '20034936',
            '20034937',
            '20034938',
            '20034950',
            '20034951',
            '20034954',
            '20034987',
            '20035030',
            '20035032',
            '20035059',
            '20035068',
            '20035069',
            '20035070',
            '20035071',
            '20035072',
            '20035073',
            '20035074',
            '20035076',
            '20035137',
            '20035174',
            '20035196',
            '20035223',
            '20035232',
            '20035233',
            '20035234',
            '20035235',
            '20035236',
            '20035237',
            '20035238',
            '20035239',
            '20035244',
            '20035245',
            '20035246',
            '20035249',
            '20035257',
            '20035265',
            '20035266',
            '20035267',
            '20035270',
            '20035271',
            '20035272',
            '20035279',
            '20035302',
            '20035303',
            '20035308',
            '20035316',
            '20035321',
            '20035368',
            '20035393',
            '20035430',
            '20035445',
            '20035446',
            '20035447',
            '20035449',
            '20035458',
            '20035459',
            '20035461',
            '20035476',
            '20035489',
            '20035497',
            '20035511',
            '20035537',
            '20035552',
            '20035559',
            '20035560',
            '20035561',
            '20035562',
            '20035592',
            '20035593',
            '20035595',
            '20035616',
            '20035617',
            '20035629',
            '20035632',
            '20035652',
            '20035662',
            '20035663',
            '20035670',
            '20035673',
            '20035674',
            '20035752',
            '20035755',
            '20035756',
            '20035785',
            '20035788',
            '20035789',
            '20035802',
            '20035803',
            '20035806',
            '20035807',
            '20035809',
            '20035810',
            '20035812',
            '20035813',
            '20035815',
            '20035818',
            '20035838',
            '20035843',
            '20035844',
            '20035879',
            '20035914',
            '20035916',
            '20035917',
            '20035926',
            '20035934',
            '20035941',
            '20035943',
            '20035956',
            '20035960',
            '20035961',
            '20035992',
            '20035995',
            '20035997',
            '20035998',
            '20036015',
            '20036018',
            '20036019',
            '20036022',
            '20036060',
            '20036075',
            '20036077',
            '20036085',
            '20036092',
            '20036113',
            '20036114',
            '20036154',
            '20036156',
            '20036182',
            '20036196',
            '20036198',
            '20036199',
            '20036267',
            '20036270',
            '20036281',
            '20036282',
            '20036283',
            '20036284',
            '20036285',
            '20036286',
            '20036320',
            '20036322',
            '20036323',
            '20036324',
            '20036325',
            '20036327',
            '20036328',
            '20036331',
            '20036332',
            '20036333',
            '20036334',
            '20036364',
            '20036365',
            '20036367',
            '20036368',
            '20036397',
            '20036402',
            '20036403',
            '20036405',
            '20036407',
            '20036408',
            '20036409',
            '20036410',
            '20036426',
            '20036428',
            '20036430',
            '20036432',
            '20036433',
            '20036434',
            '20036435',
            '20036436',
            '20036437',
            '20036438',
            '20036439',
            '20036440',
            '20036442',
            '20036444',
            '20036446',
            '20036450',
            '20036453',
            '20036482',
            '20036493',
            '20036495',
            '20036504',
            '20036543',
            '20036544',
            '20036546',
            '20036552',
            '20036604',
            '20036609',
            '20036649',
            '20036651',
            '20036652',
            '20036658',
            '20036664',
            '20036666',
            '20036667',
            '20036674',
            '20036676',
            '20036677',
            '20036699',
            '20036711',
            '20036712',
            '20036715',
            '20036763',
            '20036811',
            '20036813',
            '20036815',
            '20036816',
            '20036820',
            '20036823',
            '20036838',
            '20036841',
            '20036855',
            '20036874',
            '20036885',
            '20036886',
            '20036887',
            '20036888',
            '20036898',
            '20036905',
            '20036910',
            '20036941',
            '20036945',
            '20036948',
            '20036953',
            '20036984',
            '20037006',
            '20037017',
            '20037036',
            '20037039',
            '20037094',
            '20037095',
            '20037097',
            '20037139',
            '20037141',
            '20037142',
            '20037143',
            '20037161',
            '20037163',
            '20037166',
            '20037191',
            '20037193',
            '20037194',
            '20037200',
            '20037201',
            '20037202',
            '20037203',
            '20037207',
            '20037212',
            '20037213',
            '20037224',
            '20037225',
            '20037235',
            '20037236',
            '20037237',
            '20037242',
            '20037243',
            '20037251',
            '20037252',
            '20037258',
            '20037344',
            '20037347',
            '20037362',
            '20037370',
            '20037373',
            '20037378',
            '20037379',
            '20037380',
            '20037421',
            '20037555',
            '20037564',
            '20037573',
            '20037577',
            '20037584',
            '20037587',
            '20037590',
            '20037591',
            '20037592',
            '20037593',
            '20037653',
            '20037656',
            '20037657',
            '20037658',
            '20037680',
            '20037681',
            '20037682',
            '20037684',
            '20037695',
            '20037739',
            '20037740',
            '20037742',
            '20037744',
            '20037768',
            '20037771',
            '20037772',
            '20037773',
            '20037793',
            '20037806',
            '20037834',
            '20037836',
            '20037848',
            '20037863',
            '20037865',
            '20037867',
            '20037868',
            '20037869',
            '20037875',
            '20037893',
            '20037896',
            '20037902',
            '20037904',
            '20037922',
            '20037928',
            '20037929',
            '20037930',
            '20037961',
            '20037981',
            '20037987',
            '20037989',
            '20037991',
            '20037994',
            '20037995',
            '20037996',
            '20038001',
            '20038002',
            '20038003',
            '20038004',
            '20038057',
            '20038059',
            '20038090',
            '20038093',
            '20038095',
            '20038121',
            '20038157',
            '20038158',
            '20038159',
            '20038160',
            '20038161',
            '20038162',
            '20038177',
            '20038180',
            '20038181',
            '20038250',
            '20038251',
            '20038252',
            '20038258',
            '20038260',
            '20038261',
            '20038262',
            '20038298',
            '20038301',
            '20038308',
            '20038310',
            '20038311',
            '20038338',
            '20038343',
            '20038348',
            '20038359',
            '20038362',
            '20038363',
            '20038364',
            '20038373',
            '20038374',
            '20038386',
            '20038432',
            '20038433',
            '20038453',
            '20038454',
            '20038455',
            '20038458',
            '20038480',
            '20038482',
            '20038493',
            '20038497',
            '20038504',
            '20038524',
            '20038526',
            '20038529',
            '20038532',
            '20038537',
            '20038539',
            '20038562',
            '20038577',
            '20038583',
            '20038587',
            '20038588',
            '20038589',
            '20038590',
            '20038591',
            '20038600',
            '20038601',
            '20038604',
            '20038605',
            '20038653',
            '20038655',
            '20038678',
            '20038687',
            '20038691',
            '20038693',
            '20038694',
            '20038695',
            '20038698',
            '20038705',
            '20038710',
            '20038713',
            '20038720',
            '20038732',
            '20038740',
            '20038742',
            '20038748',
            '20038800',
            '20038802',
            '20038803',
            '20038806',
            '20038812',
            '20038813',
            '20038905',
            '20038907',
            '20038910',
            '20038911',
            '20038932',
            '20038933',
            '20038934',
            '20038937',
            '20038969',
            '20038970',
            '20038971',
            '20038983',
            '20038994',
            '20039005',
            '20039007',
            '20039008',
            '20039009',
            '20039011',
            '20039013',
            '20039049',
            '20039061',
            '20039095',
            '20039096',
            '20039101',
            '20039102',
            '20039103',
            '20039106',
            '20039108',
            '20039137',
            '20039138',
            '20039188',
            '20039190',
            '20039192',
            '20039193',
            '20039197',
            '20039206',
            '20039207',
            '20039208',
            '20039215',
            '20039217',
            '20039218',
            '20039221',
            '20039223',
            '20039225',
            '20039227',
            '20039229',
            '20039231',
            '20039236',
            '20039242',
            '20039273',
            '20039274',
            '20039280',
            '20039283',
            '20039309',
            '20039310',
            '20039311',
            '20039314',
            '20039337',
            '20039342',
            '20039344',
            '20039345',
            '20039348',
            '20039349',
            '20039387',
            '20039389',
            '20039390',
            '20039394',
            '20039397',
            '20039403',
            '20039437',
            '20039438',
            '20039464',
            '20039465',
            '20039487',
            '20039489',
            '20039491',
            '20039503',
            '20039506',
            '20039509',
            '20039541',
            '20039548',
            '20039580',
            '20039581',
            '20039583',
            '20039585',
            '20039607',
            '20039617',
            '20039632',
            '20039633',
            '20039635',
            '20039637',
            '20039677',
            '20039678',
            '20039710',
            '20039711',
            '20039731',
            '20039739',
            '20039743',
            '20039744',
            '20039773',
            '20039776',
            '20039777',
            '20039801',
            '20039802',
            '20039803',
            '20039833',
            '20039835',
            '20039836',
            '20039839',
            '20039860',
            '20039861',
            '20039862',
            '20039867',
            '20039880',
            '20039904',
            '20039913',
            '20039935',
            '20039944',
            '20039946',
            '20039947',
            '20039977',
            '20039980',
            '20039998',
            '20040010',
            '20040030',
            '20040031',
            '20040034',
            '20040073',
            '20040079',
            '20040081',
            '20040082',
            '20040087',
            '20040102',
            '20040103',
            '20040110',
            '20040136',
            '20040137',
            '20040142',
            '20040143',
            '20040144',
            '20040147',
            '20040151',
            '20040152',
            '20040215',
            '20040256',
            '20040259',
            '20040260',
            '20040261',
            '20040262',
            '20040270',
            '20040296',
            '20040299',
            '20040358',
            '20040360',
            '20040362',
            '20040366',
            '20040367',
            '20040368',
            '20040395',
            '20040397',
            '20040399',
            '20040400',
            '20040404',
            '20040448',
            '20040450',
            '20040451',
            '20040464',
            '20040493',
            '20040496',
            '20040501',
            '20040503',
            '20040506',
            '20040513',
            '20040519',
            '20040532',
            '20040572',
            '20040573',
            '20040574',
            '20040575',
            '20040612',
            '20040618',
            '20040620',
            '20040629',
            '20040632',
            '20040633',
            '20040634',
            '20040636',
            '20040661',
            '20040662',
            '20040663',
            '20040740',
            '20040746',
            '20040773',
            '20040777',
            '20040816',
            '20040819',
            '20040823',
            '20040828',
            '20040829',
            '20040831',
            '20040834',
            '20040835',
            '20040859',
            '20040861',
            '20040862',
            '20040930',
            '20040931',
            '20040933',
            '20040944',
            '20040947',
            '20040954',
            '20040955',
            '20040969',
            '20040978',
            '20040979',
            '20040981',
            '20040983',
            '20040985',
            '20040987',
            '20040989',
            '20041000',
            '20041015',
            '20041031',
            '20041032',
            '20041078',
            '20041084',
            '20041086',
            '20041087',
            '20041142',
            '20041143',
            '20041145',
            '20041166',
            '20041172',
            '20041176',
            '20041178',
            '20041180',
            '20041181',
            '20041182',
            '20041184',
            '20041186',
            '20041188',
            '20041206',
            '20041207',
            '20041209',
            '20041217',
            '20041218',
            '20041220',
            '20041221',
            '20041306',
            '20041307',
            '20041308',
            '20041312',
            '20041313',
            '20041314',
            '20041316',
            '20041317',
            '20041318',
            '20041331',
            '20041333',
            '20041337',
            '20041462',
            '20041464',
            '20041487',
            '20041502',
            '20041504',
            '20041505',
            '20041506',
            '20041508',
            '20041509',
            '20041514',
            '20041515',
            '20041536',
            '20041546',
            '20041549',
            '20041627',
            '20041641',
            '20041643',
            '20041656',
            '20041721',
            '20041724',
            '20041747',
            '20041772',
            '20041794',
            '20041796',
            '20041797',
            '20041801',
            '20041802',
            '20041809',
            '20041810',
            '20041844',
            '20041847',
            '20041906',
            '20041907',
            '20041911',
            '20041912',
            '20041913',
            '20041914',
            '20041958',
            '20041959',
            '20041960',
            '20042012',
            '20042013',
            '20042056',
            '20042097',
            '20042146',
            '20042147',
            '20042158',
            '20042162',
            '20042164',
            '20042195',
            '20042197',
            '20042198',
            '20042265',
            '20042338',
            '20042339',
            '20042340',
            '20042372',
            '20042374',
            '20042375',
            '20042379',
            '20042420',
            '20042423',
            '20042425',
            '20042442',
            '20042487',
            '20042491',
            '20042493',
            '20042522',
            '20042527',
            '20042528',
            '20042532',
            '20042569',
            '20042571',
            '20042574',
            '20042575',
            '20042578',
            '20042579',
            '20042607',
            '20042613',
            '20042615',
            '20042657',
            '20042658',
            '20042659',
            '20042727',
            '20042746',
            '20042764',
            '20042765',
            '20042766',
            '20042767',
            '20042770',
            '20042830',
            '20042835',
            '20042847',
            '20042868',
            '20042880',
            '20042881',
            '20042908',
            '20042915',
            '20042919',
            '20042921',
            '20042924',
            '20042928',
            '20042929',
            '20043004',
            '20043034',
            '20043040',
            '20043041',
            '20043042',
            '20043156',
            '20043157',
            '20043165',
            '20043166',
            '20043170',
            '20043175',
            '20043179',
            '20043182',
            '20043185',
            '20043187',
            '20043190',
            '20043229',
            '20043253',
            '20043259',
            '20043260',
            '20043275',
            '20043346',
            '20043355',
            '20043362',
            '20043364',
            '20043409',
            '20043414',
            '20043415',
            '20043417',
            '20043419',
            '20043480',
            '20043481',
            '20043546',
            '20043597',
            '20043624',
            '20043647',
            '20043663',
            '20043668',
            '20043707',
            '20043723',
            '20043724',
            '20043725',
            '20043726',
            '20043727',
            '20043728',
            '20043842',
            '20043843',
            '20043845',
            '20043944',
            '20043953',
            '20043962',
            '20044018',
            '20044039',
            '20044089',
            '20044092',
            '20044094',
            '20044107',
            '20044191',
            '20044194',
            '20044196',
            '20044206',
            '20044217',
            '20044221',
            '20044222',
            '20044223',
            '20044250',
            '20044257',
            '20044259',
            '20044278',
            '20044280',
            '20044287',
            '20044288',
            '20044290',
            '20044328',
            '20044344',
            '20044353',
            '20044393',
            '20044394',
            '20044397',
            '20044404',
            '20044405',
            '20044406',
            '20044408',
            '20044409',
            '20044473',
            '20044479',
            '20044581',
            '20044582',
            '20044583',
            '20044609',
            '20044610',
            '20044633',
            '20044636',
            '20044638',
            '20044678',
            '20044703',
            '20044713',
            '20044715',
            '20044717',
            '20044718',
            '20044719',
            '20044781',
            '20044791',
            '20044797',
            '20044807',
            '20044809',
            '20044810',
            '20044811',
            '20044828',
            '20044834',
            '20044868',
            '20044869',
            '20044872',
            '20044873',
            '20044875',
            '20044877',
            '20044880',
            '20044881',
            '20044885',
            '20044905',
            '20044917',
            '20044918',
            '20044920',
            '20044932',
            '20044953',
            '20044955',
            '20045084',
            '20045085',
            '20045087',
            '20045092',
            '20045119',
            '20045131',
            '20045145',
            '20045152',
            '20045155',
            '20045171',
            '20045264',
            '20045278',
            '20045284',
            '20045295',
            '20045365',
            '20045478',
            '20045479',
            '20045480',
            '20045530',
            '20045540',
            '20045566',
            '20045570',
            '20045686',
            '20045687',
            '20045704',
            '20045706',
            '20045708',
            '20045709',
            '20045710',
            '20045727',
            '20045801',
            '20045812',
            '20045813',
            '20045851',
            '20045875',
            '20045878',
            '20045985',
            '20045986',
            '20045992',
            '20045993',
            '20046101',
            '20046102',
            '20046103',
            '20046151',
            '20046201',
            '20046202',
            '20046203',
            '20046216',
            '20046244',
            '20046279',
            '20046294',
            '20046295',
            '20046296',
            '20046328',
            '20046416',
            '20046439',
            '20046443',
            '20046464',
            '20046469',
            '20046482',
            '20046530',
            '20046532',
            '20046537',
            '20046538',
            '20046630',
            '20046631',
            '20046632',
            '20046633',
            '20046634',
            '20046636',
            '20046641',
            '20046662',
            '20046687',
            '20046759',
            '20046763',
            '20046766',
            '20046768',
            '20046769',
            '20046841',
            '20046842',
            '20046863',
            '20046881',
            '20046884',
            '20046895',
            '20046938',
            '20047022',
            '20047024',
            '20047026',
            '20047030',
            '20047031',
            '20047072',
            '20047080',
            '20047093',
            '20047094',
            '20047095',
            '20047139',
            '20047142',
            '20047219',
            '20047220',
            '20047231',
            '20047233',
            '20047234',
            '20047235',
            '20047260',
            '20047263',
            '20047265',
            '20047267',
            '20047326',
            '20047394',
            '20047395',
            '20047396',
            '20047433',
            '20047436',
            '20047438',
            '20047473',
            '20047486',
            '20047488',
            '20047491',
            '20047502',
            '20047503',
            '20047504',
            '20047547',
            '20047550',
            '20047639',
            '20047704',
            '20047773',
            '20047802',
            '20047808',
            '20047809',
            '20047842',
            '20047843',
            '20047861',
            '20047888',
            '20047890',
            '20047895',
            '20047897',
            '20047911',
            '20047959',
            '20047961',
            '20047971',
            '20047973',
            '20047991',
            '20047993',
            '20048001',
            '20048034',
            '20048035',
            '20048047',
            '20048050',
            '20048056',
            '20048072',
            '20048079',
            '20048083',
            '20048084',
            '20048091',
            '20048092',
            '20048111',
            '20048113',
            '20048116',
            '20048120',
            '20048142',
            '20048173',
            '20048174',
            '20048219',
            '20048220',
            '20048221',
            '20048222',
            '20048259',
            '20048265',
            '20048290',
            '20048291',
            '20048294',
            '20048295',
            '20048296',
            '20048301',
            '20048303',
            '20048315',
            '20048316',
            '20048317',
            '20048318',
            '20048319',
            '20048320',
            '20048321',
            '20048332',
            '20048336',
            '20048340',
            '20048424',
            '20048503',
            '20048505',
            '20048506',
            '20048507',
            '20048514',
            '20048515',
            '20048539',
            '20048568',
            '20048569',
            '20048612',
            '20048647',
            '20048653',
            '20048655',
            '20048692',
            '20048699',
            '20048702',
            '20048716',
            '20048721',
            '20048725',
            '20048769',
            '20048770',
            '20048791',
            '20048804',
            '20048836',
            '20048844',
            '20048885',
            '20048887',
            '20048888',
            '20048893',
            '20048914',
            '20048917',
            '20048926',
            '20048934',
            '20048947',
            '20048950',
            '20048953',
            '20048958',
            '20048963',
            '20048965',
            '20048990',
            '20048992',
            '20048993',
            '20049000',
            '20049003',
            '20049005',
            '20049007',
            '20049047',
            '20049049',
            '20049052',
            '20049076',
            '20049077',
            '20049079',
            '20049084',
            '20049113',
            '20049114',
            '20049115',
            '20049120',
            '20049138',
            '20049143',
            '20049148',
            '20049157',
            '20049158',
            '20049159',
            '20049160',
            '20049161',
            '20049163',
            '20049204',
            '20049205',
            '20049206',
            '20049208',
            '20049211',
            '20049215',
            '20049233',
            '20049254',
            '20049255',
            '20049256',
            '20049259',
            '20049260',
            '20049293',
            '20049295',
            '20049296',
            '20049297',
            '20049334',
            '20049337',
            '20049343',
            '20049344',
            '20049348',
            '20049349',
            '20049351',
            '20049354',
            '20049360',
            '20049368',
            '20049379',
            '20049382',
            '20049385',
            '20049409',
            '20049410',
            '20049413',
            '20049419',
            '20049426',
            '20049446',
            '20049451',
            '20049452',
            '20049453',
            '20049455',
            '20049473',
            '20049475',
            '20049548',
            '20049550',
            '20049551',
            '20049568',
            '20049578',
            '20049584',
            '20049587',
            '20049606',
            '20049642',
            '20049644',
            '20049649',
            '20049658',
            '20049660',
            '20049661',
            '20049663',
            '20049664',
            '20049777',
            '20049782',
            '20049786',
            '20049795',
            '20049800',
            '20049801',
            '20049802',
            '20049804',
            '20049808',
            '20049809',
            '20049811',
            '20049813',
            '20049814',
            '20049815',
            '20049848',
            '20049895',
            '20049909',
            '20049912',
            '20049956',
            '20049981',
            '20049982',
            '20049983',
            '20049988',
            '20049991',
            '20049993',
            '20049996',
            '20050000',
            '20050001',
            '20050007',
            '20050008',
            '20050010',
            '20050012',
            '20050015',
            '20050019',
            '20050020',
            '20050022',
            '20050024',
            '20050063',
            '20050065',
            '20050066',
            '20050067',
            '20050069',
            '20050070',
            '20050071',
            '20050072',
            '20050080',
            '20050081',
            '20050082',
            '20050086',
            '20050087',
            '20050090',
            '20050091',
            '20050098',
            '20050143',
            '20050145',
            '20050148',
            '20050150',
            '20050151',
            '20050152',
            '20050154',
            '20050155',
            '20050156',
            '20050157',
            '20050176',
            '20050179',
            '20050180',
            '20050182',
            '20050184',
            '20050191',
            '20050192',
            '20050199',
            '20050203',
            '20050204',
            '20050205',
            '20050206',
            '20050207',
            '20050209',
            '20050210',
            '20050212',
            '20050214',
            '20050215',
            '20050233',
            '20050235',
            '20050236',
            '20050237',
            '20050238',
            '20050239',
            '20050240',
            '20050241',
            '20050242',
            '20050243',
            '20050265',
            '20050267',
            '20050268',
            '20050286',
            '20050287',
            '20050288',
            '20050304',
            '20050305',
            '20050306',
            '20050309',
            '20050352',
            '20050370',
            '20050371',
            '20050426',
            '20050427',
            '20050428',
            '20050446',
            '20050451',
            '20050454',
            '20050456',
            '20050457',
            '20050458',
            '20050478',
            '20050479',
            '20050481',
            '20050484',
            '20050486',
            '20050487',
            '20050503',
            '20050532',
            '20050535',
            '20050538',
            '20050600',
            '20050601',
            '20050602',
            '20050603',
            '20050617',
            '20050646',
            '20050651',
            '20050658',
            '20050667',
            '20050678',
            '20050681',
            '20050700',
            '20050706',
            '20050708',
            '20050709',
            '20050710',
            '20050711',
            '20050712',
            '20050713',
            '20050714',
            '20050715',
            '20050716',
            '20050717',
            '20050718',
            '20050719',
            '20050723',
            '20050730',
            '20050742',
            '20050792',
            '20050797',
            '20050832',
            '20050833',
            '20050839',
            '20050844',
            '20050889',
            '20050921',
            '20050956',
            '20050958',
            '20050964',
            '20050965',
            '20050968',
            '20050970',
            '20050974',
            '20050976',
            '20050984',
            '20051021',
            '20051023',
            '20051024',
            '20051025',
            '20051048',
            '20051073',
            '20051075',
            '20051076',
            '20051077',
            '20051078',
            '20051093',
            '20051119',
            '20051135',
            '20051136',
            '20051137',
            '20051138',
            '20051140',
            '20051143',
            '20051150',
            '20051151',
            '20051152',
            '20051157',
            '20051159',
            '20051203',
            '20051232',
            '20051234',
            '20051241',
            '20051246',
            '20051295',
            '20051304',
            '20051305',
            '20051307',
            '20051313',
            '20051323',
            '20051326',
            '20051344',
            '20051346',
            '20051354',
            '20051355',
            '20051396',
            '20051397',
            '20051410',
            '20051465',
            '20051466',
            '20051509',
            '20051510',
            '20051521',
            '20051522',
            '20051523',
            '20051524',
            '20051525',
            '20051527',
            '20051529',
            '20051533',
            '20051535',
            '20051544',
            '20051550',
            '20051553',
            '20051556',
            '20051557',
            '20051565',
            '20051571',
            '20051572',
            '20051573',
            '20051591',
            '20051611',
            '20051612',
            '20051614',
            '20051615',
            '20051616',
            '20051619',
            '20051622',
            '20051628',
            '20051630',
            '20051631',
            '20051632',
            '20051633',
            '20051634',
            '20051660',
            '20051706',
            '20051709',
            '20051710',
            '20051742',
            '20051743',
            '20051744',
            '20051761',
            '20051762',
            '20051765',
            '20051785',
            '20051786',
            '20051835',
            '20051837',
            '20051838',
            '20051839',
            '20051847',
            '20051848',
            '20051853',
            '20051868',
            '20051869',
            '20051870',
            '20051879',
            '20051898',
            '20051899',
            '20051902',
            '20051904',
            '20051908',
            '20051911',
            '20051912',
            '20051914',
            '20051915',
            '20051918',
            '20051964',
            '20051977',
            '20051979',
            '20051981',
            '20051983',
            '20051991',
            '20051992',
            '20051993',
            '20051994',
            '20052044',
            '20052070',
            '20052085',
            '20052086',
            '20052090',
            '20052101',
            '20052102',
            '20052103',
            '20052131',
            '20052134',
            '20052135',
            '20052137',
            '20052140',
            '20052153',
            '20052156',
            '20052211',
            '20052212',
            '20052238',
            '20052256',
            '20052265',
            '20052266',
            '20052267',
            '20052309',
            '20052312',
            '20052315',
            '20052326',
            '20052337',
            '20052372',
            '20052373',
            '20052375',
            '20052378',
            '20052380',
            '20052381',
            '20052383',
            '20052385',
            '20052391',
            '20052392',
            '20052394',
            '20052395',
            '20052413',
            '20052415',
            '20052416',
            '20052423',
            '20052429',
            '20052481',
            '20052482',
            '20052506',
            '20052508',
            '20052555',
            '20052576',
            '20052578',
            '20052583',
            '20052588',
            '20052591',
            '20052594',
            '20052595',
            '20052642',
            '20052647',
            '20052654',
            '20052661',
            '20052682',
            '20052683',
            '20052684',
            '20052685',
            '20052723',
            '20052724',
            '20052726',
            '20052732',
            '20052740',
            '20052741',
            '20052752',
            '20052788',
            '20052795',
            '20052799',
            '20052801',
            '20052803',
            '20052824',
            '20052825',
            '20052827',
            '20052828',
            '20052829',
            '20052831',
            '20052835',
            '20052836',
            '20052859',
            '20052860',
            '20052864',
            '20052865',
            '20052889',
            '20052890',
            '20052910',
            '20052912',
            '20052915',
            '20052919',
            '20052920',
            '20052921',
            '20052933',
            '20052934',
            '20052935',
            '20052937',
            '20052949',
            '20052952',
            '20052959',
            '20052974',
            '20052975',
            '20052983',
            '20053005',
            '20053013',
            '20053014',
            '20053059',
            '20053062',
            '20053067',
            '20053068',
            '20053071',
            '20053074',
            '20053075',
            '20053077',
            '20053078',
            '20053127',
            '20053129',
            '20053132',
            '20053142',
            '20053156',
            '20053174',
            '20053176',
            '20053177',
            '20053179',
            '20053189',
            '20053190',
            '20053213',
            '20053214',
            '20053226',
            '20053229',
            '20053230',
            '20053232',
            '20053233',
            '20053235',
            '20053272',
            '20053275',
            '20053305',
            '20053309',
            '20053310',
            '20053332',
            '20053336',
            '20053357',
            '20053358',
            '20053373',
            '20053412',
            '20053413',
            '20053415',
            '20053417',
            '20053420',
            '20053439',
            '20053443',
            '20053445',
            '20053472',
            '20053473',
            '20053484',
            '20053485',
            '20053486',
            '20053493',
            '20053494',
            '20053496',
            '20053498',
            '20053499',
            '20053500',
            '20053502',
            '20053505',
            '20053513',
            '20053518',
            '20053555',
            '20053560',
            '20053567',
            '20053583',
            '20053585',
            '20053586',
            '20053589',
            '20053617',
            '20053618',
            '20053624',
            '20053627',
            '20053639',
            '20053655',
            '20053656',
            '20053657',
            '20053659',
            '20053662',
            '20053678',
            '20053679',
            '20053680',
            '20053681',
            '20053693',
            '20053725',
            '20053727',
            '20053733',
            '20053735',
            '20053741',
            '20053754',
            '20053757',
            '20053784',
            '20053785',
            '20053788',
            '20053792',
            '20053793',
            '20053794',
            '20053797',
            '20053798',
            '20053803',
            '20053814',
            '20053816',
            '20053825',
            '20053827',
            '20053842',
            '20053896',
            '20053898',
            '20053901',
            '20053903',
            '20053904',
            '20053906',
            '20053907',
            '20053908',
            '20053909',
            '20053911',
            '20053912',
            '20053913',
            '20053914',
            '20053918',
            '20053949',
            '20053955',
            '20053959',
            '20053960',
            '20053961',
            '20053971',
            '20053987',
            '20053991',
            '20053992',
            '20053993',
            '20054026',
            '20054033',
            '20054037',
            '20054055',
            '20054059',
            '20054074',
            '20054088',
            '20054113',
            '20054127',
            '20054128',
            '20054136',
            '20054138',
            '20054145',
            '20054153',
            '20054161',
            '20054162',
            '20054168',
            '20054169',
            '20054225',
            '20054227',
            '20054228',
            '20054249',
            '20054311',
            '20054313',
            '20054315',
            '20054343',
            '20054344',
            '20054345',
            '20054346',
            '20054355',
            '20054359',
            '20054361',
            '20054363',
            '20054382',
            '20054386',
            '20054389',
            '20054393',
            '20054395',
            '20054396',
            '20054400',
            '20054403',
            '20054405',
            '20054407',
            '20054412',
            '20054421',
            '20054422',
            '20054423',
            '20054488',
            '20054511',
            '20054529',
            '20054530',
            '20054531',
            '20054545',
            '20054552',
            '20054553',
            '20054554',
            '20054584',
            '20054589',
            '20054590',
            '20054591',
            '20054592',
            '20054593',
            '20054600',
            '20054601',
            '20054606',
            '20054611',
            '20054678',
            '20054713',
            '20054746',
            '20054756',
            '20054759',
            '20054766',
            '20054770',
            '20054800',
            '20054804',
            '20054805',
            '20054806',
            '20054807',
            '20054839',
            '20054840',
            '20054854',
            '20054855',
            '20054862',
            '20054867',
            '20054923',
            '20054928',
            '20054935',
            '20054936',
            '20054939',
            '20054955',
            '20054965',
            '20054966',
            '20054967',
            '20054969',
            '20054971',
            '20054972',
            '20054981',
            '20054983',
            '20054988',
            '20054989',
            '20055013',
            '20055014',
            '20055058',
            '20055059',
            '20055061',
            '20055102',
            '20055107',
            '20055113',
            '20055114',
            '20055115',
            '20055121',
            '20055131',
            '20055155',
            '20055158',
            '20055159',
            '20055180',
            '20055181',
            '20055183',
            '20055189',
            '20055192',
            '20055194',
            '20055196',
            '20055208',
            '20055212',
            '20055215',
            '20055216',
            '20055269',
            '20055272',
            '20055277',
            '20055278',
            '20055294',
            '20055295',
            '20055304',
            '20055306',
            '20055308',
            '20055320',
            '20055333',
            '20055334',
            '20055335',
            '20055342',
            '20055361',
            '20055363',
            '20055368',
            '20055399',
            '20055400',
            '20055401',
            '20055441',
            '20055444',
            '20055451',
            '20055471',
            '20055472',
            '20055489',
            '20055492',
            '20055493',
            '20055494',
            '20055511',
            '20055512',
            '20055524',
            '20055526',
            '20055534',
            '20055535',
            '20055536',
            '20055544',
            '20055554',
            '20055583',
            '20055597',
            '20055598',
            '20055599',
            '20055620',
            '20055621',
            '20055622',
            '20055623',
            '20055624',
            '20055626',
            '20055642',
            '20055649',
            '20055669',
            '20055670',
            '20055674',
            '20055675',
            '20055676',
            '20055699',
            '20055700',
            '20055701',
            '20055702',
            '20055703',
            '20055704',
            '20055707',
            '20055714',
            '20055715',
            '20055741',
            '20055765',
            '20055773',
            '20055774',
            '20055777',
            '20055778',
            '20055782',
            '20055793',
            '20055797',
            '20055839',
            '20055848',
            '20055849',
            '20055869',
            '20055874',
            '20055879',
            '20055880',
            '20055881',
            '20055883',
            '20055885',
            '20055907',
            '20055909',
            '20055912',
            '20055913',
            '20055936',
            '20055938',
            '20055941',
            '20055942',
            '20055943',
            '20055946',
            '20055948',
            '20055949',
            '20055953',
            '20055955',
            '20055957',
            '20055962',
            '20055963',
            '20055964',
            '20055991',
            '20055993',
            '20055994',
            '20055995',
            '20055999',
            '20056000',
            '20056001',
            '20056011',
            '20056013',
            '20056015',
            '20056018',
            '20056019',
            '20056020',
            '20056061',
            '20056064',
            '20056089',
            '20056105',
            '20056106',
            '20056109',
            '20056113',
            '20056114',
            '20056115',
            '20056116',
            '20056118',
            '20056121',
            '20056126',
            '20056127',
            '20056147',
            '20056150',
            '20056151',
            '20056153',
            '20056163',
            '20056164',
            '20056184',
            '20056188',
            '20056189',
            '20056191',
            '20056199',
            '20056201',
            '20056216',
            '20056242',
            '20056243',
            '20056283',
            '20056299',
            '20056305',
            '20056316',
            '20056323',
            '20056361',
            '20056362',
            '20056363',
            '20056364',
            '20056366',
            '20056369',
            '20056382',
            '20056383',
            '20056384',
            '20056401',
            '20056406',
            '20056416',
            '20056426',
            '20056428',
            '20056453',
            '20056459',
            '20056468',
            '20056470',
            '20056471',
            '20056477',
            '20056487',
            '20056489',
            '20056491',
            '20056492',
            '20056498',
            '20056499',
            '20056506',
            '20056517',
            '20056518',
            '20056519',
            '20056521',
            '20056535',
            '20056540',
            '20056542',
            '20056544',
            '20056546',
            '20056548',
            '20056589',
            '20056590',
            '20056591',
            '20056595',
            '20056596',
            '20056597',
            '20056613',
            '20056614',
            '20056650',
            '20056653',
            '20056654',
            '20056655',
            '20056661',
            '20056662',
            '20056666',
            '20056667',
            '20056676',
            '20056702',
            '20056722',
            '20056730',
            '20056734',
            '20056735',
            '20056754',
            '20056755',
            '20056758',
            '20056765',
            '20056777',
            '20056802',
            '20056804',
            '20056814',
            '20056818',
            '20056819',
            '20056836',
            '20056840',
            '20056841',
            '20056843',
            '20056845',
            '20056846',
            '20056847',
            '20056848',
            '20056849',
            '20056850',
            '20056861',
            '20056862',
            '20056877',
            '20056878',
            '20056883',
            '20056884',
            '20056886',
            '20056888',
            '20056890',
            '20056904',
            '20056909',
            '20056910',
            '20056912',
            '20056915',
            '20056916',
            '20056918',
            '20056923',
            '20056926',
            '20056927',
            '20056929',
            '20056968',
            '20056971',
            '20056975',
            '20056976',
            '20056977',
            '20056978',
            '20056991',
            '20056992',
            '20057001',
            '20057023',
            '20057029',
            '20057052',
            '20057060',
            '20057065',
            '20057066',
            '20057073',
            '20057074',
            '20057084',
            '20057112',
            '20057123',
            '20057125',
            '20057127',
            '20057128',
            '20057138',
            '20057147',
            '20057149',
            '20057150',
            '20057161',
            '20057162',
            '20057163',
            '20057164',
            '20057165',
            '20057189',
            '20057247',
            '20057265',
            '20057269',
            '20057274',
            '20057276',
            '20057277',
            '20057278',
            '20057279',
            '20057280',
            '20057317',
            '20057321',
            '20057324',
            '20057325',
            '20057365',
            '20057372',
            '20057380',
            '20057388',
            '20057391',
            '20057441',
            '20057452',
            '20057453',
            '20057454',
            '20057455',
            '20057518',
            '20057525',
            '20057534',
            '20057539',
            '20057541',
            '20057542',
            '20057555',
            '20057556',
            '20057560',
            '20057567',
            '20057576',
            '20057600',
            '20057601',
            '20057602',
            '20057621',
            '20057622',
            '20057623',
            '20057646',
            '20057648',
            '20057654',
            '20057656',
            '20057693',
            '20057700',
            '20057712',
            '20057714',
            '20057716',
            '20057727',
            '20057733',
            '20057735',
            '20057770',
            '20057807',
            '20057812',
            '20057825',
            '20057875',
            '20057886',
            '20057890',
            '20057915',
            '20057916',
            '20057966',
            '20058007',
            '20058008',
            '20058029',
            '20058032',
            '20058033',
            '20058052',
            '20058056',
            '20058058',
            '20058062',
            '20058071',
            '20058072',
            '20058082',
            '20058083',
            '20058084',
            '20058087',
            '20058091',
            '20058092',
            '20058097',
            '20058100',
            '20058139',
            '20058142',
            '20058143',
            '20058208',
            '20058209',
            '20058213',
            '20058243',
            '20058245',
            '20058247',
            '20058248',
            '20058250',
            '20058259',
            '20058260',
            '20058262',
            '20058271',
            '20058314',
            '20058315',
            '20058316',
            '20058317',
            '20058327',
            '20058329',
            '20058330',
            '20058337',
            '20058338',
            '20058339',
            '20058348',
            '20058349',
            '20058364',
            '20058369',
            '20058370',
            '20058375',
            '20058377',
            '20058378',
            '20058385',
            '20058387',
            '20058401',
            '20058410',
            '20058416',
            '20058431',
            '20058434',
            '20058435',
            '20058447',
            '20058455',
            '20058456',
            '20058457',
            '20058507',
            '20058519',
            '20058529',
            '20058530',
            '20058534',
            '20058543',
            '20058545',
            '20058546',
            '20058556',
            '20058557',
            '20058593',
            '20058597',
            '20058658',
            '20058659',
            '20058666',
            '20058679',
            '20058680',
            '20058683',
            '20058687',
            '20058696',
            '20058704',
            '20058705',
            '20058706',
            '20058707',
            '20058709',
            '20058712',
            '20058713',
            '20058714',
            '20058724',
            '20058725',
            '20058726',
            '20058733',
            '20058734',
            '20058735',
            '20058736',
            '20058738',
            '20058739',
            '20058743',
            '20058748',
            '20058753',
            '20058763',
            '20058764',
            '20058770',
            '20058774',
            '20058793',
            '20058803',
            '20058830',
            '20058838',
            '20058870',
            '20058872',
            '20058878',
            '20058883',
            '20058922',
            '20058924',
            '20058926',
            '20058996',
            '20059006',
            '20059016',
            '20059017',
            '20059018',
            '20059019',
            '20059020',
            '20059021',
            '20059032',
            '20059033',
            '20059043',
            '20059047',
            '20059049',
            '20059053',
            '20059054',
            '20059055',
            '20059056',
            '20059060',
            '20059065',
            '20059069',
            '20059070',
            '20059072',
            '20059073',
            '20059075',
            '20059077',
            '20059079',
            '20059080',
            '20059081',
            '20059083',
            '20059084',
            '20059085',
            '20059088',
            '20059090',
            '20059092',
            '20059094',
            '20059100',
            '20059102',
            '20059103',
            '20059104',
            '20059105',
            '20059106',
            '20059107',
            '20059108',
            '20059109',
            '20059110',
            '20059113',
            '20059142',
            '20059143',
            '20059156',
            '20059157',
            '20059158',
            '20059159',
            '20059160',
            '20059162',
            '20059166',
            '20059175',
            '20059181',
            '20059182',
            '20059183',
            '20059206',
            '20059207',
            '20059208',
            '20059217',
            '20059218',
            '20059219',
            '20059220',
            '20059226',
            '20059234',
            '20059236',
            '20059237',
            '20059238',
            '20059239',
            '20059240',
            '20059245',
            '20059246',
            '20059254',
            '20059255',
            '20059256',
            '20059258',
            '20059259',
            '20059261',
            '20059269',
            '20059270',
            '20059271',
            '20059272',
            '20059273',
            '20059291',
            '20059298',
            '20059306',
            '20059313',
            '20059316',
            '20059321',
            '20059325',
            '20059326',
            '20059328',
            '20059332',
            '20059334',
            '20059335',
            '20059337',
            '20059339',
            '20059340',
            '20059341',
            '20059343',
            '20059344',
            '20059345',
            '20059346',
            '20059347',
            '20059348',
            '20059349',
            '20059350',
            '20059356',
            '20059362',
            '20059365',
            '20059367',
            '20059378',
            '20059380',
            '20059381',
            '20059393',
            '20059394',
            '20059395',
            '20059396',
            '20059397',
            '20059398',
            '20059400',
            '20059402',
            '20059404',
            '20059405',
            '20059406',
            '20059412',
            '20059413',
            '20059441',
            '20059442',
            '20059443',
            '20059444',
            '20059445',
            '20059446',
            '20059447',
            '20059448',
            '20059449',
            '20059450',
            '20059451',
            '20059452',
            '20059456',
            '20059457',
            '20059458',
            '20059459',
            '20059460',
            '20059462',
            '20059464',
            '20059467',
            '20059469',
            '20059470',
            '20059471',
            '20059472',
            '20059473',
            '20059475',
            '20059477',
            '20059481',
            '20059492',
            '20059493',
            '20059495',
            '20059500',
            '20059504',
            '20059507',
            '20059510',
            '20059511',
            '20059512',
            '20059516',
            '20059518',
            '20059519',
            '20059521',
            '20059523',
            '20059524',
            '20059530',
            '20059532',
            '20059533',
            '20059535',
            '20059546',
            '20059547',
            '20059548',
            '20059549',
            '20059552',
            '20059553',
            '20059555',
            '20059561',
            '20059565',
            '20059566',
            '20059567',
            '20059579',
            '20059591',
            '20059593',
            '20059597',
            '20059600',
            '20059602',
            '20059603',
            '20059604',
            '20059606',
            '20059607',
            '20059609',
            '20059613',
            '20059619',
            '20059655',
            '20059656',
            '20059657',
            '20059658',
            '20059665',
            '20059669',
            '20059678',
            '20059695',
            '20059698',
            '20059699',
            '20059700',
            '20059705',
            '20059706',
            '20059707',
            '20059712',
            '20059717',
            '20059718',
            '20059721',
            '20059742',
            '20059746',
            '20059770',
            '20059771',
            '20059772',
            '20059773',
            '20059775',
            '20059800',
            '20059801',
            '20059803',
            '20059804',
            '20059814',
            '20059819',
            '20059841',
            '20059843',
            '20059855',
            '20059865',
            '20059866',
            '20059874',
            '20059875',
            '20059876',
            '20059886',
            '20059904',
            '20059922',
            '20059928',
            '20059932',
            '20059964',
            '20059965',
            '20059998',
            '20060025',
            '20060028',
            '20060062',
            '20060080',
            '20060081',
            '20060083',
            '20060089',
            '20060091',
            '20060140',
            '20060147',
            '20060148',
            '20060150',
            '20060151',
            '20060152',
            '20060158',
            '20060159',
            '20060160',
            '20060177',
            '20060180',
            '20060182',
            '20060189',
            '20060221',
            '20060232',
            '20060247',
            '20060259',
            '20060260',
            '20060261',
            '20060271',
            '20060291',
            '20060301',
            '20060332',
            '20060335',
            '20060339',
            '20060340',
            '20060341',
            '20060342',
            '20060350',
            '20060351',
            '20060352',
            '20060353',
            '20060356',
            '20060357',
            '20060359',
            '20060362',
            '20060367',
            '20060369',
            '20060395',
            '20060396',
            '20060414',
            '20060420',
            '20060442',
            '20060444',
            '20060463',
            '20060486',
            '20060487',
            '20060501',
            '20060505',
            '20060506',
            '20060508',
            '20060509',
            '20060510',
            '20060524',
            '20060528',
            '20060529',
            '20060530',
            '20060534',
            '20060536',
            '20060545',
            '20060546',
            '20060547',
            '20060549',
            '20060551',
            '20060553',
            '20060558',
            '20060581',
            '20060585',
            '20060588',
            '20060589',
            '20060619',
            '20060625',
            '20060660',
            '20060661',
            '20060673',
            '20060682',
            '20060685',
            '20060711',
            '20060716',
            '20060727',
            '20060728',
            '20060730',
            '20060747',
            '20060759',
            '20060763',
            '20060784',
            '20060785',
            '20060787',
            '20060790',
            '20060822',
            '20060842',
            '20060843',
            '20060866',
            '20060867',
            '20060869',
            '20060877',
            '20060879',
            '20060886',
            '20060896',
            '20060898',
            '20060910',
            '20060913',
            '20060916',
            '20060917',
            '20060925',
            '20060943',
            '20060947',
            '20060953',
            '20060957',
            '20060969',
            '20060974',
            '20060978',
            '20060979',
            '20060981',
            '20060982',
            '20060983',
            '20061018',
            '20061020',
            '20061021',
            '20061022',
            '20061025',
            '20061047',
            '20061069',
            '20061074',
            '20061082',
            '20061095',
            '20061097',
            '20061106',
            '20061121',
            '20061125',
            '20061126',
            '20061129',
            '20061130',
            '20061156',
            '20061162',
            '20061183',
            '20061192',
            '20061215',
            '20061222',
            '20061241',
            '20061260',
            '20061282',
            '20061289',
            '20061290',
            '20061308',
            '20061310',
            '20061314',
            '20061328',
            '20061329',
            '20061330',
            '20061368',
            '20061369',
            '20061376',
            '20061377',
            '20061386',
            '20061389',
            '20061390',
            '20061391',
            '20061403',
            '20061407',
            '20061408',
            '20061437',
            '20061439',
            '20061440',
            '20061441',
            '20061444',
            '20061466',
            '20061492',
            '20061501',
            '20061503',
            '20061506',
            '20061507',
            '20061514',
            '20061523',
            '20061530',
            '20061555',
            '20061567',
            '20061568',
            '20061569',
            '20061570',
            '20061573',
            '20061583',
            '20061607',
            '20061615',
            '20061616',
            '20061617',
            '20061620',
            '20061621',
            '20061627',
            '20061652',
            '20061653',
            '20061745',
            '20061746',
            '20061751',
            '20061753',
            '20061772',
            '20061774',
            '20061777',
            '20061785',
            '20061789',
            '20061792',
            '20061813',
            '20061817',
            '20061820',
            '20061852',
            '20061865',
            '20061871',
            '20061902',
            '20061904',
            '20061906',
            '20061907',
            '20061921',
            '20061938',
            '20061944',
            '20061965',
            '20061968',
            '20061969',
            '20061974',
            '20062038',
            '20062039',
            '20062049',
            '20062056',
            '20062057',
            '20062058',
            '20062059',
            '20062065',
            '20062070',
            '20062079',
            '20062084',
            '20062100',
            '20062102',
            '20062105',
            '20062110',
            '20062129',
            '20062130',
            '20062133',
            '20062168',
            '20062178',
            '20062179',
            '20062193',
            '20062201',
            '20062234',
            '20062259',
            '20062303',
            '20062304',
            '20062305',
            '20062307',
            '20062309',
            '20062315',
            '20062323',
            '20062331',
            '20062332',
            '20062360',
            '20062362',
            '20062363',
            '20062364',
            '20062365',
            '20062371',
            '20062372',
            '20062373',
            '20062380',
            '20062381',
            '20062382',
            '20062386',
            '20062387',
            '20062392',
            '20062399',
            '20062400',
            '20062416',
            '20062417',
            '20062418',
            '20062420',
            '20062426',
            '20062430',
            '20062431',
            '20062432',
            '20062434',
            '20062445',
            '20062457',
            '20062491',
            '20062492',
            '20062502',
            '20062503',
            '20062567',
            '20062572',
            '20062574',
            '20062575',
            '20062576',
            '20062584',
            '20062586',
            '20062589',
            '20062592',
            '20062593',
            '20062604',
            '20062619',
            '20062620',
            '20062624',
            '20062626',
            '20062640',
            '20062641',
            '20062642',
            '20062643',
            '20062644',
            '20062645',
            '20062646',
            '20062649',
            '20062653',
            '20062661',
            '20062670',
            '20062708',
            '20062714',
            '20062742',
            '20062763',
            '20062764',
            '20062786',
            '20062806',
            '20062809',
            '20062832',
            '20062834',
            '20062837',
            '20062838',
            '20062857',
            '20062882',
            '20062894',
            '20062895',
            '20062912',
            '20062916',
            '20062918',
            '20062919',
            '20062922',
            '20062928',
            '20062930',
            '20062932',
            '20062941',
            '20062948',
            '20062964',
            '20062985',
            '20062987',
            '20062994',
            '20063004',
            '20063005',
            '20063018',
            '20063034',
            '20063035',
            '20063041',
            '20063058',
            '20063060',
            '20063064',
            '20063069',
            '20063073',
            '20063100',
            '20063110',
            '20063124',
            '20063126',
            '20063135',
            '20063136',
            '20063144',
            '20063150',
            '20063152',
            '20063155',
            '20063160',
            '20063161',
            '20063164',
            '20063165',
            '20063167',
            '20063170',
            '20063171',
            '20063172',
            '20063177',
            '20063179',
            '20063181',
            '20063186',
            '20063207',
            '20063219',
            '20063223',
            '20063224',
            '20063227',
            '20063230',
            '20063232',
            '20063235',
            '20063236',
            '20063238',
            '20063258',
            '20063280',
            '20063289',
            '20063294',
            '20063295',
            '20063296',
            '20063306',
            '20063318',
            '20063322',
            '20063344',
            '20063350',
            '20063355',
            '20063369',
            '20063375',
            '20063399',
            '20063401',
            '20063403',
            '20063419',
            '20063422',
            '20063448',
            '20063459',
            '20063460',
            '20063463',
            '20063464',
            '20063473',
            '20063474',
            '20063476',
            '20063507',
            '20063532',
            '20063533',
            '20063541',
            '20063542',
            '20063543',
            '20063544',
            '20063549',
            '20063567',
            '20063570',
            '20063591',
            '20063599',
            '20063604',
            '20063605',
            '20063609',
            '20063627',
            '20063645',
            '20063646',
            '20063660',
            '20063689',
            '20063690',
            '20063693',
            '20063697',
            '20063706',
            '20063725',
            '20063727',
            '20063734',
            '20063750',
            '20063752',
            '20063757',
            '20063759',
            '20063784',
            '20063793',
            '20063797',
            '20063798',
            '20063809',
            '20063813',
            '20063814',
            '20063816',
            '20063821',
            '20063863',
            '20063887',
            '20063890',
            '20063910',
            '20063911',
            '20063914',
            '20063919',
            '20063921',
            '20063922',
            '20063936',
            '20063945',
            '20063949',
            '20063977',
            '20063979',
            '20063986',
            '20063990',
            '20064015',
            '20064022',
            '20064031',
            '20064032',
            '20064035',
            '20064048',
            '20064063',
            '20064067',
            '20064068',
            '20064070',
            '20064083',
            '20064088',
            '20064096',
            '20064118',
            '20064120',
            '20064125',
            '20064126',
            '20064136',
            '20064150',
            '20064152',
            '20064155',
            '20064216',
            '20064220',
            '20064221',
            '20064226',
            '20064229',
            '20064236',
            '20064237',
            '20064240',
            '20064243',
            '20064244',
            '20064245',
            '20064349',
            '20064357',
            '20064358',
            '20064359',
            '20064360',
            '20064366',
            '20064375',
            '20064376',
            '20064386',
            '20064391',
            '20064416',
            '20064421',
            '20064422',
            '20064449',
            '20064470',
            '20064471',
            '20064502',
            '20064503',
            '20064508',
            '20064510',
            '20064511',
            '20064512',
            '20064533',
            '20064538',
            '20064540',
            '20064546',
            '20064558',
            '20064563',
            '20064568',
            '20064572',
            '20064573',
            '20064584',
            '20064589',
            '20064610',
            '20064614',
            '20064615',
            '20064647',
            '20064648',
            '20064649',
            '20064661',
            '20064672',
            '20064680',
            '20064682',
            '20064687',
            '20064693',
            '20064739',
            '20064760',
            '20064796',
            '20064810',
            '20064812',
            '20064819',
            '20064822',
            '20064829',
            '20064830',
            '20064836',
            '20064853',
            '20064857',
            '20064858',
            '20064861',
            '20064862',
            '20064863',
            '20064900',
            '20064901',
            '20064902',
            '20064913',
            '20064914',
            '20064941',
            '20064953',
            '20064954',
            '20064957',
            '20064993',
            '20065005',
            '20065017',
            '20065018',
            '20065036',
            '20065051',
            '20065052',
            '20065068',
            '20065069',
            '20065093',
            '20065112',
            '20065114',
            '20065115',
            '20065137',
            '20065138',
            '20065139',
            '20065147',
            '20065148',
            '20065149',
            '20065151',
            '20065175',
            '20065176',
            '20065179',
            '20065183',
            '20065194',
            '20065201',
            '20065202',
            '20065203',
            '20065209',
            '20065214',
            '20065217',
            '20065218',
            '20065238',
            '20065241',
            '20065245',
            '20065247',
            '20065268',
            '20065269',
            '20065306',
            '20065309',
            '20065310',
            '20065311',
            '20065314',
            '20065315',
            '20065316',
            '20065320',
            '20065333',
            '20065335',
            '20065336',
            '20065339',
            '20065343',
            '20065344',
            '20065345',
            '20065352',
            '20065353',
            '20065355',
            '20065362',
            '20065363',
            '20065376',
            '20065398',
            '20065407',
            '20065408',
            '20065415',
            '20065416',
            '20065419',
            '20065422',
            '20065451',
            '20065492',
            '20065507',
            '20065508',
            '20065518',
            '20065522',
            '20065560',
            '20065571',
            '20065572',
            '20065574',
            '20065575',
            '20065583',
            '20065591',
            '20065609',
            '20065610',
            '20065640',
            '20065641',
            '20065648',
            '20065649',
            '20065676',
            '20065677',
            '20065678',
            '20065688',
            '20065706',
            '20065707',
            '20065708',
            '20065718',
            '20065721',
            '20065766',
            '20065774',
            '20065787',
            '20065788',
            '20065799',
            '20065800',
            '20065803',
            '20065804',
            '20065808',
            '20065817',
            '20065818',
            '20065827',
            '20065832',
            '20065864',
            '20065869',
            '20065872',
            '20065873',
            '20065875',
            '20065876',
            '20065900',
            '20065922',
            '20065923',
            '20065926',
            '20065927',
            '20065929',
            '20065938',
            '20065950',
            '20065953',
            '20065954',
            '20065958',
            '20065960',
            '20065961',
            '20065962',
            '20065966',
            '20065968',
            '20065970',
            '20065971',
            '20065980',
            '20065981',
            '20065998',
            '20065999',
            '20066000',
            '20066005',
            '20066008',
            '20066009',
            '20066010',
            '20066011',
            '20066012',
            '20066014',
            '20066018',
            '20066020',
            '20066032',
            '20066033',
            '20066036',
            '20066037',
            '20066055',
            '20066057',
            '20066059',
            '20066060',
            '20066075',
            '20066078',
            '20066101',
            '20066102',
            '20066107',
            '20066126',
            '20066129',
            '20066130',
            '20066140',
            '20066141',
            '20066144',
            '20066145',
            '20066146',
            '20066155',
            '20066156',
            '20066157',
            '20066158',
            '20066167',
            '20066168',
            '20066169',
            '20066172',
            '20066174',
            '20066175',
            '20066177',
            '20066183',
            '20066184',
            '20066188',
            '20066190',
            '20066191',
            '20066192',
            '20066193',
            '20066194',
            '20066195',
            '20066198',
            '20066199',
            '20066202',
            '20066203',
            '20066205',
            '20066208',
            '20066216',
            '20066217',
            '20066219',
            '20066220',
            '20066232',
            '20066237',
            '20066240',
            '20066241',
            '20066250',
            '20066251',
            '20066253',
            '20066254',
            '20066255',
            '20066256',
            '20066295',
            '20066296',
            '20066298',
            '20066299',
            '20066302',
            '20066306',
            '20066307',
            '20066308',
            '20066313',
            '20066314',
            '20066315',
            '20066316',
            '20066317',
            '20066318',
            '20066321',
            '20066333',
            '20066334',
            '20066335',
            '20066336',
            '20066337',
            '20066341',
            '20066343',
            '20066353',
            '20066354',
            '20066357',
            '20066358',
            '20066361',
            '20066362',
            '20066372',
            '20066374',
            '20066400',
            '20066417',
            '20066421',
            '20066422',
            '20066424',
            '20066476',
            '20066477',
            '20066478',
            '20066479',
            '20066480',
            '20066481',
            '20066483',
            '20066484',
            '20066485',
            '20066486',
            '20066487',
            '20066488',
            '20066497',
            '20066503',
            '20066504',
            '20066505',
            '20066506',
            '20066507',
            '20066508',
            '20066509',
            '20066513',
            '20066524',
            '20066526',
            '20066527',
            '20066534',
            '20066535',
            '20066536',
            '20066537',
            '20066538',
            '20066540',
            '20066541',
            '20066542',
            '20066543',
            '20066544',
            '20066545',
            '20066546',
            '20066547',
            '20066548',
            '20066550',
            '20066567',
            '20066568',
            '20066569',
            '20066592',
            '20066593',
            '20066599',
            '20066600',
            '20066601',
            '20066604',
            '20066605',
            '20066606',
            '20066607',
            '20066608',
            '20066609',
            '20066611',
            '20066612',
            '20066633',
            '20066638',
            '20066663',
            '20066676',
            '20066683',
            '20066684',
            '20066688',
            '20066689',
            '20066691',
            '20066700',
            '20066704',
            '20066706',
            '20066719',
            '20066776',
            '20066794',
            '20066825',
            '20066841',
            '20066849',
            '20066852',
            '20066853',
            '20066885',
            '20066899',
            '20066904',
            '20066906',
            '20066908',
            '20066944',
            '20066945',
            '20066946',
            '20066988',
            '20066989',
            '20066991',
            '20066997',
            '20066998',
            '20066999',
            '20067001',
            '20067004',
            '20067005',
            '20067026',
            '20067034',
            '20067035',
            '20067044',
            '20067045',
            '20067046',
            '20067068',
            '20067070',
            '20067074',
            '20067107',
            '20067126',
            '20067128',
            '20067131',
            '20067133',
            '20067171',
            '20067178',
            '20067187',
            '20067189',
            '20067208',
            '20067209',
            '20067229',
            '20067251',
            '20067258',
            '20067277',
            '20067319',
            '20067320',
            '20067322',
            '20067324',
            '20067329',
            '20067331',
            '20067346',
            '20067350',
            '20067379',
            '20067391',
            '20067476',
            '20067477',
            '20067488',
            '20067489',
            '20067516',
            '20067523',
            '20067527',
            '20067528',
            '20067536',
            '20067560',
            '20067567',
            '20067575',
            '20067576',
            '20067599',
            '20067600',
            '20067605',
            '20067650',
            '20067672',
            '20067724',
            '20067725',
            '20067731',
            '20067770',
            '20067813',
            '20067829',
            '20067842',
            '20067843',
            '20067863',
            '20067883',
            '20067884',
            '20067894',
            '20067896',
            '20067905',
            '20067950',
            '20067951',
            '20067952',
            '20067973',
            '20067994',
            '20068001',
            '20068009',
            '20068038',
            '20068039',
            '20068075',
            '20068117',
            '20068136',
            '20068166',
            '20068190',
            '20068220',
            '20068227',
            '20068268',
            '20068293',
            '20068295',
            '20068296',
            '20068301',
            '20068325',
            '20068332',
            '20068352',
            '20068357',
            '20068381',
            '20068453',
            '20068460',
            '20068461',
            '20068469',
            '20068471',
            '20068498',
            '20068499',
            '20068528',
            '20068539',
            '20068550',
            '20068563',
            '20068565',
            '20068577',
            '20068600',
            '20068623',
            '20068628',
            '20068649',
            '20068654',
            '20068687',
            '20068696',
            '20068700',
            '20068721',
            '20068733',
            '20068737',
            '20068759',
            '20068773',
            '20068856',
            '20068886',
            '20068887',
            '20068897',
            '20068898',
            '20068929',
            '20069017',
            '20069028',
            '20069029',
            '20069035',
            '20069052',
            '20069055',
            '20069090',
            '20069123',
            '20069135',
            '20069146',
            '20069158',
            '20069178',
            '20069188',
            '20069229',
            '20069246',
            '20069257',
            '20069266',
            '20069267',
            '20069306',
            '20069308',
            '20069309',
            '20069312',
            '20069334',
            '20069381',
            '20069428',
            '20069442',
            '20069467',
            '20069473',
            '20069474',
            '20069477',
            '20069491',
            '20069503',
            '20069560',
            '20069590',
            '20069609',
            '20069612',
            '20069632',
            '20069634',
            '20069638',
            '20069649',
            '20069659',
            '20069679',
            '20069696',
            '20069729',
            '20069762',
            '20069777',
            '20069787',
            '20069822',
            '20069841',
            '20069923',
            '20069931',
            '20069934',
            '20069946',
            '20069947',
            '20069960',
            '20069961',
            '20069962',
            '20069990',
            '20070065',
            '20070098',
            '20070099',
            '20070108',
            '20070129',
            '20070236',
            '20070269',
            '20070270',
            '20070276',
            '20070366',
            '20070368',
            '20070372',
            '20070376',
            '20070377',
            '20070381',
            '20070387',
            '20070388',
            '20070389',
            '20070416',
            '20070417',
            '20070419',
            '20070429',
            '20070453',
            '20070463',
            '20070464',
            '20070465',
            '20070477',
            '20070519',
            '20070524',
            '20070882',
            '20070952',
            '20071108',
            '20071178',
            '20071186',
            '20071240',
            '20071366',
            '20071538',
            '20071733',
            '20071761',
            '20071889',
            '20071971',
            '20072033',
            '20072062',
            '20072142',
            '20072169',
            '20072281',
            '20072503',
            '20072625',
            '20072727',
            '20072833',
            '20073099',
            '20073109',
            '20073160',
            '20073418',
            '20073443',
            '20073612',
        ];

        self::log('一共' . count($list) . '个');
        foreach ($list as $k => $item) {
            // 输出一个百分比
            self::log('第' . ($k + 1) . '个');
            self::log('百分比：' . round(($k + 1) / count($list) * 100, 2) . '%');

            $id           = UUIDHelper::decryptionByType($item, 2);
            $companyModel = BaseCompany::findOne($id);
            if (!$companyModel) {
                self::log($item . '不存在');
            } else {
                $companyModel->is_hide = 1;
                $companyModel->save();
                self::log($item . '(' . $companyModel->full_name . ') 成功隐藏');
            }
        }
    }

    /**
     * 更新企业套餐
     * 当前周期简历下载点数
     * php timer_yii script/update-company-package-config-cycle-amount
     */
    public function actionUpdateCompanyPackageConfigCycleAmount()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $list = BaseCompanyPackageConfig::find()
                ->select([
                    'id',
                    'company_id',
                    'resume_download_amount',
                ])
                ->where([
                    '>',
                    'resume_download_amount',
                    0,
                ])
                ->asArray()
                ->all();

            foreach ($list as $item) {
                $model                     = BaseCompanyPackageConfig::findOne($item['id']);
                $cycleResumeDownloadAmount = BaseCompanyPackageChangeLog::find()
                                                 ->select([
                                                     'handle_after',
                                                 ])
                                                 ->where([
                                                     'company_id'  => $item['company_id'],
                                                     'type'        => BaseCompanyPackageChangeLog::TYPE_RESUME_DOWN,
                                                     'handle_type' => [
                                                         BaseCompanyPackageChangeLog::HANDLE_TYPE_SYSTEM_ADD_RESUME_DOWNLOAD,
                                                         BaseCompanyPackageChangeLog::HANDLE_TYPE_SYSTEM_REDUCE_RESUME_DOWNLOAD,
                                                         BaseCompanyPackageChangeLog::HANDLE_TYPE_CONFIGURE_SENIOR,
                                                         BaseCompanyPackageChangeLog::HANDLE_TYPE_CONFIGURE_TEST,
                                                     ],
                                                 ])
                                                 ->orderBy('add_time desc')
                                                 ->asArray()
                                                 ->one()['handle_after'];
                if ($cycleResumeDownloadAmount >= $item['resume_download_amount']) {
                    self::log("开始处理id:{$model['id']}\n");
                    $model->cycle_resume_download_amount = $cycleResumeDownloadAmount;
                    if (!$model->save()) {
                        throw new Exception($model->getFirstErrorsMessage());
                    }
                    self::log("更新Id:{$model['id']}成功\n");
                }
            }

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            var_dump($e->getMessage());
        }
    }

    /***
     * 修复快照数据
     * php timer_yii script/snapshot-data
     */
    public function actionSnapshotData()
    {
        $order_data = [
            //            110 => 7,
            //            112 => 7,
            //            114 => 7,
            //            115 => 7,
            //            116 => 7,
            //            117 => 7,
            //            118 => 7,
            //            119 => 7,
            //            120 => 7,
            //            124 => 7,
            //            125 => 7,
            //            126 => 7,
            //            128 => 7,
            //            130 => 7,
            //            135 => 7,
            //            169 => 7,
            //            170 => 7,
            //            204 => 7,
            //            206 => 7,
            //            272 => 7,
            //            280 => 7,
            //            312 => 7,
            //            313 => 7,
            //            314 => 7,
            //            326 => 7,
            //            378 => 30,
            //            379 => 90,
            //            380 => 90,
            //            406 => 30,
            //            565 => 90,
            //            996 => 90,
            //            997 => 90,
            378 => 90,
        ];
        foreach ($order_data as $order_id => $days) {
            //订单号
            $order_info    = BaseResumeOrder::findOne($order_id);
            $snapshot_data = json_decode($order_info->snapshot_data, true);
            //更新前日志
            self::log('更新前' . $order_id . ':' . json_encode($snapshot_data));
            $snapshot_data['service_days']        = $days;
            $snapshot_data['equity_package_name'] = $days == 7 ? '高才VIP(7天)' : ($days == 30 ? '高才VIP(30天)' : '高才VIP(90天)');
            //更新前日志
            self::log('更新后' . $order_id . ':' . json_encode($snapshot_data));
            $order_info->snapshot_data = json_encode($snapshot_data);
            $order_info->save();
            //修复快照表
            $snapshot_info                      = BaseResumeOrderSnapshot::findOne(['order_id' => $order_id]);
            $snapshot_info->service_days        = $days;
            $snapshot_info->equity_package_name = $days == 7 ? '高才VIP(7天)' : ($days == 30 ? '高才VIP(30天)' : '高才VIP(90天)');
            $snapshot_info->save();
        }
    }

    // 修复单位主表里面contact和department表为空的数据
    // php timer_yii script/fix-company-contact
    public function actionFixCompanyContact()
    {
        // 首先找到单位端里面联系人或者部门为空的
        $companyList = BaseCompany::find()
            ->select([
                'id',
                'full_name',
                'contact',
                'department',
            ])
            ->where([
                'or',
                ['contact' => ''],
                ['department' => ''],
            ])
            ->asArray()
            ->all();

        self::log('一共' . count($companyList) . '个');
        foreach ($companyList as $k => $company) {
            self::log('第' . ($k + 1) . '个');
            self::log('百分比：' . round(($k + 1) / count($companyList) * 100, 2) . '%');
            // 去找联系人表里面的信息
            $contact = BaseCompanyContact::find()
                ->select([
                    'name',
                    'department',
                ])
                ->where([
                    'company_id' => $company['id'],
                ])
                ->asArray()
                ->one();

            if ($contact) {
                $model = BaseCompany::findOne($company['id']);
                if ($company['contact'] == '') {
                    $model->contact = $contact['name'];
                }
                if ($company['department'] == '') {
                    $model->department = $contact['department'];
                }
                if (!$model->save()) {
                    self::log($company['id'] . '更新失败');
                } else {
                    self::log($company['id'] . '更新成功');
                }
            } else {
                self::log($company['id'] . '联系人表里面没有数据');
            }
        }
    }

    // 修复单位主表里面contact和department表为空的数据
    // php timer_yii script/fix-company-member-info
    public function actionFixCompanyMemberInfo()
    {
        // 这里之前因为用了company的contact导致了信息不对,需要拿修复后的contact来更新,不过更新前需要对比member的username
        $list = BaseCompanyMemberInfo::find()
            ->alias('a')
            ->innerJoin(['c' => BaseCompany::tableName()], 'a.company_id=c.id')
            ->innerJoin(['b' => BaseMember::tableName()], 'a.member_id=b.id')
            ->select([
                'a.id',
                'b.username as memberUsername',
                'c.contact as companyContact',
                'c.department as companyDepartment',
                'a.contact as memberInfoContact',
                'a.department as memberInfoDepartment',
            ])
            ->asArray()
            ->all();

        self::log('一共' . count($list) . '个');
        foreach ($list as $k => $item) {
            // 进行到第x/x条,x%
            self::log('进行到' . ($k + 1) . '/' . count($list) . '条,' . round(($k + 1) / count($list) * 100,
                    2) . '%' . ' id =' . $item['id']);
            $model = BaseCompanyMemberInfo::findOne($item['id']);

            // 原始信息
            self::log(var_export($model->attributes, true));

            $isSave = false;
            if ($item['memberInfoContact'] == $item['memberUsername']) {
                // 需要更新用户
                self::log($model->contact . ' => ' . $item['companyContact']);
                $model->contact = $item['companyContact'];
                $isSave         = true;
            }

            if (!$item['memberInfoDepartment']) {
                // 需要更新用户
                self::log($model->department . ' => ' . $item['companyDepartment']);
                $model->department = $item['companyDepartment'];
                $isSave            = true;
            }

            if ($isSave) {
                if (!$model->save()) {
                    self::log($model->id . '更新失败');
                } else {
                    self::log($model->id . '更新成功');
                }
            }
        }
    }

    /**
     * php timer_yii script/update-equity-package-type
     */
    public function actionUpdateEquityPackageType()
    {
        //将现有7号商品更新成为活动商品
        $data = BaseResumeOrder::find()
            ->select(['id'])
            ->andWhere([
                'equity_package_id'   => 7,
                'equity_package_type' => 1,
            ])
            ->asArray()
            ->all();
        foreach ($data as $item) {
            $model                      = BaseResumeOrder::findOne($item['id']);
            $model->equity_package_type = BaseResumeOrder::EQUITY_PACKAGE_TYPE_ACTIVITY;
            $model->save();
            self::log('订单ID：' . $item['id'] . '更新成功');
        }
    }

    /**
     * php timer_yii script/delete-equity-data
     */
    public function actionDeleteEquityData()
    {
        //        $data = BaseResumeOrder::findOne([
        //            'id'        => 1366,
        //            'resume_id' => 466220,
        //        ]);
        //        //删除权益表
        //        BaseResumeEquity::deleteAll([
        //            'resume_id'  => 466220,
        //            'begin_time' => '2023-11-20 10:35:56',
        //        ]);
        //删除记录表
        BaseResumeEquityActionRecord::deleteAll([
            'resume_id'   => 466220,
            'relation_id' => 1366,
            'expire_time' => '2023-12-20 10:35:56',
        ]);

        //        $equity = BaseResumeEquity::findOne([
        //            'resume_id' => 466220,
        //            'equity_id' => 1,
        //        ]);
        //
        //        $resume = BaseResume::findOne(466220);
        //        if ($resume) {
        //            self::log('完成466220用户的处理-真实处理了');
        //            $resume->vip_begin_time  = $equity->begin_time;
        //            $resume->vip_expire_time = $equity->expire_time;
        //            $resume->save();
        //        }
        self::log('完成466220用户的处理');
    }

    /**
     * 同步人才uuid数据
     * php timer_yii script/update-resume-uuid
     */
    public function actionUpdateResumeUuid()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $resumeList = BaseResume::find()
                ->select([
                    'id',
                ])
                ->where([
                    'uuid' => '',
                ])
                ->orderBy('add_time asc,id asc')
                ->limit('1000')
                ->asArray()
                ->all();
            $count      = count($resumeList);
            self::log("数据共{$count}条\n");

            foreach ($resumeList as $item) {
                $model = BaseResume::findOne($item['id']);
                self::log("开始处理人才id:{$model['id']}\n");

                $model->uuid = UUIDHelper::encrypt(UUIDHelper::TYPE_PERSON, $item['id']);

                if (!$model->save()) {
                    throw new Exception($model->getFirstErrorsMessage());
                }
                self::log("更新人才Id:{$model['id']}成功\n");
            }

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            var_dump($e->getMessage());
        }
    }

    /**
     * 同步单位uuid数据
     * php timer_yii script/update-company-uuid
     */
    public function actionUpdateCompanyUuid()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $resumeList = BaseCompany::find()
                ->select([
                    'id',
                ])
                ->where([
                    'uuid' => '',
                ])
                ->orderBy('add_time asc,id asc')
                ->limit('1000')
                ->asArray()
                ->all();
            $count      = count($resumeList);
            self::log("数据共{$count}条\n");

            foreach ($resumeList as $item) {
                $model = BaseCompany::findOne($item['id']);
                self::log("开始处理人才id:{$model['id']}\n");

                $model->uuid = UUIDHelper::encrypt(UUIDHelper::TYPE_COMPANY, $item['id']);
                if (!$model->save()) {
                    throw new Exception($model->getFirstErrorsMessage());
                }
                self::log("更新人才Id:{$model['id']}成功\n");
            }

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            var_dump($e->getMessage());
        }
    }

    /**
     * 同步职位uuid数据
     * php timer_yii script/update-job-uuid
     */
    public function actionUpdateJobUuid()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $resumeList = BaseJob::find()
                ->select([
                    'id',
                ])
                ->where([
                    'uuid' => '',
                ])
                ->orderBy('add_time asc,id asc')
                ->limit('1000')
                ->asArray()
                ->all();
            $count      = count($resumeList);
            self::log("数据共{$count}条\n");

            foreach ($resumeList as $item) {
                $model = BaseJob::findOne($item['id']);
                self::log("开始处理人才id:{$model['id']}\n");

                $model->uuid = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $item['id']);
                if (!$model->save()) {
                    throw new Exception($model->getFirstErrorsMessage());
                }
                self::log("更新人才Id:{$model['id']}成功\n");
            }

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            var_dump($e->getMessage());
        }
    }

    /**
     * 同步公告uuid数据
     * php timer_yii script/update-announcement-uuid
     */
    public function actionUpdateAnnouncementUuid()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $resumeList = BaseAnnouncement::find()
                ->select([
                    'id',
                ])
                ->where([
                    'uuid' => '',
                ])
                ->orderBy('add_time asc,id asc')
                ->limit('1000')
                ->asArray()
                ->all();
            $count      = count($resumeList);
            self::log("数据共{$count}条\n");

            foreach ($resumeList as $item) {
                $model = BaseAnnouncement::findOne($item['id']);
                self::log("开始处理人才id:{$model['id']}\n");

                $model->uuid = UUIDHelper::encrypt(UUIDHelper::TYPE_ANNOUNCEMENT, $item['id']);
                if (!$model->save()) {
                    throw new Exception($model->getFirstErrorsMessage());
                }
                self::log("更新人才Id:{$model['id']}成功\n");
            }
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            var_dump($e->getMessage());
        }
    }

    public function actionTestSendMessage()
    {
        $uid           = 487;
        $companyIdList = [
            [
                'companyMemberId' => 405,
                'companyName'     => '李叉叉单位测试',
                'chatRoomId'      => 6,
                'jobId'           => 2383,
                'uuid'            => '884b096a-f40c-3856-9e07-bb27509727ad',
            ],
            [
                'companyMemberId' => 847,
                'companyName'     => '广东汉语大学',
                'chatRoomId'      => 17,
                'jobId'           => 2414,
                'uuid'            => 'b83ed503-4c41-3ea6-8bda-cbe85ba75f41',
            ],
            [
                'chatRoomId'      => 27,
                'companyName'     => '五道口职业技术学院',
                'jobId'           => 2168,
                'companyMemberId' => 700,
                'uuid'            => 'bd6f9fe4-0093-382f-9efe-843c4de12800',
            ],
            [
                'chatRoomId'      => 28,
                'companyName'     => '1.1.5测试单位',
                'jobId'           => 2042,
                'companyMemberId' => 500,
                'uuid'            => '4261110f-36b7-389a-80b2-e48df41afd76',
            ],
            [
                'chatRoomId'      => 29,
                'companyName'     => '巴萨罗那',
                'jobId'           => 2405,
                'companyMemberId' => 816,
                'uuid'            => '47d63912-0687-3fe7-a95e-7e3b0c0c8107',
            ],
            [
                'chatRoomId'      => 31,
                'companyName'     => '李叉叉单位测试',
                'jobId'           => 2379,
                'companyMemberId' => 786,
                'uuid'            => '4d9f7ba8-9bf7-3b55-a875-1ca5a5b78bda',
            ],
            [
                'chatRoomId'      => 50,
                'companyName'     => '咩咩',
                'jobId'           => 2311,
                'companyMemberId' => 772,
                'uuid'            => 'd7bc0e91-7f3f-32fb-a9c6-fbb418f188b8',
            ],
            [
                'chatRoomId'      => 67,
                'companyName'     => '李叉叉单位测试',
                'jobId'           => 2111,
                'companyMemberId' => 787,
                'uuid'            => '2cd9c476-353a-39a3-b20c-d926d26cbfb7',

            ],
            [
                'chatRoomId'      => 68,
                'companyName'     => '1.4高级的测试单位',
                'jobId'           => 2265,
                'companyMemberId' => 751,
                'uuid'            => '531d4222-2c78-39f3-bf63-92525c2dc038',

            ],
            [
                'chatRoomId'      => 70,
                'companyName'     => '广东汉语大学',
                'jobId'           => 2412,
                'companyMemberId' => 783,
                'uuid'            => 'b94e290c-1a42-39a7-9b47-507422d10df0',

            ],

        ];
        $app           = \common\service\chat\Client::getInstance();
        $saveContent   = [
            'text' => (string)time(),
        ];

        //        for ($j = 0; $j < 10; $j++) {
        for ($i = 0; $i < 10; $i++) {
            $saveContent['text']   = $companyIdList[$i]['companyName'] . $saveContent['text'];
            $model                 = new BaseChatMessage();
            $model->content        = json_encode($saveContent);
            $model->type           = 1;
            $model->main_id        = 0;
            $model->is_read        = BaseChatMessage::IS_READ_NO;
            $model->from_member_id = $companyIdList[$i]['companyMemberId'];
            $model->to_member_id   = $uid;
            $model->chat_room_id   = $companyIdList[$i]['chatRoomId'];
            $model->job_id         = $companyIdList[$i]['jobId'];
            $model->add_time       = date('Y-m-d H:i:s');
            $model->is_show_time   = BaseChatMessage::IS_SHOW_TIME_NO;
            $model->save();

            $messageId    = $model->id;
            $sessionModel = BaseChatRoomSession::findOne([
                'chat_room_id' => $companyIdList[$i]['chatRoomId'],
                'member_id'    => $uid,
            ]);
            if (!$sessionModel) {
                $sessionModel               = new BaseChatRoomSession();
                $sessionModel->chat_room_id = $companyIdList[$i]['chatRoomId'];
                $sessionModel->member_id    = $uid;
                $sessionModel->is_delete    = BaseChatRoomSession::IS_DELETE_NO;
                $sessionModel->is_top       = BaseChatRoomSession::IS_TOP_NO;
                $sessionModel->add_time     = date('Y-m-d H:i:s');
            } else {
                $sessionModel->update_time = date('Y-m-d H:i:s');
            }
            $sessionModel->unread_amount  += 1;
            $sessionModel->last_talk_time = date('Y-m-d H:i:s');
            $sessionModel->save();

            //单位自己的session
            $selfSessionModel = BaseChatRoomSession::findOne([
                'chat_room_id' => $companyIdList[$i]['chatRoomId'],
                'member_id'    => $companyIdList[$i]['companyMemberId'],
            ]);
            if (!$selfSessionModel) {
                $selfSessionModel               = new BaseChatRoomSession();
                $selfSessionModel->chat_room_id = $companyIdList[$i]['chatRoomId'];
                $selfSessionModel->member_id    = $companyIdList[$i]['companyMemberId'];
                $selfSessionModel->is_delete    = BaseChatRoomSession::IS_DELETE_NO;
                $selfSessionModel->is_top       = BaseChatRoomSession::IS_TOP_NO;
                $selfSessionModel->add_time     = date('Y-m-d H:i:s');
            } else {
                $selfSessionModel->update_time = date('Y-m-d H:i:s');
            }
            $selfSessionModel->last_talk_time = date('Y-m-d H:i:s');

            $selfSessionModel->save();

            $arr = [];
            // 这里会有一个很奇怪的逻辑,需要知道当前接收人
            $arr['content']   = $saveContent;
            $arr['status']    = BaseChatMessage::STATUS_DELIVERY;
            $arr['messageId'] = $messageId . '';
            $arr['type']      = 'text';
            $arr['chatId']    = $companyIdList[$i]['uuid'];
            $arr['cuid']      = '';
            $arr['avatar']    = BaseChatMessage::getMessageAvatar($companyIdList[$i]['companyMemberId']);
            $arr['memberId']  = $companyIdList[$i]['companyMemberId'] . '';
            //判断接收用户是不是单位，如果是求职者发给单位，判断头像是否要模糊
            $app->sendMessageToUid($uid, json_encode($arr));
            //发送多1条session信息
            $sessionInfo = BaseChatRoomSession::getSessionInfo($uid, $messageId);

            $sessionFullInfo['content'] = $sessionInfo;
            $sessionFullInfo['type']    = 'updateSession';
            $app->sendMessageToUid($uid, json_encode($sessionFullInfo));

            // 这里如果对方已经把我们的session删除了,就要改为非删除的
            $sessionModel = BaseChatRoomSession::findOne([
                'chat_room_id' => $companyIdList[$i]['chatRoomId'],
                'member_id'    => $uid,
            ]);
            if ($sessionModel && $sessionModel->is_delete == BaseChatRoomSession::IS_DELETE_YES) {
                $sessionModel->is_delete = BaseChatRoomSession::IS_DELETE_NO;
                $sessionModel->save();
            }

            // 还多更新读取数量
            // 有三个数字,直聊未读总数
            $data = BaseMemberMessage::getUnreadData($uid);
            // 需要一个新的类型,unread
            $content = [
                'type'    => 'unread',
                'content' => $data,
            ];

            $app->sendMessageToUid($uid, json_encode($content));

            $arr              = [];
            $arr['content']   = $saveContent;
            $arr['status']    = BaseChatMessage::STATUS_DELIVERY;
            $arr['messageId'] = $messageId . '';
            // 这里有可能是卡片操作,所以返回去的类型要按照情况来判断
            $arr['type']     = 'text';
            $arr['chatId']   = $companyIdList[$i]['uuid'];
            $arr['cuid']     = '';
            $arr['memberId'] = $companyIdList[$i]['companyMemberId'];
            $arr['avatar']   = BaseChatMessage::getMessageAvatar($uid);
            $arr['time']     = '';
            $app->sendMessageToUid($companyIdList[$i]['companyMemberId'], json_encode($arr));

            //再发一条侧边栏卡片信息
            $sessionInfo                = BaseChatRoomSession::getSessionInfo($companyIdList[$i]['companyMemberId'],
                $messageId);
            $sessionFullInfo['content'] = $sessionInfo;
            $sessionFullInfo['type']    = 'updateSession';
            $app->sendMessageToUid($companyIdList[$i]['companyMemberId'], json_encode($sessionFullInfo));
            // 还多更新读取数量
            // 有三个数字,直聊未读总数
            $data = BaseMemberMessage::getUnreadData($companyIdList[$i]['companyMemberId']);
            // 需要一个新的类型,unread
            $content = [
                'type'    => 'unread',
                'content' => $data,
            ];

            $app->sendMessageToUid($companyIdList[$i]['companyMemberId'], json_encode($content));
        }
        //        }

    }

    /**
     * 更新企业套餐
     * 高级会员直聊点数
     * php timer_yii script/update-senior-company-chat-amount
     */
    public function actionUpdateSeniorCompanyChatAmount()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $currentDate = date("Y-m-d H:i:s");
            $list        = BaseCompanyPackageConfig::find()
                ->select([
                    'id',
                    'package_amount',
                ])
                ->where([
                    'status'      => BaseCompanyPackageConfig::STATUS_ACTIVE,
                    'chat_amount' => 0,
                    'code'        => [
                        'A',
                        'B',
                    ],
                ])
                ->andWhere([
                    '>',
                    'expire_time',
                    $currentDate,
                ])
                ->asArray()
                ->all();

            $totalNum = count($list);
            self::log('共有' . $totalNum . '个单位直聊数据更新');

            foreach ($list as $item) {
                $model       = BaseCompanyPackageConfig::findOne($item['id']);
                $chat_amount = 20 + $item['package_amount'] * 30;
                self::log("开始处理id:{$model['id']}\n");
                $model->chat_amount       = $chat_amount;
                $model->cycle_chat_amount = $chat_amount;

                if (!$model->save()) {
                    throw new Exception($model->getFirstErrorsMessage());
                }
                //加一个流水记录
                $companyPackageChangeLog = BaseCompanyPackageChangeLog::findOne([
                    'company_id'   => $model->company_id,
                    'handler_type' => BaseCompanyPackageChangeLog::HANDLER_TYPE_PLATFORM,
                ]);
                $packageConfigName       = BaseCompanyPackageSystemConfig::PACKAGE_CONFIG_NAME;
                $packageSurplus          = [
                    $packageConfigName['job_amount']                  => $model->job_amount,
                    $packageConfigName['announcement_amount']         => $model->announcement_amount,
                    $packageConfigName['job_refresh_amount']          => $model->job_refresh_amount,
                    $packageConfigName['announcement_refresh_amount'] => $model->announcement_refresh_amount,
                    $packageConfigName['resume_download_amount']      => $model->resume_download_amount,
                    $packageConfigName['chat_amount']                 => $chat_amount,
                ];
                $packageSurplus          = json_encode($packageSurplus);

                $data = [
                    'type'            => BaseCompanyPackageChangeLog::TYPE_CHAT_ACCOUNT,
                    'identify'        => BaseCompanyPackageChangeLog::IDENTIFY_ADD,
                    'change_amount'   => $chat_amount,
                    'surplus'         => $chat_amount,
                    'package_surplus' => $packageSurplus,
                    'member_id'       => $companyPackageChangeLog->member_id,
                    'member_name'     => $companyPackageChangeLog->member_name ?: '',
                    'company_id'      => $companyPackageChangeLog->company_id ?: '',
                    'company_name'    => $companyPackageChangeLog->company_name ?: '',
                    'handle_before'   => 0 . '',
                    'handle_after'    => $chat_amount . '',
                    'handler_id'      => $companyPackageChangeLog->handler_id,
                    'handler'         => $companyPackageChangeLog->handler ?: '',
                    'content'         => BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[BaseCompanyPackageChangeLog::HANDLE_TYPE_SYSTEM_CHANGE_RESUME_CHAT],
                    'remark'          => '版本更新同步当前高级会员直聊点数',
                    'handle_type'     => BaseCompanyPackageChangeLog::HANDLE_TYPE_SYSTEM_CHANGE_RESUME_CHAT,
                ];

                BaseCompanyPackageChangeLog::createCompanyPackageChangeLog($data);
                self::log("更新Id:{$model['id']}成功\n");
            }

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            var_dump($e->getMessage());
        }
    }

    /**
     * 初始化jobindex
     * php timer_yii script/init-job-index-data
     */
    public function actionInitJobIndexData()
    {
        //        Cache::delete('init_job_index_data');
        //        die;

        $get_id = Cache::get('init_job_index_data') ?: 0;
        self::log('get_id:' . $get_id);

        $jobList = BaseJob::find()
            ->alias('j')
            ->where([
                'j.status'  => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
                'j.is_show' => BaseJob::IS_SHOW_YES,
            ])
            ->andWhere([
                '>',
                'j.id',
                $get_id,
            ])
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = j.company_id')
            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id = j.announcement_id')
            //这里初始化要根据自身列表的顺序来放入
            //职位刷新时间
            ->orderBy('j.id asc')
            ->select([
                'j.id as jobId',
                'j.name as jobName',
                'c.full_name as companyName',
                'a.title as announcementName',
            ])
            ->limit(100000)
            ->asArray()
            ->all();
        foreach ($jobList as $item) {
            cache::setex('init_job_index_data', 3600, $item['jobId']);
        }

        if (count($jobList) > 0) {
            $c = Yii::$app->meilisearch->createCommand();

            $c->index('jobCenterBList')
                ->addDocuments($jobList);
        }

        //日志
        self::log('初始化任务创建成功');
    }

    /**
     * 扩充简历业务表
     * 更新简历意向地区中间
     * 更新教育经历表的专业字段
     * php timer_yii script/update-resume-expansion-new
     */
    public function actionUpdateResumeExpansionNew()
    {
        $id   = Cache::get('update-resume-expansion-new') ?: 0;
        $data = BaseResume::find()
            ->select(['id'])
            ->andWhere(['status' => BaseResume::STATUS_ACTIVE])
            ->andWhere([
                '>',
                'id',
                $id,
            ])
            ->asArray()
            ->orderBy('id asc')
            ->limit(1000)
            ->all();
        foreach ($data as $item) {
            //处理中间表
            $service = new ResumeAfterService();
            $service->run($item['id']);
            self::log('结束处理简历ID：' . $item['id']);
            Cache::set('update-resume-expansion-new', $item['id']);
        }
    }

    /**
     * 扩充简历业务表
     * 更新简历意向地区中间表
     * 更新教育经历表的专业字段
     * php timer_yii script/update-resume-expansion-new-item
     */
    public function actionUpdateResumeExpansionNewItem()
    {
        //获取求职意向
        $education_data = BaseResumeEducation::find()
            ->select([
                'id',
                'resume_id',
                'major_id',
            ])
            ->andWhere([
                'id'        => [
                    129416,
                    183029,
                    215207,
                    222699,
                    133586,
                    221109,
                    275642,
                    301942,
                    303368,
                    310370,
                    317703,
                    318574,
                    321260,
                    332018,
                    342941,
                    344328,
                    345111,
                    350442,
                    356327,
                    368436,
                    372199,
                    384975,
                    394412,
                    397736,
                    404332,
                    407574,
                    447317,
                ],
                'resume_id' => [
                    143707,
                    229634,
                    236942,
                    147806,
                    235411,
                    294283,
                    73931,
                    324013,
                    317810,
                    339287,
                    340217,
                    343215,
                    354553,
                    360179,
                    367676,
                    311007,
                    374940,
                    381759,
                    395994,
                    400076,
                    413696,
                    424074,
                    29860,
                    433287,
                    436671,
                    384317,
                ],
            ])
            ->asArray()
            ->all();

        foreach ($education_data as $item) {
            $major_info     = BaseMajor::findOne($item['major_id']);
            $education_info = BaseResumeEducation::findOne($item['id']);
            if ($education_info) {
                if ($major_info->level == 1) {
                    $education_info->major_id_level_1 = $item['major_id'];
                    $education_info->major_id_level_2 = 0;
                    $education_info->major_id_level_3 = 0;
                } elseif ($major_info->level == 2) {
                    $education_info->major_id_level_1 = $major_info->parent_id;
                    $education_info->major_id_level_2 = $item['major_id'];
                    $education_info->major_id_level_3 = 0;
                } elseif ($major_info->level == 3) {
                    $parent_major_info                = BaseMajor::findOne($major_info->parent_id);
                    $education_info->major_id_level_1 = $parent_major_info->parent_id;
                    $education_info->major_id_level_2 = $major_info->parent_id;
                    $education_info->major_id_level_3 = $item['major_id'];
                } else {
                    $education_info->major_id_level_1 = 0;
                    $education_info->major_id_level_2 = 0;
                    $education_info->major_id_level_3 = 0;
                }
                $education_info->save();
                self::log('结束处理ID：' . $item['id']);
            }
        }
    }

    /**
     * 扩充简历业务表
     * 更新简历意向地区中间表
     * 更新教育经历表的专业字段
     * php timer_yii script/update-resume-expansion-new-item2
     */
    public function actionUpdateResumeExpansionNewItem2()
    {
        //获取求职意向
        $education_data = BaseResumeEducation::find()
            ->select([
                'id',
                'resume_id',
                'major_id',
            ])
            ->andWhere([
                'major_id' => 0,
            ])
            ->andWhere([
                '!=',
                'major_id_level_1',
                0,
            ])
            ->asArray()
            ->all();

        foreach ($education_data as $item) {
            $education_info = BaseResumeEducation::findOne($item['id']);
            if ($education_info) {
                $education_info->major_id_level_1 = 0;
                $education_info->major_id_level_2 = 0;
                $education_info->major_id_level_3 = 0;
                $education_info->save();
                self::log('结束处理简历ID：' . $item['id']);
            }
        }
    }
}

