<?php

namespace timer\controllers;

use admin\models\Job;
use common\base\models\BaseJob;
use common\base\models\BaseJobTopConfig;

class JobTopConfigController extends BaseTimerController
{
    public function actionUpdateTopList()
    {
        $date = date('Y-m-d');
        //找出置顶中的记录，先全部结束掉
        $oldList = BaseJobTopConfig::find()
            ->select(['id'])
            ->where([
                'is_run'    => BaseJobTopConfig::IS_RUN_YES,
                'is_delete' => BaseJobTopConfig::IS_DELETE_NO,
            ])
            ->andWhere([
                '<',
                'date',
                $date,
            ])
            ->asArray()
            ->all();

        self::log('共结束了' . count($oldList) . '条数据');

        foreach ($oldList as $item) {
            self::log("开始结束记录id:{$item['id']}");
            $model         = BaseJobTopConfig::findOne(['id' => $item['id']]);
            $model->is_run = BaseJobTopConfig::IS_RUN_NO;
            $model->status = BaseJobTopConfig::STATUS_OVER;
            $model->save();
            self::log("记录id:{$item['id']}，状态设置结束完毕");
        }

        //找出今天要置顶的记录
        $list = BaseJobTopConfig::find()
            ->select([
                'id',
                'job_id',
            ])
            ->where([
                'date'      => $date,
                'is_delete' => BaseJobTopConfig::IS_DELETE_NO,
                'status'    => BaseJobTopConfig::STATUS_NOT_START,
            ])
            ->asArray()
            ->all();

        self::log('需要置顶的记录' . count($list) . '条');

        foreach ($list as $item) {
            //判断职位是否下线了，如果是就不处理
            self::log("开始置顶记录id:{$item['id']}");
            $jobStatus = BaseJob::findOneVal(['id' => $item['job_id']], 'status');
            if ($jobStatus == BaseJob::STATUS_ONLINE) {
                //操作置顶
                $model           = BaseJobTopConfig::findOne(['id' => $item['id']]);
                $model->status   = BaseJobTopConfig::STATUS_TOP_ING;
                $model->is_run   = BaseJobTopConfig::IS_RUN_YES;
                $model->run_time = date('Y-m-d H:i:s', time());
                $model->save();

                // 职位刷新
                Job::jobRefresh($item['job_id']);

                self::log("记录id:{$item['id']}置顶完成");
            } else {
                self::log("记录id:{$item['id']}职位状态不对，跳过");
            }
        }
    }

}