<?php

namespace timer\controllers;

use common\base\models\BaseResume;
use common\base\models\BaseResumeLibrary;
use common\base\models\BaseResumeSetting;
use common\base\models\BaseSystemConfig;
use common\service\commonResume\SearchService;
use Yii;

/**
 *
 */
class ResumeLibraryController extends BaseTimerController
{
    /**
     * 人才库更新
     * php ./timer_yii  resume-library/update
     */
    public function actionUpdate()
    {
        //    // 1、由C端求职者用户创建的，且简历完善度≧75%的在线简历
        // 2、在线简历隐藏状态=关闭

        // 这里是有一些条件才允许入库的,首先是建立完善度
        // 开个事务

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $completeMin = BaseSystemConfig::getValue(BaseSystemConfig::RESUME_LIBRARY_COMPLETE_MIN_KEY);
            // 首先把整个表的数据清空
            $sql = 'TRUNCATE TABLE resume_library';
            Yii::$app->db->createCommand($sql)
                ->execute();

            // 这个是把40%的博士也加入
            $sql = "INSERT INTO resume_library (add_time, resume_id) SELECT NOW(), resume.id FROM resume INNER JOIN resume_setting ON resume_setting.resume_id = resume.id WHERE resume_setting.is_hide_resume = 2 AND (resume.complete >= 65 or (resume.complete >=40 and resume.top_education_code=4)) GROUP BY id order by resume.last_update_time desc";

            // $sql = "INSERT INTO resume_library (add_time, resume_id) SELECT NOW(), resume.id FROM resume INNER JOIN resume_setting ON resume_setting.resume_id = resume.id WHERE resume_setting.is_hide_resume = :is_hide_resume AND resume.complete >= :complete_min order by resume.last_update_time desc";
            Yii::$app->db->createCommand($sql)
                ->bindValues([
                    ':is_hide_resume' => BaseResumeSetting::IS_HIDE_RESUME_NO,
                    ':complete_min'   => $completeMin,
                ])
                ->execute();


            // 然后更新resume里面的is_resume_library字段，先全部更新为2，然后再更新为1
            $sql = 'UPDATE resume SET is_resume_library = 2';
            Yii::$app->db->createCommand($sql)
                ->execute();
            $sql = 'UPDATE resume SET is_resume_library = 1 WHERE id IN (SELECT resume_id FROM resume_library)';
            Yii::$app->db->createCommand($sql)
                ->execute();

            self::log('已完成人才库更新');

            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            self::log($e->getMessage());
        }

        self::log('开始更新缓存');

        $params = [
            'isHideComplete' => 1,
            'pageSize'       => 20,
        ];
        $service        = new SearchService();
        for ($i = 1; $i < 21; $i++) {
            $params['page'] = $i;
            $service->setNoSearchData($params);
        }

        self::log('结束更新完成');
    }

}
