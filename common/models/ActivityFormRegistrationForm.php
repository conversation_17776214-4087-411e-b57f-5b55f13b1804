<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "activity_form_registration_form".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $status 状态
 * @property string $option_ids 报名意向ids
 * @property int $activity_form_id 活动表单ID
 * @property int $resume_id 用户id
 * @property string $name 姓名
 * @property int $gender 性别，1:男；2:女
 * @property string $birthday 出生日期
 * @property int $household_register_id 户籍/国籍
 * @property string $mobile 手机
 * @property string $email 邮箱
 * @property int $political_status_id 政治面貌
 * @property string $title_id 职称
 * @property string $school 学校
 * @property string $college 二级院校
 * @property int $education_id 学历水平
 * @property string $begin_date 开始时间
 * @property string $end_date 结束时间
 * @property string $major_id 专业id
 * @property string $major_custom 自定义专业
 * @property int $is_recruitment 是否统招,1:是；2否
 * @property int $is_project_school 是否985/211，1:是；2:否
 * @property int $is_abroad 是否留学，1:是；2:否
 * @property int $work_status 求职状态
 * @property int $arrive_date_type 到岗时间类型
 * @property int $job_category_id 意向职位ID
 * @property int $nature_type 工作性质
 * @property string $area_id 意向城市ID
 * @property int $wage_type 期望月薪
 * @property string $channels 了解渠道
 * @property string $unit_type 就业单位类型
 * @property int $is_share 是否推荐，1:是；2:否
 * @property string $file_token 附件简历
 * @property string $wechat_number 微信号
 * @property string $reference 推荐人
 * @property int $employment_status 您目前的学业/就业状态是（1～8）
 * @property string $postdoctor_institution 博士后培养机构
 * @property int $postdoctor_overseas_duration 博士毕业后的海外连续工作时长（含海外博士后）（1~8）
 * @property int $residence 现居住地
 * @property string $custom_content 自定义输入内容
 * @property string $mobile_code 手机号区号
 * @property string $joint_school 联合培养院校名称
 * @property int $joint_school_id 联合培养院校id
 * @property int $degree_type 学位类型：1-学术型；2-专业型
 * @property string $min_wage 最低期望薪酬
 * @property string $max_wage 最高期望薪酬
 * @property int $is_full_time 是否是全日制：1-是；2-否
 * @property int $is_sino_foreign_joint_training 是否是中外联合培养：1-是；2-否
 * @property int $is_master_bachelor_combined 是否是硕博连读：1-是；2-否
 * @property int $is_bachelor_master_combined 是否是本硕博连读：1-是；2-否
 * @property int $is_bachelor_master_continuous 是否是本硕连读：1-是；2-否
 * @property int $is_junior_college_to_bachelor 是否是专升本：1-是；2-否
 * @property int $is_only_phd 是否专攻博士：1-是；2-否
 * @property int $is_bachelor_phd_combined 是否本博连读
 * @property string $preference 求职偏好
 */
class ActivityFormRegistrationForm extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'activity_form_registration_form';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time', 'birthday', 'begin_date', 'end_date'], 'safe'],
            [['status', 'activity_form_id', 'resume_id', 'gender', 'household_register_id', 'political_status_id', 'education_id', 'is_recruitment', 'is_project_school', 'is_abroad', 'work_status', 'arrive_date_type', 'job_category_id', 'nature_type', 'wage_type', 'is_share', 'employment_status', 'postdoctor_overseas_duration', 'residence', 'joint_school_id', 'degree_type', 'is_full_time', 'is_sino_foreign_joint_training', 'is_master_bachelor_combined', 'is_bachelor_master_combined', 'is_bachelor_master_continuous', 'is_junior_college_to_bachelor', 'is_only_phd', 'is_bachelor_phd_combined'], 'integer'],
            [['option_ids'], 'string', 'max' => 1024],
            [['name'], 'string', 'max' => 32],
            [['mobile', 'mobile_code'], 'string', 'max' => 16],
            [['email', 'title_id', 'reference', 'postdoctor_institution'], 'string', 'max' => 256],
            [['school', 'college', 'major_custom', 'area_id', 'channels', 'unit_type', 'preference'], 'string', 'max' => 128],
            [['major_id', 'wechat_number'], 'string', 'max' => 64],
            [['file_token'], 'string', 'max' => 512],
            [['custom_content'], 'string', 'max' => 600],
            [['joint_school'], 'string', 'max' => 255],
            [['min_wage', 'max_wage'], 'string', 'max' => 60],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'option_ids' => 'Option Ids',
            'activity_form_id' => 'Activity Form ID',
            'resume_id' => 'Resume ID',
            'name' => 'Name',
            'gender' => 'Gender',
            'birthday' => 'Birthday',
            'household_register_id' => 'Household Register ID',
            'mobile' => 'Mobile',
            'email' => 'Email',
            'political_status_id' => 'Political Status ID',
            'title_id' => 'Title ID',
            'school' => 'School',
            'college' => 'College',
            'education_id' => 'Education ID',
            'begin_date' => 'Begin Date',
            'end_date' => 'End Date',
            'major_id' => 'Major ID',
            'major_custom' => 'Major Custom',
            'is_recruitment' => 'Is Recruitment',
            'is_project_school' => 'Is Project School',
            'is_abroad' => 'Is Abroad',
            'work_status' => 'Work Status',
            'arrive_date_type' => 'Arrive Date Type',
            'job_category_id' => 'Job Category ID',
            'nature_type' => 'Nature Type',
            'area_id' => 'Area ID',
            'wage_type' => 'Wage Type',
            'channels' => 'Channels',
            'unit_type' => 'Unit Type',
            'is_share' => 'Is Share',
            'file_token' => 'File Token',
            'wechat_number' => 'Wechat Number',
            'reference' => 'Reference',
            'employment_status' => 'Employment Status',
            'postdoctor_institution' => 'Postdoctor Institution',
            'postdoctor_overseas_duration' => 'Postdoctor Overseas Duration',
            'residence' => 'Residence',
            'custom_content' => 'Custom Content',
            'mobile_code' => 'Mobile Code',
            'joint_school' => 'Joint School',
            'joint_school_id' => 'Joint School ID',
            'degree_type' => 'Degree Type',
            'min_wage' => 'Min Wage',
            'max_wage' => 'Max Wage',
            'is_full_time' => 'Is Full Time',
            'is_sino_foreign_joint_training' => 'Is Sino Foreign Joint Training',
            'is_master_bachelor_combined' => 'Is Master Bachelor Combined',
            'is_bachelor_master_combined' => 'Is Bachelor Master Combined',
            'is_bachelor_master_continuous' => 'Is Bachelor Master Continuous',
            'is_junior_college_to_bachelor' => 'Is Junior College To Bachelor',
            'is_only_phd' => 'Is Only Phd',
            'is_bachelor_phd_combined' => 'Is Bachelor Phd Combined',
            'preference' => 'Preference',
        ];
    }
}
