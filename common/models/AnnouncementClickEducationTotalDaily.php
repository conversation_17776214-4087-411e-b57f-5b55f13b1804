<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "announcement_click_education_total_daily".
 *
 * @property int $id ID
 * @property string $add_date 时间格式
 * @property int $announcement_id 公告ID
 * @property int $guest 游客
 * @property int $rests 其他
 * @property int $college 大专
 * @property int $poaceae 本科
 * @property int $doctor 博士
 * @property int $master 硕士
 */
class AnnouncementClickEducationTotalDaily extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'announcement_click_education_total_daily';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_date'], 'required'],
            [['add_date'], 'safe'],
            [['announcement_id', 'guest', 'rests', 'college', 'poaceae', 'doctor', 'master'], 'integer'],
            [['announcement_id', 'add_date'], 'unique', 'targetAttribute' => ['announcement_id', 'add_date']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_date' => 'Add Date',
            'announcement_id' => 'Announcement ID',
            'guest' => 'Guest',
            'rests' => 'Rests',
            'college' => 'College',
            'poaceae' => 'Poaceae',
            'doctor' => 'Doctor',
            'master' => 'Master',
        ];
    }
}
