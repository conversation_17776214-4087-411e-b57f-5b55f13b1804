<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "company_package_system_config".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property int $status 状态，1:在用，2:禁用
 * @property string $name 名称
 * @property string $code 代码
 * @property int $job_amount 职位发布条数
 * @property int $announcement_amount 公告发布条数
 * @property int $job_refresh_amount 职位可刷新总量
 * @property int $announcement_refresh_amount 公告可刷新总量
 * @property int $resume_download_amount 简历下载点数
 * @property int $sms_amount 短信数量
 * @property int $base_job_amount 职位发布基础数量
 * @property int $base_announcement_amount 公告发布基础数量
 * @property int $base_job_refresh_amount 职位刷新基础数量
 * @property int $base_announcement_refresh_amount 公告刷新基础数量
 * @property int $base_resume_download_amount 简历下载基础点数
 * @property int $chat_amount 直聊沟通点数
 * @property int $base_chat_amount 直聊沟通基础点数
 * @property int $base_sms_amount 短信基础条数
 * @property int $type 套餐类型；1:高级会员；2:免费会员；3：试用会员
 */
class CompanyPackageSystemConfig extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'company_package_system_config';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time'], 'safe'],
            [['status', 'job_amount', 'announcement_amount', 'job_refresh_amount', 'announcement_refresh_amount', 'resume_download_amount', 'sms_amount', 'base_job_amount', 'base_announcement_amount', 'base_job_refresh_amount', 'base_announcement_refresh_amount', 'base_resume_download_amount', 'chat_amount', 'base_chat_amount', 'base_sms_amount', 'type'], 'integer'],
            [['name', 'code'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'status' => 'Status',
            'name' => 'Name',
            'code' => 'Code',
            'job_amount' => 'Job Amount',
            'announcement_amount' => 'Announcement Amount',
            'job_refresh_amount' => 'Job Refresh Amount',
            'announcement_refresh_amount' => 'Announcement Refresh Amount',
            'resume_download_amount' => 'Resume Download Amount',
            'sms_amount' => 'Sms Amount',
            'base_job_amount' => 'Base Job Amount',
            'base_announcement_amount' => 'Base Announcement Amount',
            'base_job_refresh_amount' => 'Base Job Refresh Amount',
            'base_announcement_refresh_amount' => 'Base Announcement Refresh Amount',
            'base_resume_download_amount' => 'Base Resume Download Amount',
            'chat_amount' => 'Chat Amount',
            'base_chat_amount' => 'Base Chat Amount',
            'base_sms_amount' => 'Base Sms Amount',
            'type' => 'Type',
        ];
    }
}
