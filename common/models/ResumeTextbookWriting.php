<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_textbook_writing".
 *
 * @property int $id 主键ID
 * @property int $resume_id 简历ID
 * @property int $member_id 会员ID
 * @property string $name 教材名称
 * @property string $role 担任角色
 * @property int $role_code 担任角色id，使用字典表
 * @property int $type_code 教材类型，使用字典表
 * @property string $writing_date 编写时间（默认初始日期避免NULL）
 * @property string $description 教材描述
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $status 状态：1-正常，2-删除
 */
class ResumeTextbookWriting extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_textbook_writing';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['resume_id', 'member_id', 'role_code', 'type_code', 'status'], 'integer'],
            [['writing_date', 'add_time', 'update_time'], 'safe'],
            [['description'], 'required'],
            [['description'], 'string'],
            [['name'], 'string', 'max' => 300],
            [['role'], 'string', 'max' => 100],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'resume_id' => 'Resume ID',
            'member_id' => 'Member ID',
            'name' => 'Name',
            'role' => 'Role',
            'role_code' => 'Role Code',
            'type_code' => 'Type Code',
            'writing_date' => 'Writing Date',
            'description' => 'Description',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
        ];
    }
}
