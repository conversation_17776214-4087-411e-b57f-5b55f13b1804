<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "company_group_relation".
 *
 * @property int $id
 * @property int $company_id 单位ID
 * @property int $group_id 群组ID
 */
class CompanyGroupRelation extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'company_group_relation';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['company_id', 'group_id'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'company_id' => 'Company ID',
            'group_id' => 'Group ID',
        ];
    }
}
