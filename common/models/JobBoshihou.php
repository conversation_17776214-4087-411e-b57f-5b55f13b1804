<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "job_boshihou".
 *
 * @property int $id
 * @property int $job_id 职位ID
 */
class JobBoshihou extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job_boshihou';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['job_id'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'job_id' => 'Job ID',
        ];
    }
}
