<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_academic_page".
 *
 * @property int $id id;主键id
 * @property string $add_time 创建时间
 * @property string $update_time 修改时间
 * @property int $status 状态
 * @property int $resume_id 简历id
 * @property int $member_id 会员id
 * @property string $title 题目
 * @property string $serial_number 刊物名/卷（期）号；新版：期刊名称
 * @property string $publish_date 发表日期
 * @property string $record_situation 收录情况
 * @property string $position 本人轮次
 * @property string $impact_factor 影响因子
 * @property string $description 描述
 * @property string $citation_count_five_year 近5年被引次数
 * @property string $doi DOI
 * @property int $is_record 是否收录；1已收录；2:暂不清楚
 * @property int $serial_number_id 期刊字典idJournalDictionary表
 * @property string $page_number 卷期页码
 */
class ResumeAcademicPage extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_academic_page';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time', 'publish_date'], 'safe'],
            [['status', 'resume_id', 'member_id', 'is_record', 'serial_number_id'], 'integer'],
            [['description'], 'string'],
            [['title'], 'string', 'max' => 256],
            [['serial_number', 'record_situation', 'position', 'impact_factor', 'citation_count_five_year', 'doi'], 'string', 'max' => 128],
            [['page_number'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'resume_id' => 'Resume ID',
            'member_id' => 'Member ID',
            'title' => 'Title',
            'serial_number' => 'Serial Number',
            'publish_date' => 'Publish Date',
            'record_situation' => 'Record Situation',
            'position' => 'Position',
            'impact_factor' => 'Impact Factor',
            'description' => 'Description',
            'citation_count_five_year' => 'Citation Count Five Year',
            'doi' => 'Doi',
            'is_record' => 'Is Record',
            'serial_number_id' => 'Serial Number ID',
            'page_number' => 'Page Number',
        ];
    }
}
