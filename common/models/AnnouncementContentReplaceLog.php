<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "announcement_content_replace_log".
 *
 * @property int $id
 * @property int $announcement_id 公告ID
 * @property int $company_id 单位ID
 * @property string $old_content 公告旧内容
 * @property string $new_content 公告新内容
 * @property string $add_time 添加时间
 * @property string $http_content http内容
 * @property string $https_content https内容
 */
class AnnouncementContentReplaceLog extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'announcement_content_replace_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['announcement_id', 'company_id'], 'integer'],
            [['old_content', 'new_content', 'http_content', 'https_content'], 'string'],
            [['add_time', 'http_content', 'https_content'], 'required'],
            [['add_time'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'announcement_id' => 'Announcement ID',
            'company_id' => 'Company ID',
            'old_content' => 'Old Content',
            'new_content' => 'New Content',
            'add_time' => 'Add Time',
            'http_content' => 'Http Content',
            'https_content' => 'Https Content',
        ];
    }
}
