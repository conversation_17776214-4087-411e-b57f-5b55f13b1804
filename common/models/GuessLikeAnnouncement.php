<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "guess_like_announcement".
 *
 * @property int $id
 * @property string $add_time 新增时间
 * @property string $ip IP
 * @property int $resume_id 用户id
 * @property int $announcement_id 公告id
 */
class GuessLikeAnnouncement extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'guess_like_announcement';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time'], 'safe'],
            [['resume_id', 'announcement_id'], 'integer'],
            [['ip'], 'string', 'max' => 15],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'ip' => 'Ip',
            'resume_id' => 'Resume ID',
            'announcement_id' => 'Announcement ID',
        ];
    }
}
