<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "job_apply_handle_stat".
 *
 * @property int $id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $job_id 职位id
 * @property int $announcement_id 公告id
 * @property int $company_id 单位id
 * @property int $all_count 全部职位投递状态数量
 * @property int $wait_amount 已投递状态数量
 * @property int $check_amount 已查看状态数量
 * @property int $through_first_amount 通过初筛状态数量
 * @property int $interview_amount 邀请面试数量
 * @property int $inappropriate_amount 不合适数量
 * @property int $employed_amount 已录用数量
 */
class JobApplyHandleStat extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job_apply_handle_stat';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['job_id', 'announcement_id', 'company_id', 'all_count', 'wait_amount', 'check_amount', 'through_first_amount', 'interview_amount', 'inappropriate_amount', 'employed_amount'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'job_id' => 'Job ID',
            'announcement_id' => 'Announcement ID',
            'company_id' => 'Company ID',
            'all_count' => 'All Count',
            'wait_amount' => 'Wait Amount',
            'check_amount' => 'Check Amount',
            'through_first_amount' => 'Through First Amount',
            'interview_amount' => 'Interview Amount',
            'inappropriate_amount' => 'Inappropriate Amount',
            'employed_amount' => 'Employed Amount',
        ];
    }
}
