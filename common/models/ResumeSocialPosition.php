<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_social_position".
 *
 * @property int $id 主键ID
 * @property int $resume_id 简历ID
 * @property int $member_id 会员ID
 * @property string $name 机构名称
 * @property string $role 担任角色
 * @property int $role_code 担任角色id，使用字典表
 * @property string $begin_date 开始时间（默认初始日期避免NULL）
 * @property string $end_date 结束时间（默认初始日期避免NULL）
 * @property int $type_code 组织类型，使用字典
 * @property string $description 详细说明
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $status 状态：1-正常，2-删除
 * @property int $is_now 结束时间是否是至今
 */
class ResumeSocialPosition extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_social_position';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['resume_id', 'member_id', 'role_code', 'type_code', 'status', 'is_now'], 'integer'],
            [['begin_date', 'end_date', 'add_time', 'update_time'], 'safe'],
            [['description'], 'required'],
            [['description'], 'string'],
            [['name'], 'string', 'max' => 200],
            [['role'], 'string', 'max' => 100],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'resume_id' => 'Resume ID',
            'member_id' => 'Member ID',
            'name' => 'Name',
            'role' => 'Role',
            'role_code' => 'Role Code',
            'begin_date' => 'Begin Date',
            'end_date' => 'End Date',
            'type_code' => 'Type Code',
            'description' => 'Description',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'is_now' => 'Is Now',
        ];
    }
}
