<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "chat_room_session".
 *
 * @property int $id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $status 状态
 * @property int $chat_room_id 房间id
 * @property int $member_id memberId
 * @property int $is_top 是否置顶1是,2否
 * @property int $is_delete 是否删除1是,2否
 * @property string $last_talk_time 最新时间
 * @property int $unread_amount 未读消息总量(对方未读)
 */
class ChatRoomSession extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'chat_room_session';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time', 'last_talk_time'], 'safe'],
            [['status', 'chat_room_id', 'member_id', 'is_top', 'is_delete', 'unread_amount'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'chat_room_id' => 'Chat Room ID',
            'member_id' => 'Member ID',
            'is_top' => 'Is Top',
            'is_delete' => 'Is Delete',
            'last_talk_time' => 'Last Talk Time',
            'unread_amount' => 'Unread Amount',
        ];
    }
}
