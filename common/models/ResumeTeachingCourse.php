<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_teaching_course".
 *
 * @property int $id 主键ID
 * @property int $resume_id 简历ID
 * @property int $member_id 会员ID
 * @property string $name 课程名称
 * @property string $number 课程数量
 * @property string $begin_date 开始时间（默认初始日期避免NULL）
 * @property string $end_date 结束时间（默认初始日期避免NULL）
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $status 状态：1-正常，2-删除
 * @property int $is_now 结束时间是否是至今
 * @property int $target_audience_code 授课对象字典type=53
 */
class ResumeTeachingCourse extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_teaching_course';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['resume_id', 'member_id', 'status', 'is_now', 'target_audience_code'], 'integer'],
            [['begin_date', 'end_date', 'add_time', 'update_time'], 'safe'],
            [['name'], 'string', 'max' => 200],
            [['number'], 'string', 'max' => 50],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'resume_id' => 'Resume ID',
            'member_id' => 'Member ID',
            'name' => 'Name',
            'number' => 'Number',
            'begin_date' => 'Begin Date',
            'end_date' => 'End Date',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'is_now' => 'Is Now',
            'target_audience_code' => 'Target Audience Code',
        ];
    }
}
