<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "company_info_auth".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $company_id 企业id
 * @property int $phase 阶段
 * @property int $audit_status 审核状态(9未审核,1审核通过,0审核信息未提交,7初审通过,-8初审拒绝,8等待复审,-9复审拒绝)
 * @property string $reason 审核不通过的原因
 * @property string $full_name 单位名称
 * @property int $type 单位类型
 * @property int $nature 单位性质
 * @property int $industry_id 所属行业id
 * @property int $area_id 地区id
 * @property string $address 详细地址
 * @property string $contact 联系人
 * @property string $mobile 联系电话
 * @property string $email 联系邮箱
 * @property string $department 所在部门
 * @property string $telephone 固定电话
 * @property string $license_path 单位资质认证
 * @property string $person_info_path 身份证路径
 * @property int $member_id 会员id
 * @property int $province_id 省
 * @property int $city_id 市
 * @property int $district_id 区
 * @property string $submit_audit_time 提交审核时间
 */
class CompanyInfoAuth extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'company_info_auth';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time', 'submit_audit_time'], 'safe'],
            [['company_id', 'phase', 'audit_status', 'type', 'nature', 'industry_id', 'area_id', 'member_id', 'province_id', 'city_id', 'district_id'], 'integer'],
            [['city_id'], 'required'],
            [['reason', 'email'], 'string', 'max' => 256],
            [['full_name', 'address', 'license_path', 'person_info_path'], 'string', 'max' => 128],
            [['contact', 'mobile', 'department', 'telephone'], 'string', 'max' => 32],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'company_id' => 'Company ID',
            'phase' => 'Phase',
            'audit_status' => 'Audit Status',
            'reason' => 'Reason',
            'full_name' => 'Full Name',
            'type' => 'Type',
            'nature' => 'Nature',
            'industry_id' => 'Industry ID',
            'area_id' => 'Area ID',
            'address' => 'Address',
            'contact' => 'Contact',
            'mobile' => 'Mobile',
            'email' => 'Email',
            'department' => 'Department',
            'telephone' => 'Telephone',
            'license_path' => 'License Path',
            'person_info_path' => 'Person Info Path',
            'member_id' => 'Member ID',
            'province_id' => 'Province ID',
            'city_id' => 'City ID',
            'district_id' => 'District ID',
            'submit_audit_time' => 'Submit Audit Time',
        ];
    }
}
