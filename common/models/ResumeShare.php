<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_share".
 *
 * @property int $id id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $status 状态
 * @property int $company_id 单位id
 * @property int $resume_id 简历id
 * @property string $password 访问密码
 * @property string $code 唯一码
 * @property string $expire_time 有效时间
 * @property int $short_link_id 短链接id
 */
class ResumeShare extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_share';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time', 'expire_time'], 'safe'],
            [['status', 'company_id', 'resume_id', 'short_link_id'], 'integer'],
            [['password'], 'string', 'max' => 4],
            [['code'], 'string', 'max' => 255],
            [['code'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'company_id' => 'Company ID',
            'resume_id' => 'Resume ID',
            'password' => 'Password',
            'code' => 'Code',
            'expire_time' => 'Expire Time',
            'short_link_id' => 'Short Link ID',
        ];
    }
}
