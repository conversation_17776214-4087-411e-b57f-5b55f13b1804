<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "company".
 *
 * @property int $id id;主键id
 * @property string $add_time 创建时间
 * @property string $update_time 修改时间
 * @property int $status 状态，审核状态(9未审核,1审核通过,0审核信息未提交,7初审通过,-8初审拒绝,8等待复审,-9复审拒绝)
 * @property int $member_id 会员id
 * @property string $full_name 名字
 * @property string $short_name 简称
 * @property string $english_name 英文名字
 * @property int $type 单位类型
 * @property int $nature 单位性质(民营等等)
 * @property int $scale 单位规模
 * @property int $industry_id 所属行业id
 * @property int $area_id 所属地区id
 * @property string $welfare_label_ids 单位福利id,逗号隔开
 * @property string $website 单位官网
 * @property string $address 所在地址
 * @property string $contact 联系人
 * @property string $department 所在部门
 * @property string $telephone 固定电话
 * @property string $fax 传真
 * @property string $introduce 单位介绍
 * @property string $logo_url logo地址
 * @property int $is_cooperation 2非合作单位，1已合作单位
 * @property int $source_type 1自主申请，2运营添加
 * @property int $province_id 省
 * @property int $city_id 市
 * @property int $district_id 区
 * @property int $is_pay 付费情况;0免费单位，1付费单位
 * @property int $admin_id 业务员id
 * @property string $head_banner_url 企业头部banner图
 * @property string $label_ids 单位标签id（逗号隔开,字典表type=26获取）
 * @property int $create_admin_id 创建人id（自主创建或者运营创建）
 * @property string $job_last_release_time 单位下最后操作职位的发布时间（用于排序）
 * @property int $package_type 企业套餐类型（不用于排序，排序试用sort字段）（1:免费会员  2:高级会员  3过期会员 4 试用会员）
 * @property string $style_atlas 企业风采图集
 * @property int $click 点击量
 * @property int $is_hide 是否隐藏（1隐藏   2展示）
 * @property int $sort 单位排序（有一套计分体系，具体参考代码）
 * @property int $delivery_type 投递类型1=站外投递,2=站内投递,3=站内&站外投递
 * @property int $account_nature 账号性质 0不详 1愿开通账号 2不愿开通账号
 * @property string $mobile_head_banner_url 移动端企业头部图片
 * @property int $is_miniapp 是否被小程序调用1调用 2没调用
 * @property int $is_manual_tag 是否运营手动标记 0没标记 1标记 2不标记
 * @property string $uuid uuid
 * @property int $admin_hide_status 管理员设置的隐藏状态0管理员未设置，1管理员设置隐藏，2管理员设置显示
 * @property int $group_score_system_id 分值系统ID
 * @property int $is_abroad 是否海外栏目1是2否
 * @property int $overseas_sort_point 海外排名积分
 */
class Company extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'company';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time', 'job_last_release_time'], 'safe'],
            [['status', 'member_id', 'type', 'nature', 'scale', 'industry_id', 'area_id', 'is_cooperation', 'source_type', 'province_id', 'city_id', 'district_id', 'is_pay', 'admin_id', 'create_admin_id', 'package_type', 'click', 'is_hide', 'sort', 'delivery_type', 'account_nature', 'is_miniapp', 'is_manual_tag', 'admin_hide_status', 'group_score_system_id', 'is_abroad', 'overseas_sort_point'], 'integer'],
            [['introduce'], 'string'],
            [['full_name', 'english_name', 'welfare_label_ids', 'address', 'logo_url', 'head_banner_url', 'label_ids', 'mobile_head_banner_url'], 'string', 'max' => 256],
            [['short_name'], 'string', 'max' => 128],
            [['website'], 'string', 'max' => 512],
            [['contact', 'department', 'telephone', 'fax'], 'string', 'max' => 32],
            [['style_atlas'], 'string', 'max' => 255],
            [['uuid'], 'string', 'max' => 64],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'member_id' => 'Member ID',
            'full_name' => 'Full Name',
            'short_name' => 'Short Name',
            'english_name' => 'English Name',
            'type' => 'Type',
            'nature' => 'Nature',
            'scale' => 'Scale',
            'industry_id' => 'Industry ID',
            'area_id' => 'Area ID',
            'welfare_label_ids' => 'Welfare Label Ids',
            'website' => 'Website',
            'address' => 'Address',
            'contact' => 'Contact',
            'department' => 'Department',
            'telephone' => 'Telephone',
            'fax' => 'Fax',
            'introduce' => 'Introduce',
            'logo_url' => 'Logo Url',
            'is_cooperation' => 'Is Cooperation',
            'source_type' => 'Source Type',
            'province_id' => 'Province ID',
            'city_id' => 'City ID',
            'district_id' => 'District ID',
            'is_pay' => 'Is Pay',
            'admin_id' => 'Admin ID',
            'head_banner_url' => 'Head Banner Url',
            'label_ids' => 'Label Ids',
            'create_admin_id' => 'Create Admin ID',
            'job_last_release_time' => 'Job Last Release Time',
            'package_type' => 'Package Type',
            'style_atlas' => 'Style Atlas',
            'click' => 'Click',
            'is_hide' => 'Is Hide',
            'sort' => 'Sort',
            'delivery_type' => 'Delivery Type',
            'account_nature' => 'Account Nature',
            'mobile_head_banner_url' => 'Mobile Head Banner Url',
            'is_miniapp' => 'Is Miniapp',
            'is_manual_tag' => 'Is Manual Tag',
            'uuid' => 'Uuid',
            'admin_hide_status' => 'Admin Hide Status',
            'group_score_system_id' => 'Group Score System ID',
            'is_abroad' => 'Is Abroad',
            'overseas_sort_point' => 'Overseas Sort Point',
        ];
    }
}
