<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "job_apply_work_experience_total".
 *
 * @property int $id ID
 * @property string $update_time 更新时间
 * @property int $job_id 职位ID
 * @property int $range_0_1 0-1
 * @property int $range_1_3 1-3
 * @property int $range_3_5 3-5
 * @property int $range_5_10 5-10
 * @property int $range_10 10+
 */
class JobApplyWorkExperienceTotal extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job_apply_work_experience_total';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['update_time'], 'safe'],
            [['job_id', 'range_0_1', 'range_1_3', 'range_3_5', 'range_5_10', 'range_10'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'update_time' => 'Update Time',
            'job_id' => 'Job ID',
            'range_0_1' => 'Range 0 1',
            'range_1_3' => 'Range 1 3',
            'range_3_5' => 'Range 3 5',
            'range_5_10' => 'Range 5 10',
            'range_10' => 'Range 10',
        ];
    }
}
