<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "company_member_operation_log".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $type 类型 1单位账号状态操作 2子账号配置  3服务过期 4手动变更账号权限 5创建子账号  6单位终审通过
 * @property int $operation_id 操作人
 * @property int $operation_port 操作端口 1系统 2运营 3单位自己
 * @property string $operation_content 操作内容
 * @property int $member_id 用户ID
 * @property int $company_id 隶属单位ID
 */
class CompanyMemberOperationLog extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'company_member_operation_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['type', 'operation_id', 'operation_port', 'member_id', 'company_id'], 'integer'],
            [['operation_content'], 'required'],
            [['operation_content'], 'string'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'type' => 'Type',
            'operation_id' => 'Operation ID',
            'operation_port' => 'Operation Port',
            'operation_content' => 'Operation Content',
            'member_id' => 'Member ID',
            'company_id' => 'Company ID',
        ];
    }
}
