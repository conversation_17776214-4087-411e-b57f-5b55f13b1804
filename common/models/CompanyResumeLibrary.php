<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "company_resume_library".
 *
 * @property int $id
 * @property string $add_time 创建时间
 * @property int $company_id 单位id
 * @property int $resume_id 简历id
 * @property int $source_type 简历来源(这里采取位来存,1是投递,2是下载,3是两者均有)
 * @property string $download_time 下载时间
 * @property string $apply_time 应聘时间
 * @property string $tag 企业对简历对标注
 * @property int $company_member_id 单位用户id
 */
class CompanyResumeLibrary extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'company_resume_library';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'download_time', 'apply_time'], 'safe'],
            [['company_id', 'resume_id', 'source_type', 'company_member_id'], 'integer'],
            [['tag'], 'string', 'max' => 1024],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'company_id' => 'Company ID',
            'resume_id' => 'Resume ID',
            'source_type' => 'Source Type',
            'download_time' => 'Download Time',
            'apply_time' => 'Apply Time',
            'tag' => 'Tag',
            'company_member_id' => 'Company Member ID',
        ];
    }
}
