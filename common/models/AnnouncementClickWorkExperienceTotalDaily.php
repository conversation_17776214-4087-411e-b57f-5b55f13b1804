<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "announcement_click_work_experience_total_daily".
 *
 * @property int $id ID
 * @property string $add_date 时间格式
 * @property int $announcement_id 公告ID
 * @property int $range_1_3 1-3
 * @property int $range_3_5 3-5
 * @property int $range_5_10 5-10
 * @property int $range_10 10+
 * @property int $range_guest 游客
 */
class AnnouncementClickWorkExperienceTotalDaily extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'announcement_click_work_experience_total_daily';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_date'], 'required'],
            [['add_date'], 'safe'],
            [['announcement_id', 'range_1_3', 'range_3_5', 'range_5_10', 'range_10', 'range_guest'], 'integer'],
            [['announcement_id', 'add_date'], 'unique', 'targetAttribute' => ['announcement_id', 'add_date']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_date' => 'Add Date',
            'announcement_id' => 'Announcement ID',
            'range_1_3' => 'Range 1 3',
            'range_3_5' => 'Range 3 5',
            'range_5_10' => 'Range 5 10',
            'range_10' => 'Range 10',
            'range_guest' => 'Range Guest',
        ];
    }
}
