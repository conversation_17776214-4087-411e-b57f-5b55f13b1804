<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_announcement_report_record".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property string $update_time 修改时间
 * @property int $resume_id 在线简历表主键id
 * @property int $announcement_id 公告表主键id
 * @property string $token 唯一标识
 */
class ResumeAnnouncementReportRecord extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_announcement_report_record';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['resume_id', 'announcement_id'], 'integer'],
            [['token'], 'string', 'max' => 32],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'resume_id' => 'Resume ID',
            'announcement_id' => 'Announcement ID',
            'token' => 'Token',
        ];
    }
}
