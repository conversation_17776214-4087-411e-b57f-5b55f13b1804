<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "job_category_relation".
 *
 * @property int $id
 * @property int $announcement_id 公告ID
 * @property int $job_id 职位ID
 * @property int $category_id 类型ID
 * @property int $level 职位类型等级跟category level字段
 */
class JobCategoryRelation extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job_category_relation';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['announcement_id', 'job_id', 'category_id', 'level'], 'integer'],
            [['category_id', 'level'], 'required'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'announcement_id' => 'Announcement ID',
            'job_id' => 'Job ID',
            'category_id' => 'Category ID',
            'level' => 'Level',
        ];
    }
}
