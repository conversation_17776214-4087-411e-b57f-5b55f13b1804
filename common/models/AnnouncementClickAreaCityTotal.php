<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "announcement_click_area_city_total".
 *
 * @property int $id ID
 * @property string $update_time 更新时间
 * @property int $announcement_id 公告ID
 * @property int $area_province_id 省份id,0:未知
 * @property int $area_city_id 城市id,0:未知
 * @property int $total 点击数量
 */
class AnnouncementClickAreaCityTotal extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'announcement_click_area_city_total';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['update_time'], 'safe'],
            [['announcement_id', 'area_province_id', 'area_city_id', 'total'], 'integer'],
            [['announcement_id', 'area_city_id'], 'unique', 'targetAttribute' => ['announcement_id', 'area_city_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'update_time' => 'Update Time',
            'announcement_id' => 'Announcement ID',
            'area_province_id' => 'Area Province ID',
            'area_city_id' => 'Area City ID',
            'total' => 'Total',
        ];
    }
}
