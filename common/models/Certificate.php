<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "certificate".
 *
 * @property int $id
 * @property string $add_time 创建时间
 * @property string $update_time 修改时间
 * @property int $status 状态
 * @property int $parent_id 父id
 * @property string $name 名称
 * @property int $sort 排序
 * @property int $level 等级
 * @property int $type 证书类型；1：证书；2-技能
 */
class Certificate extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'certificate';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['status', 'parent_id', 'sort', 'level', 'type'], 'integer'],
            [['name'], 'string', 'max' => 64],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'parent_id' => 'Parent ID',
            'name' => 'Name',
            'sort' => 'Sort',
            'level' => 'Level',
            'type' => 'Type',
        ];
    }
}
