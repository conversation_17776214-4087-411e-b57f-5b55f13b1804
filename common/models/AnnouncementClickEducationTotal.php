<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "announcement_click_education_total".
 *
 * @property int $id ID
 * @property string $update_time 更新时间
 * @property int $announcement_id 公告ID
 * @property int $guest 游客
 * @property int $rests 其他
 * @property int $college 大专
 * @property int $poaceae 本科
 * @property int $doctor 博士
 * @property int $master 硕士
 */
class AnnouncementClickEducationTotal extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'announcement_click_education_total';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['update_time'], 'safe'],
            [['announcement_id', 'guest', 'rests', 'college', 'poaceae', 'doctor', 'master'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'update_time' => 'Update Time',
            'announcement_id' => 'Announcement ID',
            'guest' => 'Guest',
            'rests' => 'Rests',
            'college' => 'College',
            'poaceae' => 'Poaceae',
            'doctor' => 'Doctor',
            'master' => 'Master',
        ];
    }
}
