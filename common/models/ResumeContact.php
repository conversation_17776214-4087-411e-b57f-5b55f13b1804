<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_contact".
 *
 * @property int $id id;主键id
 * @property string $add_time 创建时间
 * @property string $update_time 修改时间
 * @property int $status 状态
 * @property int $resume_id 简历id
 * @property int $member_id 会员id
 * @property string $mobile 联系方式
 * @property string $email 邮箱
 * @property string $qq qq
 * @property string $weixin 微信
 * @property string $mobile_code 手机号区号
 */
class ResumeContact extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_contact';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['status', 'resume_id', 'member_id'], 'integer'],
            [['mobile', 'qq', 'weixin'], 'string', 'max' => 64],
            [['email'], 'string', 'max' => 256],
            [['mobile_code'], 'string', 'max' => 16],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'resume_id' => 'Resume ID',
            'member_id' => 'Member ID',
            'mobile' => 'Mobile',
            'email' => 'Email',
            'qq' => 'Qq',
            'weixin' => 'Weixin',
            'mobile_code' => 'Mobile Code',
        ];
    }
}
