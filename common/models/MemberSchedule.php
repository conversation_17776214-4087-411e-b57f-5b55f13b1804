<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "member_schedule".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $status 状态
 * @property string $title 主题
 * @property string $content 内容
 * @property string $begin_time 开始时间
 * @property string $end_time 结束时间
 * @property string $place 地点
 * @property string $remind_time 提醒时间
 * @property int $remind_type 提醒时间类型，1:15分钟；2:30分钟；3:45分钟；4:1小时；5:2小时；6:3小时
 * @property int $member_id 会员id
 * @property int $is_need_remind 是否需要提醒
 * @property int $is_remind 是否已经提醒了
 * @property int $type 类型1自主添加的日程,2求职者面试日程,3企业面试日程
 * @property int $main_id 关联其他主表的时候，其他表的id
 */
class MemberSchedule extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'member_schedule';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time', 'begin_time', 'end_time', 'remind_time'], 'safe'],
            [['status', 'remind_type', 'member_id', 'is_need_remind', 'is_remind', 'type', 'main_id'], 'integer'],
            [['title', 'content'], 'string', 'max' => 512],
            [['place'], 'string', 'max' => 64],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'title' => 'Title',
            'content' => 'Content',
            'begin_time' => 'Begin Time',
            'end_time' => 'End Time',
            'place' => 'Place',
            'remind_time' => 'Remind Time',
            'remind_type' => 'Remind Type',
            'member_id' => 'Member ID',
            'is_need_remind' => 'Is Need Remind',
            'is_remind' => 'Is Remind',
            'type' => 'Type',
            'main_id' => 'Main ID',
        ];
    }
}
