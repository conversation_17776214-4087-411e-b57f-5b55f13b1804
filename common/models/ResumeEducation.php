<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_education".
 *
 * @property int $id id;主键id
 * @property string $add_time 创建时间
 * @property string $update_time 修改时间
 * @property int $status 状态
 * @property int $resume_id 简历id
 * @property int $member_id 会员id
 * @property string $begin_date 开始时间
 * @property string $end_date 结束时间
 * @property int $is_abroad 是否留学,0否,1是
 * @property string $school 学校
 * @property string $college 第二院系
 * @property int $major_id 专业id
 * @property int $education_id 学历水平id
 * @property int $is_recruitment 是否统招0否1是
 * @property int $is_highest 是否是最高学历
 * @property int $is_project_school 是否985/211
 * @property string $major_custom 自定义专业名字
 * @property int $major_id_level_1 专业一级
 * @property int $major_id_level_2 专业二级
 * @property int $major_id_level_3 专业三级
 * @property string $mentor 导师
 * @property int $is_full_time 是否是全日制：1-是；2-否
 * @property int $is_sino_foreign_joint_training 是否是中外联合培养：1-是；2-否
 * @property int $is_master_bachelor_combined 是否是硕博连读：1-是；2-否
 * @property int $is_bachelor_master_combined 是否是本硕博连读：1-是；2-否
 * @property int $is_bachelor_master_continuous 是否是本硕连读：1-是；2-否
 * @property int $is_junior_college_to_bachelor 是否是专升本：1-是；2-否
 * @property int $template_type 简历模板类型：常量1-学术版，2-普通版（未完善简历前使用）
 * @property int $school_id 院校id
 * @property string $joint_school 联合培养院校名称
 * @property int $joint_school_id 联合培养院校id
 * @property int $degree_type 学位类型：1-学术型；2-专业型
 * @property int $is_only_phd 是否专攻博士：1-是；2-否
 * @property int $is_bachelor_phd_combined 是否本博连读
 * @property int $complete_status 完善状态；1:已完善；2:待完善(stauts=9生效)
 */
class ResumeEducation extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_education';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time', 'begin_date', 'end_date'], 'safe'],
            [['status', 'resume_id', 'member_id', 'is_abroad', 'major_id', 'education_id', 'is_recruitment', 'is_highest', 'is_project_school', 'major_id_level_1', 'major_id_level_2', 'major_id_level_3', 'is_full_time', 'is_sino_foreign_joint_training', 'is_master_bachelor_combined', 'is_bachelor_master_combined', 'is_bachelor_master_continuous', 'is_junior_college_to_bachelor', 'template_type', 'school_id', 'joint_school_id', 'degree_type', 'is_only_phd', 'is_bachelor_phd_combined', 'complete_status'], 'integer'],
            [['school'], 'string', 'max' => 64],
            [['college'], 'string', 'max' => 100],
            [['major_custom'], 'string', 'max' => 60],
            [['mentor'], 'string', 'max' => 50],
            [['joint_school'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'resume_id' => 'Resume ID',
            'member_id' => 'Member ID',
            'begin_date' => 'Begin Date',
            'end_date' => 'End Date',
            'is_abroad' => 'Is Abroad',
            'school' => 'School',
            'college' => 'College',
            'major_id' => 'Major ID',
            'education_id' => 'Education ID',
            'is_recruitment' => 'Is Recruitment',
            'is_highest' => 'Is Highest',
            'is_project_school' => 'Is Project School',
            'major_custom' => 'Major Custom',
            'major_id_level_1' => 'Major Id Level 1',
            'major_id_level_2' => 'Major Id Level 2',
            'major_id_level_3' => 'Major Id Level 3',
            'mentor' => 'Mentor',
            'is_full_time' => 'Is Full Time',
            'is_sino_foreign_joint_training' => 'Is Sino Foreign Joint Training',
            'is_master_bachelor_combined' => 'Is Master Bachelor Combined',
            'is_bachelor_master_combined' => 'Is Bachelor Master Combined',
            'is_bachelor_master_continuous' => 'Is Bachelor Master Continuous',
            'is_junior_college_to_bachelor' => 'Is Junior College To Bachelor',
            'template_type' => 'Template Type',
            'school_id' => 'School ID',
            'joint_school' => 'Joint School',
            'joint_school_id' => 'Joint School ID',
            'degree_type' => 'Degree Type',
            'is_only_phd' => 'Is Only Phd',
            'is_bachelor_phd_combined' => 'Is Bachelor Phd Combined',
            'complete_status' => 'Complete Status',
        ];
    }
}
