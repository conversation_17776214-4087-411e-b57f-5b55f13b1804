<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_order_snapshot".
 *
 * @property int $id
 * @property string $add_time 创建时间
 * @property int $order_id 订单表id
 * @property string $equity_package_name 产品名称
 * @property int $service_days 服务天数
 * @property string $equity_content 权益内容，逗号连接文字
 */
class ResumeOrderSnapshot extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_order_snapshot';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time'], 'safe'],
            [['order_id', 'equity_package_name', 'service_days', 'equity_content'], 'required'],
            [['order_id', 'service_days'], 'integer'],
            [['equity_package_name'], 'string', 'max' => 255],
            [['equity_content'], 'string', 'max' => 2048],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'order_id' => 'Order ID',
            'equity_package_name' => 'Equity Package Name',
            'service_days' => 'Service Days',
            'equity_content' => 'Equity Content',
        ];
    }
}
