<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "job_click_log".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property int $member_id 点击的会员id
 * @property int $job_id 职位的id
 * @property int $source 1:pc,2:h5
 * @property string $useragent 请求表头信息
 * @property string $user_cookies 用户的cookie
 * @property int $ip 操作ip
 */
class JobClickLog extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job_click_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time'], 'safe'],
            [['member_id', 'job_id', 'source', 'ip'], 'integer'],
            [['useragent'], 'string', 'max' => 2048],
            [['user_cookies'], 'string', 'max' => 64],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'member_id' => 'Member ID',
            'job_id' => 'Job ID',
            'source' => 'Source',
            'useragent' => 'Useragent',
            'user_cookies' => 'User Cookies',
            'ip' => 'Ip',
        ];
    }
}
