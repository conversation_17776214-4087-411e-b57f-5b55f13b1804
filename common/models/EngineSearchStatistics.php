<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "engine_search_statistics".
 *
 * @property int $id
 * @property int $rule_id 规则ID
 * @property string $route_url 路由
 * @property string $level_content_1 参数1
 * @property string $level_content_2 参数2
 * @property string $add_time 添加时间
 * @property string $update_time 更新时间
 * @property int $number 访问次数
 * @property string $level_content_tj 推荐参数
 */
class EngineSearchStatistics extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'engine_search_statistics';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['rule_id', 'number'], 'integer'],
            [['add_time'], 'required'],
            [['add_time', 'update_time'], 'safe'],
            [['route_url', 'level_content_tj'], 'string', 'max' => 255],
            [['level_content_1', 'level_content_2'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'rule_id' => 'Rule ID',
            'route_url' => 'Route Url',
            'level_content_1' => 'Level Content 1',
            'level_content_2' => 'Level Content 2',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'number' => 'Number',
            'level_content_tj' => 'Level Content Tj',
        ];
    }
}
