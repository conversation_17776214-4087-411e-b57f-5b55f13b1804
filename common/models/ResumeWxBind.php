<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_wx_bind".
 *
 * @property int $id
 * @property string $add_time 创建时间
 * @property string $update_time 修改时间
 * @property int $status 状态
 * @property int $resume_id 简历id
 * @property string $openid openid
 * @property string $unionid unionid
 * @property string $avatar 头像url
 * @property int $is_subscribe 是否关注0否1是
 * @property string $mini_openid 小程序openid
 */
class ResumeWxBind extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_wx_bind';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['status', 'resume_id', 'is_subscribe'], 'integer'],
            [['openid', 'unionid', 'mini_openid'], 'string', 'max' => 64],
            [['avatar'], 'string', 'max' => 256],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'resume_id' => 'Resume ID',
            'openid' => 'Openid',
            'unionid' => 'Unionid',
            'avatar' => 'Avatar',
            'is_subscribe' => 'Is Subscribe',
            'mini_openid' => 'Mini Openid',
        ];
    }
}
