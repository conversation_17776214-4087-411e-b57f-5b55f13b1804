<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "article_attribute".
 *
 * @property int $id
 * @property int $type 文档属性类型
 * @property int $article_id 文章id
 * @property string $add_time 添加时间
 * @property string $sort_time 排序时间
 * @property string $expire_time 过期时间
 */
class ArticleAttribute extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'article_attribute';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['type', 'article_id'], 'integer'],
            [['add_time', 'sort_time', 'expire_time'], 'safe'],
            [['article_id', 'type'], 'unique', 'targetAttribute' => ['article_id', 'type']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'type' => 'Type',
            'article_id' => 'Article ID',
            'add_time' => 'Add Time',
            'sort_time' => 'Sort Time',
            'expire_time' => 'Expire Time',
        ];
    }
}
