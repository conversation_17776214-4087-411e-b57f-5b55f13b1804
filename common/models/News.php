<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "news".
 *
 * @property int $id
 * @property string $add_time 创建时间
 * @property string $update_time 修改时间
 * @property int $creator_id 创建人的id
 * @property int $status 状态(0:未审核,1:通过)
 * @property int $article_id 文章id
 * @property int $use_site_type 使用站点类型：0-原站点，1-高才博后
 * @property int $first_use_site_type 首次发布站点类型：0-原站点，1-高才博后，发布后不可更改
 */
class News extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'news';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['creator_id', 'status', 'article_id', 'use_site_type', 'first_use_site_type'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'creator_id' => 'Creator ID',
            'status' => 'Status',
            'article_id' => 'Article ID',
            'use_site_type' => 'Use Site Type',
            'first_use_site_type' => 'First Use Site Type',
        ];
    }
}
