<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "email_log".
 *
 * @property int $id id;主键id
 * @property string $add_time 创建时间
 * @property int $status 状态1成功,0失败
 * @property string $email 邮箱
 * @property string $content 发送内容
 * @property int $type 类型, 1注册 2修改密码  3修改邮箱  4绑定邮箱 5站外投递
 * @property string $reason 发送失败原因
 * @property int $is_read 是否已读 1已读 2未读
 */
class EmailLog extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'email_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time'], 'safe'],
            [['status', 'type', 'is_read'], 'integer'],
            [['email', 'reason'], 'string', 'max' => 256],
            [['content'], 'string', 'max' => 1024],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'status' => 'Status',
            'email' => 'Email',
            'content' => 'Content',
            'type' => 'Type',
            'reason' => 'Reason',
            'is_read' => 'Is Read',
        ];
    }
}
