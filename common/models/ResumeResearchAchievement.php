<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_research_achievement".
 *
 * @property int $id 主键ID
 * @property string $add_time 创建时间
 * @property string $update_time 修改时间
 * @property int $status 状态（0有效，1无效）
 * @property int $resume_id 关联简历ID
 * @property int $member_id 关联会员ID
 * @property string $cas1 中科院分区一区篇数
 * @property string $cas2 中科院分区二区篇数
 * @property string $cas3 中科院分区三区篇数
 * @property string $cas4 中科院分区四区篇数
 * @property string $jcr1 JCR分区一区篇数
 * @property string $jcr2 JCR分区二区篇数
 * @property string $jcr3 JCR分区三区篇数
 * @property string $jcr4 JCR分区四区篇数
 * @property string $sci_count SCI收录篇数
 * @property string $ei_count EI收录篇数
 * @property string $ssci_count SSCI收录篇数
 * @property string $cscd_count CSCD收录篇数
 * @property string $cssci_count CSSCI收录篇数
 * @property string $pku_core_count 北大核心收录篇数
 * @property string $tech_core_count 科技核心收录篇数
 * @property string $cumulative_impact 论文累计影响因子字典type=65
 * @property string $max_impact 单篇最高影响因子
 * @property string $total_citations 论文总被引次数
 * @property string $h_index H指数
 */
class ResumeResearchAchievement extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_research_achievement';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['status', 'resume_id', 'member_id'], 'integer'],
            [['cas1', 'cas2', 'cas3', 'cas4', 'jcr1', 'jcr2', 'jcr3', 'jcr4', 'sci_count', 'ei_count', 'ssci_count', 'cscd_count', 'cssci_count', 'pku_core_count', 'tech_core_count', 'cumulative_impact', 'max_impact', 'total_citations', 'h_index'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'resume_id' => 'Resume ID',
            'member_id' => 'Member ID',
            'cas1' => 'Cas1',
            'cas2' => 'Cas2',
            'cas3' => 'Cas3',
            'cas4' => 'Cas4',
            'jcr1' => 'Jcr1',
            'jcr2' => 'Jcr2',
            'jcr3' => 'Jcr3',
            'jcr4' => 'Jcr4',
            'sci_count' => 'Sci Count',
            'ei_count' => 'Ei Count',
            'ssci_count' => 'Ssci Count',
            'cscd_count' => 'Cscd Count',
            'cssci_count' => 'Cssci Count',
            'pku_core_count' => 'Pku Core Count',
            'tech_core_count' => 'Tech Core Count',
            'cumulative_impact' => 'Cumulative Impact',
            'max_impact' => 'Max Impact',
            'total_citations' => 'Total Citations',
            'h_index' => 'H Index',
        ];
    }
}
