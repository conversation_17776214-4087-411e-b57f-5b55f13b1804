<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_conference_page".
 *
 * @property int $id 主键ID，自增
 * @property string $add_time 创建时间
 * @property string $update_time 修改时间
 * @property int $status 状态（0有效，1无效）
 * @property int $resume_id 关联简历ID
 * @property int $member_id 关联会员ID
 * @property string $title 论文题目
 * @property string $conference_name 会议名称
 * @property string $publish_time 发表时间（表单时间选择器对应）
 * @property int $author_position 本人位次（如"第一作者""通讯作者"等）
 * @property int $conference_level 会议级别，使用字典表
 * @property string $acceptance_rate 论文录用率（支持百分比/文本描述，如"15%""同行评审严格"）
 * @property int $citation_count_five_year 近5年被引次数
 * @property string $awards 所获奖项（示例：最佳论文奖）
 * @property string $description 论文描述（项目背景、成果、个人贡献等详细内容）
 * @property string $address_detail 举办地点
 * @property string $page_number 卷期页码
 * @property string $collection_name 论文集名称
 */
class ResumeConferencePage extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_conference_page';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time', 'publish_time'], 'safe'],
            [['status', 'resume_id', 'member_id', 'author_position', 'conference_level', 'citation_count_five_year'], 'integer'],
            [['description'], 'required'],
            [['description'], 'string'],
            [['title', 'conference_name', 'awards'], 'string', 'max' => 256],
            [['acceptance_rate'], 'string', 'max' => 64],
            [['address_detail', 'page_number', 'collection_name'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'resume_id' => 'Resume ID',
            'member_id' => 'Member ID',
            'title' => 'Title',
            'conference_name' => 'Conference Name',
            'publish_time' => 'Publish Time',
            'author_position' => 'Author Position',
            'conference_level' => 'Conference Level',
            'acceptance_rate' => 'Acceptance Rate',
            'citation_count_five_year' => 'Citation Count Five Year',
            'awards' => 'Awards',
            'description' => 'Description',
            'address_detail' => 'Address Detail',
            'page_number' => 'Page Number',
            'collection_name' => 'Collection Name',
        ];
    }
}
