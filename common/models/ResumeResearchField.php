<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_research_field".
 *
 * @property int $id 主键ID
 * @property int $member_id 会员ID
 * @property int $resume_id 简历ID
 * @property int $research_field_id 研究领域id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $status 状态（1有效，2无效）
 */
class ResumeResearchField extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_research_field';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['member_id', 'resume_id', 'research_field_id', 'status'], 'integer'],
            [['add_time', 'update_time'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'member_id' => 'Member ID',
            'resume_id' => 'Resume ID',
            'research_field_id' => 'Research Field ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
        ];
    }
}
