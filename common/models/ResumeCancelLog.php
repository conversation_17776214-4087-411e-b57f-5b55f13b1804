<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_cancel_log".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $member_id 求职者会员id
 * @property int $resume_id 简历id
 * @property int $admin_id 操作的运营id，在运营操作的时候会有这个
 * @property int $cancel_reason_type 注销原因类型：1已找到工作，2不满意服务，3注册多个账号等等，99其他
 * @property string $cancel_reason_detail 注销原因详细说明（选择其他时填写）
 * @property string $apply_time 申请时间
 * @property string $cooldown_end_time 冷静期结束时间（申请时间+7天）
 * @property string $complete_time 注销完成时间
 * @property string $withdraw_time 撤回时间
 * @property int $status 状态：1申请中，2已撤回，3已完成
 * @property string $ip IP地址（支持IPv4和IPv6）
 * @property int $sms_status 短信发送状态：1已发送申请成功，2已发送提醒，3已发送完成注销
 * @property string $resume_setting 求职者注销前消息通知json
 */
class ResumeCancelLog extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_cancel_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time', 'apply_time', 'cooldown_end_time', 'complete_time', 'withdraw_time'], 'safe'],
            [['member_id', 'resume_id', 'admin_id', 'cancel_reason_type', 'status', 'sms_status'], 'integer'],
            [['cancel_reason_detail'], 'string', 'max' => 1024],
            [['ip'], 'string', 'max' => 45],
            [['resume_setting'], 'string', 'max' => 500],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'member_id' => 'Member ID',
            'resume_id' => 'Resume ID',
            'admin_id' => 'Admin ID',
            'cancel_reason_type' => 'Cancel Reason Type',
            'cancel_reason_detail' => 'Cancel Reason Detail',
            'apply_time' => 'Apply Time',
            'cooldown_end_time' => 'Cooldown End Time',
            'complete_time' => 'Complete Time',
            'withdraw_time' => 'Withdraw Time',
            'status' => 'Status',
            'ip' => 'Ip',
            'sms_status' => 'Sms Status',
            'resume_setting' => 'Resume Setting',
        ];
    }
}
