<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "chat_common_phrase".
 *
 * @property int $id
 * @property string $add_time 创建时间
 * @property string $update_time 修改时间
 * @property string $content 内容
 * @property int $member_id 所属member_id
 */
class ChatCommonPhrase extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'chat_common_phrase';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['member_id'], 'integer'],
            [['content'], 'string', 'max' => 512],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'content' => 'Content',
            'member_id' => 'Member ID',
        ];
    }
}
