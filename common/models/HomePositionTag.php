<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "home_position_tag".
 *
 * @property int $id
 * @property string $add_time 新增时间
 * @property string $update_time 更新时间
 * @property string $name 标签内容
 * @property int $admin_id 添加人id
 */
class HomePositionTag extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'home_position_tag';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['admin_id'], 'integer'],
            [['name'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'name' => 'Name',
            'admin_id' => 'Admin ID',
        ];
    }
}
