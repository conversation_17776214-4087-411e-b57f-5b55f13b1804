<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "company_package_config".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property int $status 状态
 * @property string $name 名称
 * @property string $code 代码
 * @property int $job_amount 职位发布条数
 * @property int $announcement_amount 公告发布条数
 * @property int $job_refresh_amount 职位刷新次数
 * @property int $announcement_refresh_amount 公告刷新次数
 * @property int $job_refresh_interval_day 职位刷新间隔时间
 * @property int $announcement_refresh_interval_day 公告刷新间隔时间
 * @property int $announcement_release_interval_day 公告发布间隔时间
 * @property int $job_release_interval_day 职位发布间隔时间
 * @property int $member_id 会员id
 * @property int $company_id 企业id
 * @property int $package_amount 权益包数
 * @property string $expire_time 有效期
 * @property string $effect_time 生效时间
 * @property int $package_id 套餐id
 * @property int $resume_download_amount 简历下载点数
 * @property int $sms_amount 短信数量
 * @property int $chat_amount 直聊沟通点数
 * @property int $cycle_resume_download_amount 当前周期简历下载点数
 * @property int $cycle_chat_amount 当前周期直聊沟通点数
 * @property int $cycle_sms_amount 短信数量（总量）
 */
class CompanyPackageConfig extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'company_package_config';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'expire_time', 'effect_time'], 'safe'],
            [['status', 'job_amount', 'announcement_amount', 'job_refresh_amount', 'announcement_refresh_amount', 'job_refresh_interval_day', 'announcement_refresh_interval_day', 'announcement_release_interval_day', 'job_release_interval_day', 'member_id', 'company_id', 'package_amount', 'package_id', 'resume_download_amount', 'sms_amount', 'chat_amount', 'cycle_resume_download_amount', 'cycle_chat_amount', 'cycle_sms_amount'], 'integer'],
            [['name'], 'string', 'max' => 256],
            [['code'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'status' => 'Status',
            'name' => 'Name',
            'code' => 'Code',
            'job_amount' => 'Job Amount',
            'announcement_amount' => 'Announcement Amount',
            'job_refresh_amount' => 'Job Refresh Amount',
            'announcement_refresh_amount' => 'Announcement Refresh Amount',
            'job_refresh_interval_day' => 'Job Refresh Interval Day',
            'announcement_refresh_interval_day' => 'Announcement Refresh Interval Day',
            'announcement_release_interval_day' => 'Announcement Release Interval Day',
            'job_release_interval_day' => 'Job Release Interval Day',
            'member_id' => 'Member ID',
            'company_id' => 'Company ID',
            'package_amount' => 'Package Amount',
            'expire_time' => 'Expire Time',
            'effect_time' => 'Effect Time',
            'package_id' => 'Package ID',
            'resume_download_amount' => 'Resume Download Amount',
            'sms_amount' => 'Sms Amount',
            'chat_amount' => 'Chat Amount',
            'cycle_resume_download_amount' => 'Cycle Resume Download Amount',
            'cycle_chat_amount' => 'Cycle Chat Amount',
            'cycle_sms_amount' => 'Cycle Sms Amount',
        ];
    }
}
