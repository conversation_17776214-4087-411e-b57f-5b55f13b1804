<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "admin".
 *
 * @property int $id
 * @property string $add_time 创建时间
 * @property string $update_time 修改时间
 * @property int $status 状态
 * @property string $name 名称
 * @property string $username 用户名
 * @property string $password 密码
 * @property string $job_number 工号
 * @property string $wx_work_userid 企业微信userid
 * @property string $email 邮箱
 * @property int $position_id 职位id
 * @property int $department_id 部门id
 * @property string $last_login_time 最近一次登录的时间
 * @property int $last_login_ip 最近一次登录的ip地址
 */
class Admin extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'admin';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time', 'last_login_time'], 'safe'],
            [['status', 'position_id', 'department_id', 'last_login_ip'], 'integer'],
            [['name', 'username'], 'string', 'max' => 32],
            [['password', 'job_number', 'wx_work_userid'], 'string', 'max' => 64],
            [['email'], 'string', 'max' => 256],
            [['username'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'name' => 'Name',
            'username' => 'Username',
            'password' => 'Password',
            'job_number' => 'Job Number',
            'wx_work_userid' => 'Wx Work Userid',
            'email' => 'Email',
            'position_id' => 'Position ID',
            'department_id' => 'Department ID',
            'last_login_time' => 'Last Login Time',
            'last_login_ip' => 'Last Login Ip',
        ];
    }
}
