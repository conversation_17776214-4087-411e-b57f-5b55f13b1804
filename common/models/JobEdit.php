<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "job_edit".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property int $status 状态，1:在用；2:关闭
 * @property int $job_id 职位id
 * @property string $edit_content 编辑修改内容
 * @property string $editor 编辑人名称
 * @property int $editor_type 编辑类型，1:平台；2:用户
 * @property int $editor_id 用户id
 * @property int $announcement_id 关联公告id
 */
class JobEdit extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job_edit';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time'], 'safe'],
            [['status', 'job_id', 'editor_type', 'editor_id', 'announcement_id'], 'integer'],
            [['edit_content'], 'required'],
            [['edit_content'], 'string'],
            [['editor'], 'string', 'max' => 32],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'status' => 'Status',
            'job_id' => 'Job ID',
            'edit_content' => 'Edit Content',
            'editor' => 'Editor',
            'editor_type' => 'Editor Type',
            'editor_id' => 'Editor ID',
            'announcement_id' => 'Announcement ID',
        ];
    }
}
