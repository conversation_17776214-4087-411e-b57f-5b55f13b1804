<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_academic_book_stage_type_relation".
 *
 * @property int $id 主键ID
 * @property int $member_id 会员ID
 * @property int $resume_academic_book_id 学术著作ID
 * @property int $resume_id 简历ID
 * @property int $type 阶段类型；1教育经历、2工作经历、3博士后经历、4项目经历
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $status 状态（1有效，2无效）
 * @property int $related_stage_id 关联阶段ID
 */
class ResumeAcademicBookStageTypeRelation extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_academic_book_stage_type_relation';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['member_id', 'resume_academic_book_id', 'resume_id', 'type', 'status', 'related_stage_id'], 'integer'],
            [['add_time', 'update_time'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'member_id' => 'Member ID',
            'resume_academic_book_id' => 'Resume Academic Book ID',
            'resume_id' => 'Resume ID',
            'type' => 'Type',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'related_stage_id' => 'Related Stage ID',
        ];
    }
}
