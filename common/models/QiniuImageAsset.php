<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "qiniu_image_asset".
 *
 * @property int $id
 * @property string $asset_code 图片编码
 * @property string $school_name 学校名称
 * @property int $file_id 文件表ID
 * @property string $file_ext 文件后缀
 * @property int $file_size 文件大小
 * @property int $image_width 图片宽度
 * @property int $image_height 图片高度
 * @property string $preview_url 预览链接
 * @property string $full_url 完整链接
 * @property string|null $remark 备注
 * @property int $admin_id 创建人ID
 * @property string $admin_name 创建人姓名
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $status 状态 1=正常 2=删除
 */
class QiniuImageAsset extends \common\base\BaseActiveRecord
{
    public static function tableName()
    {
        return 'qiniu_image_asset';
    }

    public function rules()
    {
        return [
            [['asset_code', 'school_name', 'file_id', 'file_ext', 'file_size', 'image_width', 'image_height', 'preview_url', 'full_url', 'admin_id', 'admin_name'], 'required'],
            [['file_id', 'file_size', 'image_width', 'image_height', 'admin_id', 'status'], 'integer'],
            [['add_time', 'update_time'], 'safe'],
            [['remark'], 'string', 'max' => 500],
            [['asset_code'], 'string', 'max' => 64],
            [['school_name'], 'string', 'max' => 128],
            [['file_ext'], 'string', 'max' => 16],
            [['preview_url', 'full_url'], 'string', 'max' => 512],
            [['admin_name'], 'string', 'max' => 64],
            [['asset_code'], 'unique'],
        ];
    }

    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'asset_code' => 'Asset Code',
            'school_name' => 'School Name',
            'file_id' => 'File ID',
            'file_ext' => 'File Ext',
            'file_size' => 'File Size',
            'image_width' => 'Image Width',
            'image_height' => 'Image Height',
            'preview_url' => 'Preview Url',
            'full_url' => 'Full Url',
            'remark' => 'Remark',
            'admin_id' => 'Admin ID',
            'admin_name' => 'Admin Name',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
        ];
    }

    public function getTagRelations()
    {
        return $this->hasMany(QiniuImageAssetTagRel::class, ['asset_id' => 'id']);
    }

    public function getTags()
    {
        return $this->hasMany(QiniuImageTag::class, ['id' => 'tag_id'])
            ->via('tagRelations');
    }
}
