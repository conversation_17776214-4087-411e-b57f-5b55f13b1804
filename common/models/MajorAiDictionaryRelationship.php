<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "major_ai_dictionary_relationship".
 *
 * @property int $id
 * @property string $add_time 创建时间
 * @property int $dictionary_id major_ai_dictionary的主键id
 * @property int $major_id 识别的学科id
 */
class MajorAiDictionaryRelationship extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'major_ai_dictionary_relationship';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time'], 'safe'],
            [['dictionary_id', 'major_id'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'dictionary_id' => 'Dictionary ID',
            'major_id' => 'Major ID',
        ];
    }
}
