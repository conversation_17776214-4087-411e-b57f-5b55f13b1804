<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "admin_position".
 *
 * @property int $id 主键ID
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $status 0：无效，1：有效
 * @property string $name 名称;部门名称
 * @property string $key 关键字
 * @property int $department_id 部门id
 */
class AdminPosition extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'admin_position';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['status', 'department_id'], 'integer'],
            [['key'], 'required'],
            [['name'], 'string', 'max' => 128],
            [['key'], 'string', 'max' => 64],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'name' => 'Name',
            'key' => 'Key',
            'department_id' => 'Department ID',
        ];
    }
}
