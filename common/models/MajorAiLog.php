<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "major_ai_log".
 *
 * @property int $id
 * @property string $add_time 创建时间
 * @property string $text 识别文本
 * @property string $dictionary_ids major_ai_dictionary的主键id
 * @property int $type 单位类型1:批量导入,2:手动识别
 * @property string $dictionary_names 名字(需要识别的文案)
 * @property string $major_ids 识别的学科ids逗号隔开
 * @property string $major_names 识别的学科名字s逗号隔开
 */
class MajorAiLog extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'major_ai_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time'], 'safe'],
            [['type'], 'integer'],
            [['text'], 'string', 'max' => 2048],
            [['dictionary_ids', 'major_ids'], 'string', 'max' => 2048],
            [['dictionary_names'], 'string', 'max' => 8192],
            [['major_names'], 'string', 'max' => 1024],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'text' => 'Text',
            'dictionary_ids' => 'Dictionary Ids',
            'type' => 'Type',
            'dictionary_names' => 'Dictionary Names',
            'major_ids' => 'Major Ids',
            'major_names' => 'Major Names',
        ];
    }
}
