<?php

namespace common\service\announcement;

use common\base\models\BaseAdmin;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseArticle;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseJob;
use common\helpers\IpHelper;
use yii\base\Exception;

class RemoveService extends BaseService
{
    /**
     * 执行移动文档
     * @throws \yii\db\Exception
     * @throws Exception
     */
    public function run()
    {
        $this->actionType = self::ACTION_TYPE_REMOVE;
        $this->beforeRun();
        $this->remove();
        $this->afterRun();
    }

    /**
     * 设置好要处理的数据
     * @param $params
     * @return RemoveService
     */
    public function setData($params): RemoveService
    {
        $oldHome = BaseAnnouncement::find()
            ->alias('a')
            ->leftJoin(['art' => BaseArticle::tableName()], 'art.id=a.article_id')
            ->select([
                'art.home_column_id',
                'a.article_id',
            ])
            ->where(['a.id' => $params['announcementId']])
            ->asArray()
            ->one();

        $this->announcementId  = $params['announcementId'];
        $this->articleId       = $oldHome['article_id'];
        $this->handleType      = BaseAnnouncementHandleLog::HANDLE_TYPE_EDIT;
        $this->oldHomeColumnId = $oldHome['home_column_id'];
        $this->newHomeColumnId = $params['homeColumnId'];

        return $this;
    }

    /**
     * 移动文档
     * @throws \yii\db\Exception
     * @throws Exception
     */
    public function remove()
    {
        $newTemplateType   = BaseHomeColumn::findOneVal(['id' => $this->oldHomeColumnId], 'template_type');
        $checkTemplateType = BaseHomeColumn::findOneVal(['id' => $this->newHomeColumnId], 'template_type');

        if ($newTemplateType != $checkTemplateType) {
            return true;
        }

        $articleModel                 = BaseArticle::findOne(['id' => $this->articleId]);
        $articleModel->home_column_id = $this->newHomeColumnId;

        if (!$articleModel->save()) {
            throw new Exception('移动失败');
        }
    }

}
