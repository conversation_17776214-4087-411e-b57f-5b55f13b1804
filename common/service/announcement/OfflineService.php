<?php

namespace common\service\announcement;

use common\base\models\BaseAdmin;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseCompany;
use common\base\models\BaseJob;
use common\helpers\IpHelper;
use common\service\jobTop\JobTopApplication;
use yii\base\Exception;

class OfflineService extends BaseService
{
    public $reason;

    /**
     * 执行
     */
    public function run()
    {
        $this->actionType = self::ACTION_TYPE_OFFLINE;
        $this->beforeRun();
        $this->offline();
        $this->afterRun();
    }

    /**
     * 设置公告-下线
     * @return $this
     */
    public function setOffline(): OfflineService
    {
        $this->handleType = self::ACTION_TYPE_OFFLINE;

        return $this;
    }

    /**
     * 下线
     * @throws Exception
     */
    private function offline()
    {
        $this->offlineRun();
    }

    /**
     * 设置好要处理的再发布数据
     * @param $data
     * @return $this
     * @throws Exception
     */
    public function setData($data): OfflineService
    {
        $this->setAnnouncement($data['id']);
        // 合作单位操作下线
        $this->setCompany($this->announcementModel->company_id);
        if ($this->companyModel->is_cooperation == BaseCompany::COOPERATIVE_UNIT_YES && $this->operatorType == self::OPERATOR_TYPE_ADMIN) {
            if (!$data['reason']) {
                throw new Exception('合作单位操作下线请填写下线原因');
            }
        }
        // 下线
        if ($this->articleModel->status != BaseArticle::STATUS_ONLINE || $this->announcementModel->audit_status == BaseAnnouncement::STATUS_AUDIT_AWAIT) {
            throw new Exception('公告id：' . $data['id'] . ',不满足下线条件');
        }

        $this->jobIds = BaseJob::find()
            ->select('id,status')
            ->where([
                'announcement_id' => $data['id'],
                'status'          => BaseJob::STATUS_ONLINE,
            ])
            ->asArray()
            ->all();
        if ($this->jobIds) {
            $jobIds = [];
            foreach ($this->jobIds as $item) {
                $jobIds[] = $item['id'];
            }
        }

        $this->jobIds                   = $jobIds;
        $this->statusAnnouncementBefore = $this->articleModel->status;
        $this->announcementId           = $this->announcementModel->id;
        $this->reason                   = $data['reason'] ?: '';

        return $this;
    }

    /**
     * 操作下线
     */
    private function offlineRun()
    {
        $this->articleModel->status = BaseArticle::STATUS_OFFLINE;
        if (!$this->articleModel->save()) {
            throw new Exception($this->articleModel->getFirstErrorsMessage());
        }
        // 运营后台操作下线=违规

        switch ($this->operatorType) {
            case self::OPERATOR_TYPE_ADMIN:
                $this->announcementModel->offline_type = BaseAnnouncement::OFFLINE_TYPE_VIOLATION;
                break;
            case self::OPERATOR_TYPE_COMPANY:
                $this->announcementModel->offline_type = BaseAnnouncement::OFFLINE_TYPE_HAND;
                break;
            default:
                $this->announcementModel->offline_type = BaseAnnouncement::OFFLINE_TYPE_AUTO;
                break;
        }

        $this->announcementModel->offline_reason = $this->reason;
        $this->announcementModel->offline_time   = CUR_DATETIME;
        if (!$this->announcementModel->save()) {
            throw new Exception($this->announcementModel->getFirstErrorsMessage());
        }

        switch ($this->operatorType) {
            case self::OPERATOR_TYPE_ADMIN:
                $jobOfflineType = BaseJob::OFFLINE_TYPE_VIOLATION;
                break;
            case self::OPERATOR_TYPE_COMPANY:
                $jobOfflineType = BaseJob::OFFLINE_TYPE_HAND;
                break;
            default:
                $jobOfflineType = BaseJob::OFFLINE_TYPE_AUTO;
                break;
        }

        // 删除公告文档属性
        BaseArticleAttribute::deleteAttribute($this->articleId);

        if (!BaseJob::updateAll([
            'status'         => BaseJob::STATUS_OFFLINE,
            'offline_type'   => $jobOfflineType,
            'offline_time'   => date('Y-m-d H:i:s'),
            'offline_reason' => $this->reason,
        ], ['id' => $this->jobIds])) {
            // 找到对应的职位看看
            $list = BaseJob::find()
                ->where(['id' => $this->jobIds])
                ->asArray()
                ->count();

            // 检查一下这个公告下面的全部职位是不是都下线了
            $onlineCount = BaseJob::find()
                ->where(['announcement_id' => $this->announcementId])
                ->andWhere(['status' => BaseJob::STATUS_ONLINE])
                ->count();

            if ($onlineCount != 0) {
                // 并没有职位,跳出
                if (!$list) {
                    throw new Exception('职位下线失败');
                }
            }
        }

        if ($this->companyModel->is_cooperation == BaseCompany::COOPERATIVE_UNIT_YES) {
            $this->operationRecord = '下线公告id:' . $this->announcementModel->id . '，职位id:' . implode(',',
                $this->jobIds) . '，下线原因：' . $this->reason ?: '';
        } else {
            $this->operationRecord = '下线公告id:' . $this->announcementModel->id . '，职位id:' . implode(',',
                    $this->jobIds);
        }
        //置顶取消
        $app = JobTopApplication::getInstance();
        $app->updateStatusByJobStatus($this->jobIds);

        $this->statusAnnouncementAfter = BaseArticle::STATUS_OFFLINE;
    }

    /**
     * 操作日志
     * @param
     * @throws Exception
     * @throws \yii\base\NotSupportedException
     */
    protected function offlineLog()
    {
        //操作动作入表
        $handleBefore = [
            'status'           => $this->statusAnnouncementBefore,
            'operation_record' => '-',
        ];

        $handleAfter  = [
            'status'           => $this->statusAnnouncementAfter,
            'operation_record' => $this->operationRecord,
        ];
        $handleLogArr = [
            'add_time'        => CUR_DATETIME,
            'announcement_id' => $this->announcementId,
            'handle_type'     => (string)$this->handleType,
            'handler_type'    => $this->operatorType,
            'handler_id'      => $this->operatorId,
            'handler_name'    => $this->operatorUserName ?: '',
            'handle_before'   => json_encode($handleBefore),
            'handle_after'    => json_encode($handleAfter),
            'ip'              => IpHelper::getIpInt() ?: 0,
        ];
        BaseAnnouncementHandleLog::createInfo($handleLogArr);
    }

}
