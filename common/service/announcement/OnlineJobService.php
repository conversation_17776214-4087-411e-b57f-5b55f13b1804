<?php

namespace common\service\announcement;

use common\base\models\BaseAdmin;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArticle;
use common\base\models\BaseCompany;
use common\base\models\BaseJob;
use common\base\models\BaseJobHandleLog;
use common\helpers\IpHelper;
use yii\base\Exception;

class OnlineJobService extends BaseService
{
    /**
     * 执行
     */
    public function run()
    {
        $this->actionType = self::ACTION_TYPE_ONLINE_JOB;
        $this->beforeRun();
        $this->online();
        $this->afterRun();
    }

    /**
     * 设置职位-再发布
     * @return $this
     */
    public function setOnline(): OnlineJobService
    {
        $this->handleType = self::ACTION_TYPE_ONLINE_JOB;

        return $this;
    }

    /**
     * 再发布
     * @throws Exception
     */
    private function online()
    {
        $this->onlineRun();
    }

    /**
     * 设置好要处理的再发布数据
     * @param $data
     * @return $this
     * @throws Exception
     */
    public function setData($data): OnlineJobService
    {
        $jobId = $data['id'];

        $this->jobModel = BaseJob::findOne(['id' => $jobId]);
        $this->setAnnouncement($this->jobModel->announcement_id);

        // 再发布
        if ($this->articleModel->is_show != BaseArticle::IS_SHOW_YES) {
            throw new Exception('公告id：' . $this->announcementId . ',不满足再发布条件');
        }
        if ($this->jobModel->status != BaseJob::STATUS_OFFLINE || $this->jobModel->is_show != BaseJob::IS_SHOW_YES) {
            throw new Exception('职位id：' . $jobId . ',不满足再发布条件');
        }

        $this->jobId           = $jobId;
        $this->statusJobBefore = $this->jobModel->status;

        return $this;
    }

    /**
     * 操作再发布
     */
    private function onlineRun()
    {
        $timePlusYear = date("Y-m-d H-i-s", (time() + 60 * 60 * 24 * BaseJob::ADD_RELEASE_AGAIN_DAY));

        $jobModel = BaseJob::findOne([
            'id'      => $this->jobId,
            'is_show' => BaseJob::IS_SHOW_YES,
        ]);

        $jobModel->status       = BaseJob::STATUS_ONLINE;
        $jobModel->period_date  = $timePlusYear;
        $jobModel->release_time = CUR_DATETIME;
        $jobModel->refresh_time = CUR_DATETIME;
        $jobModel->refresh_date = CUR_DATE;
        $jobModel->offline_type = 0;
        $jobModel->offline_time = '0000-00-00 00:00:00';
        if (!$jobModel->save()) {
            throw new Exception('职位id：' . $this->jobId . '再发布失败');
        }

        $this->operationRecord = '再发布职位id:' . $this->jobId;

        $this->statusJobAfter = BaseJob::STATUS_OFFLINE;
    }

    /**
     * 操作日志
     * @param
     * @throws Exception
     * @throws \yii\base\NotSupportedException
     */
    protected function onlineJobLog()
    {
        //操作动作入表
        $handleBefore = [
            'status'           => $this->statusJobBefore,
            'operation_record' => '-',
        ];

        $handleAfter  = [
            'status'           => $this->statusJobAfter,
            'operation_record' => $this->operationRecord,
        ];
        $handleLogArr = [
            'add_time'      => CUR_DATETIME,
            'job_id'        => $this->jobId,
            'handle_type'   => (string)$this->handleType,
            'handler_type'  => $this->operatorType,
            'handler_id'    => $this->operatorId,
            'handler_name'  => $this->operatorUserName ?: '',
            'handle_before' => json_encode($handleBefore),
            'handle_after'  => json_encode($handleAfter),
            'ip'            => IpHelper::getIpInt(),
        ];
        BaseJobHandleLog::createInfo($handleLogArr);
    }

}
