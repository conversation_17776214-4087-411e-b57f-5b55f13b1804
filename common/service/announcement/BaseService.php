<?php

namespace common\service\announcement;

use common\base\models\BaseAdmin;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyPackageChangeLog;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseCompanyPackageSystemConfig;
use common\base\models\BaseJob;
use common\base\models\BaseJobTemp;
use common\base\models\BaseMember;
use common\base\models\BaseUser;
use common\helpers\TimeHelper;
use common\libs\ColumnAuto\AnnouncementAutoClassify;
use common\service\companyPackage\CompanyPackageApplication;
use queue\Producer;
use yii\base\Exception;

/**
 * 整个公告模块的基础服务
 */
class BaseService
{
    /**
     * 正文内容
     * @var string
     */
    public $content   = '';
    public $companyId = '';

    public $jobList          = [];
    public $jobIds           = [];
    public $jobId            = '';
    public $operatorId       = '';
    public $operatorType     = '';
    public $operatorUserName = '';
    // 操作类型
    const ACTION_TYPE_EDIT      = 1;//编辑
    const ACTION_TYPE_REFRESH   = 2;//刷新
    const ACTION_TYPE_ADD       = 3;//发布
    const ACTION_TYPE_ONLINE    = 4;//再发布（上线）
    const ACTION_TYPE_OFFLINE   = 5;//下线
    const ACTION_TYPE_AUDIT     = 6;//审核
    const ACTION_TYPE_HIDDEN    = 7;//隐藏
    const ACTION_TYPE_SHOW      = 8;//显示
    const ACTION_TYPE_DELETE    = 9;//删除
    const ACTION_TYPE_COPY      = 10;//复制
    const ACTION_TYPE_REDUCTION = 11;//还原
    const ACTION_TYPE_REMOVE    = 12;//移动

    const ACTION_TYPE_BATCH_EDIT    = 13;//批量编辑
    const ACTION_TYPE_BATCH_AUDIT   = 14;//批量审核
    const ACTION_TYPE_BATCH_COPY    = 15;//批量复制
    const ACTION_TYPE_BATCH_DELETE  = 16;//批量复制
    const ACTION_TYPE_HIDDEN_JOb    = 17;//职位隐藏
    const ACTION_TYPE_SHOW_JOb      = 18;//职位显示
    const ACTION_TYPE_REFRESH_JOB   = 19;//职位刷新
    const ACTION_TYPE_DELETE_JOB    = 20;//职位删除
    const ACTION_TYPE_REDUCTION_JOB = 21;//职位还原
    const ACTION_TYPE_ONLINE_JOB    = 22;//职位上线
    const ACTION_TYPE_OFFLINE_JOb   = 23;//职位下线

    const OPERATOR_TYPE_ADMIN   = BaseUser::TYPE_ADMIN;
    const OPERATOR_TYPE_COMPANY = BaseUser::TYPE_MEMBER;

    const CREATE_TYPE_EDIT = 1;
    const CREATE_TYPE_ADD  = 2;

    public $baseData;

    public $articleData;
    public $articleId;

    public $announcementData;
    public $announcementId;

    public $jobTempData;
    public $jobTempId;
    public $jobTempIds;

    public $statusJobBefore;
    public $statusJobAfter;
    public $statusAnnouncementBefore;
    public $statusAnnouncementAfter;
    public $operationRecord;

    public $editorType;
    public $handleType;

    public $oldHomeColumnId;
    public $newHomeColumnId;

    public $oldAttribute;
    public $newAttribute;

    public $insertAnnouncementId;

    /**
     * @var 操作的类型
     */
    public $actionType;

    /**
     * @var BaseCompany
     */
    public $companyModel;

    /**
     * @var BaseMember
     */
    public $companyMemberModel;

    /**
     * @var BaseAdmin
     */
    public $adminModel;

    /**
     * @var BaseArticle
     */
    public $articleModel;

    /**
     * @var BaseAnnouncement
     */
    public $announcementModel;

    /**
     * @var BaseJob
     */
    public $jobModel;

    /**
     * @var BaseCompanyPackageConfig
     */
    public $companyPackageConfigModel;

    /**
     * @var BaseJobTemp
     */
    public $jobTempModel;

    /**
     * 设置操作人(如果是企业操作,这里的operatorId其实是memberId)
     * @return $this
     */
    public function setOperator($operatorId, $operatorType)
    {
        $this->operatorId   = $operatorId;
        $this->operatorType = $operatorType;

        if ($this->operatorType == self::OPERATOR_TYPE_COMPANY) {
            // 企业的
            $company                = BaseCompany::findOne(['member_id' => $operatorId]);
            $this->companyId        = $company->id;
            $this->operatorUserName = $company->full_name;
        }

        if ($this->operatorType == self::OPERATOR_TYPE_ADMIN) {
            // 运营后台添加的
            $this->operatorUserName = BaseAdmin::findOneVal(['id' => $operatorId], 'name');
        }

        return $this;
    }

    /**
     * 添加日志
     */
    protected function log()
    {
        switch ($this->actionType) {
            case self::ACTION_TYPE_ADD:
                // 新增、编辑
                $this->createAnnouncementHandleLog();
                break;
            case self::ACTION_TYPE_AUDIT:
                // 审核
                $this->auditHandleLog();
                break;
            case self::ACTION_TYPE_ONLINE:
                // 公告再发布（上线）
                $this->onlineLog();
                break;
            case self::ACTION_TYPE_OFFLINE:
                // 公告下线
                $this->offlineLog();
                break;
            case self::ACTION_TYPE_HIDDEN:
                // 公告隐藏
                $this->hiddenLog();
                break;
            case self::ACTION_TYPE_SHOW:
                // 公告显示
                $this->showLog();
                break;
            case self::ACTION_TYPE_DELETE:
                // 公告删除
                $this->deleteLog();
                break;
            case self::ACTION_TYPE_BATCH_DELETE:
                // 公告批量删除
                $this->batchDeleteLog();
                break;
            case self::ACTION_TYPE_REFRESH:
                // 公告刷新
                $this->refreshLog();
                break;
            case self::ACTION_TYPE_HIDDEN_JOb:
                // 职位隐藏
                $this->hiddenJobLog();
                break;
            case self::ACTION_TYPE_SHOW_JOb:
                // 职位显示
                $this->showJobLog();
                break;
            case self::ACTION_TYPE_ONLINE_JOB:
                // 职位上线
                $this->onlineJobLog();
                break;
            case self::ACTION_TYPE_OFFLINE_JOb:
                // 职位下线
                $this->offlineJobLog();
                break;
            case self::ACTION_TYPE_DELETE_JOB:
                // 职位删除
                $this->deleteJobLog();
                break;
            case self::ACTION_TYPE_REFRESH_JOB:
                // 职位刷新
                $this->refreshJobLog();
                break;
            case self::ACTION_TYPE_BATCH_AUDIT:
                // 批量审核
                $this->batchAuditHandleLog();
                break;
            case self::ACTION_TYPE_BATCH_EDIT:
                // 批量编辑属性
                $this->changeAttributeHandleLog();
                break;
        }
    }

    /**
     * 检查会员套餐
     * `job_amount` int(11) NOT NULL DEFAULT '0' COMMENT '职位发布条数',
     * `announcement_amount` int(11) NOT NULL DEFAULT '0' COMMENT '公告发布条数',
     * `job_refresh_amount` int(11) NOT NULL DEFAULT '0' COMMENT '职位刷新次数',
     * `announcement_refresh_amount` int(11) NOT NULL DEFAULT '0' COMMENT '公告刷新次数',
     * `job_refresh_interval_day` int(11) NOT NULL DEFAULT '0' COMMENT '职位刷新间隔时间',
     * `announcement_refresh_interval_day` int(11) NOT NULL DEFAULT '0' COMMENT '公告刷新间隔时间',
     * `announcement_release_interval_day` int(11) NOT NULL DEFAULT '0' COMMENT '公告发布间隔时间',
     * `job_release_interval_day` int(11) NOT NULL DEFAULT '0' COMMENT '职位发布间隔时间',
     */
    protected function checkMemberPackage()
    {
        if ($this->operatorType == self::OPERATOR_TYPE_COMPANY) {
            // hotfix/1883【单位端】主账号单位后台-公告下线操作报错 调整下线无需验证单位套餐
            if ($this->actionType == self::ACTION_TYPE_OFFLINE) {
                return true;
            }

            // 企业自己操作的
            $this->companyPackageConfigModel = BaseCompanyPackageConfig::findOne([
                'company_id' => $this->companyModel->id,
                'status'     => BaseCompanyPackageConfig::STATUS_ACTIVE,
            ]);
            if (!$this->companyPackageConfigModel) {
                BaseCompanyPackageConfig::setCurrencyCompanyPackageConfig($this->companyModel->id);
                $this->companyPackageConfigModel = BaseCompanyPackageConfig::findOne([
                    'company_id' => $this->companyModel->id,
                    'status'     => BaseCompanyPackageConfig::STATUS_ACTIVE,
                ]);
            }

            if (!$this->companyPackageConfigModel) {
                throw new \Exception('套餐不存在');
            }

            if ($this->companyPackageConfigModel->effect_time > CUR_DATETIME) {
                throw new Exception('套餐还没生效');
            }

            // 只是编辑，不用检查套餐
            if ($this->companyPackageConfigModel->expire_time < CUR_DATETIME && $this->actionType != self::ACTION_TYPE_EDIT) {
                throw new Exception('套餐已过期');
            }

            switch ($this->actionType) {
                case self::ACTION_TYPE_ADD:
                case self::ACTION_TYPE_ONLINE:
                    // 新增,这里其实有编辑和新增的区别,如果是新增或者没有任何审核通过历史
                    // $article = self::Art
                    $this->checkRelease();
                    break;
                case self::ACTION_TYPE_REFRESH:
                    // 新增,这里其实有编辑和新增的区别,如果是新增或者没有任何审核通过历史
                    // $article = self::Art
                    $this->checkRefresh();
                    break;
            }
        } else {
            // 运营操作审核拒绝，获取单位的套餐信息返还消费
            $this->companyPackageConfigModel = BaseCompanyPackageConfig::findOne([
                'company_id' => $this->companyModel->id,
                'status'     => BaseCompanyPackageConfig::STATUS_ACTIVE,
            ]);
        }
    }

    /**
     * 检查是否可以发布
     */
    protected function checkRelease()
    {
        // 找到现在还有多少公告发布的条数
        $remainAmount = $this->companyPackageConfigModel->announcement_amount;

        if ($this->announcementModel->is_consume_release == BaseAnnouncement::IS_CONSUME_RELEASE_NO) {
            if ($remainAmount <= 0) {
                throw new Exception('公告发布数量已达上限');
            }
        }

        // 下面开始检查公告的发布间隔时间
        if ($this->articleModel->status != BaseArticle::STATUS_ONLINE) {
            $releaseTime = $this->articleModel->release_time;

            // 未曾发布成功过
            if ($releaseTime && $releaseTime != TimeHelper::ZERO_TIME) {
                $subDay = TimeHelper::computeDaySub($releaseTime, CUR_DATETIME);

                if ($subDay <= $this->companyPackageConfigModel->announcement_release_interval_day) {
                    throw new Exception('公告发布间隔时间未到');
                }
            }
        }

        return true;
    }

    /**
     * 检查是否可以刷新
     */
    protected function checkRefresh()
    {
        // 找到现在还有多少公告刷新次数
        $remainAmount = $this->companyPackageConfigModel->announcement_refresh_amount;
        if ($remainAmount <= 0) {
            throw new Exception('公告刷新次数已达上限');
        }
        // 找到上次刷新的时间
        $lastRefreshTime = $this->articleModel->real_refresh_time;

        $subDay = TimeHelper::computeDaySub($lastRefreshTime, CUR_DATETIME);

        if ($subDay <= $this->companyPackageConfigModel->announcement_refresh_interval_day) {
            throw new Exception('公告刷新间隔时间未到');
        }

        return true;
    }

    /**
     * 消费
     * @return void
     * @throws Exception
     */
    protected function consumptionRelease()
    {
        $oldAmount = $this->companyPackageConfigModel->announcement_amount;

        if ($oldAmount < 1) {
            throw new Exception('公告发布数量已达上限');
        }

        // 扣除发布次数
        $this->companyPackageConfigModel->updateCounters([
            'announcement_amount' => -1,
        ]);

        // 首次发布or再发布上线or拒绝后修改再发布(无审核通过历史)
        if ($this->actionType == self::ACTION_TYPE_ADD || $this->actionType == self::ACTION_TYPE_ONLINE || $this->actionType == self::ACTION_TYPE_EDIT) {
            $this->announcementModel->is_consume_release = BaseAnnouncement::IS_CONSUME_RELEASE_YES;
            $this->announcementModel->save();
        }

        $type              = BaseCompanyPackageChangeLog::TYPE_ANNOUNCEMENT_RELEASE;
        $packageConfigName = BaseCompanyPackageSystemConfig::PACKAGE_CONFIG_NAME;
        $packageSurplus    = [
            $packageConfigName['job_amount']                  => $this->companyPackageConfigModel->job_amount,
            $packageConfigName['announcement_amount']         => $this->companyPackageConfigModel->announcement_amount,
            $packageConfigName['job_refresh_amount']          => $this->companyPackageConfigModel->job_refresh_amount,
            $packageConfigName['announcement_refresh_amount'] => $this->companyPackageConfigModel->announcement_refresh_amount,
        ];
        $packageSurplus    = json_encode($packageSurplus);
        $data              = [
            'type'            => $type,
            'identify'        => BaseCompanyPackageChangeLog::IDENTIFY_REDUCE,
            'change_amount'   => 1,
            'surplus'         => $this->companyPackageConfigModel->announcement_amount,
            'package_surplus' => $packageSurplus,
            'member_id'       => $this->companyMemberModel->id,
            'member_name'     => $this->companyMemberModel->username ?: '',
            'company_id'      => $this->companyModel->id ?: '',
            'company_name'    => $this->companyModel->full_name ?: '',
            'handle_before'   => $oldAmount . '',
            'handle_after'    => $this->companyPackageConfigModel->announcement_amount . '',
            'handler_type'    => $this->operatorType,
            'handler_id'      => $this->operatorId,
            'handler'         => $this->operatorUserName,
            'content'         => BaseCompanyPackageChangeLog::TYPE_NAME[$type],
            'remark'          => '',
        ];

        BaseCompanyPackageChangeLog::createCompanyPackageChangeLog($data);
    }

    protected function consumptionRefresh()
    {
        $oldAmount = $this->companyPackageConfigModel->announcement_refresh_amount;
        // 扣除发布次数
        $this->companyPackageConfigModel->updateCounters([
            'announcement_refresh_amount' => -1,
        ]);
        $type              = BaseCompanyPackageChangeLog::TYPE_ANNOUNCEMENT_REFRESH;
        $packageConfigName = BaseCompanyPackageSystemConfig::PACKAGE_CONFIG_NAME;
        $packageSurplus    = [
            $packageConfigName['job_amount']                  => $this->companyPackageConfigModel->job_amount,
            $packageConfigName['announcement_amount']         => $this->companyPackageConfigModel->announcement_amount,
            $packageConfigName['job_refresh_amount']          => $this->companyPackageConfigModel->job_refresh_amount,
            $packageConfigName['announcement_refresh_amount'] => $this->companyPackageConfigModel->announcement_refresh_amount,
        ];
        $packageSurplus    = json_encode($packageSurplus);
        $data              = [
            'type'            => $type,
            'identify'        => BaseCompanyPackageChangeLog::IDENTIFY_REDUCE,
            'change_amount'   => 1,
            'surplus'         => $this->companyPackageConfigModel->announcement_refresh_amount,
            'package_surplus' => $packageSurplus,
            'member_id'       => $this->companyMemberModel->id,
            'member_name'     => $this->companyMemberModel->username ?: '',
            'company_id'      => $this->companyModel->id ?: '',
            'company_name'    => $this->companyModel->full_name ?: '',
            'handle_before'   => $oldAmount . '',
            'handle_after'    => $this->companyPackageConfigModel->announcement_refresh_amount . '',
            'handler_type'    => $this->operatorType,
            'handler_id'      => $this->operatorId,
            'handler'         => $this->operatorUserName,
            'content'         => BaseCompanyPackageChangeLog::TYPE_NAME[$type],
            'remark'          => '',
        ];

        BaseCompanyPackageChangeLog::createCompanyPackageChangeLog($data);
    }

    /**
     * @throws \yii\db\Exception
     * @throws \Exception
     */
    protected function handleMemberPackage()
    {
        if ($this->operatorType == self::OPERATOR_TYPE_COMPANY) {
            // 在这里处理会员套餐的消费
            switch ($this->actionType) {
                case self::ACTION_TYPE_ADD:
                case self::ACTION_TYPE_ONLINE:
                    // 新增,这里其实有编辑和新增的区别,如果是新增或者没有任何审核通过历史
                    //$this->consumptionRelease();
                    $companyPackageApplication = new CompanyPackageApplication();
                    $handleType                = BaseCompanyPackageChangeLog::HANDLE_TYPE_ANNOUNCEMENT_RELEASE;
                    $remark                    = BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[$handleType];
                    $companyPackageApplication->announcementCreate($this->companyModel->id, 1, $remark);
                    break;
                case self::ACTION_TYPE_EDIT:
                    if ($this->articleModel->release_time == TimeHelper::ZERO_TIME && $this->announcementModel->is_consume_release == BaseAnnouncement::IS_CONSUME_RELEASE_NO) {
                        $companyPackageApplication = new CompanyPackageApplication();
                        $handleType                = BaseCompanyPackageChangeLog::HANDLE_TYPE_ANNOUNCEMENT_RELEASE;
                        $remark                    = BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[$handleType];
                        $companyPackageApplication->announcementCreate($this->companyModel->id, 1, $remark);
                        //$this->consumptionRelease();
                    }
                    break;
                case self::ACTION_TYPE_REFRESH:
                    $companyPackageApplication = new CompanyPackageApplication();
                    $handleType                = BaseCompanyPackageChangeLog::HANDLE_TYPE_ANNOUNCEMENT_REFRESH;
                    $remark                    = BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[$handleType];
                    $companyPackageApplication->announcementRefresh($this->companyModel->id, 1, $remark);
                    break;
            }
        }

        // 这里不管是谁操作了审核拒绝,都需要处理一下返回套餐
        if ($this->actionType == self::ACTION_TYPE_AUDIT && $this->auditType == 2) {
            // 找到这篇公告,如果没有审核通过历史,并且这个公告之前是消耗过资源的
            if ($this->articleModel->release_time == TimeHelper::ZERO_TIME && $this->announcementModel->is_consume_release == BaseAnnouncement::IS_CONSUME_RELEASE_YES) {
                // 退还
                //$this->returnRelease();
                $companyPackageApplication = new CompanyPackageApplication();
                $handleType                = BaseCompanyPackageChangeLog::HANDLE_TYPE_ANNOUNCEMENT_REFUSE;
                $remark                    = BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[$handleType];
                $companyPackageApplication->announcementRefuse($this->companyModel->id, 1, $remark);
            }
        }
    }

    protected function returnRelease()
    {
        $oldAmount = $this->companyPackageConfigModel->announcement_amount;
        // 扣除发布次数
        $this->companyPackageConfigModel->updateCounters([
            'announcement_amount' => 1,
        ]);

        $this->announcementModel->is_consume_release = BaseAnnouncement::IS_CONSUME_RELEASE_NO;
        $this->announcementModel->save();

        $type              = BaseCompanyPackageChangeLog::TYPE_ANNOUNCEMENT_REJECT;
        $packageConfigName = BaseCompanyPackageSystemConfig::PACKAGE_CONFIG_NAME;
        $packageSurplus    = [
            $packageConfigName['job_amount']                  => $this->companyPackageConfigModel->job_amount,
            $packageConfigName['announcement_amount']         => $this->companyPackageConfigModel->announcement_amount,
            $packageConfigName['job_refresh_amount']          => $this->companyPackageConfigModel->job_refresh_amount,
            $packageConfigName['announcement_refresh_amount'] => $this->companyPackageConfigModel->announcement_refresh_amount,
        ];
        $packageSurplus    = json_encode($packageSurplus);
        $data              = [
            'type'            => $type,
            'identify'        => BaseCompanyPackageChangeLog::IDENTIFY_ADD,
            'change_amount'   => 1,
            'surplus'         => $this->companyPackageConfigModel->announcement_amount,
            'package_surplus' => $packageSurplus,
            'member_id'       => $this->companyMemberModel->id,
            'member_name'     => $this->companyMemberModel->username ?: '',
            'company_id'      => $this->companyModel->id ?: '',
            'company_name'    => $this->companyModel->full_name ?: '',
            'handle_before'   => $oldAmount . '',
            'handle_after'    => $this->companyPackageConfigModel->announcement_amount . '',
            'handler_type'    => $this->operatorType,
            'handler_id'      => $this->operatorId,
            'handler'         => $this->operatorUserName,
            'content'         => BaseCompanyPackageChangeLog::TYPE_NAME[$type],
            'remark'          => '',
        ];

        BaseCompanyPackageChangeLog::createCompanyPackageChangeLog($data);
    }

    protected function setJob($announcementId)
    {
        $this->jobModel = BaseJob::findOne(['announcement_id' => $announcementId]);
        if (!$this->jobModel) {
            throw new Exception('职位不存在');
        }
    }

    protected function setAnnouncement($announcementId)
    {
        $this->announcementModel = BaseAnnouncement::findOne(['id' => $announcementId]);
        $this->articleModel      = BaseArticle::findOne($this->announcementModel->article_id);
        if (!$this->announcementModel || !$this->articleModel) {
            throw new Exception('不存在公告id=' . $announcementId);
        }
        $this->announcementId = $announcementId;
        $this->articleId      = $this->announcementModel->article_id;

        // 保证企业的信息后面是可以拿到的
        if (!$this->companyModel) {
            $this->setCompany($this->announcementModel->company_id);
        }
    }

    /**
     * 设置companyID同时检查
     * @param $companyId
     */
    protected function setCompany($companyId)
    {
        $this->companyId          = $companyId;
        $this->companyModel       = BaseCompany::findOne($this->companyId);
        $this->companyMemberModel = BaseMember::findOne($this->companyModel->member_id);

        // 开始检查,根据不同的操作类型,是可以做不同的操作的
        switch ($this->actionType) {
            case self::ACTION_TYPE_ADD:
                // 检查新增
                break;
            case self::ACTION_TYPE_EDIT:
                // 检查编辑
                break;
            case self::ACTION_TYPE_AUDIT:
                // 检查审核
                break;
        }
    }

    protected function autoClassifyRun()
    {
        // 自动归属公告栏目规则
        // $model = new AnnouncementAutoClassify($this->announcementId);
        // $model->run();
        // 改为队列去调用
        Producer::afterAnnouncementUpdateJob($this->announcementId);
    }

    /**
     * 在执行前
     */
    protected function beforeRun()
    {
        $this->checkPermission();
        // 检查套餐,检查文案,检查等等
        $this->checkMemberPackage();
    }

    /**
     * 在执行后
     * @throws \yii\db\Exception
     */
    protected function afterRun()
    {
        // 创建操作日志
        $this->handleMemberPackage();
        $this->log();
        $this->autoClassifyRun();
    }

    protected function checkPermission()
    {
        return true;
    }

    /**
     * 检查属性结束时间
     * @param $comboAttribute
     */
    protected function checkAttributeTime($comboAttribute, $periodDate)
    {
        foreach ($comboAttribute as $item) {
            if ($item['expireTime'] > $periodDate) {
                throw new Exception(BaseArticleAttribute::ATTRIBUTE_ANNOUNCEMENT_LIST[$item['type']] . '-文档属性结束时间不得大于公告截止时间');
            }
        }
    }
}
