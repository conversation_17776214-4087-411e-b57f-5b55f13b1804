<?php

namespace common\service\memberCancel;

use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeCancelSnapshot;
use Yii;

/**
 * 求职者数据快照服务
 */
class ResumeCancelSnapshotService
{
    /**
     * 创建数据快照
     * @param int $resumeId    简历ID
     * @param int $cancelLogId 注销日志ID
     * @return int 快照记录ID
     * @throws \Exception
     */
    public function createSnapshot($resumeId, $cancelLogId)
    {
        $resume = BaseResume::findOne($resumeId);
        if (!$resume) {
            throw new \Exception('简历不存在');
        }

        $memberId = $resume->member_id;
        $member   = BaseMember::findOne($memberId);
        if (!$member) {
            throw new \Exception('用户不存在');
        }

        try {
            // 使用现有的 checkResumeInfo 方法获取完整的简历数据
            $completeResumeData = BaseResume::checkResumeInfo($memberId);

            // 创建快照记录
            $snapshot                = new BaseResumeCancelSnapshot();
            $snapshot->member_id     = $memberId;
            $snapshot->resume_id     = $resumeId;
            $snapshot->cancel_log_id = $cancelLogId;

            // 设置关键联系信息
            $snapshot->mobile           = $member->mobile ?? '';
            $snapshot->mobile_code      = $member->mobile_code ?? '86';
            $snapshot->email            = $member->email ?? '';
            $snapshot->username         = $member->username ?? '';
            $snapshot->name             = $resume->name ?? '';
            $snapshot->resume_data_json = json_encode($completeResumeData, JSON_UNESCAPED_UNICODE);

            if (!$snapshot->save()) {
                throw new \Exception('保存数据快照失败：' . $snapshot->getFirstErrorsMessage());
            }

            return $snapshot->id;
        } catch (\Exception $e) {
            Yii::error('创建数据快照失败：' . $e->getMessage(), 'resume-cancel');
            throw $e;
        }
    }

}
