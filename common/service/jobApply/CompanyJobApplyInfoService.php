<?php

namespace common\service\jobApply;

use common\base\BaseActiveRecord;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseCompanyInterview;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyResumePvTotal;
use common\base\models\BaseCompanyViewResume;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobApplyTopEquityRecord;
use common\base\models\BaseJobContact;
use common\base\models\BaseJobContactSynergy;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeWork;
use common\helpers\DebugHelper;
use common\helpers\FileHelper;
use common\helpers\UrlHelper;
use common\helpers\ValidateHelper;
use common\libs\CompanyAuthority\CompanyAuthority;
use common\service\companyAuth\ButtonGroupAuthService;
use yii\base\Exception;

class CompanyJobApplyInfoService extends BaseService
{

    public $originalParams;
    public $memberId;
    public $jobId;
    public $companyId;
    public $announcementId;

    public $page;
    public $pageSize;

    public $whereStatus;
    public $contact;
    public $source;

    public $isjoinResume = false;
    public $isSubAccount = false;

    public $params;

    public $resumeWhere = [
        'householdRegisterId',
        'education',
        'isProjectSchool',
        'isAbroad',
        'titleId',
        'gender',
        'announcementName',
        'jobCategoryId',
        'workExperience',
        'workExperienceMin',
        'workExperienceMax',
        'ageMin',
        'ageMax',
        'political',
    ];

    public $companyAuthorityList = [];

    public $cacheResumeList    = [];
    public $cacheAreaList      = [];
    public $cacheMajorList     = [];
    public $cacheEducationList = [];

    public function getList($params, $memberId)
    {
        // 获取投递的简历列表(简历管理-平台应聘)
        // 首先看一下是不是子账号,子账号就得是协同职位才有相关简历

        $this->originalParams = $params;
        $this->memberId       = $memberId;

        $this->setData();

        // 开始查询(主要就是拿到投递数据
        $query = BaseJobApply::find()
            ->alias('a')
            ->where(['a.company_id' => $this->companyId]);
        $query->andFilterWhere(['a.is_invitation' => $this->params['isInvitation']]);

        if ($this->isjoinResume) {
            $query->innerJoin(['r' => BaseResume::tableName()], 'r.id = a.resume_id');
            // 户籍国际
            $query->andFilterWhere(['r.household_register_id' => $this->params['householdRegisterId']]);
            // 学历
            $query->andFilterWhere(['r.top_education_code' => $this->params['education']]);
            // 是否985/211
            $query->andFilterWhere(['r.is_project_school' => $this->params['isProjectSchool']]);
            //政治面貌
            if (!empty($this->params['political'])) {
                $political = explode(',', $this->params['political']);
                $query->andWhere(['r.political_status_id' => $political]);
            }
            if ($this->params['titleId']) {
                // $title_id = explode(',', $keywords['title_id']);
                // $query->andWhere(['r.title_id' => $title_id]);

                $titleArray = explode(',', $this->params['titleId']);
                $titleList  = BaseDictionary::getAllChildTitleArray($titleArray);

                $titleCondition = ['or'];
                foreach ($titleList as $item) {
                    $titleCondition[] = "find_in_set(" . $item . ",r.title_id)";
                }
                $query->andWhere($titleCondition);
            }
            // 是否海外
            if ($this->params['isAbroad']) {
                if ($this->params['isAbroad'] == 1) {
                    $query->andWhere(['r.is_abroad' => 1]);
                } else {
                    // 不等于1
                    $query->andWhere([
                        '!=',
                        'r.is_abroad',
                        1,
                    ]);
                }
            }
            //年龄
            if (mb_strlen($this->params['ageMin']) > 0 && mb_strlen($this->params['ageMax']) > 0 && $this->params['ageMin'] > $this->params['ageMax']) {
                throw new Exception('年龄最小值不允许大于最大值');
            }
            if (mb_strlen($this->params['ageMin']) > 0) {
                $query->andWhere([
                    '>=',
                    'r.age',
                    $this->params['ageMin'],
                ]);
            }
            if (mb_strlen($this->params['ageMax']) > 0) {
                $query->andWhere([
                    '<=',
                    'r.age',
                    $this->params['ageMax'],
                ]);
            }
            // 性别
            $query->andFilterWhere(['r.gender' => $this->params['gender']]);

            // 工作年限
            if (($this->params['workExperience'] && mb_strlen($this->params['workExperienceMin']) > 0) || ($this->params['workExperience'] && mb_strlen($this->params['workExperienceMax']) > 0)) {
                throw new Exception('参数错误');
            }
            if ($this->params['workExperience']) {
                if ($this->params['workExperience'] == 1) {
                    //应届生/在校生
                    $query->andFilterWhere(['r.identity_type' => BaseResume::IDENTITY_TYPE_GRADUATE]);
                } else {
                    $workExperience = $this->params['workExperience'];
                    $query->andFilterWhere([
                        '>=',
                        'r.work_experience',
                        $workExperience['min'],
                    ]);
                    $query->andFilterWhere([
                        '<=',
                        'r.work_experience',
                        $workExperience['max'],
                    ]);
                }
            }

            if (mb_strlen($this->params['workExperienceMin']) > 0 && mb_strlen($this->params['workExperienceMax']) > 0 && $this->params['workExperienceMin'] > $this->params['workExperienceMax']) {
                throw new Exception('自定义工作年限最小值不允许大于最大值');
            }
            //自定义工作年限
            if (mb_strlen($this->params['workExperienceMin']) > 0) {
                $query->andFilterWhere([
                    '>=',
                    'r.work_experience',
                    $this->params['workExperienceMin'],
                ]);
            }
            if (mb_strlen($this->params['workExperienceMax']) > 0) {
                $query->andFilterWhere([
                    '<=',
                    'r.work_experience',
                    $this->params['workExperienceMax'],
                ]);
            }
        }
        if ($this->params['announcementId']) {
            $query->leftJoin(['j' => BaseJob::tableName()], 'j.id = a.job_id')
                ->andFilterCompare('j.announcement_id', $this->params['announcementId']);
        }
        // 投递开始时间
        if ($this->params['addTimeStartTime']) {
            $query->andFilterCompare('a.add_time', $this->params['addTimeStartTime'], '>=');
        }

        // 投递结束时间
        if ($this->params['addTimeEndTime']) {
            $query->andFilterCompare('a.add_time', $this->params['addTimeEndTime'], '<=');
        }

        $query->andFilterWhere(['a.job_id' => $this->jobId]);

        if ($this->params['contact']) {
            // 职位联系人,需要关键新的表
            $query->innerJoin(['c' => BaseJobContact::tableName()], 'c.job_id = a.job_id')
                ->andWhere([
                    'c.company_member_info_id' => $this->params['contact'],
                ]);
        }
        if (!empty($this->params['majorId'])) {
            if (!empty($this->params['education'])) {
                $query->leftJoin(['re' => BaseResumeEducation::tableName()], 're.id = r.last_education_id');
            } else {
                $query->leftJoin(['re' => BaseResumeEducation::tableName()], 're.resume_id = a.resume_id');
            }
            $majorList = explode(',', $this->params['majorId']);
            $query->andWhere([
                're.major_id' => $majorList,
                're.status'   => BaseResumeEducation::STATUS_ACTIVE,
            ]);
        }

        $statisticsQuery = clone $query;
        $query->andFilterCompare('a.status', $this->whereStatus);

        $count = $query->count();

        $statistics = $statisticsQuery->select([
            'count(a.id) as allStatusAmount',
            'count(CASE a.status WHEN 1 THEN 1 ElSE null END) as waitStatusAmount',
            'count(CASE a.status WHEN 2 THEN 1 ELSE null END) as throughFirstStatusAmount',
            'count(CASE a.is_invitation WHEN 1 THEN 1 ELSE null END) as interviewAmount',
            'count(CASE a.status WHEN 4 THEN 1 ELSE null END) as inappropriateStatusAmount',
            'count(CASE a.status WHEN 5 THEN 1 ELSE null END) as employedStatusAmount',
        ])
            ->asArray()
            ->one();

        // 这里要根据过来的状态,拿其他的数据 (status没有是全部,status=1是待处理status=2是通过初筛,status=4是不合适,status=5是已录用,因为下面有count了

        $pages = BaseActiveRecord::setPage($count, $this->page, $this->pageSize);

        $list = $query->select([
            'a.id',
            'a.source',
            'a.status',
            'a.source',
            'a.job_id',
            'a.is_check',
            'a.resume_id',
            'a.add_time',
            'a.resume_attachment_id',
            'a.company_tag',
            'a.company_mark_status',
        ])
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('equity_status desc,a.add_time desc')
            ->asArray()
            ->all();

        $newList = [];
        foreach ($list as $item) {
            $newList[] = self::getJobApplyResumeItem($item);
        }

        return [
            'list'                 => $newList,
            'page'                 => [
                'count' => $count * 1,
                'limit' => $this->pageSize * 1,
                'page'  => $this->page * 1,
            ],
            'statistics'           => $statistics,
            'companyAuthorityList' => $this->companyAuthorityList,

        ];
    }

    public function setData()
    {
        $params                     = $this->originalParams;
        $memberId                   = $this->memberId;
        $companyId                  = BaseCompanyMemberInfo::findOne(['member_id' => $memberId])->company_id;
        $subAccount                 = CompanyAuthority::getAuthority(['memberId' => $memberId]);
        $isSubAccount               = $subAccount->isSubAccount();
        $this->isSubAccount         = $isSubAccount;
        $this->companyAuthorityList = $subAccount->getCompanyAuthorityList('resumeList');
        // 子账号
        if ($isSubAccount) {
            $jobList = $subAccount->getSynergyJob();
            if (count($jobList) == 0) {
                // 如果没有协同职位,就直接返回
                $jobList = [0];
            }
        } else {
            // 这里还有一种情况,就是虽然是主账号,但是传了子账号的id过来,那么也查协同账号
            if ($this->originalParams['cooperateAccount']) {
                $jobList = BaseJobContactSynergy::find()
                    ->select('job_id')
                    ->where([
                        'company_member_info_id' => $this->originalParams['cooperateAccount'],
                    ])
                    ->column();
            }
        }
        $this->companyId = $companyId;

        $page     = $params['page'] ?? 1;
        $pageSize = $params['limit'] ?? 20;

        $this->page     = $page;
        $this->pageSize = $pageSize;

        $this->whereStatus = $params['status'] ?? '';

        // 前端给isProject传了3?,其实应该是2
        if (isset($this->originalParams['isProjectSchool']) && $this->originalParams['isProjectSchool'] == 3) {
            $params['isProjectSchool'] = 2;
        }

        // 2就变成1
        if (isset($this->originalParams['isProjectSchool']) && $this->originalParams['isProjectSchool'] == 2) {
            $params['isProjectSchool'] = 1;
        }

        // education逗号隔开转数组
        if ($this->originalParams['education']) {
            $params['education'] = explode(',', $this->originalParams['education']);
        }

        if ($this->originalParams['workExperience']) {
            $workExperience           = BaseDictionary::WORK_YEARS_LIST[$this->originalParams['workExperience']];
            $params['workExperience'] = [
                'min' => $workExperience['min'],
                'max' => $workExperience['max'],
            ];
        }

        if ($this->originalParams['addTimeStart']) {
            $params['addTimeStartTime'] = $this->originalParams['addTimeStart'] . ' 00:00:00';
        }

        if ($this->originalParams['addTimeEnd']) {
            $params['addTimeEndTime'] = $this->originalParams['addTimeEnd'] . ' 23:59:59';
        }

        // 海外
        if ($this->originalParams['isAbroad']) {
            $params['isAbroad'] = $this->originalParams['isAbroad'];
        }

        // 这里专业用一层去处理职位id的,避免资源浪费? 必须有jobId或者announcementName或者jobCategoryId才会进入这个逻辑
        if ($this->originalParams['jobId'] || $this->originalParams['announcementName'] || $this->originalParams['jobCategoryId'] || $this->originalParams['contact'] || $jobList) {
            $jobQuery = BaseJob::find()
                ->alias('j')
                ->select([
                    'j.id',
                ])
                ->where([
                    'j.company_id' => $this->companyId,
                ])
                ->andFilterWhere([
                    'j.id' => $params['jobId'],
                ])
                ->andFilterWhere([
                    'job_category_id' => $params['jobCategoryId'],
                ])
                ->andFilterWhere([
                    'j.id' => $jobList,
                ]);
            if ($this->originalParams['announcementName']) {
                // 关联公告
                $jobQuery->innerJoin(['an' => BaseAnnouncement::tableName()], 'an.id = j.announcement_id')
                    ->andWhere([
                        'like',
                        'an.title',
                        $this->originalParams['announcementName'],
                    ]);
            }

            $jobId       = $jobQuery->asArray()
                ->column();
            $this->jobId = $jobId ?: -1;
        }

        if ($this->originalParams['contact']) {
            // 职位联系人,需要关键新的表
            $params['contact'] = $this->originalParams['contact'];
        }

        $this->params = $params;

        // 只要params里面含有resumeWhere的key,就需要直接join resume
        foreach ($this->resumeWhere as $item) {
            if (isset($params[$item])) {
                $this->isjoinResume = true;
                break;
            }
        }
    }

    /**
     * limit: 20
     * page: 1
     * jobId:
     * status: 1
     * householdRegisterId:
     * education:
     * isProjectSchool:
     * isAbroad:
     * titleId:
     * gender:
     * source:
     * announcementName:
     * jobCategoryId:
     * addTimeStart:
     * addTimeEnd:
     * age:
     * cooperateAccount:
     * contact: 201
     */
    public function setWhere()
    {
    }

    public static function getJobApplyResumeItem($item)
    {
        // companyMarkStatus 不太理解
        // isInvitation 是否已经邀约了

        // 拿求职者的信息

        $sourceList     = BaseJobApply::SOURCE_LIST;
        $statusList     = BaseJobApply::STATUS_LIST;
        $markStatusList = BaseJobApply::MARK_STATUS_LIST;

        $resumeBaseInfo = BaseResume::find()
            ->alias('a')
            ->innerJoin(['b' => BaseMember::tableName()], 'a.member_id = b.id')
            ->select([
                'b.status as member_status',
                'age',
                'gender',
                'name',
                'last_education_id',
                'last_work_id',
                'member_id',
                'avatar',
                'mobile',
                'top_education_code',
                'work_experience',
                'last_update_time as refresh_time',
            ])
            ->where(['a.id' => $item['resume_id']])
            ->asArray()
            ->one();

        // 把item和resumeBaseInfo合并
        $item = array_merge($item, $resumeBaseInfo);

        $jobInfo = BaseJob::find()
            ->select([
                'name',
                'announcement_id',
                'city_id',
            ])
            ->where(['id' => $item['job_id']])
            ->asArray()
            ->one();

        # https://zentao.jugaocai.com/index.php?m=story&f=view&id=1180
        $item['announcementUrl'] = UrlHelper::createAnnouncementDetailPath($jobInfo['announcement_id']);
        $item['jobUrl']          = UrlHelper::createJobDetailPath($item['job_id']);

        $item['jobName']         = $jobInfo['name'];
        $item['announcement_id'] = $jobInfo['announcement_id'];
        if ($jobInfo['announcement_id']) {
            $item['announcement_title'] = BaseAnnouncement::findOneVal(['id' => $jobInfo['announcement_id']], 'title');
        }

        $last_education_info      = BaseResumeEducation::findOne($item['last_education_id']);
        $item['tag']              = BaseJobApplyRecord::DELIVERY_TYPE_INSIDE;
        $item['status_title']     = $statusList[$item['status']];
        $item['source_title']     = $sourceList[$item['source']];
        $item['avatar']           = FileHelper::getFullUrl($item['avatar']) ?: \Yii::$app->params['defaultMemberAvatar'];
        $last_major_name          = BaseMajor::getMajorName($last_education_info->major_id);
        $last_education_name      = BaseDictionary::getEducationName($last_education_info->education_id);
        $item['major']            = $last_major_name;
        $item['education_school'] = $last_education_info->school;
        $item['education_list']   = BaseResumeEducation::getLastRecord($item['resume_id'], 3);
        $item['work_list']        = BaseResumeWork::getLastRecord($item['resume_id'], 1);
        //简历类型便签
        $item['resume_type_tag']  = BaseResume::getResumeLevel($item['resume_id']);
        $item['resume_title_tag'] = BaseResume::getUserSpecialInfo($item['resume_id']);
        //活跃标签
        $item['active_tag']      = BaseMember::getUserActiveTime($item['member_id']);
        $item['education_title'] = $last_education_name;
        //获取30天内是否被查看
        $item['is_resume_check'] = BaseCompanyViewResume::getCheckStatus($item['resume_id'], $item['company_id']);

        //根据求职者身份，获取求职者工作经验或者毕业时间文案
        $identityExperienceText = BaseResume::getIdentityExperienceText($item['resume_id']);
        $resume_info_arr        = [
            ($item['age']) . '岁',
            $last_education_name,
            $last_major_name ?: $last_education_info->major_custom,
        ];
        if ($identityExperienceText) {
            $resume_info_arr[] = $identityExperienceText;
        }
        $item['user_info'] = implode('·', $resume_info_arr);
        $area              = '';
        if ($jobInfo['city_id'] && $item['member_status'] != BaseMember::STATUS_RESUME_CANCELED) {
            $areaIds = explode(',', $jobInfo['city_id']);
            foreach ($areaIds as $areaId) {
                $area .= BaseArea::getAreaName($areaId) . ',';
            }
            $item['area'] = substr($area, 0, -1);
        }
        if ($item['member_status'] == BaseMember::STATUS_RESUME_CANCELED) {
            $item['gender'] = '';
        }
        $item['company_mark_status_title'] = $item['company_mark_status'] ? $markStatusList[$item['company_mark_status']] : '';
        $item['announcement_title']        = $item['announcement_title'] ?? '';
        if (strlen($item['company_tag']) > 0) {
            $item['company_tag'] = explode(',', $item['company_tag']);
        }

        if ($item['is_invitation'] == BaseJobApply::IS_INVITATION_YES) {
            $companyInterviewInfo = BaseCompanyInterview::find()
                ->select([
                    'id',
                    'count(*) as interview_num',
                ])
                ->where(['job_apply_id' => $item['id']])
                ->orderBy('add_time desc')
                ->asArray()
                ->all();

            $item['company_interview_id'] = $companyInterviewInfo[0]['id'];
            $item['interview_num']        = ValidateHelper::number2chinese($companyInterviewInfo[0]['interview_num']) . '面';
        }
        unset($item['area_id']);
        // 这里需要找出来在投递总表的id,用于给下载使用
        $item['jobApplyMainId'] = BaseJobApplyRecord::findOneVal(['apply_id' => $item['id']], 'id');
        //出一下高意向标签
        $job_apply_info = BaseJobApplyTopEquityRecord::getLastUseEquity($item['job_id'], $item['resume_id'], 20);
        if ($job_apply_info) {
            $item['high_intention_is']   = 1;
            $item['high_intention_text'] = '高意向';
        } else {
            $item['high_intention_is']   = 0;
            $item['high_intention_text'] = '';
        }
        //做一个PV统计
        BaseCompanyResumePvTotal::updateDailyTotalPv($item['resume_id']);

        // 后置数据处理,add_time 变成年月日
        $item['add_time']     = substr($item['add_time'], 0, 10);
        $item['refresh_time'] = substr($item['refresh_time'], 0, 10);

        // 按钮组
        $jobApplyRecordId = BaseJobApplyRecord::findOneVal(['apply_id' => $item['id']], 'id');
        if (!$jobApplyRecordId) {
            BaseJobApply::deleteAll(['id' => $item['id']]);
        }
        $buttonGroupAuthService = new ButtonGroupAuthService();
        $item['buttonGroup']    = $buttonGroupAuthService->setParams(['memberStatus' => $item['member_status']])
            ->setType(ButtonGroupAuthService::TYPE_APPLY)
            ->handleResumeButtonGroup($jobApplyRecordId);

        return $item;
    }
}