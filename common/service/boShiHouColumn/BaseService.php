<?php
/**
 * create user：shannon
 * create time：2024/9/14 下午2:29
 */
namespace common\service\boShiHouColumn;

use common\base\models\BaseCompanyFeatureTag;
use common\service\CommonService;

class BaseService extends CommonService
{
    protected $isCache = false;
    const AJ_LIMIT_MAX_PAGE = 40;//公告职位页面列表限制40页

    const Column_ID_7   = 7;
    const Column_ID_263 = 263;
    const Column_ID_264 = 264;
    const Column_ID_265 = 265;
    const Column_ID_266 = 266;
    //栏目相关ID
    const Column_ID         = [
        self::Column_ID_7,
        self::Column_ID_263,
        self::Column_ID_264,
        self::Column_ID_265,
        self::Column_ID_266,
    ];
    const Column_ID_LEVEL_1 = self::Column_ID_7;
    const Column_ID_LEVEL_2 = [
        self::Column_ID_263,
        self::Column_ID_264,
        self::Column_ID_265,
        self::Column_ID_266,
    ];

    //用于公告与职位页面
    const ANNOUNCEMENT_TYPE = 1;
    const JOB_TYPE          = 2;

    /** @var int 博士后职位ID */
    const BOSHIHOU_JOB_CATEGORY_ID  = 29;
    const BOSHIHOU_JOB_CATEGORY_IDS = [
        29,
        263,
    ];

    /** @var array 对应的标题 */
    const COLUMN_ANNOUNCEMENT_NEW_TITLE           = [
        self::Column_ID_7                => '最新推荐',
        self::Column_ID_263              => '高等院校',
        self::Column_ID_264              => '科研机构',
        self::Column_ID_265              => '企事业单位',
        self::Column_ID_266              => '医疗单位',
        BaseCompanyFeatureTag::PI_TAG_ID => 'PI直招',
    ];
    const COLUMN_ANNOUNCEMENT_NEW_ICON_ACTIVE     = [
        self::Column_ID_7                => 'https://img.gaoxiaojob.com/uploads/boshihou/announcementNewIcon/column7y.png',
        self::Column_ID_263              => 'https://img.gaoxiaojob.com/uploads/boshihou/announcementNewIcon/column263y.png',
        self::Column_ID_264              => 'https://img.gaoxiaojob.com/uploads/boshihou/announcementNewIcon/column264y.png',
        self::Column_ID_265              => 'https://img.gaoxiaojob.com/uploads/boshihou/announcementNewIcon/column265y.png',
        self::Column_ID_266              => 'https://img.gaoxiaojob.com/uploads/boshihou/announcementNewIcon/column266y.png',
        BaseCompanyFeatureTag::PI_TAG_ID => 'https://img.gaoxiaojob.com/uploads/boshihou/announcementNewIcon/pi1y.png',
    ];
    const COLUMN_ANNOUNCEMENT_NEW_ICON_NOT_ACTIVE = [
        self::Column_ID_7                => 'https://img.gaoxiaojob.com/uploads/boshihou/announcementNewIcon/column7n.png',
        self::Column_ID_263              => 'https://img.gaoxiaojob.com/uploads/boshihou/announcementNewIcon/column263n.png',
        self::Column_ID_264              => 'https://img.gaoxiaojob.com/uploads/boshihou/announcementNewIcon/column264n.png',
        self::Column_ID_265              => 'https://img.gaoxiaojob.com/uploads/boshihou/announcementNewIcon/column265n.png',
        self::Column_ID_266              => 'https://img.gaoxiaojob.com/uploads/boshihou/announcementNewIcon/column266n.png',
        BaseCompanyFeatureTag::PI_TAG_ID => 'https://img.gaoxiaojob.com/uploads/boshihou/announcementNewIcon/pi1n.png',
    ];

    /** 栏目关联的单位类型 */
    const COLUMN_RELATED_UNIT_TYPE = [
        self::Column_ID_7                => [],
        self::Column_ID_263              => ['companyType' => '1_2_3'],
        self::Column_ID_264              => ['companyType' => '16_17_18_19'],
        self::Column_ID_265              => ['companyType' => '5_7_8_9_10'],
        self::Column_ID_266              => ['companyType' => 21],
        BaseCompanyFeatureTag::PI_TAG_ID => ['isPi' => BaseCompanyFeatureTag::PI_TAG_ID],
    ];

    /**
     * 单位群组调用
     */
    const COMPANY_GROUP_ID = [
        1,
        2,
        3,
        4,
        5,
        6,
        7,
        8,
        9,
        10,
        11,
        12,
        13,
        14,
        19,
    ];

    /**
     * 解析一下时间
     */
    protected function parseTime($timeNumber)
    {
        if (!in_array($timeNumber, [
            SeoAnnouncementJobService::TIME_3,
            SeoAnnouncementJobService::TIME_7,
            SeoAnnouncementJobService::TIME_15,
            SeoAnnouncementJobService::TIME_30,
        ])) {
            //去404
            $this->redirect404();
        }
        switch ($timeNumber) {
            case SeoAnnouncementJobService::TIME_3:
                $startTime = date('Y-m-d H:i:s', strtotime('-' . SeoAnnouncementJobService::TIME_3 . ' day'));
                $endTime   = date('Y-m-d H:i:s');
                break;
            case SeoAnnouncementJobService::TIME_7:
                $startTime = date('Y-m-d H:i:s', strtotime('-' . SeoAnnouncementJobService::TIME_7 . ' day'));
                $endTime   = date('Y-m-d H:i:s');
                break;
            case SeoAnnouncementJobService::TIME_15:
                $startTime = date('Y-m-d H:i:s', strtotime('-' . SeoAnnouncementJobService::TIME_15 . ' day'));
                $endTime   = date('Y-m-d H:i:s');
                break;
            case SeoAnnouncementJobService::TIME_30:
                $startTime = date('Y-m-d H:i:s', strtotime('-' . SeoAnnouncementJobService::TIME_30 . ' day'));
                $endTime   = date('Y-m-d H:i:s');
                break;
        }

        return [
            'startTime' => $startTime ?? '',
            'endTime'   => $endTime ?? '',
        ];
    }

    /**
     * 重定向去404页面
     */
    public function redirect404()
    {
        \Yii::$app->response->setStatusCode(404)
            ->send();
        echo \Yii::$app->view->renderFile('@app/views/home/<USER>', ['message' => '这里是404页面']);
        exit;
    }
}