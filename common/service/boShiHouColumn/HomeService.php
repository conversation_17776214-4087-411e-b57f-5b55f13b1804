<?php
/**
 * create user：shannon
 * create time：2024/9/25 上午9:14
 */
namespace common\service\boShiHouColumn;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementAreaRelation;
use common\base\models\BaseAnnouncementBoshihou;
use common\base\models\BaseAnnouncementExtra;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseArticleColumn;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyFeatureTag;
use common\base\models\BaseCompanyFeatureTagRelation;
use common\base\models\BaseCompanyGroupScoreSystem;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyStatData;
use common\base\models\BaseDictionary;
use common\base\models\BaseHwActivity;
use common\base\models\BaseHwActivityPromotion;
use common\base\models\BaseJob;
use common\base\models\BaseJobContact;
use common\base\models\BaseJobExtra;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseMajor;
use common\base\models\BaseMajorSpecialty;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeComplete;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeIntention;
use common\base\models\BaseResumeIntentionAreaRelation;
use common\base\models\BaseWelfareLabel;
use common\helpers\DebugHelper;
use common\helpers\FileHelper;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use common\libs\Cache;
use Yii;
use yii\db\Expression;

class HomeService extends BaseService
{
    //热招地区
    const AREA_HOT = [
        //        北京,
        2,
        //        上海,
        802,
        //        天津,
        20,
        //        重庆,
        2324,
        //        广州,
        1965,
        //        深圳,
        1988,
        //        武汉,
        1710,
        //        南京,
        821,
        //        西安,
        2899,
        //        成都,
        2368,
        //        杭州,
        934,
        //        安徽,
        1046,
        //        福建,
        1168,
        //        广东,
        1964,
        //        江苏,
        820,
        //        黑龙江,
        655,
        //        山东,
        1375,
        //        湖北,
        1709,
        //        四川,
        2367,
        //        浙江,
        933,
        //        陕西,
        2898,
        //        江西,
        1263,
        //        吉林,
        585,
        //        河南,
        1532,
        //        湖南,
        1827,
        //        辽宁,
        466,
        //        河北,
        37,
        //        山西,
        220,
        //        海南,
        2291,
        //        贵州,
        2572,
        //        广西,
        2162,
        //        甘肃,
        3022,
        //        内蒙古,
        351,
        //        云南,
        2670,
        //        宁夏,
        3178,
        //        青海,
        3126,
        //        西藏,
        2816,
        //        新疆,
        3206,
    ];
    //品牌单位-默认背景图
    const COMPANY_BRAND_DEFAULT_BG_IMG = [
        'https://img.gaoxiaojob.com/uploads/boshihou/brandCompany/brandCompany01.png',
        'https://img.gaoxiaojob.com/uploads/boshihou/brandCompany/brandCompany02.png',
        'https://img.gaoxiaojob.com/uploads/boshihou/brandCompany/brandCompany03.png',
        'https://img.gaoxiaojob.com/uploads/boshihou/brandCompany/brandCompany04.png',
    ];

    const BOSHIHOU_HOME_ANNOUNCEMENT_TOUTIAO_EXPIRE = 3600;
    const BOSHIHOU_HOME_SPECIALTY_RECOMMEND_EXPIRE  = 3900;
    const BOSHIHOU_HOME_ANNOUNCEMENT_HOT_EXPIRE     = 4200;
    const BOSHIHOU_HOME_ANNOUNCEMENT_NEW_EXPIRE     = 4500;
    const BOSHIHOU_HOME_AREA_HOT_EXPIRE             = 604800;//7天
    const BOSHIHOU_HOME_SELECTED_JOB_NEW_EXPIRE     = 4800;
    const BOSHIHOU_HOME_SELECTED_JOB_ONLINE_EXPIRE  = 21600;//6小时
    const BOSHIHOU_HOME_BRAND_COMPANY_EXPIRE        = 5100;
    const BOSHIHOU_HOME_BOHOU_ACTIVITY_EXPIRE       = 5400;

    /**
     * @param bool $isCache
     */
    public function setIsCache(bool $isCache)
    {
        $this->isCache = $isCache;

        return $this;
    }

    /**
     * 首页数据
     */
    public function run()
    {
        if (!$this->isCache) {
            $this->isCache = (bool)Yii::$app->request->get('isCache', 0);
        }

        //获取头条位置数据
        $touTiaoData     = $this->getTouTiao();
        $announcementNew = $this->getAnnouncementNewList();

        return [
            'showcaseT1'          => ShowcaseService::getHomeT1(),
            'showcaseT2'          => ShowcaseService::getHomeT2(),
            'showcaseT3'          => ShowcaseService::getHomeT3(),
            'showcaseTouTiao'     => $touTiaoData['showcaseTouTiao'],
            'announcementTouTiao' => $touTiaoData['announcementTouTiao'],
            'showcaseA1'          => ShowcaseService::getHomeA1(),
            'showcasePI'          => ShowcaseService::getHomePI(),
            'showcaseK1'          => $this->getSpecialtyK1(),
            'announcementHot'     => $this->getAnnouncementHotList(),
            'announcementNew'     => $announcementNew,
            'areaHot'             => $this->getAreaHotList(),
            // 'guessLike'           => (new GuessLikeService())->getList(),
            'selectedJob'         => $this->getSelectedJobList(),
            'brandCompany'        => $this->getBrandCompany(),
            'bohouActivity'       => $this->getBoHouActivity(),
            'seo'                 => Yii::$app->params['tdk']['home'],
            'latestAnnouncement'  => $this->getLatestAnnouncement($announcementNew),
        ];
    }

    /**
     * 获取博士后页面中，博士后栏目最新一条公告
     * @param $announcementNew
     * @return array|mixed
     */
    private function getLatestAnnouncement($announcementNew)
    {
        $announcementNewList = $announcementNew['list'][0] ?? [];
        $latestAnnouncement  = [];
        $latestTime          = 0;
        foreach ($announcementNewList as $announcementNewInfo) {
            $newTime = strtotime($announcementNewInfo['refresh_time']);
            if ($newTime > $latestTime) {
                $latestTime = $newTime;
                $latestAnnouncement = $announcementNewInfo;
            }
        }

        return $latestAnnouncement;
    }

    /**
     * 首页头条
     */
    private function getTouTiao()
    {
        //做一个缓存
        $result = Cache::get(Cache::BOSHIHOU_HOME_ANNOUNCEMENT_TOUTIAO);
        if ($result && !$this->isCache) {
            return json_decode($result, true);
        }
        //先获取头条广告位
        $showcaseTouTiaoData = ShowcaseService::getHomeTouTiao();
        $showcaseCiTiaoData  = ShowcaseService::getHomeCiTiao();
        //（1）展示所属栏目为本栏目（栏目id=7）、且公告属性为“栏目头条”的在线公告（不含已下线、已隐藏）；
        //（2）按发布时间倒序，最多展示前7条公告；点击，新页面打开【公告详情】；
        $limit           = 7 - count($showcaseTouTiaoData) - count($showcaseCiTiaoData);
        $topList         = BaseAnnouncement::find()
            ->alias('a')
            ->select([
                'a.id',
                'a.title',
            ])
            ->innerJoin(['art' => BaseArticle::tableName()], 'a.article_id = art.id')
            ->leftJoin(['ac' => BaseArticleColumn::tableName()], 'art.id = ac.article_id')
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'art.id=aa.article_id')
            ->where([
                'ac.column_id' => self::Column_ID_LEVEL_1,
                'art.status'   => BaseArticle::STATUS_ONLINE,
                'aa.type'      => BaseArticleAttribute::ATTRIBUTE_COLUMN_HEAD,
                'art.is_show'  => BaseArticle::IS_SHOW_YES,
            ])
            ->orderBy('art.refresh_time desc')
            ->limit($limit)
            ->groupBy('a.id')
            ->asArray()
            ->all();
        $showcaseTouTiao = [];
        if ($showcaseTouTiaoData) {
            $showcaseTouTiao = [
                'url'      => $showcaseTouTiaoData[0]['url'],
                'name'     => $showcaseTouTiaoData[0]['title'],
                'showcase' => $showcaseTouTiaoData[0],
            ];
        }
        $announcementTouTiao = [];
        foreach ($showcaseCiTiaoData as $showcaseItem) {
            $announcementTouTiao[] = [
                'url'      => $showcaseItem['url'],
                'name'     => $showcaseItem['title'],
                'showcase' => $showcaseItem,
            ];
        }
        foreach ($topList as $item) {
            if (!$showcaseTouTiao) {
                $showcaseTouTiao = [
                    'url'  => UrlHelper::createPcAnnouncementDetailPath($item['id']),
                    'name' => $item['title'],
                ];
                continue;
            }
            $announcementTouTiao[] = [
                'url'  => UrlHelper::createPcAnnouncementDetailPath($item['id']),
                'name' => $item['title'],
            ];
        }
        $result = [
            'showcaseTouTiao'     => $showcaseTouTiao,
            'announcementTouTiao' => $announcementTouTiao,
        ];
        Cache::set(Cache::BOSHIHOU_HOME_ANNOUNCEMENT_TOUTIAO, json_encode($result),
            self::BOSHIHOU_HOME_ANNOUNCEMENT_TOUTIAO_EXPIRE);

        return $result;
    }

    /**
     * 领域广告位
     */
    private function getSpecialtyK1()
    {
        //做一个缓存
        $specialtyList = Cache::get(Cache::BOSHIHOU_HOME_SPECIALTY_RECOMMEND);
        if ($specialtyList && !$this->isCache) {
            return json_decode($specialtyList, true);
        }
        //先获取领域数据
        $specialtyList = BaseMajorSpecialty::find()
            ->select([
                'id',
                'name',
                'spell',
                'icon',
            ])
            ->where(['status' => BaseMajorSpecialty::STATUS_SHOW])
            ->orderBy('sort_home asc')
            ->asArray()
            ->all();
        //先头部塞入一个元素-重点推荐
        array_unshift($specialtyList, [
            'id'   => 0,
            'name' => '重点推荐',
            'icon' => 'https://img.gaoxiaojob.com/uploads/boshihou/specialtyIcon/specialty0.png',
        ]);
        //保存单位ID
        $notCompanyId = [];
        $companyIdSet = [];
        $showcaseList = [];
        //获取每个领域的广告位数据
        foreach ($specialtyList as &$item) {
            $item['moreUrl'] = $item['id'] > 0 ? UrlHelper::getBoShiHouGongGao() . '/' . $item['spell'] : UrlHelper::getBoShiHouGongGao();
            //规则如下：
            $showcase           = ShowcaseService::getHomeK1($item['id']);
            $showcaseCompanyIds = $showcase ? array_column($showcase, 'companyId') : [];
            //组装数据
            foreach ($showcase as &$showcaseItem) {
                //获取单位信息
                $company                       = BaseCompany::findOne($showcaseItem['companyId']);
                $showcaseItem['companyLogo']   = $showcaseItem['image'] ?: BaseCompany::getCompanyLogo($company->logo_url);
                $showcaseItem['companyName']   = $showcaseItem['title'] ?: $company->full_name;
                $showcaseItem['companyType']   = BaseDictionary::getCompanyTypeName($company->type);
                $showcaseItem['companyNature'] = BaseDictionary::getCompanyNatureName($company->nature);
                $showcaseItem['url']           = $showcaseItem['url'] ?: UrlHelper::createPcCompanyDetailPath($company->id);
                $showcaseItem['isShowcase']    = true;
            }
            $showcaseCount = count($showcase);
            if ($showcaseCount < 25) {
                //推荐-按照系统规则补齐25个
                //2、重点推荐 tab 广告获取&交互说明：
                //（1）默认选中该tab，该tab下方列表优先展示 手动配置的广告，其次展示系统调用的单位；
                //（2）手动配置的广告：
                //        展示生效中、且为显示状态的广告（已隐藏的不显示）；25个/页（即5个*5行/页），不限数量，有多少展示多少；
                //（3）系统调用的单位：
                //        若手动配置的广告<25个时，则按下方规则调用合作单位信息，补齐25个（若补不齐，则有多少补多少）——
                //        步骤①：取 含“职位类型=博士后”在线职位的合作单位；
                //        步骤②：与该tab下生效中广告的“关联单位ID”去重，按单位群组权重分值倒序展示；单位群组权重分值相同，再按单位ID倒序展示；
                //      （PS：若手动配置的广告≥25个时，则无需按系统规则调用单位）
                //（4）该tab下，不限页面展示；页数>1时，每5s自动向左轮播下一页，支持手动左右滑动切换；页数=1时，无自动轮播效果，不展示轮播点；
                //3、学科领域 tab 广告获取&交互说明：
                //（1）鼠标移入某学科领域时，切换选中，下方列表展示 手动配置的广告+系统调用的单位；
                //（2）手动配置的广告：展示生效中、且为显示状态的广告（已隐藏的不显示），最多25个（即1页）；
                //（3）系统调用的单位：当某学科领域下，手动配置的广告不足25个时，按下方规则调用合作单位信息，补齐25个（若补不齐，则有多少补多少）：
                //        步骤①：取 含“职位类型=博士后”的在线职位、且为职位的“需求学科”命中该领域对应的一级学科之一的合作单位；
                //        步骤②：与该领域下生效中广告的“关联单位ID”去重，按单位群组权重分值倒序展示；单位群组权重分值相同，再按单位ID倒序展示；
                //        步骤③：限制每个单位，最多出现在2个学科领域tab下（不含“重点推荐tab”）
                //（4）展示优先级：手动配置的广告>系统调用的单位；
                //（5）若既无生效的广告，亦无符合条件的单位，则列表显示缺省样式。
                //获取领域下的专业ID
                $unitQuery = BaseJob::find()
                    ->alias('j')
                    ->select([
                        'c.id',
                        'c.full_name',
                        'c.logo_url',
                        'c.type',
                        'c.nature',
                    ])
                    ->innerJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
                    ->innerJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()], 'c.group_score_system_id=cgss.id')
                    ->where([
                        'c.is_cooperation'  => BaseCompany::COOPERATIVE_UNIT_YES,
                        'j.status'          => BaseJob::STATUS_ONLINE,
                        'j.job_category_id' => self::BOSHIHOU_JOB_CATEGORY_ID,
                    ]);
                if ($showcaseCompanyIds) {
                    $unitQuery->andWhere([
                        'not in',
                        'c.id',
                        $showcaseCompanyIds,
                    ]);
                }
                if ($item['id'] == 0) {
                    $isRecommend = true;
                } else {
                    $specialtyMajorIds = BaseMajor::find()
                        ->select('id')
                        ->where([
                            'status'             => BaseMajor::STATUS_ACTIVE,
                            'major_specialty_id' => $item['id'],
                        ])
                        ->column();
                    $unitQuery->leftJoin(['jmr' => BaseJobMajorRelation::tableName()], 'jmr.job_id=j.id')
                        ->andWhere([
                            'jmr.major_id' => $specialtyMajorIds,
                        ]);
                    if ($notCompanyId) {
                        $unitQuery->andWhere([
                            'not in',
                            'c.id',
                            $notCompanyId,
                        ]);
                    }
                    $isRecommend = false;
                }
                $unitList = $unitQuery->limit(25 - $showcaseCount)
                    ->orderBy('cgss.score desc,c.id desc')
                    ->groupBy('c.id')
                    ->asArray()
                    ->all();
                foreach ($unitList as &$unitItem) {
                    $unitItem['companyLogo']   = BaseCompany::getCompanyLogo($unitItem['logo_url']);
                    $unitItem['companyName']   = $unitItem['full_name'];
                    $unitItem['companyType']   = BaseDictionary::getCompanyTypeName($unitItem['type']);
                    $unitItem['companyNature'] = BaseDictionary::getCompanyNatureName($unitItem['nature']);
                    $unitItem['url']           = UrlHelper::createPcCompanyDetailPath($unitItem['id']);
                    $unitItem['subTitle']      = '';
                    if (!$isRecommend) {
                        if (isset($companyIdSet[$unitItem['id']]) && $companyIdSet[$unitItem['id']] == 1) {
                            $notCompanyId[] = $unitItem['id'];
                        } else {
                            $companyIdSet[$unitItem['id']] = 1;
                        }
                    }
                }
                //合并
                $showcase = array_merge($showcase, $unitList);
            }

            //            $item['showcase'] = $showcase;
            $showcaseList[] = $showcase;
        }

        // 将推荐数据做成分页数据
        $showcaseList[0] = array_chunk($showcaseList[0], 25);

        //改变结构
        $result = [
            'specialtyList' => $specialtyList,
            'showcaseList'  => $showcaseList,
        ];

        Cache::set(Cache::BOSHIHOU_HOME_SPECIALTY_RECOMMEND, json_encode($result),
            self::BOSHIHOU_HOME_SPECIALTY_RECOMMEND_EXPIRE);

        return $result;
    }

    /**
     * 公告热榜
     */
    private function getAnnouncementHotList()
    {
        //做一个缓存
        $hotList = Cache::get(Cache::BOSHIHOU_HOME_ANNOUNCEMENT_HOT);
        if ($hotList && !$this->isCache) {
            return json_decode($hotList, true);
        }
        //规则如下：
        // 1、调用规则——调用本栏目下（栏目id=7）
        // （1）近15天发布的、公告属性为“首页置顶”、“滚动”、“栏目置顶”、“首头1”、“首头2”、“栏目头条”、“焦点”、“推荐”、“博后加推”的在线公告；
        // （2）含“PI”单位特色标签（标签ID=1）的单位，近15天发布的在线公告；
        // 2、排序（从左到右，从上到下）：
        // ① 合作单位>非合作单位；
        // ② 同一合作类型，近5天发布的>近6～15天发布的；
        // ③ 优先含“PI”单位特色标签（标签ID=1）的单位的公告；
        //     其他公告按公告属性顺序排列：“滚动”=“博后加推”＞“首页置顶”＞“栏目置顶”＞“首头1”=“首头2”=“栏目头条”=“焦点”=“推荐”；
        // ④ 同一属性，按发布时间倒序排列；
        // 3、分页：20条/页；点击“换一批”，循环展示20条*4页的公告信息；
        //      若公告条数不足20条*4页，则不足条数自动调用最新发布的、未标识公告属性的合作单位的在线公告信息来填充。

        $nowDate = date('Y-m-d');
        $date5   = TimeHelper::calculateDifferenceDate($nowDate, 5, 2);
        $date15  = TimeHelper::calculateDifferenceDate($nowDate, 15, 2);

        $data      = BaseAnnouncement::find()
            ->alias('a')
            ->select([
                'a.id',
                'a.title',
                'art.refresh_time',
                //art.refresh_time>5输出1否则输出2
                new Expression('CASE WHEN art.refresh_date>"' . $date5 . '" THEN 1 ELSE 2 END as sort_type'),
                //aa.type=1,22输出1000,aa.type=3输出900,aa.type=4输出800,,aa.type=5,6,7,8,2输出700,其他输出500
                new Expression('CASE WHEN aa.type=1 OR aa.type=22 THEN 1000 WHEN aa.type=3 THEN 900 WHEN aa.type=4 THEN 800 WHEN aa.type=5 OR aa.type=6 OR aa.type=7 OR aa.type=8 OR aa.type=2 THEN 700 ELSE 500 END as sort_value'),
            ])
            ->innerJoin(['art' => BaseArticle::tableName()], 'a.article_id = art.id')
            ->innerJoin(['c' => BaseCompany::tableName()], 'a.company_id = c.id')
            ->innerJoin(['csd' => BaseCompanyStatData::tableName()], 'csd.company_id = c.id')
            ->leftJoin(['ac' => BaseArticleColumn::tableName()], 'art.id = ac.article_id')
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'art.id=aa.article_id')
            ->where([
                'ac.column_id' => self::Column_ID_LEVEL_1,
                'art.status'   => BaseArticle::STATUS_ONLINE,
                'art.is_show'  => BaseArticle::IS_SHOW_YES,
            ])
            ->andWhere([
                'or',
                [
                    'aa.type' => [
                        BaseArticleAttribute::ATTRIBUTE_HOME_TOP,
                        BaseArticleAttribute::ATTRIBUTE_ROLLING,
                        BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP,
                        BaseArticleAttribute::ATTRIBUTE_COLUMN_HEAD,
                        BaseArticleAttribute::ATTRIBUTE_FOCUS,
                        BaseArticleAttribute::ATTRIBUTE_RECOMMEND,
                        BaseArticleAttribute::ATTRIBUTE_HOME_TOP_ONE,
                        BaseArticleAttribute::ATTRIBUTE_HOME_TOP_TWO,
                        BaseArticleAttribute::ATTRIBUTE_DOCTOR_PUSH,
                    ],
                ],
                [
                    'csd.is_pi' => BaseCompanyStatData::IS_PI_YES,
                ],
            ])
            ->andWhere([
                '>=',
                'art.refresh_date',
                $date15,
            ])
            ->orderBy('c.is_cooperation asc,sort_type asc,csd.is_pi asc,sort_value desc,art.refresh_time desc')
            ->groupBy('a.id')
            ->limit(80)
            ->asArray()
            ->all();
        $initCount = count($data);
        if ($initCount < 80) {
            //想最后面补充数据
            $supplementData = BaseAnnouncement::find()
                ->alias('a')
                ->select([
                    'a.id',
                    'a.title',
                    'art.refresh_time',
                ])
                ->innerJoin(['art' => BaseArticle::tableName()], 'a.article_id = art.id')
                ->innerJoin(['c' => BaseCompany::tableName()], 'a.company_id = c.id')
                ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'art.id=aa.article_id')
                ->leftJoin(['ac' => BaseArticleColumn::tableName()], 'art.id = ac.article_id')
                ->where([
                    'c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
                    'ac.column_id'     => self::Column_ID_LEVEL_1,
                    'art.status'       => BaseArticle::STATUS_ONLINE,
                    'art.is_show'      => BaseArticle::IS_SHOW_YES,
                    //公告属性aa.type为空
                    'aa.type'          => null,
                ])
                ->orderBy('art.refresh_time desc')
                ->limit(80 - $initCount)
                ->groupBy('a.id')
                ->asArray()
                ->all();

            $data = array_merge($data, $supplementData);
        }
        foreach ($data as &$item) {
            $item['refresh_time'] = TimeHelper::formatDateByYear($item['refresh_time'], '.');
            $item['url']          = UrlHelper::createPcAnnouncementDetailPath($item['id']);
        }
        $result    = [];
        $count     = count($data);
        $pageLimit = 20;
        $page      = ceil($count / $pageLimit);
        for ($i = 0; $i < $page; $i++) {
            $result[$i] = array_slice($data, $i * $pageLimit, $pageLimit);
        }
        Cache::set(Cache::BOSHIHOU_HOME_ANNOUNCEMENT_HOT, json_encode($result),
            self::BOSHIHOU_HOME_ANNOUNCEMENT_HOT_EXPIRE);

        return $result;
    }

    /**
     * 最新发布
     */
    private function getAnnouncementNewList()
    {
        //做一个缓存
        $result = Cache::get(Cache::BOSHIHOU_HOME_ANNOUNCEMENT_NEW);
        if ($result && !$this->isCache) {
            return json_decode($result, true);
        }
        $result    = [
            'title' => [],
            'list'  => [],
        ];
        $titleList = self::COLUMN_ANNOUNCEMENT_NEW_TITLE;
        //直接循环栏目
        foreach ($titleList as $columnId => $title) {
            if ($columnId != BaseCompanyFeatureTag::PI_TAG_ID) {
                $topList            = BaseAnnouncement::find()
                    ->alias('a')
                    ->select([
                        'a.id',
                        'a.title',
                        'art.refresh_date',
                        'art.refresh_time',
                        'a.sub_title',
                        'a.highlights_describe',
                        'a.all_job_amount',
                        'a.period_date',
                        'a.company_id',
                    ])
                    ->addSelect(new Expression("'1' as is_top"))
                    ->innerJoin(['art' => BaseArticle::tableName()], 'a.article_id = art.id')
                    ->leftJoin(['ac' => BaseArticleColumn::tableName()], 'art.id = ac.article_id')
                    ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'art.id=aa.article_id')
                    ->where([
                        'ac.column_id' => $columnId,
                        'art.status'   => BaseArticle::STATUS_ONLINE,
                        'art.is_show'  => BaseArticle::IS_SHOW_YES,
                        'aa.type'      => BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP,
                    ])
                    ->limit(4)
                    ->groupBy('a.id')
                    ->orderBy('aa.sort_time desc')
                    ->asArray()
                    ->all();
                $topCount           = count($topList);
                $topAnnouncementIds = [];
                if ($topCount > 0) {
                    $topAnnouncementIds = array_column($topList, 'id');
                }

                $generalQuery = BaseAnnouncement::find()
                    ->alias('a')
                    ->select([
                        'a.id',
                        'a.title',
                        'art.refresh_date',
                        'art.refresh_time',
                        'a.sub_title',
                        'a.highlights_describe',
                        'a.all_job_amount',
                        'a.period_date',
                        'a.company_id',
                    ])
                    ->addSelect(new Expression("'0' as is_top"))
                    ->innerJoin(['art' => BaseArticle::tableName()], 'a.article_id = art.id')
                    ->innerJoin(['c' => BaseCompany::tableName()], 'a.company_id = c.id')
                    ->innerJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()],
                        'c.group_score_system_id = cgss.id')
                    ->leftJoin(['ac' => BaseArticleColumn::tableName()], 'art.id = ac.article_id')
                    ->andWhere([
                        'ac.column_id' => $columnId,
                        'art.status'   => BaseArticle::STATUS_ONLINE,
                        'art.is_show'  => BaseArticle::IS_SHOW_YES,
                    ])
                    ->andWhere([
                        'not in',
                        'a.id',
                        $topAnnouncementIds,
                    ])
                    ->orderBy('art.refresh_date desc,cgss.score desc,a.id desc');
                if ($columnId == self::Column_ID_7) {
                    $generalListOrg = $generalQuery->limit(120)
                        ->asArray()
                        ->all();
                    $generalList    = [];
                    $companyIds     = [];
                    foreach ($generalListOrg as $item) {
                        if (count($generalList) >= (12 - $topCount)) {
                            break;
                        }
                        if (in_array($item['company_id'], $companyIds)) {
                            continue;
                        }
                        $generalList[] = $item;
                        $companyIds[]  = $item['company_id'];
                    }
                } else {
                    $generalList = $generalQuery->limit((12 - $topCount))
                        ->groupBy('a.id')
                        ->asArray()
                        ->all();
                }
                $list = array_merge($topList, $generalList);
            } else {
                //（1）调用含“PI”单位特色标签（标签ID=1）的单位，发布的含“职位类型=博士后”在线职位的在线公告；
                //（2）调用勾选了“PI直招”公告属性的在线公告；且含“职位类型=博士后”在线职位的在线公告
                $topList            = BaseAnnouncementBoshihou::find()
                    ->alias('ab')
                    ->select([
                        'a.id',
                        'a.title',
                        'art.refresh_date',
                        'art.refresh_time',
                        'a.sub_title',
                        'a.highlights_describe',
                        'a.all_job_amount',
                        'a.period_date',
                    ])
                    ->addSelect(new Expression("'1' as is_top"))
                    ->leftJoin(['a' => BaseAnnouncement::tableName()], 'ab.announcement_id = a.id')
                    ->leftJoin(['art' => BaseArticle::tableName()], 'a.article_id = art.id')
                    ->innerJoin(['ae' => BaseAnnouncementExtra::tableName()], 'a.id = ae.announcement_id')
                    ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'a.article_id=aa.article_id')
                    ->where([
                        'ae.is_pi'   => BaseAnnouncementExtra::IS_PI_YES,
                        'aa.type'    => BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP,
                        'art.status' => BaseArticle::STATUS_ONLINE,
                    ])
                    ->orderBy('aa.sort_time desc')
                    ->limit(4)
                    ->asArray()
                    ->all();
                $topCount           = count($topList);
                $topAnnouncementIds = [];
                if ($topCount > 0) {
                    $topAnnouncementIds = array_column($topList, 'id');
                }
                $generalListOrg  = BaseAnnouncementBoshihou::find()
                    ->alias('ab')
                    ->select([
                        'a.id',
                        'a.title',
                        'art.refresh_date',
                        'art.refresh_time',
                        'a.sub_title',
                        'a.highlights_describe',
                        'a.all_job_amount',
                        'a.period_date',
                        new Expression('CASE WHEN aa.type=' . BaseArticleAttribute::ATTRIBUTE_DOCTOR_PUSH . ' THEN 30 WHEN cftr.feature_tag_id=' . BaseCompanyFeatureTag::PI_TAG_ID . ' THEN 10 ELSE 1 END as sort_value'),
                    ])
                    ->addSelect(new Expression("'0' as is_top"))
                    ->leftJoin(['a' => BaseAnnouncement::tableName()], 'ab.announcement_id = a.id')
                    ->leftJoin(['art' => BaseArticle::tableName()], 'a.article_id = art.id')
                    ->innerJoin(['ae' => BaseAnnouncementExtra::tableName()], 'a.id = ae.announcement_id')
                    ->innerJoin(['c' => BaseCompany::tableName()], 'a.company_id = c.id')
                    ->leftJoin(['cftr' => BaseCompanyFeatureTagRelation::tableName()], 'cftr.company_id = c.id')
                    ->innerJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()],
                        'c.group_score_system_id = cgss.id')
                    ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'a.article_id=aa.article_id')
                    ->where([
                        'ae.is_pi'   => BaseAnnouncementExtra::IS_PI_YES,
                        'art.status' => BaseArticle::STATUS_ONLINE,
                    ])
                    ->andWhere([
                        'not in',
                        'a.id',
                        $topAnnouncementIds,
                    ])
                    ->groupBy('sort_value,a.id')
                    ->orderBy('art.refresh_date desc,sort_value desc,cgss.score desc,a.id desc')
                    ->limit(120)
                    ->asArray()
                    ->all();
                $generalList     = [];
                $announcementIds = [];
                foreach ($generalListOrg as $piItem) {
                    if (count($generalList) >= (12 - $topCount)) {
                        break;
                    }
                    if (in_array($piItem['id'], $announcementIds)) {
                        continue;
                    }
                    $generalList[]     = $piItem;
                    $announcementIds[] = $piItem['id'];
                }
                $list = array_merge($topList, $generalList);
            }
            foreach ($list as &$item) {
                //发布时间处理
                $item['refresh_time_year']      = date('Y', strtotime($item['refresh_date']));
                $item['refresh_time_month_day'] = date('m.d', strtotime($item['refresh_date']));
                //截止时间-详见正文
                $item['period_date'] = $item['period_date'] == TimeHelper::ZERO_TIME ? '详见正文' : TimeHelper::formatDateByYear($item['period_date'],
                    '.');
                $item['jobAmount']   = BaseJob::getAnnouncementJobRecruitAmount($item['id']);
                $item['areaText']    = BaseAnnouncementAreaRelation::getCityTextByAnnouncementId($item['id']);
                $item['url']         = UrlHelper::createPcAnnouncementDetailPath($item['id']);
            }
            $urlParams         = self::COLUMN_RELATED_UNIT_TYPE[$columnId];
            $result['title'][] = [
                'columnId'      => $columnId,
                'activeIcon'    => self::COLUMN_ANNOUNCEMENT_NEW_ICON_ACTIVE[$columnId],
                'activeNotIcon' => self::COLUMN_ANNOUNCEMENT_NEW_ICON_NOT_ACTIVE[$columnId],
                'title'         => $title,
                'moreUrl'       => UrlHelper::getBoShiHouGongGao() . ($urlParams ? '?' . http_build_query(self::COLUMN_RELATED_UNIT_TYPE[$columnId]) : ''),
            ];
            $result['list'][]  = $list ?: [];
        }
        Cache::set(Cache::BOSHIHOU_HOME_ANNOUNCEMENT_NEW, json_encode($result),
            self::BOSHIHOU_HOME_ANNOUNCEMENT_NEW_EXPIRE);

        return $result;
    }

    /**
     * 各地热招
     */
    private function getAreaHotList()
    {
        //做一个缓存
        $result = Cache::get(Cache::BOSHIHOU_HOME_AREA_HOT);
        if ($result && !$this->isCache) {
            return json_decode($result, true);
        }
        $areaList = BaseArea::find()
            ->select([
                'id',
                'name',
                'spell',
            ])
            ->andWhere([
                'status' => BaseArea::STATUS_ACTIVE,
                'id'     => self::AREA_HOT,
            ])
            //按照id self::AREA_HOT排序
            ->orderBy(new Expression('FIELD(id, ' . implode(',', self::AREA_HOT) . ')'))
            ->asArray()
            ->all();

        foreach ($areaList as &$area) {
            $area['url'] = UrlHelper::getBoShiHouGongGao() . '/' . $area['spell'];
        }
        $result = [
            'moreUrl'  => UrlHelper::getBoShiHouGongGao(),
            'areaList' => $areaList,
        ];
        Cache::set(Cache::BOSHIHOU_HOME_AREA_HOT, json_encode($result), self::BOSHIHOU_HOME_AREA_HOT_EXPIRE);

        return $result;
    }

    /**
     * 精选职位
     */
    private function getSelectedJobList()
    {
        // $isShowTabType 1显示最新职位&在线机会 2显示为你推荐&在线机会
        $isShowTabType = 1;
        //是否登录-登录就看看用户的一些信息
        $result = [
            'title'         => [],
            'newList'       => [
                'notice' => [],
                'list'   => [],
            ],
            'recommendList' => [
                'list' => [],
            ],
            'onlineList'    => [],
        ];

        try {
            if (!Yii::$app->user->isGuest && Yii::$app->user->identity->type == BaseMember::TYPE_PERSON) {
                $memberId         = Yii::$app->user->id;
                $resumeInfo       = BaseResume::findOne(['member_id' => $memberId]);
                $resumeStepNumber = BaseResumeComplete::getResumeStep($resumeInfo->id);
                if ($resumeStepNumber > 3) {
                    $isShowTabType = 2;
                }
                //没有完善简历前三步
                $result['newList']['notice']  = [
                    'url' => UrlHelper::createPcResumeStepUrl($resumeStepNumber),
                ];
                $result['newList']['isLogin'] = true;
                $result['isLogin']            = true;
                $result['resumeStepBool']     = $resumeStepNumber > 3 ? true : false;
            } else {
                $result['newList']['notice']  = [
                    'url' => '',
                ];
                $result['newList']['isLogin'] = false;
                $result['isLogin']            = false;
                $result['resumeStepBool']     = false;
            }
        } catch (\Exception $e) {
            $result['newList']['notice']  = [
                'url' => '',
            ];
            $result['newList']['isLogin'] = false;
            $result['isLogin']            = false;
            $result['resumeStepBool']     = false;
        }

        if ($isShowTabType == 1) {
            //缓存一下
            $newList = Cache::get(Cache::BOSHIHOU_HOME_SELECTED_JOB_NEW);
            if ($newList && !$this->isCache) {
                $newList = json_decode($newList, true);
            } else {
                //最新职位
                $newList = BaseJob::find()
                    ->alias('j')
                    ->select([
                        'j.id as jobId',
                        'j.name as jobName',
                        'j.city_id as cityId',
                        'j.amount',
                        'j.major_id as majorId',
                        'j.welfare_tag as welfareTag',
                        'j.refresh_date as refreshDate',
                        'a.id as announcementId',
                        'a.title as announcementName',
                        'c.full_name as companyName',
                    ])
                    ->innerJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
                    ->leftJoin(['a' => BaseAnnouncement::tableName()], 'j.announcement_id = a.id')
                    ->where([
                        'j.status'          => BaseJob::STATUS_ONLINE,
                        'j.job_category_id' => self::BOSHIHOU_JOB_CATEGORY_IDS,
                    ])
                    ->orderBy('j.refresh_time desc,j.id desc')
                    ->groupBy('c.id')
                    ->limit(12)
                    ->asArray()
                    ->all();
                foreach ($newList as &$item) {
                    $item['welfareTag']  = $item['welfareTag'] ? implode('，',
                        BaseWelfareLabel::getWelfareLabelNameList($item['welfareTag'])) : '';
                    $item['refreshDate'] = TimeHelper::formatDateByYear($item['refreshDate'], '.');
                    $item['cityText']    = BaseArea::getAreaName($item['cityId']);
                    $item['majorId']     = BaseMajor::getAllMajorName(explode(',', $item['majorId']));
                    $item['wage']        = BaseJob::getWageText($item['jobId']);
                    $item['jobUrl']      = UrlHelper::createPcJobDetailPath($item['jobId']);
                }
                Cache::set(Cache::BOSHIHOU_HOME_SELECTED_JOB_NEW, json_encode($newList),
                    self::BOSHIHOU_HOME_SELECTED_JOB_NEW_EXPIRE);
            }
            $result['newList']['list'] = $newList;
        } else {
            //看下登录人的最高学历所属专业及意向地点
            $topEducationInfo           = BaseResumeEducation::findOne($resumeInfo->last_education_id);
            $majorId                    = $topEducationInfo->major_id_level_2;
            $topEducationMajorId        = $majorId ?: 0;
            $intentionAreaData          = BaseResumeIntention::find()
                ->alias('ri')
                ->leftJoin(['riar' => BaseResumeIntentionAreaRelation::tableName()], 'ri.id=riar.intention_id')
                ->select([
                    'riar.area_id',
                    'riar.level',
                ])
                ->where([
                    'ri.resume_id' => $resumeInfo->id,
                    'ri.status'    => BaseResumeIntention::STATUS_ACTIVE,
                ])
                ->groupBy('area_id')
                ->asArray()
                ->all();
            $intentionAreaProvinceIdSet = [];
            $intentionAreaCityIdSet     = [];
            foreach ($intentionAreaData as $item) {
                if ($item['level'] == 1) {
                    $intentionAreaProvinceIdSet[] = $item['area_id'];
                } elseif ($item['level'] == 2) {
                    $intentionAreaCityIdSet[]     = $item['area_id'];
                    $itemAreaInfo                 = BaseArea::findOne($item['area_id']);
                    $intentionAreaProvinceIdSet[] = $itemAreaInfo->parent_id;
                }
            }
            $listNumber = 12;
            //为你推荐
            //3个匹配度
            //A1：调用至本模块的职位，与求职者的最高学历专业&意向工作地点均相同的职位；【意向城市匹配】
            //A2：调用至本模块的职位，与求职者的最高学历专业相同、且意向工作地点所属省份相同的职位；[省份+所属省份匹配]
            //A3：调用至本模块的职位，与求职者的最高学历专业相同的职位；
            //武汉、江西、深圳
            $recommendList = BaseJob::find()
                ->alias('j')
                ->innerJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
                ->innerJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()], 'c.group_score_system_id=cgss.id')
                ->leftJoin(['jmr' => BaseJobMajorRelation::tableName()], 'j.id = jmr.job_id')
                ->leftJoin(['a' => BaseAnnouncement::tableName()], 'j.announcement_id = a.id')
                ->select([
                    'j.id as jobId',
                    'j.name as jobName',
                    'j.city_id as cityId',
                    'j.amount',
                    'j.major_id as majorId',
                    'j.welfare_tag as welfareTag',
                    'j.refresh_date as refreshDate',
                    'j.company_id as companyId',
                    'a.id as announcementId',
                    'a.title as announcementName',
                    'c.full_name as companyName',
                ])
                ->where([
                    'j.status'          => BaseJob::STATUS_ONLINE,
                    'j.job_category_id' => self::BOSHIHOU_JOB_CATEGORY_IDS,
                    'jmr.major_id'      => $topEducationMajorId,
                    'j.city_id'         => $intentionAreaCityIdSet,
                ])
                ->orderBy('cgss.score desc,j.refresh_time desc,j.id desc')
                ->groupBy('c.id')
                ->limit($listNumber)
                ->asArray()
                ->all();
            $listNumber    = $listNumber - count($recommendList);
            if ($listNumber > 0) {
                $recommendListA2 = BaseJob::find()
                    ->alias('j')
                    ->innerJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
                    ->innerJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()], 'c.group_score_system_id=cgss.id')
                    ->leftJoin(['jmr' => BaseJobMajorRelation::tableName()], 'j.id = jmr.job_id')
                    ->leftJoin(['a' => BaseAnnouncement::tableName()], 'j.announcement_id = a.id')
                    ->select([
                        'j.id as jobId',
                        'j.name as jobName',
                        'j.city_id as cityId',
                        'j.amount',
                        'j.major_id as majorId',
                        'j.welfare_tag as welfareTag',
                        'j.refresh_date as refreshDate',
                        'j.company_id as companyId',
                        'a.id as announcementId',
                        'a.title as announcementName',
                        'c.full_name as companyName',
                    ])
                    ->where([
                        'j.status'          => BaseJob::STATUS_ONLINE,
                        'j.job_category_id' => self::BOSHIHOU_JOB_CATEGORY_IDS,
                        'jmr.major_id'      => $topEducationMajorId,
                        'j.province_id'     => $intentionAreaProvinceIdSet,
                    ])
                    ->andWhere([
                        'not in',
                        'j.id',
                        array_column($recommendList, 'jobId'),
                    ])
                    ->andWhere([
                        'not in',
                        'j.company_id',
                        array_column($recommendList, 'companyId'),
                    ])
                    ->orderBy('cgss.score desc,j.refresh_time desc,j.id desc')
                    ->groupBy('c.id')
                    ->limit($listNumber)
                    ->asArray()
                    ->all();
                $listNumber      = $listNumber - count($recommendListA2);
                $recommendList   = array_merge($recommendList, $recommendListA2);
            }
            if ($listNumber > 0) {
                $recommendListA3 = BaseJob::find()
                    ->alias('j')
                    ->innerJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
                    ->innerJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()], 'c.group_score_system_id=cgss.id')
                    ->leftJoin(['jmr' => BaseJobMajorRelation::tableName()], 'j.id = jmr.job_id')
                    ->leftJoin(['a' => BaseAnnouncement::tableName()], 'j.announcement_id = a.id')
                    ->select([
                        'j.id as jobId',
                        'j.name as jobName',
                        'j.city_id as cityId',
                        'j.amount',
                        'j.major_id as majorId',
                        'j.welfare_tag as welfareTag',
                        'j.refresh_date as refreshDate',
                        'j.company_id as companyId',
                        'a.id as announcementId',
                        'a.title as announcementName',
                        'c.full_name as companyName',
                    ])
                    ->where([
                        'j.status'          => BaseJob::STATUS_ONLINE,
                        'j.job_category_id' => self::BOSHIHOU_JOB_CATEGORY_IDS,
                        'jmr.major_id'      => $topEducationMajorId,
                    ])
                    ->andWhere([
                        'not in',
                        'j.id',
                        array_column($recommendList, 'jobId'),
                    ])
                    ->andWhere([
                        'not in',
                        'j.company_id',
                        array_column($recommendList, 'companyId'),
                    ])
                    ->orderBy('cgss.score desc,j.refresh_time desc,j.id desc')
                    ->groupBy('c.id')
                    ->limit($listNumber)
                    ->asArray()
                    ->all();
                $recommendList   = array_merge($recommendList, $recommendListA3);
            }
            foreach ($recommendList as &$item) {
                $item['welfareTag']  = $item['welfareTag'] ? implode('，',
                    BaseWelfareLabel::getWelfareLabelNameList($item['welfareTag'])) : '';
                $item['refreshDate'] = TimeHelper::formatDateByYear($item['refreshDate'], '.');
                $item['cityText']    = BaseArea::getAreaName($item['cityId']);
                $item['majorId']     = BaseMajor::getAllMajorName(explode(',', $item['majorId']));
                $item['wage']        = BaseJob::getWageText($item['jobId']);
                $item['jobUrl']      = UrlHelper::createPcJobDetailPath($item['jobId']);
            }
            $result['recommendList']['list'] = $recommendList;
        }

        //缓存一下
        $onlineList = Cache::get(Cache::BOSHIHOU_HOME_SELECTED_JOB_ONLINE);
        if ($onlineList && !$this->isCache) {
            $onlineList = json_decode($onlineList, true);
        } else {
            //在线机会
            $onlineList = BaseJob::find()
                ->alias('j')
                ->innerJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
                ->innerJoin(['jc' => BaseJobContact::tableName()], 'jc.job_id = j.id')
                ->innerJoin(['cmi' => BaseCompanyMemberInfo::tableName()], 'jc.company_member_info_id = cmi.id')
                ->innerJoin(['m' => BaseMember::tableName()], 'cmi.member_id = m.id')
                ->innerJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()], 'c.group_score_system_id=cgss.id')
                ->leftJoin(['a' => BaseAnnouncement::tableName()], 'j.announcement_id = a.id')
                ->select([
                    'j.id as jobId',
                    'j.name as jobName',
                    'j.city_id as cityId',
                    'j.amount',
                    'j.major_id as majorId',
                    'j.welfare_tag as welfareTag',
                    'j.refresh_date as refreshDate',
                    'a.id as announcementId',
                    'a.title as announcementName',
                    'c.full_name as companyName',
                ])
                ->where([
                    'j.status'          => BaseJob::STATUS_ONLINE,
                    'j.job_category_id' => self::BOSHIHOU_JOB_CATEGORY_IDS,
                ])
                ->andWhere([
                    'and',
                    [
                        '>=',
                        'j.refresh_time',
                        date('Y-m-d H:i:s', time() - 30 * 24 * 3600),
                    ],
                    [
                        '>=',
                        'm.last_active_time',
                        date('Y-m-d H:i:s', time() - 30 * 24 * 3600),
                    ],
                ])
                ->andWhere([
                    'or',
                    [
                        'j.delivery_type' => BaseJob::DELIVERY_TYPE_INSIDE,
                    ],
                    [
                        'j.delivery_type' => BaseJob::DELIVERY_TYPE_UP_ANNOUNCEMENT,
                        'a.delivery_type' => BaseAnnouncement::DELIVERY_TYPE_INSIDE,
                    ],
                ])
                ->orderBy('m.last_active_time desc,j.refresh_time desc,j.id desc')
                ->groupBy('c.id')
                ->limit(12)
                ->asArray()
                ->all();
            foreach ($onlineList as &$item) {
                $item['welfareTag']                 = $item['welfareTag'] ? implode('，',
                    BaseWelfareLabel::getWelfareLabelNameList($item['welfareTag'])) : '';
                $item['refreshDate']                = TimeHelper::formatDateByYear($item['refreshDate'], '.');
                $item['cityText']                   = BaseArea::getAreaName($item['cityId']);
                $item['majorId']                    = BaseMajor::getAllMajorName(explode(',', $item['majorId']));
                $item['wage']                       = BaseJob::getWageText($item['jobId']);
                $item['jobUrl']                     = UrlHelper::createPcJobDetailPath($item['jobId']);
                $item['chatUrl']                    = '';
                $item['isCompanyMember15DayActive'] = $this->getCompanyMemberActive($item['jobId']);
            }
            Cache::set(Cache::BOSHIHOU_HOME_SELECTED_JOB_ONLINE, json_encode($onlineList),
                self::BOSHIHOU_HOME_SELECTED_JOB_ONLINE_EXPIRE);
        }

        $result['onlineList']['list'] = $onlineList;
        $result['moreUrl']            = UrlHelper::getBoShiHouZhiWei();

        // 整理展示tab
        if ($result['isLogin'] && $result['resumeStepBool']) {
            $result['title'][] = [
                'tab'           => 'recommendList',
                'name'          => '为你推荐',
                'activeIcon'    => 'https://img.gaoxiaojob.com/uploads/boshihou/selectedJob/recommendListY.png',
                'activeNotIcon' => 'https://img.gaoxiaojob.com/uploads/boshihou/selectedJob/recommendListN.png',
            ];
        } else {
            $result['title'][] = [
                'tab'           => 'newList',
                'name'          => '最新职位',
                'activeIcon'    => 'https://img.gaoxiaojob.com/uploads/boshihou/selectedJob/newListY.png',
                'activeNotIcon' => 'https://img.gaoxiaojob.com/uploads/boshihou/selectedJob/newListN.png',
            ];
        }

        $result['title'][] = [
            'tab'           => 'onlineList',
            'name'          => '在线机会',
            'activeIcon'    => 'https://img.gaoxiaojob.com/uploads/boshihou/selectedJob/onlineListY.png',
            'activeNotIcon' => 'https://img.gaoxiaojob.com/uploads/boshihou/selectedJob/onlineListN.png',
        ];

        return $result;
    }

    /**
     * 品牌单位
     */
    private function getBrandCompany()
    {
        //缓存一下
        $result = Cache::get(Cache::BOSHIHOU_HOME_BRAND_COMPANY);
        if ($result && !$this->isCache) {
            return json_decode($result, true);
        }
        $result = [];

        //先获取广告位数据
        $showcaseList = ShowcaseService::getHomeM1();
        foreach ($showcaseList as $key => $item) {
            //获取关联单位信息
            $companyInfo = BaseCompany::findOne($item['companyId']);
            if ($companyInfo) {
                $result['list'][] = [
                    'showcase'                        => $item,
                    'id'                              => $companyInfo->id,
                    'companyName'                     => $companyInfo->full_name,
                    'companyLogo'                     => BaseCompany::getCompanyLogo($companyInfo->logo_url),
                    'companyUrl'                      => UrlHelper::createPcCompanyDetailPath($companyInfo->id),
                    'companyType'                     => BaseDictionary::getCompanyTypeName($companyInfo->type),
                    'companyNature'                   => BaseDictionary::getCompanyNatureName($companyInfo->nature),
                    //单位城市
                    'companyCity'                     => BaseArea::getAreaName($companyInfo->city_id),
                    //单位标签
                    'companyLabel'                    => BaseCompany::getCompanyLabelName($companyInfo->label_ids),
                    //介绍
                    'companyDesc'                     => $item['secondTitle'] ?: $companyInfo->introduce,
                    //副标题
                    'companySub'                      => $item['subTitle'],
                    //背景图
                    'companyImageBg'                  => $item['image'] ?: self::COMPANY_BRAND_DEFAULT_BG_IMG[$key],
                    //获取单位在招公告数量
                    'companyOnLineAnnouncementAmount' => BaseAnnouncement::getCompanyOnLineAnnouncementAmount($companyInfo->id),
                    //获取单位在招职位数量
                    'companyOnLineJobAmount'          => BaseJob::getCompanyJobAmount($companyInfo->id),
                    'detailUrl'                       => $item['url'] ?: UrlHelper::createPcCompanyDetailPath($companyInfo->id),
                ];
            }
        }

        $showcaseListCount = count($showcaseList);
        if ($showcaseListCount < 4) {
            $notCompanyIds = array_column($result['list'], 'id');
            //补足4条
            $CompanyList = BaseJob::find()
                ->alias('j')
                ->innerJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
                ->innerJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()], 'c.group_score_system_id=cgss.id')
                ->leftJoin(['a' => BaseAnnouncement::tableName()], 'j.announcement_id = a.id')
                ->select([
                    'c.id as companyId',
                    'c.full_name as companyName',
                    'c.logo_url as companyLogo',
                    'c.city_id as companyCityId',
                    'c.type as companyType',
                    'c.nature as companyNature',
                    'c.label_ids as companyLabelIds',
                    'c.introduce as companyIntroduce',
                ])
                ->where([
                    'c.is_cooperation'  => BaseCompany::COOPERATIVE_UNIT_YES,
                    'j.status'          => BaseJob::STATUS_ONLINE,
                    'j.job_category_id' => self::BOSHIHOU_JOB_CATEGORY_ID,
                ])
                ->andFilterWhere([
                    'not in',
                    'c.id',
                    $notCompanyIds,
                ])
                ->orderBy('cgss.score desc,c.id desc')
                ->limit(4 - $showcaseListCount)
                ->groupBy('c.id')
                ->asArray()
                ->all();
            foreach ($CompanyList as $key => $item) {
                $result['list'][] = [
                    'id'                              => $item['companyId'],
                    'companyName'                     => $item['companyName'],
                    'companyLogo'                     => BaseCompany::getCompanyLogo($item['companyLogo']),
                    'companyUrl'                      => UrlHelper::createPcCompanyDetailPath($item['companyId']),
                    'companyType'                     => BaseDictionary::getCompanyTypeName($item['companyType']),
                    'companyNature'                   => BaseDictionary::getCompanyNatureName($item['companyNature']),
                    'companyCity'                     => BaseArea::getAreaName($item['companyCityId']),
                    //单位标签
                    'companyLabel'                    => BaseCompany::getCompanyLabelName($item['companyLabelIds']),
                    //介绍
                    'companyDesc'                     => $item['companyIntroduce'],
                    //副标题
                    'companySub'                      => '',
                    //背景图
                    'companyImageBg'                  => self::COMPANY_BRAND_DEFAULT_BG_IMG[$key + $showcaseListCount],
                    //获取单位在招公告数量
                    'companyOnLineAnnouncementAmount' => BaseAnnouncement::getCompanyOnLineAnnouncementAmount($item['companyId']),
                    //获取单位在招职位数量
                    'companyOnLineJobAmount'          => BaseJob::getCompanyJobAmount($item['companyId']),
                    'detailUrl'                       => $item['url'] ?: UrlHelper::createPcCompanyDetailPath($item['companyId']),
                ];
            }
        }

        $result['moreUrl'] = UrlHelper::getBoShiHouDanWei();

        Cache::set(Cache::BOSHIHOU_HOME_BRAND_COMPANY, json_encode($result), self::BOSHIHOU_HOME_BRAND_COMPANY_EXPIRE);

        return $result;
    }

    /**
     * 博后活动
     */
    private function getBoHouActivity()
    {
        //缓存一下
        $result = Cache::get(Cache::BOSHIHOU_HOME_BOHOU_ACTIVITY);
        if ($result && !$this->isCache) {
            return json_decode($result, true);
        }
        $list = BaseHwActivityPromotion::find()
            ->alias('hap')
            ->select([
                'ha.id as activityId',
                'ha.name as activityName',
                'ha.activity_status as activityStatus',
                'ha.detail_url as activityDetailUrl',
                'ha.sign_up_url as activitySignUpUrl',
                'ha.is_outside_url as isOutsideUrl',
                'hap.img_file_id as activityImgFileId',
            ])
            ->innerJoin(['ha' => BaseHwActivity::tableName()], 'hap.activity_id=ha.id')
            ->where(['hap.position_type' => BaseHwActivityPromotion::PROMOTION_POSITION_BOHOU_ACTIVITY])
            ->andWhere([
                'and',
                [
                    '<=',
                    'hap.start_date',
                    CUR_DATETIME,
                ],
                [
                    '>=',
                    'hap.end_date',
                    CUR_DATETIME,
                ],
            ])
            ->orderBy('hap.sort desc,ha.id desc')
            ->limit(4)
            ->asArray()
            ->all();
        foreach ($list as &$item) {
            $item['activityImgUrl']   = FileHelper::getFullPathById($item['activityImgFileId']);
            $item['activityDate']     = BaseHwActivity::getActivityDate($item['activityId']);
            $item['activityAreaText'] = BaseHwActivity::getAllAddressBySeries($item['activityId']);
            if ($item['isOutsideUrl'] == BaseHwActivity::IS_OUTSIDE_URL_YES) {
                $item['rel'] = 'nofollow';
            } else {
                $item['rel'] = '';
            }
        }

        $result = [
            'moreUrl' => UrlHelper::getBoShiHouHuoDong(),
            'list'    => $list,
        ];
        Cache::set(Cache::BOSHIHOU_HOME_BOHOU_ACTIVITY, json_encode($result),
            self::BOSHIHOU_HOME_BOHOU_ACTIVITY_EXPIRE);

        return $result;
    }

    /**
     * 职位联系人单位账号（即职位联系人账号）的活跃时间在近15天内的
     * 展示“绿点”标识
     */
    private function getCompanyMemberActive($jobId)
    {
        //获取职位联系人
        $contactInfo = BaseJobContact::findOne(['job_id' => $jobId]);
        //获取账号ID
        $memberId = BaseCompanyMemberInfo::findOneVal(['id' => $contactInfo->company_member_info_id], 'member_id');
        //获取账号信息
        $memberInfo = BaseMember::findOne(['id' => $memberId]);
        //是否近15日活跃
        if ($memberInfo && $memberInfo->last_active_time && (time() - strtotime($memberInfo->last_active_time)) < 15 * 24 * 3600) {
            return true;
        }

        return false;
    }
}