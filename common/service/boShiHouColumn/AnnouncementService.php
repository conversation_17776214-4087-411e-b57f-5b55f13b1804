<?php
/**
 * create user：shannon
 * create time：2024/9/18 上午9:52
 */
namespace common\service\boShiHouColumn;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementAreaRelation;
use common\base\models\BaseAnnouncementBoshihou;
use common\base\models\BaseAnnouncementExtra;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseArticleClickLog;
use common\base\models\BaseArticleColumn;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyFeatureTag;
use common\base\models\BaseCompanyFeatureTagRelation;
use common\base\models\BaseCompanyGroupRelation;
use common\base\models\BaseCompanyGroupScoreSystem;
use common\base\models\BaseJob;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobBoshihou;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseMajor;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEducation;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use common\libs\Cache;
use Yii;
use yii\db\Expression;

/**
 * 处理博士后公告&职位页面的公告相关业务逻辑
 */
class AnnouncementService extends BaseService
{

    /** 参数 */
    private $parmas;

    /** 详细性能统计数据 */
    private $lastDetailStats = null;

    /**
     * 初始化
     * @return $this
     */
    public function init()
    {
        //先获取参数
        $this->parmas  = Yii::$app->request->get();
        $this->isCache = (bool)Yii::$app->request->get('isCache', 0);
        unset($this->parmas['isCache']);
        if (!isset($this->parmas['type']) || $this->parmas['type'] != self::ANNOUNCEMENT_TYPE) {
            $this->parmas['type'] = self::ANNOUNCEMENT_TYPE;
        }

        return $this;
    }

    /**
     * 执行业务逻辑
     */
    public function run()
    {
        //初始化
        $this->init();
        //获取SEO
        $searchData = (new SeoAnnouncementJobService())->setCache($this->isCache)
            ->getSeoSearchRun($this->parmas);

        return [
            'showcaseA1' => ShowcaseService::getAnnouncementAndJobA1(),
            'showcaseB1' => ShowcaseService::getAnnouncementAndJobB1(),
            'searchData' => $searchData,
            'listData'   => $this->getList($searchData['currentSearchParams']),
            'ranking'    => self::getRankingList($this->isCache),
        ];
    }

    /**
     * 公告列表数据
     * @param $params
     * @throws \Exception
     */
    public function getList($params)
    {
        // 开始完整的性能监控
        $methodStartTime = microtime(true);

        // 初始化所有阶段变量
        $stage1Time       = 0;
        $stage2Time       = 0;
        $stage3Time       = 0;
        $stage4Time       = 0;
        $stage5Time       = 0;
        $stage6Time       = 0;
        $keywordQueryTime = 0;

        \common\helpers\DebugHelper::postdocAnnouncementRecommend([
            'action'     => 'start',
            'start_time' => date('Y-m-d H:i:s'),
            'params'     => $params,
        ]);

        try {
            // 1. 参数处理和缓存检查阶段
            $stage1StartTime = microtime(true);

            $page = isset($params['page']) && $params['page'] ? $params['page'] : 1;
            unset($params['page']);
            if (empty($params) && $page > self::AJ_LIMIT_MAX_PAGE) {
                $page = self::AJ_LIMIT_MAX_PAGE;
            }
            if ($page <= self::AJ_LIMIT_MAX_PAGE && empty($params)) {
                //第一页打一个缓存
                $list = Cache::get(Cache::BOSHIHOU_ANNOUNCEMENT_LIST_PAGE_ONE . ':' . $page);
                if ($list && !$this->isCache) {
                    $stage1EndTime = microtime(true);
                    $stage1Time    = round(($stage1EndTime - $stage1StartTime) * 1000, 2);

                    // 计算总执行时间
                    $methodEndTime      = microtime(true);
                    $totalExecutionTime = round(($methodEndTime - $methodStartTime) * 1000, 2);

                    // 解析缓存数据获取数据量
                    $cacheData = json_decode($list, true);
                    $dataCount = isset($cacheData['list']) ? count($cacheData['list']) : 0;

                    // 记录缓存命中的完整性能数据
                    \common\helpers\DebugHelper::postdocAnnouncementRecommend([
                        'action'                  => 'complete',
                        'query_params'            => array_merge(['page' => $page], $params),
                        'total_data_count'        => $dataCount,
                        'total_execution_time_ms' => $totalExecutionTime,
                        'stage_times_ms'          => [
                            'stage1_params_cache_ms' => $stage1Time,
                        ],
                        'stage_percentages'       => [
                            'stage1_params_cache_ms' => 100.0,
                        ],
                        'data_source'             => '缓存命中',
                        'end_time'                => date('Y-m-d H:i:s'),
                    ]);

                    return json_decode($list, true);
                }
            }

            $stage1EndTime = microtime(true);
            $stage1Time    = round(($stage1EndTime - $stage1StartTime) * 1000, 2);

            // 2. 查询构建阶段
            $stage2StartTime = microtime(true);

            $query = BaseAnnouncementBoshihou::find()
                ->alias('ab')
                ->select([
                    'a.id as announcementId',
                    'a.title as announcementName',
                    'a.sub_title as announcementSubName',
                    'a.highlights_describe as announcementHighlightsDescribe',
                    'a.all_job_amount as announcementAllJobAmount',
                    'a.period_date as periodDate',
                    'a.online_job_amount as onlineJobAmount',
                    'art.refresh_time as refreshTime',
                    'art.status',
                    'c.id as companyId',
                    'c.full_name as companyName',
                    'c.logo_url as companyLogo',
                    'ae.is_pi as isPi',
                ])
                ->innerJoin(['a' => BaseAnnouncement::tableName()], 'a.id = ab.announcement_id')
                ->innerJoin(['c' => BaseCompany::tableName()], 'a.company_id = c.id')
                ->innerJoin(['art' => BaseArticle::tableName()], 'a.article_id = art.id')
                ->innerJoin(['ae' => BaseAnnouncementExtra::tableName()], 'ae.announcement_id = a.id');
            //关键字搜索
            if (isset($params['keyword']) && $params['keyword'] && trim($params['keyword'])) {
                // 关键词子查询阶段监控
                $keywordQueryStartTime = microtime(true);

                try {
                    // 设置查询超时时间（10秒）
                    $keywordAnnouncementIds = BaseJobBoshihou::find()
                        ->alias('jb')
                        ->select('j.announcement_id')
                        ->innerJoin(['j' => BaseJob::tableName()], 'jb.job_id = j.id')
                        ->andWhere([
                            '>',
                            'j.announcement_id',
                            0,
                        ])
                        ->andWhere([
                            'like',
                            'j.name',
                            $params['keyword'],
                        ])
                        ->limit(1000) // 限制结果数量，避免过多数据
                        ->asArray()
                        ->column();

                    $keywordQueryEndTime = microtime(true);
                    $keywordQueryTime    = round(($keywordQueryEndTime - $keywordQueryStartTime) * 1000, 2);

                    // 如果关键词查询超过10秒，记录警告
                    if ($keywordQueryTime > 10000) {
                        \common\helpers\DebugHelper::postdocAnnouncementRecommend([
                            'action'        => 'warning',
                            'message'       => '关键词查询超时',
                            'keyword'       => $params['keyword'],
                            'query_time_ms' => $keywordQueryTime,
                            'result_count'  => count($keywordAnnouncementIds),
                        ]);
                    }
                } catch (\Exception $e) {
                    $keywordQueryEndTime = microtime(true);
                    $keywordQueryTime    = round(($keywordQueryEndTime - $keywordQueryStartTime) * 1000, 2);

                    // 关键词查询失败，记录错误但不中断整个流程
                    \common\helpers\DebugHelper::postdocAnnouncementRecommend([
                        'action'        => 'keyword_error',
                        'error_message' => $e->getMessage(),
                        'keyword'       => $params['keyword'],
                        'query_time_ms' => $keywordQueryTime,
                    ]);

                    // 设置空结果，继续执行
                    $keywordAnnouncementIds = [];
                }
                //公告名称/单位名称
                $query->andWhere([
                    'or',
                    [
                        'like',
                        'a.title',
                        $params['keyword'],
                    ],
                    [
                        'like',
                        'c.full_name',
                        $params['keyword'],
                    ],
                    [
                        'in',
                        'a.id',
                        $keywordAnnouncementIds,
                    ],
                ]);
            }
            //地区
            if (isset($params['areaId']) && $params['areaId']) {
                $query->leftJoin(['aar' => BaseAnnouncementAreaRelation::tableName()], 'a.id = aar.announcement_id');
                $query->andWhere(['aar.area_id' => $params['areaId']]);
            }
            //单位类型
            if (isset($params['companyType']) && $params['companyType']) {
                $query->andWhere(['c.type' => explode('_', $params['companyType'])]);
            }
            //领域
            if (isset($params['specialtyId']) && $params['specialtyId']) {
                $query->leftJoin(['jmr' => BaseJobMajorRelation::tableName()], 'jmr.announcement_id = a.id');
                $majorIds = BaseMajor::find()
                    ->select('id')
                    ->where([
                        'major_specialty_id' => $params['specialtyId'],
                        'status'             => BaseMajor::STATUS_ACTIVE,
                    ])
                    ->column();
                $query->andWhere(['jmr.major_id' => $majorIds]);
            }
            //专业
            if (isset($params['majorId']) && $params['majorId']) {
                $query->leftJoin(['jmr' => BaseJobMajorRelation::tableName()], 'jmr.announcement_id = a.id');
                $query->andWhere(['jmr.major_id' => $params['majorId']]);
            }
            //发布时间筛选
            if (isset($params['refreshTime']) && $params['refreshTime']) {
                $time = $this->parseTime($params['refreshTime']);
                $query->andWhere([
                    '>=',
                    'art.refresh_time',
                    $time['startTime'],
                ])
                    ->andWhere([
                        '<=',
                        'art.refresh_time',
                        $time['endTime'],
                    ]);
            }
            //是否PI直招
            if (isset($params['isPi']) && $params['isPi'] == BaseCompanyFeatureTag::PI_TAG_ID) {
                $query->andWhere(['ae.is_pi' => BaseAnnouncementExtra::IS_PI_YES]);
            }

            $stage2EndTime = microtime(true);
            $stage2Time    = round(($stage2EndTime - $stage2StartTime) * 1000, 2);

            // 3. 置顶查询执行阶段
            $stage3StartTime = microtime(true);

            //获取需要置顶的职位3条
            $topQuery = clone $query;
            $topList  = $topQuery->addSelect(new Expression("'0' as isRecommend"))
                ->addSelect(new Expression("'1' as isTop"))
                ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'aa.article_id = art.id')
                ->andWhere([
                    'aa.type'    => BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP,
                    'art.status' => BaseArticle::STATUS_ONLINE,
                ])
                ->orderBy('aa.sort_time desc')
                ->groupBy('a.id')
                //->limit(3)
                ->asArray()
                ->all();

            $stage3EndTime = microtime(true);
            $stage3Time    = round(($stage3EndTime - $stage3StartTime) * 1000, 2);

            // 4. 主查询执行阶段
            $stage4StartTime = microtime(true);

            //第一组3个置顶
            $top3 = array_slice($topList, 0, 3);
            //第一组剩下置顶
            $top = array_slice($topList, 3);
            //指定的职位IDs
            $topAnnouncementIds = array_column($top3, 'announcementId');
            //序号列表
            $numberQuery = clone $query;
            $numberList  = $numberQuery->leftJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()],
                'c.group_score_system_id = cgss.id')
                ->andWhere([
                    'not in',
                    'a.id',
                    $topAnnouncementIds,
                ])
                ->andWhere([
                    'a.status' => [
                        BaseArticle::STATUS_ONLINE,
                        BaseArticle::STATUS_OFFLINE,
                    ],
                ])
                ->orderBy('art.status asc,art.refresh_date desc,ae.is_boshihou_pay asc,cgss.score desc,a.id desc')
                ->groupBy('a.id')
                ->asArray()
                ->all();
            $i           = 1;
            foreach ($numberList as &$item) {
                $item['number'] = $i;
                $i++;
            }
            $announcementIdNumberList = array_column($numberList, 'number', 'announcementId');
            //获取第二组数据
            //2.a 规则列表
            $announcementQueryA = clone $query;
            $announcementListA  = $announcementQueryA->addSelect([
                'doctorNumber' => BaseJobApplyRecord::find()
                    ->alias('jar')
                    ->select('count(jar.id)')
                    ->leftJoin(['r' => BaseResume::tableName()], 'r.id= jar.resume_id')
                    ->andWhere([
                        'r.top_education_code' => BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                    ])
                    ->andWhere([
                        '>',
                        'jar.add_time',
                        date('Y-m-d H:i:s', time() - 30 * 24 * 3600),
                    ])
                    ->andWhere('jar.announcement_id=a.id'),
            ])
                ->andWhere(['art.status' => BaseArticle::STATUS_ONLINE])
                ->andWhere([
                    'not in',
                    'a.id',
                    $topAnnouncementIds,
                ])
                //发布时间非30天内 首发：发布时间=初始发布时间
                ->andWhere([
                    '<=',
                    'art.refresh_time',
                    date('Y-m-d H:i:s', time() - 30 * 24 * 3600),
                ])
                ->andWhere(['ae.is_boshihou_pay' => BaseAnnouncementExtra::IS_BOSHIHOU_PAY_YES])
                ->andWhere('art.first_release_time=art.refresh_time')
                ->having([
                    'doctorNumber' => 0,
                ])
                ->groupBy('a.id')
                ->orderBy('art.refresh_time desc,a.id desc')
                ->asArray()
                ->all();

            $companyIdsA      = [];
            $announcementIdsA = [];
            foreach ($announcementListA as $itemA) {
                //同一单位，最多取对外发布时间最新的2条公告
                if (!isset($companyIdsA[$itemA['companyId']]) || count($companyIdsA[$itemA['companyId']]) < 2) {
                    $announcementIdsA[]                 = $itemA['announcementId'];
                    $companyIdsA[$itemA['companyId']][] = $itemA['announcementId'];
                }
            }

            //2.b 规则列表
            $announcementQueryB = clone $query;
            $announcementListB  = $announcementQueryB->andWhere(['art.status' => BaseArticle::STATUS_ONLINE])
                ->leftJoin(['cgr' => BaseCompanyGroupRelation::tableName()], 'c.id=cgr.company_id')
                ->leftJoin(['cftr' => BaseCompanyFeatureTagRelation::tableName()], 'c.id=cftr.company_id')
                ->andWhere([
                    'not in',
                    'a.id',
                    $topAnnouncementIds,
                ])
                ->andWhere([
                    'not in',
                    'a.id',
                    $announcementIdsA,
                ])
                ->andWhere([
                    'cftr.feature_tag_id' => BaseCompanyFeatureTag::PI_TAG_ID,
                    'cgr.group_id'        => self::COMPANY_GROUP_ID,
                ])
                ->groupBy('a.id')
                ->asArray()
                ->all();
            $announcementIdsB   = array_column($announcementListB, 'announcementId');
            //2.c 规则列表
            $announcementQueryC = clone $query;
            $announcementListC  = $announcementQueryC->leftJoin(['aa' => BaseArticleAttribute::tableName()],
                'aa.article_id = art.id')
                ->andWhere(['art.status' => BaseArticle::STATUS_ONLINE])
                ->andWhere([
                    'not in',
                    'a.id',
                    $topAnnouncementIds,
                ])
                ->andWhere([
                    'not in',
                    'a.id',
                    $announcementIdsA,
                ])
                ->andWhere([
                    'not in',
                    'a.id',
                    $announcementIdsB,
                ])
                ->andWhere([
                    'aa.type' => BaseArticleAttribute::ATTRIBUTE_DOCTOR_PUSH,
                ])
                ->groupBy('a.id')
                ->asArray()
                ->all();
            $announcementIdsC   = array_column($announcementListC, 'announcementId');

            //第二组职位集合
            $announcementIdRecommend = array_merge($announcementIdsA, $announcementIdsB, $announcementIdsC);
            // 按$JobIdRecommend获取职位并排序
            // 排序规则：
            //  1、按发布日期倒序；
            //  2、同一发布日：
            //  （1）“加推”属性 > 含“PI”单位特色标签的单位的职位 > 其他符合条件的职位；
            //  （2）其次，按职位所属单位群组权重分值倒序展示；单位群组权重分值相同，再按职位ID倒序展示。
            $recommendQuery    = clone $query;
            $recommendListOld  = $recommendQuery->addSelect(['recommendType' => new Expression("CASE WHEN aa.type=22 THEN 300 WHEN cftr.feature_tag_id=1 THEN 200 ELSE 100 END")])
                ->leftJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()], 'c.group_score_system_id = cgss.id')
                ->leftJoin(['cftr' => BaseCompanyFeatureTagRelation::tableName()],
                    'c.id=cftr.company_id and cftr.feature_tag_id=1')
                ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'aa.article_id = art.id and aa.type=22')
                ->addSelect(new yii\db\Expression("'0' as isTop"))
                ->addSelect(new yii\db\Expression("'1' as isRecommend"))
                ->andWhere(['a.id' => $announcementIdRecommend])
                ->orderBy('art.refresh_date desc,recommendType desc,cgss.score desc,a.id desc')
                ->asArray()
                ->all();
            $newAnnouncementId = [];
            $recommendList     = [];
            foreach ($recommendListOld as $recommendItem) {
                if (in_array($recommendItem['announcementId'], $newAnnouncementId)) {
                    continue;
                }
                $recommendItem['number'] = $announcementIdNumberList[$recommendItem['announcementId']];
                $recommendList[]         = $recommendItem;
                $newAnnouncementId[]     = $recommendItem['announcementId'];
            }

            //3、第三组普通列表
            $generalQuery = clone $query;
            $generalQuery->leftJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()],
                'c.group_score_system_id = cgss.id')
                ->addSelect(new yii\db\Expression("'0' as isTop"))
                ->addSelect(new yii\db\Expression("'0' as isRecommend"))
                ->andWhere([
                    'not in',
                    'a.id',
                    $topAnnouncementIds,
                ])
                ->andWhere([
                    'not in',
                    'a.id',
                    $announcementIdRecommend,
                ])
                ->andWhere([
                    'art.status' => [
                        BaseArticle::STATUS_ONLINE,
                        BaseArticle::STATUS_OFFLINE,
                    ],
                ])
                ->orderBy('art.status asc,art.refresh_date desc,ae.is_boshihou_pay asc,cgss.score desc,a.id desc')
                ->groupBy('a.id');
            if (empty($params)) {
                $top3Count          = count($top3);
                $recommendListCount = count($recommendList);
                $generalQuery->limit((self::AJ_LIMIT_MAX_PAGE * 20) - $top3Count - $recommendListCount);
            }
            $generalList = $generalQuery->asArray()
                ->all();
            foreach ($generalList as &$generalItem) {
                $generalItem['number'] = $announcementIdNumberList[$generalItem['announcementId']];
            }

            $stage4EndTime = microtime(true);
            $stage4Time    = round(($stage4EndTime - $stage4StartTime) * 1000, 2);

            // 5. 数据组装和分页阶段
            $stage5StartTime = microtime(true);
            $listOriginal    = [];
            $listKey         = 0;
            $listItemEmpty   = [];
            // 公告穿插规则：
            // 公告穿插规则：
            // （1）3急-  3普-2荐-  3普-2荐…；
            //  任一组别条数不足时，直接接入下一组别，如1普-荐-荐-荐-荐-荐…
            // （2）优推公告组的公告，其穿插排序不得低于其自然排序（即作为普通公告时的排序序号）；
            //  若低于，则该条优推公告，按自然排序生效（此时不显示“荐”标签&【优推公告卡片样式】）；
            $generalNumber      = 0;
            $generalNumberMax   = 3;
            $recommendNumber    = 0;
            $recommendNumberMax = 2;
            $interleavedSort    = 1;
            $generalListSort    = [];
            $recommendListSort  = [];
            while (count($generalList) > 0 || count($recommendList) > 0) {
                //1、先处理$generalList=3
                if (count($generalList) > 0) {
                    //在$generalList中在头部取出三条普通职位放入$list
                    for ($i = 0; $i < $generalNumberMax; $i++) {
                        if (count($generalList) > 0) {
                            $generalInfo = array_shift($generalList);
                            if (isset($generalInfo['interleavedSortOriginal']) && $generalInfo['interleavedSortOriginal'] > 0) {
                                $generalInfo['interleavedSort'] = $generalInfo['interleavedSortOriginal'];
                            } else {
                                $generalInfo['interleavedSort'] = $interleavedSort;
                                $interleavedSort++;
                            }
                            $generalInfo['listKey'] = $listKey;
                            $listKey++;
                            $generalListSort[] = $generalInfo;
                        }
                    }
                }
                //2、再处理$recommendList=2
                if (count($recommendList) > 0) {
                    //在$recommendList中取出两条优推职位放入$list
                    for ($j = 0; $j < $recommendNumberMax; $j++) {
                        if (count($recommendList) > 0) {
                            $recommendInfo = array_shift($recommendList);
                            if ($recommendInfo['number'] < $interleavedSort) {
                                //变成普通职位
                                $recommendInfo['isRecommend']             = '2';
                                $recommendInfo['isRecommendOld']          = '1';
                                $recommendInfo['interleavedSortOriginal'] = $interleavedSort;
                                $generalListSort[]                        = $recommendInfo;
                            } else {
                                $recommendInfo['interleavedSort'] = $interleavedSort;
                                $recommendInfo['listKey']         = $listKey;
                                $recommendListSort[]              = $recommendInfo;
                            }
                            $listKey++;
                            $interleavedSort++;
                        }
                    }
                }
            }
            //$generalListSort二维数组按照number排序
            usort($generalListSort, function ($a, $b) {
                return $a['number'] - $b['number'];
            });
            //重置索引
            $generalListSort = array_values($generalListSort);
            while (count($generalListSort) > 0 || count($recommendListSort) > 0) {
                //1、先处理$generalList=3
                if (count($generalListSort) > 0) {
                    //在$generalList中在头部取出三条普通职位放入$list
                    for ($i = 0; $i < $generalNumberMax; $i++) {
                        if (count($generalListSort) > 0) {
                            $generalInfoSort = array_shift($generalListSort);
                            $listOriginal[]  = $generalInfoSort;
                        }
                    }
                }
                //2、再处理$recommendList=2
                if (count($recommendListSort) > 0) {
                    //在$recommendList中取出两条优推职位放入$list
                    for ($j = 0; $j < $recommendNumberMax; $j++) {
                        if (count($recommendListSort) > 0) {
                            $recommendInfoSort = array_shift($recommendListSort);
                            $listOriginal[]    = $recommendInfoSort;
                        }
                    }
                }
            }
            //这时候将三条置顶职位放到第一位
            $listOriginal = array_merge($top3, $listOriginal);
            $count        = count($listOriginal);
            $pageSize     = 20;
            $pageCount    = ceil($count / $pageSize);
            if ($count > 0 && $page > $pageCount) {
                $this->redirect404();
            }
            $offset = ($page - 1) * $pageSize;
            $list   = array_slice($listOriginal, $offset, $pageSize);

            $stage5EndTime = microtime(true);
            $stage5Time    = round(($stage5EndTime - $stage5StartTime) * 1000, 2);

            // 6. 数据处理阶段
            $stage6StartTime = microtime(true);

            // 优化数据处理 - 使用批量查询替代循环查询
            $list = $this->optimizeAnnouncementDataProcessing($list);

            $stage6EndTime = microtime(true);
            $stage6Time    = round(($stage6EndTime - $stage6StartTime) * 1000, 2);

            $result = [
                'list'      => $list,
                'count'     => $count,
                'pageCount' => $pageCount,
            ];
            //第一页 无参数 允许缓存 则缓存
            if ($page <= self::AJ_LIMIT_MAX_PAGE && empty($params)) {
                //缓存5分钟
                Cache::set(Cache::BOSHIHOU_ANNOUNCEMENT_LIST_PAGE_ONE . ':' . $page, json_encode($result), 3600);
            }

            // 结束完整的性能监控
            $methodEndTime      = microtime(true);
            $totalExecutionTime = round(($methodEndTime - $methodStartTime) * 1000, 2);

            // 收集所有阶段的性能数据
            $allStageStats = [];

            // 添加各阶段时间（如果存在）
            if (isset($stage1Time)) {
                $allStageStats['stage1_params_cache_ms'] = $stage1Time;
            }
            if (isset($stage2Time)) {
                $allStageStats['stage2_query_build_ms'] = $stage2Time;
            }
            if (isset($keywordQueryTime)) {
                $allStageStats['keyword_subquery_ms'] = $keywordQueryTime;
            }
            if (isset($stage3Time)) {
                $allStageStats['stage3_top_query_ms'] = $stage3Time;
            }
            if (isset($stage4Time)) {
                $allStageStats['stage4_main_query_ms'] = $stage4Time;
            }
            if (isset($stage5Time)) {
                $allStageStats['stage5_data_assembly_ms'] = $stage5Time;
            }
            if (isset($stage6Time)) {
                $allStageStats['stage6_data_processing_ms'] = $stage6Time;
            }

            // 计算各阶段占比
            $stagePercentages = [];
            foreach ($allStageStats as $stage => $time) {
                $stagePercentages[$stage] = round(($time / $totalExecutionTime) * 100, 1);
            }

            // 准备完整的监控数据（包含查询参数、全量性能分解、数据处理详情）
            $completeMonitoringData = [
                'action'                  => 'complete',
                'query_params'            => $params,
                // 查询参数
                'total_data_count'        => count($list),
                'total_execution_time_ms' => $totalExecutionTime,
                'stage_times_ms'          => $allStageStats,
                'stage_percentages'       => $stagePercentages,
                'end_time'                => date('Y-m-d H:i:s'),
            ];

            // 如果有详细的数据处理统计，也加入到完整监控数据中
            if ($this->lastDetailStats !== null) {
                $completeMonitoringData['data_processing_detail'] = $this->lastDetailStats;
            }

            // 一次性记录完整的性能监控数据（包含所有信息）
            \common\helpers\DebugHelper::postdocAnnouncementRecommend($completeMonitoringData);

            // 如果执行时间超过2000ms，记录到慢查询日志
            if ($totalExecutionTime > 2000) {
                // 找出主要瓶颈
                $mainBottleneck = '';
                $maxTime        = 0;
                foreach ($allStageStats as $stage => $time) {
                    if ($time > $maxTime) {
                        $maxTime        = $time;
                        $mainBottleneck = \common\helpers\DebugHelper::getStageNameInChinese($stage) . " ({$time}ms)";
                    }
                }

                \common\helpers\DebugHelper::slowQueryLog([
                    'type'              => 'announcement',
                    'execution_time'    => $totalExecutionTime,
                    'query_params'      => $originalParams,
                    'data_count'        => count($list),
                    'main_bottleneck'   => $mainBottleneck,
                    'stage_times'       => $allStageStats,
                    'stage_percentages' => $stagePercentages,
                ]);
            }

            return $result;
        } catch (\Exception $e) {
            // 如果出现异常，记录异常信息和已收集的性能数据
            $methodEndTime      = microtime(true);
            $totalExecutionTime = round(($methodEndTime - $methodStartTime) * 1000, 2);

            \common\helpers\DebugHelper::postdocAnnouncementRecommend([
                'action'                  => 'error',
                'error_message'           => $e->getMessage(),
                'error_file'              => $e->getFile(),
                'error_line'              => $e->getLine(),
                'total_execution_time_ms' => $totalExecutionTime,
                'query_params'            => $params,
                'end_time'                => date('Y-m-d H:i:s'),
            ]);

            // 重新抛出异常
            throw $e;
        }
    }

    /**
     * 优化博士后公告数据处理 - 使用批量查询替代循环查询
     * @param array $list
     * @return array
     */
    private function optimizeAnnouncementDataProcessing($list)
    {
        if (empty($list)) {
            return $list;
        }

        // 记录详细的性能监控
        $detailStats    = [];
        $totalStartTime = microtime(true);

        // 1. 基础数据格式化
        $formatStartTime = microtime(true);
        foreach ($list as &$item) {
            $item['refreshTime']     = TimeHelper::formatDateByYear($item['refreshTime'], '.');
            $item['periodDate']      = $item['periodDate'] == TimeHelper::ZERO_TIME ? '详见正文' : TimeHelper::formatDateByYear($item['periodDate'],
                '.');
            $item['announcementUrl'] = UrlHelper::createPcAnnouncementDetailPath($item['announcementId']);
        }
        $formatEndTime               = microtime(true);
        $detailStats['basic_format'] = [
            'operation' => '基础数据格式化',
            'count'     => count($list),
            'time_ms'   => round(($formatEndTime - $formatStartTime) * 1000, 2),
        ];

        // 2. 批量获取招聘人数
        $jobAmountStartTime              = microtime(true);
        $announcementIds                 = array_column($list, 'announcementId');
        $jobAmountMap                    = $this->batchGetAnnouncementJobAmount($announcementIds);
        $jobAmountEndTime                = microtime(true);
        $detailStats['job_amount_query'] = [
            'operation' => '招聘人数查询',
            'count'     => count($announcementIds),
            'time_ms'   => round(($jobAmountEndTime - $jobAmountStartTime) * 1000, 2),
        ];

        // 3. 批量获取专业信息（优化版）
        $majorStartTime             = microtime(true);
        $majorMap                   = $this->batchGetAnnouncementMajorText($announcementIds);
        $majorEndTime               = microtime(true);
        $detailStats['major_query'] = [
            'operation' => '专业信息查询',
            'count'     => count($announcementIds),
            'time_ms'   => round(($majorEndTime - $majorStartTime) * 1000, 2),
        ];

        // 4. 批量获取地区信息
        $areaStartTime             = microtime(true);
        $areaMap                   = $this->batchGetAnnouncementAreaText($announcementIds);
        $areaEndTime               = microtime(true);
        $detailStats['area_query'] = [
            'operation' => '地区信息查询',
            'count'     => count($announcementIds),
            'time_ms'   => round(($areaEndTime - $areaStartTime) * 1000, 2),
        ];

        // 5. 数据组装
        $assembleStartTime = microtime(true);
        foreach ($list as &$item) {
            $item['jobAmount'] = $jobAmountMap[$item['announcementId']] ?? 0;
            $item['majorText'] = $majorMap[$item['announcementId']] ?? '';
            $item['areaText']  = $areaMap[$item['announcementId']] ?? '';
        }
        $assembleEndTime              = microtime(true);
        $detailStats['data_assemble'] = [
            'operation' => '数据组装',
            'count'     => count($list),
            'time_ms'   => round(($assembleEndTime - $assembleStartTime) * 1000, 2),
        ];

        // 记录总体统计
        $totalEndTime = microtime(true);
        $totalTime    = round(($totalEndTime - $totalStartTime) * 1000, 2);

        // 将性能数据存储到类属性中，供主方法使用
        $this->lastDetailStats = [
            'data_count'    => count($list),
            'total_time_ms' => $totalTime,
            'detail_stats'  => $detailStats,
        ];

        return $list;
    }

    /**
     * 批量获取公告专业信息（优化版）
     * @param array $announcementIds
     * @return array
     */
    private function batchGetAnnouncementMajorText($announcementIds)
    {
        if (empty($announcementIds)) {
            return [];
        }

        // 1. 批量查询所有公告的专业关系
        $majorRelations = \common\base\models\BaseJobMajorRelation::find()
            ->select([
                'announcement_id',
                'major_id',
            ])
            ->where([
                'announcement_id' => $announcementIds,
                'level'           => 2,
            ])
            ->asArray()
            ->all();

        // 2. 按公告ID分组专业ID
        $announcementMajorMap = [];
        $allMajorIds          = [];
        foreach ($majorRelations as $relation) {
            $announcementId = $relation['announcement_id'];
            $majorId        = $relation['major_id'];

            if (!isset($announcementMajorMap[$announcementId])) {
                $announcementMajorMap[$announcementId] = [];
            }

            if (!in_array($majorId, $announcementMajorMap[$announcementId])) {
                $announcementMajorMap[$announcementId][] = $majorId;
            }

            if (!in_array($majorId, $allMajorIds)) {
                $allMajorIds[] = $majorId;
            }
        }

        // 3. 批量查询所有专业名称
        $majorNameMap = [];
        if (!empty($allMajorIds)) {
            $majors = \common\base\models\BaseMajor::find()
                ->select([
                    'id',
                    'name',
                ])
                ->where([
                    'id'     => $allMajorIds,
                    'status' => \common\base\models\BaseMajor::STATUS_ACTIVE,
                ])
                ->asArray()
                ->all();

            foreach ($majors as $major) {
                $majorNameMap[$major['id']] = $major['name'];
            }
        }

        // 4. 组装最终结果
        $result = [];
        foreach ($announcementIds as $announcementId) {
            $majorNames = [];
            if (isset($announcementMajorMap[$announcementId])) {
                foreach ($announcementMajorMap[$announcementId] as $majorId) {
                    if (isset($majorNameMap[$majorId])) {
                        $majorNames[] = $majorNameMap[$majorId];
                    }
                }
            }
            $result[$announcementId] = implode(',', $majorNames);
        }

        return $result;
    }

    /**
     * 批量获取公告地区信息（优化版）
     * @param array $announcementIds
     * @return array
     */
    private function batchGetAnnouncementAreaText($announcementIds)
    {
        if (empty($announcementIds)) {
            return [];
        }

        // 1. 批量查询所有公告的地区关系
        $areaRelations = \common\base\models\BaseAnnouncementAreaRelation::find()
            ->select([
                'announcement_id',
                'area_id',
            ])
            ->where(['announcement_id' => $announcementIds])
            ->asArray()
            ->all();

        // 2. 按公告ID分组地区ID
        $announcementAreaMap = [];
        $allAreaIds          = [];
        foreach ($areaRelations as $relation) {
            $announcementId = $relation['announcement_id'];
            $areaId         = $relation['area_id'];

            if (!isset($announcementAreaMap[$announcementId])) {
                $announcementAreaMap[$announcementId] = [];
            }

            if (!in_array($areaId, $announcementAreaMap[$announcementId])) {
                $announcementAreaMap[$announcementId][] = $areaId;
            }

            if (!in_array($areaId, $allAreaIds)) {
                $allAreaIds[] = $areaId;
            }
        }

        // 3. 批量查询所有地区名称
        $areaNameMap = [];
        if (!empty($allAreaIds)) {
            $areas = \common\base\models\BaseArea::find()
                ->select([
                    'id',
                    'name',
                ])
                ->where(['id' => $allAreaIds])
                ->asArray()
                ->all();

            foreach ($areas as $area) {
                $areaNameMap[$area['id']] = $area['name'];
            }
        }

        // 4. 组装最终结果
        $result = [];
        foreach ($announcementIds as $announcementId) {
            $areaNames = [];
            if (isset($announcementAreaMap[$announcementId])) {
                foreach ($announcementAreaMap[$announcementId] as $areaId) {
                    if (isset($areaNameMap[$areaId])) {
                        $areaNames[] = $areaNameMap[$areaId];
                    }
                }
            }
            $result[$announcementId] = implode(',', $areaNames);
        }

        return $result;
    }

    /**
     * 批量获取公告招聘人数（优化版）
     * @param array $announcementIds
     * @return array
     */
    private function batchGetAnnouncementJobAmount($announcementIds)
    {
        if (empty($announcementIds)) {
            return [];
        }

        // 使用聚合查询一次性获取所有公告的招聘人数
        $jobAmounts = \common\base\models\BaseJob::find()
            ->select([
                'announcement_id',
                //                'SUM(CASE WHEN amount > 0 AND amount != "若干" THEN CAST(amount AS UNSIGNED) ELSE 1 END) as total_amount',
                'CASE WHEN MAX(CASE WHEN amount = "若干" THEN 1 ELSE 0 END) = 1 THEN "若干" ELSE CAST(SUM(CAST(amount AS UNSIGNED)) AS CHAR) END AS total_amount',
            ])
            ->where([
                'announcement_id' => $announcementIds,
                'status'          => [
                    \common\base\models\BaseJob::STATUS_ONLINE,
                    \common\base\models\BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->groupBy('announcement_id')
            ->asArray()
            ->all();
        // 组装结果
        $result = [];
        foreach ($announcementIds as $announcementId) {
            $result[$announcementId] = 0; // 默认值
        }

        foreach ($jobAmounts as $jobAmount) {
            $result[$jobAmount['announcement_id']] = $jobAmount['total_amount'];
        }

        return $result;
    }

    /**
     * 公告列表数据
     * @param $params
     * @throws \Exception
     */
    private function getList123($params)
    {
        $page = isset($params['page']) && $params['page'] ? $params['page'] : 1;
        unset($params['page']);
        if ($page == 1 && empty($params)) {
            //第一页打一个缓存
            $list = Cache::get(Cache::BOSHIHOU_ANNOUNCEMENT_LIST_PAGE_ONE);
            if ($list && !$this->isCache) {
                return json_decode($list, true);
            }
        }
        $query = BaseAnnouncementBoshihou::find()
            ->alias('ab')
            ->select([
                'a.id as announcementId',
                'a.title as announcementName',
                'a.sub_title as announcementSubName',
                'a.highlights_describe as announcementHighlightsDescribe',
                'a.all_job_amount as announcementAllJobAmount',
                'a.period_date as periodDate',
                'a.online_job_amount as onlineJobAmount',
                'art.refresh_time as refreshTime',
                'art.status',
                'c.id as companyId',
                'c.full_name as companyName',
                'c.logo_url as companyLogo',
                'ae.is_pi as isPi',
            ])
            ->innerJoin(['a' => BaseAnnouncement::tableName()], 'a.id = ab.announcement_id')
            ->innerJoin(['c' => BaseCompany::tableName()], 'a.company_id = c.id')
            ->innerJoin(['art' => BaseArticle::tableName()], 'a.article_id = art.id')
            ->innerJoin(['ae' => BaseAnnouncementExtra::tableName()], 'ae.announcement_id = a.id');
        //关键字搜索
        if (isset($params['keyword']) && $params['keyword'] && trim($params['keyword'])) {
            //公告名称/单位名称
            $query->andWhere([
                'or',
                [
                    'like',
                    'a.title',
                    $params['keyword'],
                ],
                [
                    'like',
                    'c.full_name',
                    $params['keyword'],
                ],
            ]);
        }
        //地区
        if (isset($params['areaId']) && $params['areaId']) {
            $query->leftJoin(['aar' => BaseAnnouncementAreaRelation::tableName()], 'a.id = aar.announcement_id');
            $query->andWhere(['aar.area_id' => $params['areaId']]);
        }
        //单位类型
        if (isset($params['companyType']) && $params['companyType']) {
            $query->andWhere(['c.type' => explode('_', $params['companyType'])]);
        }
        //领域
        if (isset($params['specialtyId']) && $params['specialtyId']) {
            $query->leftJoin(['jmr' => BaseJobMajorRelation::tableName()], 'jmr.announcement_id = a.id');
            $majorIds = BaseMajor::find()
                ->select('id')
                ->where([
                    'major_specialty_id' => $params['specialtyId'],
                    'status'             => BaseMajor::STATUS_ACTIVE,
                ])
                ->column();
            $query->andWhere(['jmr.major_id' => $majorIds]);
        }
        //专业
        if (isset($params['majorId']) && $params['majorId']) {
            $query->leftJoin(['jmr' => BaseJobMajorRelation::tableName()], 'jmr.announcement_id = a.id');
            $query->andWhere(['jmr.major_id' => $params['majorId']]);
        }
        //发布时间筛选
        if (isset($params['refreshTime']) && $params['refreshTime']) {
            $time = $this->parseTime($params['refreshTime']);
            $query->andWhere([
                '>=',
                'art.refresh_time',
                $time['startTime'],
            ])
                ->andWhere([
                    '<=',
                    'art.refresh_time',
                    $time['endTime'],
                ]);
        }
        //是否PI直招
        if (isset($params['isPi']) && $params['isPi'] == BaseCompanyFeatureTag::PI_TAG_ID) {
            $query->andWhere(['ae.is_pi' => BaseAnnouncementExtra::IS_PI_YES]);
        }
        //获取需要置顶的职位3条
        $topQuery = clone $query;
        $topList  = $topQuery->addSelect(new Expression("'0' as isRecommend"))
            ->addSelect(new Expression("'1' as isTop"))
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'aa.article_id = art.id')
            ->andWhere([
                'aa.type'    => BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP,
                'art.status' => BaseArticle::STATUS_ONLINE,
            ])
            ->orderBy('aa.sort_time desc')
            ->groupBy('a.id')
            //->limit(3)
            ->asArray()
            ->all();

        //第一组3个置顶
        $top3 = array_splice($topList, 0, 3);
        //第一组剩下置顶
        $top = array_splice($topList, 3);
        //指定的职位IDs
        $topAnnouncementIds = array_column($top3, 'announcementId');
        //序号列表
        $numberQuery = clone $query;
        $numberList  = $numberQuery->leftJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()],
            'c.group_score_system_id = cgss.id')
            ->andWhere([
                'not in',
                'a.id',
                $topAnnouncementIds,
            ])
            ->andWhere([
                'a.status' => [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
            ])
            ->orderBy('art.status asc,art.refresh_date desc,ae.is_boshihou_pay asc,cgss.score desc,a.id desc')
            ->groupBy('a.id')
            ->asArray()
            ->all();
        $i           = 1;
        foreach ($numberList as &$item) {
            $item['number'] = $i;
            $i++;
        }
        $announcementIdNumberList = array_column($numberList, 'number', 'announcementId');
        //获取第二组数据
        //2.a 规则列表
        $announcementQueryA = clone $query;
        $announcementListA  = $announcementQueryA->addSelect([
            'doctorNumber' => BaseJobApplyRecord::find()
                ->alias('jar')
                ->select('count(jar.id)')
                ->leftJoin(['r' => BaseResume::tableName()], 'r.id= jar.resume_id')
                ->andWhere([
                    'r.top_education_code' => BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
                ])
                ->andWhere([
                    '>',
                    'jar.add_time',
                    date('Y-m-d H:i:s', time() - 30 * 24 * 3600),
                ])
                ->andWhere('jar.announcement_id=a.id'),
        ])
            ->andWhere(['art.status' => BaseArticle::STATUS_ONLINE])
            ->andWhere([
                'not in',
                'a.id',
                $topAnnouncementIds,
            ])
            //发布时间非30天内 首发：发布时间=初始发布时间
            ->andWhere([
                '<=',
                'art.refresh_time',
                date('Y-m-d H:i:s', time() - 30 * 24 * 3600),
            ])
            ->andWhere(['ae.is_boshihou_pay' => BaseAnnouncementExtra::IS_BOSHIHOU_PAY_YES])
            ->andWhere('art.first_release_time=art.refresh_time')
            ->having([
                'doctorNumber' => 0,
            ])
            ->groupBy('a.id')
            ->orderBy('art.refresh_time desc,a.id desc')
            ->asArray()
            ->all();

        $companyIdsA      = [];
        $announcementIdsA = [];
        foreach ($announcementListA as $itemA) {
            //同一单位，最多取对外发布时间最新的2条公告
            if (!isset($companyIdsA[$itemA['companyId']]) || count($companyIdsA[$itemA['companyId']]) < 2) {
                $announcementIdsA[]                 = $itemA['announcementId'];
                $companyIdsA[$itemA['companyId']][] = $itemA['announcementId'];
            }
        }

        //2.b 规则列表
        $announcementQueryB = clone $query;
        $announcementListB  = $announcementQueryB->andWhere(['art.status' => BaseArticle::STATUS_ONLINE])
            ->leftJoin(['cgr' => BaseCompanyGroupRelation::tableName()], 'c.id=cgr.company_id')
            ->leftJoin(['cftr' => BaseCompanyFeatureTagRelation::tableName()], 'c.id=cftr.company_id')
            ->andWhere([
                'not in',
                'a.id',
                $topAnnouncementIds,
            ])
            ->andWhere([
                'not in',
                'a.id',
                $announcementIdsA,
            ])
            ->andWhere([
                'cftr.feature_tag_id' => BaseCompanyFeatureTag::PI_TAG_ID,
                'cgr.group_id'        => self::COMPANY_GROUP_ID,
            ])
            ->groupBy('a.id')
            ->asArray()
            ->all();
        $announcementIdsB   = array_column($announcementListB, 'announcementId');
        //2.c 规则列表
        $announcementQueryC = clone $query;
        $announcementListC  = $announcementQueryC->leftJoin(['aa' => BaseArticleAttribute::tableName()],
            'aa.article_id = art.id')
            ->andWhere(['art.status' => BaseArticle::STATUS_ONLINE])
            ->andWhere([
                'not in',
                'a.id',
                $topAnnouncementIds,
            ])
            ->andWhere([
                'not in',
                'a.id',
                $announcementIdsA,
            ])
            ->andWhere([
                'not in',
                'a.id',
                $announcementIdsB,
            ])
            ->andWhere([
                'aa.type' => BaseArticleAttribute::ATTRIBUTE_DOCTOR_PUSH,
            ])
            ->groupBy('a.id')
            ->asArray()
            ->all();
        $announcementIdsC   = array_column($announcementListC, 'announcementId');

        //第二组职位集合
        $announcementIdRecommend = array_merge($announcementIdsA, $announcementIdsB, $announcementIdsC);
        // 按$JobIdRecommend获取职位并排序
        // 排序规则：
        //  1、按发布日期倒序；
        //  2、同一发布日：
        //  （1）“加推”属性 > 含“PI”单位特色标签的单位的职位 > 其他符合条件的职位；
        //  （2）其次，按职位所属单位群组权重分值倒序展示；单位群组权重分值相同，再按职位ID倒序展示。
        $recommendQuery = clone $query;
        $recommendList  = $recommendQuery->addSelect(['recommendType' => new Expression("CASE WHEN aa.type=22 THEN 300 WHEN cftr.feature_tag_id=1 THEN 200 ELSE 100 END")])
            ->leftJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()], 'c.group_score_system_id = cgss.id')
            ->leftJoin(['cftr' => BaseCompanyFeatureTagRelation::tableName()],
                'c.id=cftr.company_id and cftr.feature_tag_id=1')
            ->leftJoin(['aa' => BaseArticleAttribute::tableName()], 'aa.article_id = art.id and aa.type=22')
            ->addSelect(new yii\db\Expression("'0' as isTop"))
            ->addSelect(new yii\db\Expression("'1' as isRecommend"))
            ->andWhere(['a.id' => $announcementIdRecommend])
            ->orderBy('art.refresh_date desc,recommendType desc,cgss.score desc,a.id desc')
            ->asArray()
            ->all();
        foreach ($recommendList as &$recommendItem) {
            $recommendItem['number'] = $announcementIdNumberList[$recommendItem['announcementId']];
        }
        //3、第三组普通列表
        $generalQuery = clone $query;
        $generalList  = $generalQuery->leftJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()],
            'c.group_score_system_id = cgss.id')
            ->addSelect(new yii\db\Expression("'0' as isTop"))
            ->addSelect(new yii\db\Expression("'0' as isRecommend"))
            ->andWhere([
                'not in',
                'a.id',
                $topAnnouncementIds,
            ])
            ->andWhere([
                'not in',
                'a.id',
                $announcementIdRecommend,
            ])
            ->andWhere([
                'art.status' => [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
            ])
            ->orderBy('art.status asc,art.refresh_date desc,ae.is_boshihou_pay asc,cgss.score desc,a.id desc')
            ->groupBy('a.id')
            ->asArray()
            ->all();
        foreach ($generalList as &$generalItem) {
            $generalItem['number'] = $announcementIdNumberList[$generalItem['announcementId']];
        }

        $listOriginal  = [];
        $listKey       = 0;
        $listItemEmpty = [];
        // 公告穿插规则：
        // 公告穿插规则：
        // （1）3急-  3普-2荐-  3普-2荐…；
        //  任一组别条数不足时，直接接入下一组别，如1普-荐-荐-荐-荐-荐…
        // （2）优推公告组的公告，其穿插排序不得低于其自然排序（即作为普通公告时的排序序号）；
        //  若低于，则该条优推公告，按自然排序生效（此时不显示“荐”标签&【优推公告卡片样式】）；
        $generalNumber      = 0;
        $generalNumberMax   = 3;
        $recommendNumber    = 0;
        $recommendNumberMax = 2;
        $interleavedSort    = 1;
        while (count($generalList) > 0 || count($recommendList) > 0) {
            //1、先处理$generalList=3
            if (count($generalList) > 0) {
                //在$generalList中在头部取出三条普通职位放入$list
                for ($i = 0; $i < $generalNumberMax; $i++) {
                    if (count($generalList) > 0) {
                        $generalInfo = array_shift($generalList);
                        if (isset($generalInfo['interleavedSortOriginal']) && $generalInfo['interleavedSortOriginal'] > 0) {
                            $generalInfo['interleavedSort'] = $generalInfo['interleavedSortOriginal'];
                        } else {
                            $generalInfo['interleavedSort'] = $interleavedSort;
                            $interleavedSort++;
                        }
                        $generalInfo['listKey'] = $listKey;
                        $listKey++;
                        $listOriginal[] = $generalInfo;
                    }
                }
            }
            //2、再处理$recommendList=2
            if (count($recommendList) > 0) {
                //保存要进普通列表的元素
                $recommendListSave = [];
                //在$recommendList中取出两条优推职位放入$list
                for ($j = 0; $j < $recommendNumberMax; $j++) {
                    if (count($recommendList) > 0) {
                        $recommendInfo = array_shift($recommendList);
                        if ($recommendInfo['number'] < $interleavedSort) {
                            //变成普通职位
                            $recommendInfo['isRecommend']             = '2';
                            $recommendInfo['isRecommendOld']          = '1';
                            $recommendInfo['interleavedSortOriginal'] = $interleavedSort;
                            //塞到普通列表--直接塞入导致位置倒置
                            //array_unshift($generalList, $recommendInfo);
                            array_unshift($recommendListSave, $recommendInfo);
                            //这时候当前位置就少了一个推荐职位，所以$list缺省key记录下来
                            $listItemEmpty[]        = $listKey;
                            $listOriginal[$listKey] = [];
                        } else {
                            $recommendInfo['interleavedSort'] = $interleavedSort;
                            $listItemEmptyMin                 = count($listItemEmpty) > 0 ? array_shift($listItemEmpty) : 0;
                            //正常推荐职位放位置时候要看原始位置之前有没有空位，用则追溯放到前位，并且记录自身位置空缺，否则放入自身位置
                            if ($listItemEmptyMin > 0 && $listItemEmptyMin < $listKey) {
                                //追溯并记录自身
                                $recommendInfo['listKey']        = $listItemEmptyMin;
                                $listOriginal[$listItemEmptyMin] = $recommendInfo;
                                $listOriginal[$listKey]          = [];
                                //                                $list[]          = $recommendInfo;
                                $listItemEmpty[] = $listKey;
                            } else {
                                //自身位置
                                $recommendInfo['listKey'] = $listKey;
                                $listOriginal[]           = $recommendInfo;
                            }
                        }
                        $listKey++;
                        $interleavedSort++;
                    }
                }
                if ($recommendListSave) {
                    //塞回普通列表
                    foreach ($recommendListSave as $saveItem) {
                        array_unshift($generalList, $saveItem);
                    }
                }
            }
        }
        //把剩下的空位置删掉
        foreach ($listItemEmpty as $key) {
            unset($listOriginal[$key]);
        }
        //         重新索引数组
        $listOriginal = array_values($listOriginal);
        //这时候将三条置顶职位放到第一位
        $listOriginal = array_merge($top3, $listOriginal);
        $pageSize     = 20;
        $offset       = ($page - 1) * $pageSize;
        $list         = array_slice($listOriginal, $offset, $pageSize);
        foreach ($list as &$item) {
            $item['refreshTime'] = TimeHelper::formatDateByYear($item['refreshTime'], '.');
            $item['periodDate']  = $item['periodDate'] == TimeHelper::ZERO_TIME ? '详见正文' : TimeHelper::formatDateByYear($item['periodDate'],
                '.');
            //招聘人数
            $item['jobAmount'] = BaseJob::getAnnouncementJobRecruitAmount($item['announcementId']);
            //专业
            $item['majorText'] = BaseJobMajorRelation::getAnnouncementMajorText($item['announcementId']);
            //地区
            $item['areaText'] = BaseAnnouncementAreaRelation::getCityTextByAnnouncementId($item['announcementId']);
            //公告详情地址
            $item['announcementUrl'] = UrlHelper::createPcAnnouncementDetailPath($item['announcementId']);
        }
        $count  = count($listOriginal);
        $result = [
            'list'      => $list,
            'count'     => $count,
            'pageCount' => ceil($count / 20),
        ];
        //第一页 无参数 允许缓存 则缓存
        if ($page == 1 && empty($params)) {
            //缓存5分钟
            Cache::set(Cache::BOSHIHOU_ANNOUNCEMENT_LIST_PAGE_ONE, json_encode($result), 300);
        }

        return $result;
    }

    /**
     * 公告榜单
     * 调用&排序规则：
     * （1）取调用至本站点的在线公告（不含已下线、已隐藏）；
     * （2）按近15天公告浏览量（全端口数据）倒序，展示排行前10的公告；
     * （3）同一单位只取浏览量最高的一条公告。
     */
    public static function getRankingList($isCache)
    {
        $list = Cache::get(Cache::BOSHIHOU_ANNOUNCEMENT_RANKING);
        if ($list && !$isCache) {
            return json_decode($list, true);
        }

        return [];
    }

    public static function getRankingListCache()
    {
        $data = BaseAnnouncement::find()
            ->alias('a')
            ->select([
                'a.id',
                'a.title',
                'c.id as companyId',
                'count(acl.id) as clickNumber',
            ])
            ->innerJoin(['art' => BaseArticle::tableName()], 'a.article_id = art.id')
            ->innerJoin(['c' => BaseCompany::tableName()], 'a.company_id = c.id')
            ->leftJoin(['ac' => BaseArticleColumn::tableName()], 'art.id = ac.article_id')
            ->leftJoin(['acl' => BaseArticleClickLog::tableName()], 'a.article_id=acl.article_id')
            ->andWhere([
                'ac.column_id' => self::Column_ID_7,
                'art.status'   => BaseArticle::STATUS_ONLINE,
                'art.is_show'  => BaseArticle::IS_SHOW_YES,
            ])
            ->andWhere([
                '>',
                'acl.add_time',
                date('Y-m-d H:i:s', time() - 15 * 24 * 3600),
            ])
            ->groupBy('a.id')
            ->orderBy('clickNumber desc,a.id desc')
            ->limit(100)
            ->asArray()
            ->all();

        $companyIds = [];
        $list       = [];
        $i          = 0;
        foreach ($data as $item) {
            if (!in_array($item['companyId'], $companyIds) && $i < 10) {
                $list[] = [
                    'id'    => $item['id'],
                    'title' => $item['title'],
                    'url'   => UrlHelper::createPcAnnouncementDetailPath($item['id']),
                ];
                $i++;
                $companyIds[] = $item['companyId'];
            }
        }

        Cache::set(Cache::BOSHIHOU_ANNOUNCEMENT_RANKING, json_encode($list), 86400);

        return $list;
    }
}