<?php
/**
 * create user：shannon
 * create time：2024/9/27 下午4:50
 */
namespace common\service\boShiHouColumn;

use common\base\BaseActiveRecord;
use common\base\models\BaseArticle;
use common\base\models\BaseHwActivity;
use common\base\models\BaseHwActivityPromotion;
use common\base\models\BaseNews;
use common\helpers\FileHelper;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use common\libs\Cache;
use Yii;

/**
 * 处理博后活动相关的业务逻辑
 */
class ActivityService extends BaseService
{
    const BOSHIHOU_HOME_BOHOU_RECOMMEND_ACTIVITY_EXPIRE = 3600;

    /**
     * 页面渲染
     * @return array
     */
    public function run()
    {
        $this->isCache = (bool)Yii::$app->request->get('isCache', 0);

        return [
            'showcaseHF'        => ShowcaseService::getActivityHF(),
            'recommendActivity' => $this->getBoHouRecommendActivity(),
            'activityList'      => $this->getList(),
            'newsList'          => $this->getNewsList(),
            'seo'               => Yii::$app->params['tdk']['activity'],
        ];
    }

    /**
     * 推荐活动
     * @return array|mixed
     */
    private function getBoHouRecommendActivity()
    {
        $result = Cache::get(Cache::BOSHIHOU_HOME_BOHOU_RECOMMEND_ACTIVITY);
        if ($result && !$this->isCache) {
            return json_decode($result, true);
        }
        $list = BaseHwActivityPromotion::find()
            ->alias('hap')
            ->select([
                'ha.id as activityId',
                'ha.name as activityName',
                'ha.sign_end_date as signEndDate',
                'ha.sign_custom_end_date as signCustomEndDate',
                'ha.activity_status as activityStatus',
                'ha.detail_url as activityDetailUrl',
                'ha.sign_up_url as activitySignUpUrl',
                'ha.is_outside_url as isOutsideUrl',
                'hap.img_file_id as activityImgFileId',
            ])
            ->innerJoin(['ha' => BaseHwActivity::tableName()], 'hap.activity_id=ha.id')
            ->where(['hap.position_type' => BaseHwActivityPromotion::PROMOTION_POSITION_BOHOU_RECOMMEND_ACTIVITY])
            ->andWhere([
                'and',
                [
                    '<=',
                    'hap.start_date',
                    CUR_DATETIME,
                ],
                [
                    '>=',
                    'hap.end_date',
                    CUR_DATETIME,
                ],
                [
                    '=',
                    'ha.status',
                    BaseHwActivity::STATUS_ACTIVE,
                ],
            ])
            ->orderBy('hap.sort desc,ha.id desc')
            ->limit(3)
            ->asArray()
            ->all();
        foreach ($list as &$item) {
            $item['activityImgUrl']   = FileHelper::getFullPathById($item['activityImgFileId']);
            $item['activityDate']     = BaseHwActivity::getActivityDate($item['activityId']);
            $item['signEndDate']      = $item['signEndDate'] == TimeHelper::ZERO_DATE ? $item['signCustomEndDate'] : date('Y.m.d',
                strtotime($item['signEndDate']));
            $item['activityAreaText'] = BaseHwActivity::getAllAddressBySeries($item['activityId']);
            if ($item['isOutsideUrl'] == BaseHwActivity::IS_OUTSIDE_URL_YES) {
                $item['rel'] = 'nofollow';
            } else {
                $item['rel'] = '';
            }
            unset($item['signCustomEndDate']);
        }

        $result = [
            'list' => $list,
        ];
        Cache::set(Cache::BOSHIHOU_HOME_BOHOU_RECOMMEND_ACTIVITY, json_encode($result),
            self::BOSHIHOU_HOME_BOHOU_RECOMMEND_ACTIVITY_EXPIRE);

        return $result;
    }

    /**
     * 活动列表
     */
    public function getList()
    {
        $p     = Yii::$app->request->get('page');
        $query = BaseHwActivity::find()
            ->alias('ha')
            ->select([
                'ha.id as activityId',
                'ha.name as activityName',
                'ha.sign_end_date as signEndDate',
                'ha.sign_custom_end_date as signCustomEndDate',
                'ha.activity_status as activityStatus',
                'ha.detail_url as activityDetailUrl',
                'ha.sign_up_url as activitySignUpUrl',
                'ha.is_outside_url as isOutsideUrl',
                'ha.main_img_file_id as mainImgFileId',
            ])
            ->where([
                'ha.status'           => BaseHwActivity::STATUS_ACTIVE,
                'ha.series_type'      => BaseHwActivity::SERIES_BOSHIHOU,
                'ha.grounding_status' => BaseHwActivity::GROUNDING_STATUS_ON,
            ]);
        $count = $query->count();
        $page  = BaseActiveRecord::setPage($count, $p, 9);
        $list  = $query->orderBy('ha.activity_status asc,ha.sort desc,ha.activity_start_date desc,ha.id desc')
            ->offset($page['offset'])
            ->limit($page['limit'])
            ->asArray()
            ->all();
        foreach ($list as &$item) {
            $item['mainImgUrl']       = FileHelper::getFullPathById($item['mainImgFileId']);
            $item['activityDate']     = BaseHwActivity::getActivityDate($item['activityId']);
            $item['signEndDate']      = $item['signEndDate'] == TimeHelper::ZERO_DATE ? $item['signCustomEndDate'] : date('Y.m.d',
                strtotime($item['signEndDate']));
            $item['activityAreaText'] = BaseHwActivity::getAllAddressBySeries($item['activityId']);
            if ($item['isOutsideUrl'] == BaseHwActivity::IS_OUTSIDE_URL_YES) {
                $item['rel'] = 'nofollow';
            } else {
                $item['rel'] = '';
            }
            unset($item['signCustomEndDate']);
        }

        return [
            'list'  => $list,
            'count' => (int)$count,
        ];
    }

    /**
     * 最新资讯
     *
     *
     * 1、新增“最新资讯“模块：
     *
     * 该模块调用“调用站点”勾选了“高才博士后”、且审核通过的资讯；
     *
     * 无相关资讯时，整个模块隐藏不展示；页面自适应展示。
     *
     *
     *
     * 2、字段说明&排序&分页：
     *
     * （1）发布时间最新的1条资讯，展示在左侧：
     *
     * ① 展示发布时间、资讯标题（超2行…）、资讯摘要（超1行…）、正文头图；
     *
     * ② 点击整张卡片，若该资讯有配置跳转链接，则新页面打开链接；若未配置，则新页面跳转该资讯详情页。
     *
     *
     *
     * （2）其余资讯，按发布时间倒序，展示在右侧列表，3条/页，最多6页，每6S自动切换至下一页（只有一页不展示轮播效果）；支持手动切换；
     *
     * ① 展示发布时间、资讯标题（超1行…）、资讯摘要（超2行…）、正文头图；
     *
     * ② 鼠标移入单张卡片，资讯标题显示移入效果（见UI）；
     *
     * ③ 点击单张卡片，若该资讯有配置跳转链接，则新页面打开链接；若未配置，则新页面跳转该资讯详情页。
     */
    public function getNewsList()
    {
        $list = BaseArticle::find()
            ->alias('a')
            ->innerJoin(['n' => BaseNews::tableName()], 'n.article_id = a.id')
            ->where([
                'a.is_delete'     => BaseArticle::IS_DELETE_NO,
                'a.status'        => BaseArticle::STATUS_ACTIVE,
                'a.is_show'       => BaseArticle::IS_SHOW_YES,
                'a.type'          => BaseArticle::TYPE_NEWS,
                'n.status'        => BaseNews::STATUS_ONLINE,
                'n.use_site_type' => BaseNews::USE_SITE_TYPE_BO_HOU,
            ])
            ->select([
                'a.title',
                'a.cover_thumb as coverThumb',
                'a.seo_description',
                'a.refresh_date',
                'a.link_url',
                'n.id',
            ])
            ->orderBy('a.refresh_date desc, n.id desc')
            ->limit(19) // 左侧1条 + 右侧18条（3条/页 * 6页）
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            // 如果有自定义跳转链接，使用自定义链接，否则使用详情页链接
            $item['url']         = !empty($item['link_url']) ? $item['link_url'] : BaseNews::getDetailUrl($item['id']);
            $item['refreshDate'] = date('Y.m.d', strtotime($item['refresh_date']));
            unset($item['cover_thumb'], $item['refresh_date']);
        }


        return [
            'leftNews'  => isset($list[0]) ? $list[0] : null,
            'rightNews' => array_slice($list, 1),
        ];
    }

}