<?php

namespace common\service\jobSubscribe;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseJobSubscribe;
use common\base\models\BaseJobSubscribeSendLog;
use common\base\models\BaseJobTopConfig;
use common\base\models\BaseMajor;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseShieldCompany;
use common\helpers\ArrayHelper;
use common\helpers\TimeHelper;

class JobRecommendService
{
    private $id;
    private $majorId;
    private $educationIdArr;
    private $jobCategoryidArr;
    private $areaIdArr;
    private $resumeId;
    private $shieldCompanyIdList;
    private $removeJobIdList;
    private $selectJobIdList = [];

    const RECOMMEND_JOB_COUNT = 12;

    public function run($id)
    {
        $this->id = $id;
        $this->setData();

        return $this->getList();
    }

    private function setData()
    {
        $info                 = BaseJobSubscribe::findOne($this->id);
        $this->resumeId       = $info->resume_id;
        $resumeInfo           = BaseResume::findOne($info->resume_id);
        $memberTopEducationId = $resumeInfo->last_education_id;
        //求职者需求专业
        $resumeMajorId = BaseResumeEducation::findOneVal(['id' => $memberTopEducationId], 'major_id');
        //向上取专业
        $this->majorId = BaseMajor::findOneVal(['id' => $resumeMajorId], 'parent_id');
        //所选学历要求
        $this->educationIdArr = explode(',', $info->education_ids);
        //意向职位类型
        $this->jobCategoryidArr = explode(',', $info->job_category_ids);
        //意向城市
        $this->areaIdArr = explode(',', $info->area_ids);
    }

    private function getList()
    {
        //获取需要排除的职位列表
        $this->setRemoveInfo();
        $ruleOneList = $this->getRuleOneList();
        //判断是否数量足够了
        $surplusCount = self::RECOMMEND_JOB_COUNT - count($ruleOneList);

        $otherList = [];
        if ($surplusCount > 0) {
            //0\716专业不限或者为空，若按规则1匹配的职位不足12条，则再推荐几条所选学历要求、意向职位类型、意向城市匹配、专业不限或为空的职位；
            $otherList = $this->searchJobList(null, [
                0,
                716,
            ], $surplusCount);
        }

        return array_merge($ruleOneList, $otherList);
    }

    private function setRemoveInfo()
    {
        //30天内推送过的职位
        $sendLogJobInfo = BaseJobSubscribeSendLog::find()
            ->where(['resume_id' => $this->resumeId])
            ->andWhere([
                'between',
                'add_time',
                date('Y-m-d H:i:s', strtotime('-30 days')),
                date('Y-m-d H:i:s', time()),
            ])
            ->select('job_ids')
            ->asArray()
            ->all();
        $sendJobIdList  = [];
        foreach ($sendLogJobInfo as $item) {
            foreach (explode(',', $item['job_ids']) as $k => $v) {
                array_push($sendJobIdList, $v);
            }
        }
        //30天内投递过的职位
        $applyJobList          = BaseJobApplyRecord::find()
            ->where(['resume_id' => $this->resumeId])
            ->andWhere([
                'between',
                'add_time',
                date('Y-m-d H:i:s', strtotime('-30 days')),
                date('Y-m-d H:i:s', time()),
            ])
            ->select('job_id')
            ->groupBy('job_id')
            ->column();
        $this->removeJobIdList = array_unique(array_merge($sendJobIdList, $applyJobList));

        //屏蔽单位
        $this->shieldCompanyIdList = BaseShieldCompany::find()
            ->where(['resume_id' => $this->resumeId])
            ->select('company_id')
            ->column();
    }

    private function searchJobList($ids = [], $majorId = 0, $limit = 0)
    {
        //近50日的限制
        $last50Days = TimeHelper::dayToBeginTime(date('Y-m-d', strtotime('-50 days')));
        //不包含推送日的时间限制
        $todayTime = TimeHelper::dayToBeginTime(date('Y-m-d', strtotime('today')));
        $query     = BaseJob::find()
            ->alias('j')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id=j.company_id')
            ->select([
                'j.id',
                'j.name as jobName',
                'j.education_type as educationType',
                'j.major_id as majorId',
                'j.amount',
                'j.refresh_date as refreshDate',
                'j.first_release_time as firstReleaseTime',
                'c.full_name as companyName',
                'j.city_id as cityId',
                'j.min_wage as minWage',
                'j.max_wage as maxWage',
                'j.wage_type as wageType',
                'j.announcement_id as announcementId',
                'j.experience_type as experienceType',
                'j.company_id as companyId',
                'c.type',
            ])
            ->where([
                'j.education_type'  => $this->educationIdArr,
                'j.job_category_id' => $this->jobCategoryidArr,
                'j.status'          => BaseJob::STATUS_ONLINE,
            ])
            ->andWhere([
                'or',
                [
                    'j.province_id' => $this->areaIdArr,
                ],
                [
                    'j.city_id' => $this->areaIdArr,
                ],
            ])
            ->andWhere([
                'between',
                'j.refresh_time',
                $last50Days,
                $todayTime,
            ]);

        if ($majorId) {
            $query->leftJoin(['jm' => BaseJobMajorRelation::tableName()], 'jm.job_id = j.id');
            if (is_array($majorId) && in_array(0, $majorId)) {
                $otherMajorId = array_diff($majorId, [0]);
                $query->andWhere([
                    'or',
                    ['jm.major_id' => $otherMajorId],
                    ['j.major_id' => 0],
                ]);
            } else {
                $query->andWhere(['jm.major_id' => $majorId]);
            }
        }
        $query->andFilterWhere(['j.id' => $ids])
            ->andFilterWhere([
                'NOT IN',
                'j.id',
                $this->selectJobIdList,
            ])
            ->andFilterWhere([
                'not in',
                'j.id',
                $this->removeJobIdList,
            ])
            ->andFilterWhere([
                'not in',
                'j.company_id',
                $this->shieldCompanyIdList,
            ])
            ->orderBy('c.sort desc,is_cooperation asc,j.id desc');
        //判断是否传了limit，没有就用默认的
        if ($limit) {
            $query->limit($limit);
        } else {
            $query->limit(self::RECOMMEND_JOB_COUNT);
        }
        $list = $query->asArray()
            ->all();

        $visitUrl = 'http://' . str_replace('http://', '', \Yii::$app->params['pcHost']);
        foreach ($list as &$item) {
            //获取教育名称
            $item['education'] = BaseDictionary::getEducationName($item['educationType']);
            //获取专业名称
            $item['majorName'] = BaseJob::getSimpleMajorText($item['id']);
            //获取城市
            $item['city'] = BaseArea::getAreaName($item['cityId']);
            //获取职位跳转链接
            $item['url']         = $visitUrl . '/job/detail/' . $item['id'] . '.html';
            $item['address']     = 'https://img.gaoxiaojob.com/uploads/static/image/resume/address.png';
            $item['refreshDate'] = TimeHelper::formatDateByYear($item['refreshDate']);
            //获取公告
            $item['announcementName'] = BaseAnnouncement::findOneVal(['id' => $item['announcementId']], 'title');
            //获取工作经验
            $item['experience'] = BaseDictionary::getExperienceName($item['experience_type']);
            //单位类型
            $item['companyTypeName']   = BaseDictionary::getCompanyTypeName($item['type']);
            $item['companyNatureName'] = BaseDictionary::getCompanyNatureName($item['type']);
            //急聘、回复快
            $item['isTop']  = BaseJob::isTop($item['id']);
            $item['isFast'] = BaseJob::isFast($item['id']);

            //拼接工资
            if ($item['minWage'] == 0 && $item['maxWage'] == 0) {
                $item['wage'] = '面议';
            } else {
                $item['wage'] = BaseJob::formatWage($item['minWage'], $item['maxWage'], $item['wageType']);
            }
        }

        $checkData = [
            'education',
            'majorName',
            'amount',
            'city',
            'refreshDate',
        ];

        return ArrayHelper::arrayToDisplay($list, $checkData);
    }

    private function getRuleOneList()
    {
        //规则1：与求职者需求专业、所选学历要求、意向职位类型、意向城市完全匹配的12条职位；
        //排序规则：优先推符合要求的近一周有置顶历史的职位，其次推“高级会员”类型的职位，再次是其他合作单位的职位，最后推非合作单位的职位；
        //须过滤：30天内已推送过的职位、30天内已投递过的职位、屏蔽单位的职位；
        //获取7天内，有过置顶历史的职位
        $last7Days = TimeHelper::dayToBeginTime(date('Y-m-d', strtotime('-7 days')));

        $topJobIdList = BaseJobTopConfig::find()
            ->where([
                '>=',
                'run_time',
                $last7Days,
            ])
            ->select('job_id')
            ->groupBy('job_id')
            ->column();

        //获取7天内置顶的职位列表
        $topJobList   = [];
        $otherJobList = [];
        if (!empty($topJobIdList)) {
            //如果有，就获取列表
            $topJobList = $this->searchJobList($topJobIdList, $this->majorId, null);
            if (!empty($topJobList)) {
                //排重处理
                foreach ($topJobList as $item) {
                    array_push($this->selectJobIdList, $item['id']);
                }
            }

            //计算剩余数量
            $surplusCount = self::RECOMMEND_JOB_COUNT - count($topJobList);
            if ($surplusCount > 0) {
                $otherJobList = $this->searchJobList(null, $this->majorId, $surplusCount);
                if (!empty($otherJobList)) {
                    //排重处理
                    foreach ($otherJobList as $item) {
                        array_push($this->selectJobIdList, $item['id']);
                    }
                }
            }
        } else {
            //如果没有，直接获取非置顶列表
            $otherJobList = $this->searchJobList(null, $this->majorId, null);
            if (!empty($otherJobList)) {
                //排重处理
                foreach ($otherJobList as $item) {
                    array_push($this->selectJobIdList, $item['id']);
                }
            }
        }
        //合并两个列表
        $ruleOneJobList = array_merge($otherJobList, $topJobList);

        return $ruleOneJobList;
    }
}