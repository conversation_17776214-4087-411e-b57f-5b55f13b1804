<?php

namespace common\service\jobSubscribe;

class JobSubscribeApplication
{
    static $instance;

    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * 保存订阅
     * @param $params
     * @return void
     */
    public function save($params)
    {
        (new SaveService())->run($params);
    }

    /**
     * 获取编辑信息
     * @param $resumeId
     * @return array|\yii\db\ActiveRecord|null
     */
    public function getInfo($resumeId)
    {
        return (new GetEditInfoService())->run($resumeId);
    }

    /**
     * 获取推送的推荐职位列表
     * @param $id
     * @return array
     */
    public function getRecommendJobList($id)
    {
        return (new JobRecommendService())->run($id);
    }

    /**
     * 获取推送的推荐职位列表
     * @param $id
     * @return array
     */
    public function getLogJobList($id)
    {
        return (new GetLogJobListService())->run($id);
    }

    /**
     * 取消订阅
     * @param $resumeId
     * @return void
     */
    public function cancel($resumeId)
    {
        (new CancelService())->run($resumeId);
    }

}
