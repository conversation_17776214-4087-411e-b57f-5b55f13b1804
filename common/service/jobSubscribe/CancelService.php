<?php

namespace common\service\jobSubscribe;

use common\base\BaseActiveRecord;
use common\base\models\BaseJobSubscribe;
use yii\base\Exception;

class CancelService
{
    private $resumeId;

    public function run($resumeId)
    {
        $this->resumeId = $resumeId;
        $this->cancel();
    }

    private function cancel()
    {
        $model = BaseJobSubscribe::findOne(['resume_id' => $this->resumeId]);
        if (!$model) {
            return true;
        }
        // $model->status = BaseActiveRecord::STATUS_DELETE;
        if ($model->delete()) {
            return true;
        }
        throw new Exception('取消订阅失败' . $model->getFirstErrorsMessage());
    }

}