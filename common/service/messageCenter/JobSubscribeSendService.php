<?php

namespace common\service\messageCenter;

use common\base\models\BaseCategoryJob;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseJobSubscribe;
use common\base\models\BaseJobSubscribeSendLog;
use common\base\models\BaseResume;
use common\base\models\BaseResumeWxBind;
use common\base\models\BaseTrade;
use common\helpers\ArrayHelper;
use common\helpers\TimeHelper;
use common\service\jobSubscribe\JobSubscribeApplication;
use Yii;
use yii\base\Exception;

class JobSubscribeSendService extends BaseService
{
    private $resumeId;
    private $info;
    private $jobList;
    private $logId;

    public function run($resumeId)
    {
        $this->key      = self::JOB_SUBSCRIBE_SEND_KEY;
        $this->resumeId = $resumeId;

        //找出记录
        $this->info = BaseJobSubscribe::find()
            ->where(['resume_id' => $resumeId])
            ->asArray()
            ->one();
        if (empty($this->info)) {
            throw new Exception('简历id：' . $resumeId . '操作失败,未找到订阅信息');
        }

        $this->email = $this->info['send_email'];

        //获取与求职者匹配的12条职位
        $service = JobSubscribeApplication::getInstance();

        $this->jobList = $service->getRecommendJobList($this->info['id']);
        //如果没有职位，直接忽略
        if (empty($this->jobList)) {
            throw new Exception('简历id：' . $resumeId . '操作失败,没有可用推荐职位');
        }

        //获取配置内容
        $this->setConfig();

        //没有邮箱不发送
        if (!$this->email) {
            foreach ($this->channel as $k => $v) {
                if ($v == self::TYPE_CHANNEL_EMAIL) {
                    unset($this->channel[$k]);
                }
            }
        }

        //没有绑定不发送微信
        $this->setBind($resumeId);
        //没有勾选推送，不发送
        $isSendWechat = $this->info['is_send_wechat'];
        if ($this->bind < 1 || $isSendWechat != BaseJobSubscribe::IS_SEND_WECHAT_YES) {
            foreach ($this->channel as $k => $v) {
                if ($v == self::TYPE_CHANNEL_WECHAT) {
                    unset($this->channel[$k]);
                }
            }
        }

        if (sizeof($this->channel) < 1) {
            throw new Exception('简历id：' . $resumeId . '操作失败,没有可用推送渠道');
        }

        $this->logId = $this->addSendLog();

        if (in_array(self::TYPE_CHANNEL_EMAIL, $this->channel)) {
            $this->emailContent = [
                'resumeId' => $resumeId,
                'id'       => $this->logId,
            ];
        }

        //微信通知数据
        if (in_array(self::TYPE_CHANNEL_WECHAT, $this->channel)) {
            $this->getWxData();
        }
        $this->remindData['resumeId'] = $resumeId;
        $this->product();

        $this->updateEmailId();
    }

    private function getWxData()
    {
        //获取用户信息
        $resumeName = BaseResume::findOneVal(['id' => $this->resumeId], 'name');

        $env = Yii::$app->params['environment'];
        if ($env == 'prod') {
            $templateId = Yii::$app->params['wx']['privateTemplates'][$this->key]['prod'];
        } else {
            $templateId = Yii::$app->params['wx']['privateTemplates'][$this->key]['gray'];
        }

        $resumeWxBind = BaseResumeWxBind::findOne([
            'resume_id'    => $this->resumeId,
            'is_subscribe' => BaseResumeWxBind::STATUS_ACTIVE,
        ]);
        // GetSubscribeRecommendList
        $url   = Yii::$app->params['h5Host'] . '/job/exclusive-recommend?id=' . $this->logId;
        $title = 'Hi，' . $resumeName . '，高校人才网根据您的职位订阅条件，为您精挑细选一批优质职位，推荐您投递！';
        $time  = TimeHelper::getMinuteChinese(CUR_DATETIME);
        /**
         * {{first.DATA}}
         * 职位名称：{{keyword1.DATA}}
         * 所在行业：{{keyword2.DATA}}
         * 发布时间：{{keyword3.DATA}}
         * {{remark.DATA}}
         * 字段调用规则：
         * first.DATA=
         * Hi，{求职者姓名；完整展示}，高校人才网根据您的职位订阅条件，为您精挑细选一批优质职位，推荐您投递！
         * keyword1.DATA=
         * 求职者当次推送时的意向职位类型；
         * 按职位类型添加时间倒序展示，职位类型文字完整展示；多个职位类型用“、”分隔。
         * keyword2.DATA=
         * 展示推送职位所属单位的所在行业，精确至二级行业；
         * 行业类型随机取，文字完整展示；最多展示5个二级行业，多个行业用“、”分隔。
         * 若所在行业内容为空，则展示文本“见详情”。
         * keyword3.DATA=
         * 推送职位中最早的发布日期（初次发布时间，精确至日维度）-推送职位中最晚的发布日期（初次发布时间，精确至日维度）
         * 日期格式；年.月.日
         * 例：2023.04.01-2023.04.16
         */

        $jobCategoryIds = $this->info['job_category_ids'];
        // 根据职位去找对应的单位
        $companyIds = array_reduce($this->jobList, function ($result, $item) {
            $result[] = $item['companyId'];

            return $result;
        }, []);

        $jobCategoryName = BaseCategoryJob::find()
            ->select('name')
            ->where(['id' => explode(',', $jobCategoryIds)])
            ->asArray()
            ->column();

        $jobCategoryName = array_slice($jobCategoryName, 0, 5);
        $jobCategoryName = implode('、', $jobCategoryName);

        // 找到这些单位的行业
        $industryName = BaseCompany::find()
            ->alias('a')
            ->select('t.name')
            ->innerJoin(['t' => BaseTrade::tableName()], 't.id=a.industry_id')
            ->where(['a.id' => $companyIds])
            ->asArray()
            ->column();

        $industryName = array_unique($industryName);

        $industryName = array_slice($industryName, 0, 5);
        $industryName = implode('、', $industryName);

        if (!$industryName) {
            $industryName = '见详情';
        }

        $allTimeList = ArrayHelper::getColumn($this->jobList, 'firstReleaseTime');

        // $firstReleaseTime = min($allTimeList);
        // $lastReleaseTime  = max($allTimeList);

        // 时间格式化一下(8天前和1天前
        $firstReleaseTime = date('Y.m.d', strtotime('-7 day'));
        $lastReleaseTime  = date('Y.m.d', strtotime('-1 day'));

        $this->wxData = [
            'first'      => [
                'value' => $title,
                'color' => '#0000FF',
            ],
            'keyword1'   => [
                'value' => $jobCategoryName,
                'color' => '#37416b',
            ],
            'keyword2'   => [
                'value' => $industryName,
                'color' => '#37416b',
            ],
            'keyword3'   => [
                'value' => $firstReleaseTime . '-' . $lastReleaseTime,
                'color' => '#37416b',
            ],
            'remark'     => [
                'value' => $this->config['wxRemark'],
                'color' => '#37416b',
            ],
            'openid'     => $resumeWxBind['openid'],
            'templateId' => $templateId,
            'url'        => $url,
        ];
    }

    /**
     * 新增推送日志
     * @return int|void
     * @throws \Exception
     */
    private function addSendLog()
    {
        $jobText = '';
        foreach ($this->jobList as $job) {
            $jobText .= $job['id'] . ',';
        }
        $jobText = substr($jobText, 0, -1);
        //写入log日志
        $subscribeSendLogModel                    = new BaseJobSubscribeSendLog();
        $subscribeSendLogModel->email_log_id      = 0;
        $subscribeSendLogModel->status            = BaseJobSubscribeSendLog::STATUS_WAIT_SEND;
        $subscribeSendLogModel->subscribe_content = json_encode($this->info);
        $subscribeSendLogModel->subscribe_id      = $this->info['id'];
        $subscribeSendLogModel->job_ids           = $jobText;
        $subscribeSendLogModel->resume_id         = $this->resumeId;
        $subscribeSendLogModel->save();
        if (!$subscribeSendLogModel->save()) {
            throw new \Exception($subscribeSendLogModel->getFirstErrorsMessage());
        } else {
            return $subscribeSendLogModel->id;
        }
    }

    private function updateEmailId()
    {
        if ($this->emailId) {
            $model               = BaseJobSubscribeSendLog::findOne($this->logId);
            $model->email_log_id = $this->emailId;
            $model->save();
        }
    }
}