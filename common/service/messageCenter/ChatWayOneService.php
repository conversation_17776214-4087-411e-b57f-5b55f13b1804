<?php
/**
 * create user：shannon
 * create time：2024/11/27 上午9:15
 */
namespace common\service\messageCenter;

use common\base\models\BaseChatRoom;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyMemberWxBind;
use common\base\models\BaseJob;
use common\base\models\BaseMember;
use common\base\models\BaseResumeWxBind;
use common\helpers\DebugHelper;
use common\helpers\TimeHelper;
use common\libs\WxPublic;
use Yii;

/***
 * 处理单向沟通提醒-新招呼
 */
class ChatWayOneService extends BaseService
{
    public function run($roomId)
    {
        //获取房间信息
        $roomInfo = BaseChatRoom::findOne($roomId);
        if (!$roomInfo || $roomInfo->talk_progress != BaseChatRoom::TALK_PROGRESS_ONE_WAY) {
            return true;
        }
        //这里注意 谁创建的得单向则是推向另一方的消息
        if ($roomInfo->creator_type == BaseChatRoom::CREATOR_TYPE_PERSON) {
            $this->wxPublicType = WxPublic::TYPE_COMPANY;
            $this->key          = self::CHAT_WAY_ONE_COMMUNICATION_COMPANY;
            $bindInfo           = BaseCompanyMemberWxBind::findOne([
                'company_member_id' => $roomInfo->company_member_id,
                'is_subscribe'      => BaseCompanyMemberWxBind::IS_SUBSCRIBE_YES,
            ]);
        } else {
            $this->wxPublicType = WxPublic::TYPE_RESUME;
            $this->key          = self::CHAT_WAY_ONE_COMMUNICATION;
            $bindInfo           = BaseResumeWxBind::findOne([
                'resume_id'    => $roomInfo->resume_id,
                'is_subscribe' => BaseResumeWxBind::STATUS_ACTIVE,
            ]);
        }
        //仅在每天的7:00～21:00期间推送
        $hour = date('H', strtotime($roomInfo->add_time));
        if ($hour < 7 || $hour > 21) {
            return true;
        }
        if (!$bindInfo) {
            return true;
        }
        $this->setConfig();
        //判断单向沟通推送人
        $jobInfo = BaseJob::findOne($roomInfo->current_job_id);
        if (!$jobInfo) {
            return true;
        }
        //获取单位信息
        $companyInfo = BaseCompany::findOne($roomInfo->company_id);
        $env         = Yii::$app->params['environment'];
        if ($env == 'prod') {
            $templateId = Yii::$app->params['wx']['privateTemplates'][$this->key]['prod'];
        } else {
            $templateId = Yii::$app->params['wx']['privateTemplates'][$this->key]['gray'];
        }

        $url          = '';
        $title        = '新招呼通知提醒';
        $time         = TimeHelper::getMinuteChinese($roomInfo->add_time);
        $this->wxData = [
            'first'        => [
                'value' => $title,
                'color' => '#0000FF',
            ],
            'keyword1'     => [
                'value' => $jobInfo->name,
                'color' => '#37416b',
            ],
            'keyword2'     => [
                'value' => $companyInfo->full_name,
                'color' => '#37416b',
            ],
            'keyword3'     => [
                'value' => $time,
                'color' => '#37416b',
            ],
            'keyword4'     => [
                'value' => '单位向您发起了聊天，有消息待您查收！',
                'color' => '#37416b',
            ],
            'remark'       => [
                'value' => $this->config['wxRemark'],
                'color' => '#37416b',
            ],
            'openid'       => $bindInfo->openid,
            'templateId'   => $templateId,
            'url'          => $url,
            'miniPagePath' => 'pages/chat/index',
        ];
        DebugHelper::chat('推送消息config121：' . json_encode($this->config));
        DebugHelper::chat('推送消息wxData21321：' . json_encode($this->wxData));
        $this->product();

        return true;
    }
}