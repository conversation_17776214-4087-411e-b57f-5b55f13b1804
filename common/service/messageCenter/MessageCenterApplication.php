<?php

namespace common\service\messageCenter;

use Exception;

/**
 * 消息中心不做前置判断,只做消息分发,前置判断和数据处理由各个业务模块自己处理
 */
class MessageCenterApplication
{
    static $instance;

    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * @param int $memberId
     * @param int $companyId
     */
    public function companyNewFollow(int $memberId, int $companyId)
    {
        (new CompanyNewFollowService())->run($memberId, $companyId);
    }

    /**
     * 职位邀约投递
     * @param $inviteId    int 邀请id
     * @param $isSendEmail int 0:不发邮件；1：发邮件
     * @param $isSendSms   int 0:不发短信；1：发短信
     * @return array|false
     * @throws Exception
     */
    public function jobInvitation($inviteId, $isSendEmail, $isSendSms = 0)
    {
        return (new JobInvitationService())->run($inviteId, $isSendEmail, $isSendSms);
    }

    /**
     * 职位操作--不合适
     * @param int $memberId
     * @param int $jobId
     * @throws Exception
     */
    public function jobInappropriate(int $memberId, int $jobId)
    {
        (new JobInappropriateService())->run($memberId, $jobId);
    }

    /**
     * 简历操作--查看
     * @param $jobApplyId
     * @throws Exception
     */
    public function jobApplyCheck($jobApplyId)
    {
        (new JobApplyCheckService())->run($jobApplyId);
    }

    /**
     * 简历操作--通过初筛
     * @param $jobApplyId
     * @throws Exception
     */
    public function jobApplyApprove($jobApplyId)
    {
        (new JobApplyApproveService())->run($jobApplyId);
    }

    /**
     * 投递简历操作--面试邀约
     * @param $companyInterViewId
     * @throws Exception
     */
    public function jobApplyInvitation($companyInterViewId, $isEmail = false)
    {
        (new JobApplyInvitationService())->run($companyInterViewId, $isEmail);
    }

    /**
     * 投递简历操作--暂不匹配
     * @param $jobApplyId
     * @throws Exception
     */
    public function jobApplyInappropriate($jobApplyId)
    {
        (new JobInappropriateService())->run($jobApplyId);
    }

    // 已录用
    public function jobApplyEmployed($jobApplyId)
    {
        (new JobApplyEmployedService())->run($jobApplyId);
    }

    // 撤销不合适
    public function jobApplyInappropriateRevocation($jobApplyId)
    {
        (new JobApplyInappropriateRevocationService())->run($jobApplyId);
    }

    // 撤销已录用
    public function jobApplyEmployedRevocation($jobApplyId)
    {
        (new JobApplyEmployedRevocationService())->run($jobApplyId);
    }

    public function companyViewResume($companyId, $resumeId)
    {
        (new CompanyViewResumeService())->run($companyId, $resumeId);
    }

    public function resumeViewResume($resumeId, $companyId)
    {
        (new ResumeViewCompanyService())->run($resumeId, $companyId);
    }

    public function resumeViewCompanyInvite($inviteId)
    {
        (new ResumeViewCompanyInviteService())->run($inviteId);
    }

    public function resumeViewAdminInvite($inviteId)
    {
        (new ResumeViewAdminInviteService())->run($inviteId);
    }

    public function adminInviteJobApply($inviteId)
    {
        (new AdminJobInvitationService())->run($inviteId);
    }

    public function ChatWayOneService($roomId)
    {
        (new ChatWayOneService())->run($roomId);
    }

    public function ChatWayTwoService($roomId)
    {
        (new ChatWayTwoService())->run($roomId);
    }

    /**
     * 扫码登录
     * @param $resumeId
     * @throws Exception
     */
    public function wxSignIn($resumeId)
    {
        (new WxSignInService())->run($resumeId);
    }

    /**
     * 单位扫码登录
     * @param $memberId
     * @throws Exception
     */
    public function wxSignInCompany($memberId)
    {
        (new WxSignInCompanyService())->run($memberId);
    }

    /**
     * 订阅职位推送
     * @param $resumeId
     * @return void
     */
    public function jobSubscribe($resumeId)
    {
        (new JobSubscribeSendService())->run($resumeId);
    }

    /**
     * 站内投递通知
     * @param $memberId
     * @throws Exception
     */
    public function sideDeliveryNotice($applyId)
    {
        (new SideDeliveryNoticeCompanyService())->run($applyId);
    }

    /**
     * 直聊邀请通知
     * @param $jobId
     * @param $resumeId
     * @param $createRoomRes array 每个简历创建房间
     * @return array
     * @throws Exception
     */
    public function jobChatInvitation($resumeId, $createRoomRes)
    {
        return (new JobChatInvitationService())->run($resumeId, $createRoomRes);
    }

    /**
     * 直聊提示用户
     * @return array
     * @throws Exception
     */
    public function chatRoomPromptMember($resumeId)
    {
        return (new ChatRoomPromptMember())->run($resumeId);
    }
}
