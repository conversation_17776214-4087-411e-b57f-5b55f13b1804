<?php

namespace common\service\messageCenter;

use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeWxBind;
use common\helpers\TimeHelper;
use yii;

class JobInappropriateService extends BaseService
{

    /**
     * @throws \Exception
     */
    public function run($jobApplyId)
    {
        $jobApply = BaseJobApply::findOne(['id' => $jobApplyId]);
        if (!$jobApply) {
            return true;
        }
        $memberId = $jobApply->resume_member_id;
        $jobId    = $jobApply->job_id;
        //配置公共信息
        $this->key = self::JOB_INAPPROPRIATE_KEY;
        $this->setConfig();

        //查找信息
        $this->email = BaseMember::findOneVal(['id' => $memberId], 'email');
        $info        = Basejob::getInappropriateJob($memberId, $jobId);
        if (!$this->email || !$info) {
            foreach ($this->channel as $k => $v) {
                if ($v == self::TYPE_CHANNEL_EMAIL) {
                    unset($this->channel[$k]);
                }
            }
        }

        $resumeId = BaseResume::findOneVal(['member_id' => $memberId], 'id') ?: 0;
        $this->setBind($resumeId);
        if ($this->bind < 1) {
            foreach ($this->channel as $k => $v) {
                if ($v == self::TYPE_CHANNEL_WECHAT) {
                    unset($this->channel[$k]);
                }
            }
        }

        if (sizeof($this->channel) < 1) {
            return true;
        }

        //微信通知数据
        if (in_array(self::TYPE_CHANNEL_WECHAT, $this->channel)) {
            $env = Yii::$app->params['environment'];
            if ($env == 'prod') {
                $templateId = Yii::$app->params['wx']['privateTemplates'][$this->key]['prod'];
            } else {
                $templateId = Yii::$app->params['wx']['privateTemplates'][$this->key]['gray'];
            }

            $resumeWxBind = BaseResumeWxBind::findOne([
                'resume_id'    => $resumeId,
                'is_subscribe' => BaseResumeWxBind::STATUS_ACTIVE,
            ]);
            $url          = Yii::$app->params['h5Host'] . '/person/on-site-apply-list';
            $resumeName   = BaseResume::findOneVal(['id' => $resumeId], 'name');
            $title        = $resumeName . ',您好。很抱歉，您的简历暂未通过用人部门的筛选';
            $time         = TimeHelper::getMinuteChinese(CUR_DATETIME);
            $this->wxData = [
                'first'        => [
                    'value' => $title,
                    'color' => '#0000FF',
                ],
                'keyword1'     => [
                    'value' => $info['jobName'],
                    'color' => '#37416b',
                ],
                'keyword2'     => [
                    'value' => $info['companyName'],
                    'color' => '#37416b',
                ],
                'keyword3'     => [
                    'value' => $time,
                    'color' => '#37416b',
                ],
                'keyword4'     => [
                    'value' => '简历未通过用人部门筛选',
                    'color' => '#37416b',
                ],
                'remark'       => [
                    'value' => $this->config['wxRemark'],
                    'color' => '#37416b',
                ],
                'openid'       => $resumeWxBind['openid'],
                'templateId'   => $templateId,
                'url'          => $url,
                'miniPagePath' => 'pages/person/index',
            ];
        }

        if (in_array(self::TYPE_CHANNEL_EMAIL, $this->channel)) {
            $mailRecommend = BaseJob::getMailRecommend($memberId, $jobId);
            //邮件主体页面内容
            $html = '<div class="container">';
            $tips = '';
            if ($info['tags']) {
                $tips = '<div class="resume-tips">
                            <span class="warning">*</span>
                            温馨提示：您的简历中
                            <span class="wait-perfect">' . $info['tags'] . '</span>
                            未填写完整，详尽的简历内容更容易提高竞争力、受到用人部门青睐哦。
                            <a href="' . $info['resumeLink'] . '" target="_blank">去完善简历</a>
                        </div>';
            }

            $html = $html . '<div class="email-body">
                                <div class="hi">' . $info['memberName'] . '，您好：</div>
                                <div class="email-content">很抱歉！您投递至“
                                    <a href="' . $info['companyLink'] . '" target="_blank">' . $info['companyName'] . '</a>&nbsp;-&nbsp;<a
                                            href="' . $info['jobLink'] . '"
                                            target="_blank">' . $info['jobName'] . '</a>”职位的简历暂不符合用人部门要求。高校人才网根据您的求职意向为您精选了以下职位，欢迎您查看！
                                </div>
                                ' . $tips . '
                            </div>';
            if ($mailRecommend) {
                $tagContent = '';
                $visitUrl   = 'http://' . str_replace('http://', '', Yii::$app->params['pcHost']);
                $moreLink   = $visitUrl . '/job';
                foreach ($mailRecommend as $item) {
                    $tagContent .= '<a target="_blank" href="' . $item['jobLink'] . '" class="recommend-card">
                                        <div class="job-top">
                                            <div class="job-name">' . $item['name'] . '</div>
                                            <div class="release-date" style="display:' . $item['refreshDateShow'] . '">' . $item['refreshDate'] . '发布</div>
                                        </div>
                                        <div class="tag-content">
                                            <span class="tag" style="display:' . $item['educationNameShow'] . '">' . $item['educationName'] . '</span>
                                            <span class="tag" style="display:' . $item['majorNameShow'] . '">' . $item['majorName'] . '</span>
                                            <span class="tag" style="display:' . $item['amountShow'] . '">' . $item['amount'] . '人</span>
                                        </div>
                                        <div class="job-bottom">
                                            <div class="organizational">' . $item['fullName'] . '</div>
                                            <div class="address" style="display:' . $item['cityShow'] . '"><img src="' . $item['address'] . '"/>' . $item['city'] . '</div>
                                        </div>
                                    </a>';
                }

                $html = $html . '<div class="recommend-wrapper">
                    <div class="content">
                        <div class="recommend-title">
                            <b class="big">职位推荐</b>
                            <span class="recommend-tips">高校人才网根据您的求职意向为您推荐以下职位</span>
                        </div>
                        <div class="recommend-content">
                           ' . $tagContent . '
                        </div>
                        <div class="more"><a target="_blank" href="' . $moreLink . '">查看更多精选职位</a></div>
                    </div>
                </div>';
            }
            $html            = $html . '</div>';
            $this->emailData = ['html' => $html];
        }

        // 强提醒
        $this->remindData['jobApplyId'] = $jobApplyId;
        $this->remindData['resumeId']   = $resumeId;

        $this->product();

        return true;
    }

}
