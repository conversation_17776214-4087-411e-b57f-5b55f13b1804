<?php

namespace common\service\jobInvite;

use common\base\BaseActiveRecord;
use common\base\models\BaseAdminJobInvite;
use common\base\models\BaseAdminJobInviteConfig;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseJob;
use common\base\models\BaseResume;
use common\base\models\BaseResumeLibraryInviteLog;
use common\helpers\TimeHelper;

class CompanyJobInviteListService extends CommonService
{
    static $instance;

    public $originalParams = [];

    public $page;
    public $pageSize;

    public $data = [];

    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    public function getList($params)
    {
        $this->originalParams = $params;

        $this->handelParams();

        $this->getListData();

        return $this->data;
    }

    public function getListData()
    {
        // 一切都是基于
        $query = BaseResumeLibraryInviteLog::find()
            ->alias('a');

        // 在这里做一些求职者的筛选
        // resumeId
        // resumeName
        // 如果有上面两个字段，就需要关联简历表
        if ($this->originalParams['resumeId'] || $this->originalParams['resumeName']) {
            $query->innerJoin(['c' => BaseResume::tableName()], 'c.id = a.resume_id');

            if ($this->originalParams['resumeId']) {
                $query->andWhere(['c.uuid' => $this->originalParams['resumeId']]);
            }
            if ($this->originalParams['resumeName']) {
                $query->andWhere([
                    'like',
                    'c.name',
                    $this->originalParams['resumeName'],
                ]);
            }
        }

        // 在这里做一些职位的筛选
        // jobId
        // jobName
        // 如果有上面两个字段，就需要关联职位表
        if ($this->originalParams['jobId'] || $this->originalParams['jobName']) {
            $query->innerJoin(['d' => BaseJob::tableName()], 'd.id = a.job_id');

            if ($this->originalParams['jobId']) {
                $query->andWhere(['d.uuid' => $this->originalParams['jobId']]);
            }
            if ($this->originalParams['jobName']) {
                $query->andWhere([
                    'like',
                    'd.name',
                    $this->originalParams['jobName'],
                ]);
            }
        }

        // 在这里做一些单位的筛选
        // companyId
        // companyName
        // 如果有上面两个字段，就需要关联单位表
        if ($this->originalParams['companyId'] || $this->originalParams['companyName']) {
            $query->innerJoin(['e' => BaseCompany::tableName()], 'e.id = a.company_id');

            if ($this->originalParams['companyId']) {
                $query->andWhere(['e.uuid' => $this->originalParams['companyId']]);
            }
            if ($this->originalParams['companyName']) {
                $query->andWhere([
                    'like',
                    'e.full_name',
                    $this->originalParams['companyName'],
                ]);
            }
        }

        if ($this->originalParams['inviteUsername'] || $this->originalParams['inviteUserId']) {
            $query->innerJoin(['f' => BaseCompanyMemberInfo::tableName()], 'f.member_id = a.company_member_id');
            $query->andFilterWhere([
                'like',
                'f.contact',
                $this->originalParams['inviteUsername'],
            ]);

            $query->andFilterWhere([
                'f.member_id' => $this->originalParams['inviteUserId'],
            ]);
        }

        //isRemindCheck
        if ($this->originalParams['isRemindCheck'] != '-1') {
            $query->andWhere(['a.is_remind_check' => $this->originalParams['isRemindCheck']]);
        }

        // isDelivery
        if ($this->originalParams['isDelivery'] != '-1') {
            $query->andWhere(['a.is_apply' => $this->originalParams['isDelivery']]);
        }

        // 邀约时间
        if ($this->originalParams['inviteTimeStart']) {
            $query->andWhere([
                '>=',
                'a.add_time',
                TimeHelper::dayToBeginTime($this->originalParams['inviteTimeStart']),
            ]);
        }

        if ($this->originalParams['inviteTimeEnd']) {
            $query->andWhere([
                '<=',
                'a.add_time',
                TimeHelper::dayToEndTime($this->originalParams['inviteTimeEnd']),
            ]);
        }

        $query->select([
            'a.id',
            'inviteAddTime' => 'a.add_time',
            'a.job_id',
            'a.resume_id',
            'a.remark',
            'a.is_apply',
            'a.company_id',
            'a.company_member_id',
            'a.is_remind_check',
        ]);

        if (!$this->isExcel) {
            $baseQuery        = clone $query;
            $resumeIdCount    = $baseQuery->count('distinct resume_id');
            $baseQuery        = clone $query;
            $inviteApplyTotal = $baseQuery->andWhere(['is_apply' => 1])
                ->count();
            $count            = $query->count();

            $pages = BaseActiveRecord::setPage($count, $this->page, $this->pageSize);
            $list  = $query->offset($pages['offset'])
                ->limit($pages['limit'])
                ->orderBy('a.add_time desc')
                ->asArray()
                ->all();
        } else {
            $list = $query->orderBy('a.add_time desc')
                ->asArray()
                ->all();
        }

        foreach ($list as &$v) {
            // 找到求职者的信息
            $resumeInfo           = $this->getResumeInfo($v['resume_id']);
            $v['resumeInfo']      = $resumeInfo['resumeInfo'];
            $v['resumeId']        = $resumeInfo['resumeId'];
            $v['resumeName']      = $resumeInfo['resumeName'];
            $v['deliveryText']    = BaseAdminJobInvite::IS_APPLY_LIST[$v['is_apply']];
            $v['remindCheckText'] = BaseAdminJobInvite::IS_REMIND_CHECK_LIST[$v['is_remind_check']] ?: '否';
            $jobInfo              = $this->getJobInfo($v['job_id']);
            $v['jobName']         = $jobInfo['name'];
            $v['companyName']     = $jobInfo['full_name'];
            $v['inviteUsername']  = $this->getCompanyMemberInfo($v['company_member_id']);
            $applyInfo            = $this->getApplyInfo($v['job_id'], $v['resume_id'], $v['inviteAddTime']);
            $v['applyAddTime']    = $applyInfo ? $applyInfo['add_time'] : '-';
            $v['wayTypeText']     = BaseAdminJobInviteConfig::INVITE_WAY_TYPE_TEXT[$v['way_type']] ?: "-";
            if ($this->isExcel) {
                $excelList[] = $this->itemToExcel($v);
            }
        }

        if ($this->isExcel) {
            $this->data = $this->listToExcel($excelList);

            return true;
        }

        $pages = [
            'count' => (int)$count,
            'limit' => (int)$this->pageSize,
            'page'  => (int)$this->page,
        ];

        $this->data = [
            'list'   => $list,
            'pages'  => $pages,
            'amount' => [
                'inviteResumeTotal' => $resumeIdCount,
                'inviteApplyTotal'  => $inviteApplyTotal,
                'inviteTotal'       => $count,
            ],
        ];
    }

}