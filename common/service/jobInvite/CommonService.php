<?php

namespace common\service\jobInvite;

use common\base\models\BaseAdmin;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEducation;

class CommonService
{

    public $originalParams = [];

    public $page;
    public $pageSize;

    public $data = [];

    public $isExcel = false;

    public $headers = [
        '人才ID',
        '人才姓名',
        '人才基本信息',
        '邀约投递职位',
        '邀约时间',
        '所属单位',
        '邀约人',
        '邀约方式',
        '是否投递',
        '投递时间',
        // '应聘状态',
    ];

    public function itemToExcel($item)
    {
        return [
            $item['resumeId'],
            $item['resumeName'],
            $item['resumeInfo'],
            $item['jobName'],
            $item['inviteAddTime'],
            $item['companyName'],
            $item['inviteUsername'],
            $item['wayTypeText'],
            $item['deliveryText'],
            $item['applyAddTime'],
        ];
    }

    protected function listToExcel($list)
    {
        return [
            'headers' => $this->headers,
            'list'    => $list,
        ];
    }

    protected function handelParams()
    {
        $page     = $this->originalParams['page'] ?? 1;
        $pageSize = $this->originalParams['pageSize'] ?? 20;

        $this->page     = $page;
        $this->pageSize = $pageSize;

        if ($this->originalParams['export']) {
            $this->isExcel = true;
        }
    }

    protected function getApplyInfo($jobId, $resumeId, $addTime)
    {
        // 获取这个时间后面第一次的投递记录
        $applyInfo = BaseJobApplyRecord::find()
            ->select([
                'add_time',
            ])
            ->where([
                'job_id'    => $jobId,
                'resume_id' => $resumeId,
            ])
            ->andWhere([
                '>',
                'add_time',
                $addTime,
            ])
            ->orderBy('add_time asc')
            ->asArray()
            ->one();

        return $applyInfo ?: [];
    }

    protected function getCompanyMemberInfo($companyMemberId)
    {
        $data = BaseCompanyMemberInfo::findOne(['member_id' => $companyMemberId]);

        return "$data->contact({$data->department})";
    }

    protected function getAdminInfo($adminId)
    {
        $adminInfo = BaseAdmin::find()
            ->select([
                'name',
            ])
            ->where(['id' => $adminId])
            ->asArray()
            ->one();

        return $adminInfo;
    }

    protected function getJobInfo($jobId)
    {
        $baseInfo = BaseJob::find()
            ->alias('a')
            ->innerJoin(['b' => BaseCompany::tableName()], 'a.company_id = b.id')
            ->select([
                'a.name',
                'b.full_name',
            ])
            ->where(['a.id' => $jobId])
            ->asArray()
            ->one();

        return $baseInfo;
    }

    protected function getResumeInfo($resumeId)
    {
        $baseInfo = BaseResume::find()
            ->select([
                'uuid',
                'age',
                'gender',
                'complete',
                'name',
                'last_education_id',
                'top_education_code',
                'member_id',
            ])
            ->where(['id' => $resumeId])
            ->asArray()
            ->one();

        $memberModel = BaseMember::findOne($baseInfo['member_id']);

        // 找专业
        if ($baseInfo['last_education_id']) {
            $major = BaseResumeEducation::find()
                ->alias('re')
                ->innerJoin(['e' => BaseMajor::tableName()], 'e.id = re.major_id_level_3')
                ->select(['e.name'])
                ->where(['re.id' => $baseInfo['last_education_id']])
                ->asArray()
                ->one();
        }

        $resumeInfoArray = [
            $baseInfo['age'] . '岁',
            BaseResume::getGenderName($baseInfo['gender']),
            BaseDictionary::getEducationName($baseInfo['top_education_code']),
            $major['name'],
            $baseInfo['complete'] . '%',
        ];

        // 去空
        $resumeInfoArray = array_filter($resumeInfoArray);

        return [
            'resumeInfo' => ($memberModel->status == BaseMember::STATUS_RESUME_CANCELED) ? '' : implode('/',
                $resumeInfoArray),
            'resumeId'   => $baseInfo['uuid'],
            'resumeName' => $baseInfo['name'],
        ];
    }
}