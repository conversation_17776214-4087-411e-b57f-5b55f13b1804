<?php

namespace common\service\resumeBatchDownload;

/**
 * 地区榜单,分为职位\公告\单位
 */
class ResumeBatchDownloadApplication
{
    static $instance;

    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    public function createCompanyByApply($applyIds, $companyId)
    {
        return (new CompanyService())->create($applyIds, $companyId);
    }

    public function downloadByToken($token,$companyId)
    {
        return (new CompanyService())->download($token, $companyId);
    }

}
