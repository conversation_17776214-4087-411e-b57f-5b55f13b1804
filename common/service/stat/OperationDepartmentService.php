<?php

namespace common\service\stat;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArticle;
use common\base\models\BaseCompany;
use common\base\models\BaseJob;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\helpers\TimeHelper;
use common\libs\Excel;
use common\libs\WxWork;
use common\models\JobApply;
use common\models\JobApplyRecord;
use common\models\OffSiteJobApply;

/**
 * 网站运营部数据统计
 */
class OperationDepartmentService extends BaseService
{
    // 注册人数（包含各个渠道）
    // 投递量 站内 站外
    // 用户活跃度
    // 单日投递活跃用户
    // 单位端活跃用户
    // 内容发布量

    public $beginTime;
    public $endTime;

    public $dataList;

    static $instance;

    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    // 日报
    public function daily()
    {
        // 14天前的0点
        $this->beginTime = date('Y-m-d 00:00:00', strtotime('-14 day'));
        // 现在
        $this->endTime = date('Y-m-d H:i:s');

        // 做一个日期数组
        $dateList = [];
        for ($i = 0; $i < 14; $i++) {
            $dateList[] = date('Y-m-d', strtotime("-$i day"));
        }

        $this->dataList = $dateList;

        $filePath = $this->getData();

        // 组成一个富文本内容 有时间，有url，有基本的信息描述

        $wxWork = WxWork::getInstance();

        try {
            // 组成富文本，标题是 今天是几号 星期几(中文)，点击下载，是一个下载链接
            // # 标题一
            // [这是一个链接](http://work.weixin.qq.com/api/doc)
            $content = CUR_DATETIME . ' 现在推送网站运营部日报，今天是' . date('Y-m-d') . ' 星期' . TimeHelper::getWeekDay() . '，[点击下载](' . $filePath . ')';
            $wxWork->robotDataDaily($content, ['type' => 'markdown']);
        } catch (\Exception $e) {
            // 记录日志
            bb($e);
        }
    }

    // 周报（暂时预留）
    public function weekly()
    {
    }

    // 月报 （暂时预留）
    public function monthly()
    {
    }

    public function textToData($text)
    {
        // 14天前的0点
        $this->beginTime = date('Y-m-d 00:00:00', strtotime('-14 day'));
        // 现在
        $this->endTime = date('Y-m-d H:i:s');

        // 做一个日期数组
        $dateList = [];
        for ($i = 0; $i < 14; $i++) {
            $dateList[] = date('Y-m-d', strtotime("-$i day"));
        }

        $this->dataList = $dateList;

        switch ($text) {
            case '注册':
                $data = $this->getAllDataNewResumeData();

                return $this->handelExcelDataToStr($data);
            case '投递':
                $data = $this->getAllJobApplyList();

                return $this->handelExcelDataToStr($data);

            case '入库人才':
                $data = $this->getResumeLibrary();

                return $this->handelExcelDataToStr($data);

            case '人才分布':
                $data = $this->getResumeDistribution();

                return $this->handelExcelDataToStr($data);

            case '内容发布':
                $data = $this->getContentRelease();

                return $this->handelExcelDataToStr($data);

            case 'C端用户活跃':
                $data = $this->resumeActivity();

                return $this->handelExcelDataToStr($data);

            case '投递活跃用户':
                $data = $this->getJobApplyActivity();

                return $this->handelExcelDataToStr($data);

            case 'B端用户活跃':
                $data = $this->companyActivity();

                return $this->handelExcelDataToStr($data);

            default:
                $str  = '没有找到对应的数据,请尝试以下关键字' . PHP_EOL;
                $str  .= '#注册' . PHP_EOL;
                $str  .= '#投递' . PHP_EOL;
                $str  .= '#入库人才' . PHP_EOL;
                $str  .= '#人才分布' . PHP_EOL;
                $str  .= '#内容发布' . PHP_EOL;
                $str  .= '#C端用户活跃' . PHP_EOL;
                $str  .= '#投递活跃用户' . PHP_EOL;
                $str  .= '#B端用户活跃' . PHP_EOL;
                $data = $str;
        }

        return $data;
    }

    public function handelExcelDataToStr($data)
    {
        // 组成markdown表格
        $str = '';
        foreach ($data as $item) {
            foreach ($item as $k => $value) {
                if ($k == 0) {
                    continue;
                }
                $str .= $value . '|';
            }
            $str .= PHP_EOL;
        }

        return $str;
    }

    public function getData()
    {
        // 注册
        $newResumeData = $this->getAllDataNewResumeData();
        // 投递
        $jobApplyData = $this->getAllJobApplyList();
        // 入库人才
        $resumeLibraryData = $this->getResumeLibrary();
        // 人才分布
        $resumeDistributionData = $this->getResumeDistribution();
        // 内容发布
        $contentReleaseData = $this->getContentRelease();
        // 活跃用户
        $resumeActivityData = $this->resumeActivity();
        // 投递用户量
        $jobApplyActivity = $this->getJobApplyActivity();
        // b端活跃用户
        $companyActivity = $this->companyActivity();

        // 合并成data
        $data = $this->merge($newResumeData, $jobApplyData, $resumeLibraryData, $resumeDistributionData,
            $contentReleaseData, $resumeActivityData, $jobApplyActivity, $companyActivity);

        $excel = new Excel();

        $rs = $excel->export($data, [], '网站运营部日报_' . date('Y-m-d') . '_' . CUR_TIMESTAMP);

        return $rs;
    }

    public function merge(...$array)
    {
        // 合并，并且每个data之间加两个空数组
        $data = [];
        foreach ($array as $item) {
            $data = array_merge($data, $item, [
                [],
                [],
            ]);
        }

        return $data;
    }

    public function getAllDataNewResumeData()
    {
        // 先找到全部的注册人数
        $pcData = BaseMember::find()
            ->select('count(*) as count, date(add_time) date')
            ->where([
                'type'        => BaseMember::TYPE_PERSON,
                'source_type' => BaseMember::SOURCE_TYPE_PC,
            ])
            ->andWhere([
                '>=',
                'add_time',
                $this->beginTime,
            ])
            ->groupBy('date(add_time)')
            ->asArray()
            ->all();

        $h5Data = BaseMember::find()
            ->select('count(*) as count, date(add_time) as date')
            ->where([
                'type'        => BaseMember::TYPE_PERSON,
                'source_type' => BaseMember::SOURCE_TYPE_H5,
            ])
            ->andWhere([
                '>=',
                'add_time',
                $this->beginTime,
            ])
            ->groupBy('date(add_time)')
            ->asArray()
            ->all();

        $miniData = BaseMember::find()
            ->select('count(*) as count, date(add_time) as date')
            ->where([
                'type'        => BaseMember::TYPE_PERSON,
                'source_type' => BaseMember::SOURCE_TYPE_MINI_APP,
            ])
            ->andWhere([
                '>=',
                'add_time',
                $this->beginTime,
            ])
            ->groupBy('date(add_time)')
            ->asArray()
            ->all();

        $allData = [];
        foreach ($pcData as $item) {
            $allData[$item['date']] = [
                'pc'    => $item['count'],
                'total' => $item['count'],
            ];
        }

        foreach ($h5Data as $item) {
            if (isset($allData[$item['date']])) {
                $allData[$item['date']]['h5']    = $item['count'];
                $allData[$item['date']]['total'] += $item['count'];
            } else {
                $allData[$item['date']] = [
                    'h5'    => $item['count'],
                    'total' => $item['count'],
                ];
            }
        }

        foreach ($miniData as $item) {
            if (isset($allData[$item['date']])) {
                $allData[$item['date']]['mini']  = $item['count'];
                $allData[$item['date']]['total'] += $item['count'];
            } else {
                $allData[$item['date']] = [
                    'mini'  => $item['count'],
                    'total' => $item['count'],
                ];
            }
        }

        // 最后统一整理成 ['日期','pc', 'h5', 'mini', 'total']
        $data = [
            [
                '注册',
                '日期',
                'PC端',
                'H5端',
                '小程序端',
                '总数',
            ],
        ];

        // 先倒叙
        // $allData = array_reverse($allData);

        foreach ($allData as $k => $allDatum) {
            $data[] = [
                '',
                $k,
                $allDatum['pc'],
                $allDatum['h5'],
                $allDatum['mini'],
                $allDatum['total'],
            ];
        }

        return $data;
    }

    public function getAllJobApplyList()
    {
        $onSiteData = JobApply::find()
            ->select('count(*) as count, date(add_time) as date')
            ->where([
                '>=',
                'add_time',
                $this->beginTime,
            ])
            ->groupBy('date(add_time)')
            ->asArray()
            ->all();

        $offSiteData = OffSiteJobApply::find()
            ->select('count(*) as count, date(add_time) as date')
            ->groupBy('date(add_time)')
            ->where([
                '>=',
                'add_time',
                $this->beginTime,
            ])
            ->asArray()
            ->all();

        $allData = [];

        foreach ($onSiteData as $item) {
            $allData[$item['date']] = [
                'onSite' => $item['count'],
                'total'  => $item['count'],
            ];
        }

        foreach ($offSiteData as $item) {
            if (isset($allData[$item['date']])) {
                $allData[$item['date']]['offSite'] = $item['count'];
                $allData[$item['date']]['total']   += $item['count'];
            } else {
                $allData[$item['date']] = [
                    'offSite' => $item['count'],
                    'total'   => $item['count'],
                ];
            }
        }

        $data = [
            [
                '投递',
                '日期',
                '站内',
                '站外',
                '总数',
            ],
        ];

        // 先倒叙
        // $allData = array_reverse($allData);

        foreach ($allData as $k => $allDatum) {
            $data[] = [
                '',
                $k,
                $allDatum['onSite'],
                $allDatum['offSite'],
                $allDatum['total'],
            ];
        }

        return $data;
    }

    // 入库人才
    public function getResumeLibrary()
    {
        // 日期，昨天
        $data = [
            [
                '入库人才',
                '日期',
                '人才库人才',
                '75%简历完善人才',
            ],
            [
                '',
                date('Y-m-d', strtotime('-1 day')),
                BaseResume::find()
                    ->where(['is_resume_library' => 1])
                    ->count(),
                BaseResume::find()
                    ->andWhere([
                        '>=',
                        'complete',
                        75,
                    ])
                    ->count(),
            ],
        ];

        return $data;
    }

    // 人才分布
    public function getResumeDistribution()
    {
        $educationGroup = BaseResume::find()
            ->select([
                'top_education_code',
                'count(*) as count',
            ])
            ->groupBy('top_education_code')
            ->asArray()
            ->all();
        $data           = [
            [
                '人才分布',
                '日期',
                '未填写',
                '大专',
                '本科',
                '硕士',
                '博士',
                '其他',
            ],
            [
                '',
                date('Y-m-d', strtotime('-1 day')),
                $educationGroup[0]['count'] ?? 0,
                $educationGroup[1]['count'] ?? 0,
                $educationGroup[2]['count'] ?? 0,
                $educationGroup[3]['count'] ?? 0,
                $educationGroup[4]['count'] ?? 0,
                $educationGroup[5]['count'] ?? 0,
            ],

        ];

        return $data;
    }

    // 内容发布
    public function getContentRelease()
    {
        $cooperationAnnouncement = BaseAnnouncement::find()
            ->select('count(*) as count, date(first_release_time) as date')
            ->innerJoin(['b' => BaseCompany::tableName()], 'b.id = company_id')
            ->innerJoin(['c' => BaseArticle::tableName()], 'c.id = article_id')
            ->where([
                'is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
            ])
            ->andWhere([
                '>=',
                'first_release_time',
                $this->beginTime,
            ])
            ->groupBy('date(first_release_time)')
            ->asArray()
            ->all();

        $unCooperationAnnouncement = BaseAnnouncement::find()
            ->select('count(*) as count, date(first_release_time) as date')
            ->innerJoin(['b' => BaseCompany::tableName()], 'b.id = company_id')
            ->innerJoin(['c' => BaseArticle::tableName()], 'c.id = article_id')
            ->where([
                'is_cooperation' => BaseCompany::COOPERATIVE_UNIT_NO,
            ])
            ->andWhere([
                '>=',
                'first_release_time',
                $this->beginTime,
            ])
            ->groupBy('date(first_release_time)')
            ->asArray()
            ->all();

        $cooperationJob = BaseJob::find()
            ->select('count(*) as count, date(first_release_time) as date')
            ->innerJoin(['b' => BaseCompany::tableName()], 'b.id = company_id')
            ->where([
                'is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
            ])
            ->andWhere([
                '>=',
                'first_release_time',
                $this->beginTime,
            ])
            ->groupBy('date(first_release_time)')
            ->asArray()
            ->all();

        $unCooperationJob = BaseJob::find()
            ->innerJoin(['b' => BaseCompany::tableName()], 'b.id = company_id')
            ->select('count(*) as count, date(first_release_time) as date')
            ->where([
                'is_cooperation' => BaseCompany::COOPERATIVE_UNIT_NO,
            ])
            ->andWhere([
                '>=',
                'first_release_time',
                $this->beginTime,
            ])
            ->groupBy('date(first_release_time)')
            ->asArray()
            ->all();

        $allData = [];

        foreach ($cooperationJob as $item) {
            $allData[$item['date']] = [
                'cooperationJob' => $item['count'],
                'total'          => $item['count'],
            ];
        }

        foreach ($unCooperationJob as $item) {
            if (isset($allData[$item['date']])) {
                $allData[$item['date']]['unCooperationJob'] = $item['count'];
                $allData[$item['date']]['total']            += $item['count'];
            } else {
                $allData[$item['date']] = [
                    'noCooperationJob' => $item['count'],
                    'total'            => $item['count'],
                ];
            }
        }

        foreach ($cooperationAnnouncement as $item) {
            if (isset($allData[$item['date']])) {
                $allData[$item['date']]['cooperationAnnouncement'] = $item['count'];
                $allData[$item['date']]['total']                   += $item['count'];
            } else {
                $allData[$item['date']] = [
                    'cooperationAnnouncement' => $item['count'],
                    'total'                   => $item['count'],
                ];
            }
        }

        foreach ($unCooperationAnnouncement as $item) {
            if (isset($allData[$item['date']])) {
                $allData[$item['date']]['unCooperationAnnouncement'] = $item['count'];
                $allData[$item['date']]['total']                     += $item['count'];
            } else {
                $allData[$item['date']] = [
                    'noCooperationAnnouncement' => $item['count'],
                    'total'                     => $item['count'],
                ];
            }
        }

        $data = [
            [
                '内容发布',
                '日期',
                '合作单位公告',
                '合作单位职位',
                '非合作单位公告',
                '非合作单位职位',
            ],
        ];

        // 倒序dataList
        $this->dataList = array_reverse($this->dataList);

        foreach ($this->dataList as $date) {
            $data[] = [
                '',
                $date,
                $allData[$date]['cooperationAnnouncement'] ?? 0,
                $allData[$date]['cooperationJob'] ?? 0,
                $allData[$date]['unCooperationAnnouncement'] ?? 0,
                $allData[$date]['unCooperationJob'] ?? 0,
            ];
        }

        return $data;
    }

    // C端用户活跃
    public function resumeActivity()
    {
        $data = [
            [
                'C端用户活跃',
                '数据周期',
                '日活',
                '月活',
                '周活',
            ],
            [
                '',
                date('Y-m-d', strtotime('-1 day')),
                BaseMember::find()
                    ->where([
                        'type' => BaseMember::TYPE_PERSON,
                    ])
                    ->andWhere([
                        '>=',
                        'last_active_time',
                        date('Y-m-d 00:00:00', strtotime('-1 day')),
                    ])
                    ->count(),
                BaseMember::find()
                    ->where([
                        'type' => BaseMember::TYPE_PERSON,
                    ])
                    ->andWhere([
                        '>=',
                        'last_active_time',
                        date('Y-m-d 00:00:00', strtotime('-1 month')),
                    ])
                    ->count(),
                BaseMember::find()
                    ->where([
                        'type' => BaseMember::TYPE_PERSON,
                    ])
                    ->andWhere([
                        '>=',
                        'last_active_time',
                        date('Y-m-d 00:00:00', strtotime('-1 week')),
                    ])
                    ->count(),
            ],

        ];

        return $data;
    }

    // 投递活跃用户
    public function getJobApplyActivity()
    {
        $data = [
            [
                '投递活跃用户',
                '日期',
                '用户量',
            ],
        ];

        $jobApplyData = JobApplyRecord::find()
            ->select('count(distinct resume_id) as count, date(add_time) as date')
            ->where([
                '>=',
                'add_time',
                $this->beginTime,
            ])
            ->groupBy('date(add_time)')
            ->asArray()
            ->all();

        // 先倒序
        // $jobApplyData = array_reverse($jobApplyData);

        foreach ($jobApplyData as $item) {
            $data[] = [
                '',
                $item['date'],
                $item['count'],
            ];
        }

        return $data;
    }

    // B端用户活跃
    public function companyActivity()
    {
        $data = [
            [
                'b端用户活跃',
                '数据周期',
                '日活',
                '月活',
                '周活',
            ],
            [
                '',
                date('Y-m-d', strtotime('-1 day')),
                BaseMember::find()
                    ->where([
                        'type' => BaseMember::TYPE_COMPANY,
                    ])
                    ->andWhere([
                        '>=',
                        'last_active_time',
                        date('Y-m-d 00:00:00', strtotime('-1 day')),
                    ])
                    ->count(),
                BaseMember::find()
                    ->where([
                        'type' => BaseMember::TYPE_COMPANY,
                    ])
                    ->andWhere([
                        '>=',
                        'last_active_time',
                        date('Y-m-d 00:00:00', strtotime('-1 month')),
                    ])
                    ->count(),
                BaseMember::find()
                    ->where([
                        'type' => BaseMember::TYPE_COMPANY,
                    ])
                    ->andWhere([
                        '>=',
                        'last_active_time',
                        date('Y-m-d 00:00:00', strtotime('-1 week')),
                    ])
                    ->count(),
            ],

        ];

        return $data;
    }
}
