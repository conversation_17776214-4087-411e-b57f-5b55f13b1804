<?php

namespace common\service\stat;

use common\service\stat\AllCompanyJobApplyService;

class StateApplication
{
    static $instance;

    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    public function allCompanyJobApply()
    {
        (new AllCompanyJobApplyService())->run();
    }

    public function resumeList($params)
    {
        (new ResumeService())->run($params);
    }

    // 双会数据
    public function doubleMeetingResumeList($adminId, $isNow)
    {
        (new DoubleMeetingResumeService())->run($adminId, $isNow);
    }

    // 过去90天投递数据
    public function last90DayAllJobApply()
    {
        return (new DailyService())->setTimeLast90Day()
            ->allJobApply();
    }

    // 过去30天投递数据
    public function last30DayAllJobApply()
    {
        return (new DailyService())->setTimeLast30Day()
            ->allJobApply();
    }

    // 近10天订单数据
    public function last10DayOrder()
    {
        return (new OrderService())->setTimeLast10Day()
            ->getList();
    }


    // 昨天投递数据
    public function yesterdayAllJobApply()
    {
        return (new DailyService())->setTimeLastDay()
            ->allJobApply();
    }

    // 过去三天投递数据
    public function last3DayAllJobApply()
    {
        return (new DailyService())->setTimeLast3Day()
            ->allJobApply();
    }

    // 过去7天投递数据
    public function last7DayAllJobApply()
    {
        return (new DailyService())->setTimeLast7Day()
            ->allJobApply();
    }

    public function todayAllJobApply()
    {
        return (new DailyService())->setTimeToday()
            ->allJobApply();
    }

    public function last90DayResumeData()
    {
        return (new DailyService())->setTimeLast90Day()
            ->getResumeData();
    }

    public function last30DayResumeData()
    {
        return (new DailyService())->setTimeLast30Day()
            ->getResumeData();
    }

    public function last3DayResumeData()
    {
        return (new DailyService())->setTimeLast3Day()
            ->getResumeData();
    }

    public function invitationResultData($adminId)
    {
        (new InvitationResultService())->run($adminId);
    }

    public function getResumeDateList()
    {
        return (new DailyService())->getAllDataNewResumeData();
    }

    public function getJobApplyDateList()
    {
        return (new DailyService())->getAllJobApplyList();
    }

    public function getCompanyHomePageDate()
    {
        return (new DailyService())->getCompanyHomePageDate();
    }

    public function getResume20221102()
    {
        return (new Tmp())->getResume20221102();
    }

    public function getResume20221115()
    {
        return (new Tmp())->getResume20221115();
    }

    public function getResume20230131()
    {
        (new Tmp())->getResume20230131();
    }

    public function getResume20230220()
    {
        (new Tmp())->getResume20230220();
    }

    public function getResume20230418()
    {
        (new Tmp())->getResume20230418();
    }


    public function getResume20230426()
    {
        (new Tmp())->getResume20230426();
    }

    public function getResume20230518()
    {
        (new Tmp())->getResume20230518();
    }

    public function getResume202305181()
    {
        (new Tmp())->getResume202305181();
    }

    public function getResume20230524()
    {
        (new Tmp())->getResume20230524();
    }

    public function getResume20230625()
    {
        (new Tmp())->getResume20230625();
    }



}
