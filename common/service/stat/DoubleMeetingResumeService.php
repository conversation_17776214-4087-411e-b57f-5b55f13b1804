<?php

namespace common\service\stat;

use common\base\models\BaseArea;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeIntention;
use common\base\models\BaseResumeSetting;
use common\libs\Excel;
use common\libs\WxWork;
use frontendPc\models\Dictionary;

class DoubleMeetingResumeService extends BaseService
{

    private $jobId   = [];
    private $adminId = [];
    private $isNow   = false;
    private $endTime;
    private $beginTime;

    // 主要是统计双会的投递情况,定时推送给对于的人员

    public function run($adminId, $isNow)
    {
        if ($adminId) {
            if (is_array($adminId)) {
                $this->adminId = $adminId;
            } else {
                $this->adminId = [$adminId];
            }
        }
        $this->isNow = $isNow;
        $this->setParams();
        $this->getData();
        $this->send();
    }

    public function setTime($type)
    {
    }

    private function getData()
    {
        /**
         * 投递职位名称
         * 姓名
         * 性别
         * 手机号码
         * 最高学历
         * 最高学历毕业院校
         * 最高学历毕业学历
         * 最高学历毕业时间
         * 最高学历二级学院（有则填，无则空）
         * 最高学历学科（大类）
         * 最高学历学科（一级）
         * 最高学历学科（二级）
         * 目前居住地
         */

        $query = BaseResume::find()
            ->alias('a')
            ->innerJoin(['b' => BaseResumeSetting::tableName()], 'a.id=b.resume_id')
            ->innerJoin(['c' => BaseJobApply::tableName()], 'a.id=c.resume_id')
            ->innerJoin(['d' => BaseJob::tableName()], 'd.id=c.job_id')
            ->innerJoin(['e' => BaseMember::tableName()], 'e.id=a.member_id')
            ->select([
                'a.id as aid',
                'a.name as name',
                'mobile',
                'gender',
                'd.name as jobName',
                'top_education_code',
                'last_education_id',
                'is_hide_resume',
                'residence',
                'title_id',
                'work_status',
                'c.add_time',
            ])
            ->where(['c.job_id' => $this->jobId]);

        // if (!$this->isNow) {
        //     $query->andWhere([
        //         '<=',
        //         'c.add_time',
        //         CUR_DATE,
        //     ]);
        // }

        $query->andFilterWhere([
            '>=',
            'c.add_time',
            $this->beginTime,
        ]);

        $query->andFilterWhere([
            '<=',
            'c.add_time',
            $this->endTime,
        ]);

        $list = $query->asArray()
            ->groupBy('d.id,a.id')
            ->all();

        $headers = [
            '投递职位名称',
            '姓名',
            '性别',
            '手机号码',
            '最高学历',
            '最高学历毕业院校',
            '最高学历毕业时间',
            '最高学历二级学院（有则填，无则空）',
            '最高学历学科（大类）',
            '最高学历学科（一级）',
            '最高学历学科（二级）',
            '目前居住地',
            '意向就业城市',
            '求职状态',
            '投递日期',
        ];

        $data = [];

        $params = \Yii::$app->params['baiduZZ'];
        $pcSite = $params['pcSite'];

        foreach ($list as $k => $val) {
            $val['id'] = $val['aid'];
            // 拿到最高教育经历的数据
            $educationId = BaseResumeEducation::getTopEducationId($val['id']);
            $education   = BaseResumeEducation::find()
                ->where([
                    'id' => $educationId,
                ])
                ->asArray()
                ->one();

            $school  = $education['school'];
            $endDate = $education['end_date'];
            $college = $education['college'];

            $educationName = BaseDictionary::getEducationName($education['education_id']);
            $majorId       = $education['major_id'];
            $major         = BaseMajor::find()
                ->where([
                    'id' => $majorId,
                ])
                ->asArray()
                ->one();

            $majorLevel2 = BaseMajor::find()
                ->where([
                    'id' => $major['parent_id'],
                ])
                ->asArray()
                ->one();

            $majorLevel3 = BaseMajor::find()
                ->where([
                    'id' => $majorLevel2['parent_id'],
                ])
                ->asArray()
                ->one();

            // 找出所有的意向职位
            $intention = BaseResumeIntention::find()
                ->where([
                    'resume_id' => $val['id'],
                ])
                ->asArray()
                ->all();

            $allIntentionAreaArray = [];
            foreach ($intention as $item) {
                $intentionAreaId       = $item['area_id'];
                $intentionAreaArray    = explode(',', $intentionAreaId);
                $allIntentionAreaArray = array_merge($allIntentionAreaArray, $intentionAreaArray);
            }

            $areaArray = [];
            // 找出所有的意向职位的省份
            $intentionArea = BaseArea::find()
                ->select([
                    'name',
                ])
                ->where([
                    'id' => $allIntentionAreaArray,
                ])
                ->asArray()
                ->column();

            $intentionArea = implode(',', $intentionArea);

            // 所在地区
            $residenceCity = BaseArea::find()
                ->where([
                    'id' => $val['residence'],
                ])
                ->asArray()
                ->one();

            if ($residenceCity) {
                $residenceProvince = BaseArea::find()
                    ->where([
                        'id' => $residenceCity['parent_id'],
                    ])
                    ->asArray()
                    ->one();
            }

            $workStatusTxt = Dictionary::getJobStatusName($val['work_status']);
            $genderTxt     = BaseResume::GENDER_LIST[$val['gender']];

            // 求职状态

            /**
             * '投递职位名称',
             * '姓名',
             * '性别',
             * '手机号码',
             * '最高学历',
             * '最高学历毕业院校',
             * '最高学历毕业学历',
             * '最高学历毕业时间',
             * '最高学历二级学院（有则填，无则空）',
             * '最高学历学科（大类）',
             * '最高学历学科（一级）',
             * '最高学历学科（二级）',
             * '目前居住地',
             * '意向就业城市',
             * '求职状态',
             * '投递日期',
             */
            $data[] = [
                $val['jobName'],
                $val['name'],
                $genderTxt,
                $val['mobile'],
                $educationName,
                $school,
                $endDate,
                $college,
                $majorLevel3['name'],
                $majorLevel2['name'],
                $major['name'],
                $residenceProvince['name'] . $residenceCity['name'],
                $intentionArea,
                $workStatusTxt,
                substr($val['add_time'], 0, 10),
            ];
        }

        $excel = new Excel();

        $rs = $excel->export($data, $headers, CUR_DATE . '_双会投递数据_' . CUR_TIMESTAMP);

        $this->url = $rs;
    }

    protected function setParams()
    {
        // 这里拿双会的配置
        $params      = \Yii::$app->params['doubleMeetingActivity2022'];
        $this->jobId = $params['jobId'];
        if (!$this->adminId) {
            $this->adminId = $params['adminId'];
        }

        if ($this->isNow) {
            // 这里对于isNow做一个分发,转很换成type
            $type = $this->isNow;
            switch ($type) {
                case 1:
                    // 全部数据
                    $this->endTime = CUR_DATETIME;
                    break;
                case 2:
                    // 今天数据
                    $this->beginTime = CUR_DATE . ' 00:00:00';
                    $this->endTime   = CUR_DATETIME;
                    break;
                case 3:
                    // 昨天数据
                    $this->beginTime = date('Y-m-d', strtotime('-1 day')) . ' 00:00:00';
                    $this->endTime   = date('Y-m-d', strtotime('-1 day')) . ' 23:59:59';
                    break;
                case 4:
                    // 近7天
                    $this->beginTime = date('Y-m-d', strtotime('-7 day')) . ' 00:00:00';
                    $this->endTime   = date('Y-m-d', strtotime('-1 day')) . ' 23:59:59';
                    break;
                case 5:
                    // 近30天
                    $this->beginTime = date('Y-m-d', strtotime('-30 day')) . ' 00:00:00';
                    $this->endTime   = date('Y-m-d', strtotime('-1 day')) . ' 23:59:59';
                    break;
            }
        }
    }

    private function send()
    {
        $wxWork = WxWork::getInstance();
        // 找到操作人的信息

        $content = $this->url;

        foreach ($this->adminId as $item) {
            $wxWork->card($item, '网站双会报名数据', '该数据为' . $this->beginTime . '到' . $this->endTime . '为止的数据', $content);
        }
    }

}
