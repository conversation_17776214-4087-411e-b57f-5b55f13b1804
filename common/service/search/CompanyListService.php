<?php

namespace common\service\search;

use common\base\BaseActiveRecord;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyCollect;
use common\base\models\BaseCompanyGroupScoreSystem;
use common\base\models\BaseCompanyStatData;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobCollect;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseJobTopConfig;
use common\base\models\BaseJobWelfareRelation;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseMemberActionLog;
use common\base\models\BaseResume;
use common\base\models\BaseTrade;
use common\base\models\BaseWelfareLabel;
use common\components\MessageException;
use common\helpers\ArrayHelper;
use common\helpers\IpHelper;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use common\libs\Cache;
use common\service\jobTop\DailyServer;
use common\service\jobTop\JobTopApplication;
use common\service\meilisearch\job\SearchService;
use Yii;
use yii\base\Exception;
use yii\helpers\Url;

class CompanyListService extends BaseService
{

    private $originParams;
    private $key;

    private $sort;

    public $companyIds = [];

    public $returnData = [];

    const             CACHE_TIME                = 300;
    const             DEFAULT_PAGE_SIZE         = 20;
    const             NO_PARAMS_CACHE_PAGE      = 40;
    const             NO_PARAMS_CACHE_PAGE_SIZE = self::DEFAULT_PAGE_SIZE * self::NO_PARAMS_CACHE_PAGE;
    const             NO_PARAMS_CACHE_KEY       = Cache::ALL_COMPANY_LIST_NO_PARAMS_LIST_KEY;

    const             DEFAULT_PAGE_TOTAL = 400;

    const SPECIAL_SYMBOL = [
        '-',
        '团队',
        '课题组',
    ];

    const PARAMS_KEY_LIST = [
        'page'             => [],
        'memberId'         => [],
        'areaId'           => [],
        'companyType'      => [],
        'companyNature'    => [],
        'companyScaleType' => [],
        'industryId'       => [],
        'keyword'          => [],
        'sort'             => [],
    ];

    public function run($params, $type)
    {
        $this->originParams = $params;
        $this->type         = $type;

        $this->filter();
        $this->setKey();
        $this->check();
        //
        BaseJob::openDb2();
        $isDB2 = true;
        $this->getData();

        if ($isDB2) {
            BaseJob::closeDb2();
        }

        $this->afterRun();

        return $this->returnData;
    }

    /**
     * 这个方法是用于保存一下职位列表的无参数缓存(暂时考虑缓存40页,定时更新一下,这样可以避免用户在单纯翻页的时候也需要实时获取)
     */
    public function setNoParamsListCache()
    {
        // resume
        // update
        // default
        $this->pageSize = self::NO_PARAMS_CACHE_PAGE_SIZE;
        // sort保留两种就好了
        $this->sort               = 'resume';
        $this->params['pageSize'] = $this->pageSize;
        $this->params['page']     = 1;
        $key                      = self::NO_PARAMS_CACHE_KEY . ':' . $this->sort;
        $data                     = $this->search();

        // 20为一个page
        for ($i = 0; $i < self::NO_PARAMS_CACHE_PAGE; $i++) {
            $list     = $data['list'];
            $pageList = array_slice($list, $i * self::DEFAULT_PAGE_SIZE, self::DEFAULT_PAGE_SIZE);
            $page     = $i + 1;
            $total    = count($list);
            $saveData = [
                'list'        => $pageList,
                'pageSize'    => self::DEFAULT_PAGE_SIZE,
                'currentPage' => $page,
                'totalNum'    => $total,
            ];
            $cacheKey = $key . ':' . $page;
            Cache::set($cacheKey, json_encode($saveData));
        }

        $this->sort = 'update';
        $key        = self::NO_PARAMS_CACHE_KEY . ':' . $this->sort;

        $data = $this->search();

        // 20为一个page
        for ($i = 0; $i < self::NO_PARAMS_CACHE_PAGE; $i++) {
            $list     = $data['list'];
            $pageList = array_slice($list, $i * self::DEFAULT_PAGE_SIZE, self::DEFAULT_PAGE_SIZE);
            $page     = $i + 1;
            $total    = count($list);
            $saveData = [
                'list'        => $pageList,
                'pageSize'    => self::DEFAULT_PAGE_SIZE,
                'currentPage' => $page,
                'totalNum'    => $total,
            ];
            $cacheKey = $key . ':' . $page;
            Cache::set($cacheKey, json_encode($saveData));
        }

        $this->sort = 'default';
        $key        = self::NO_PARAMS_CACHE_KEY . ':' . $this->sort;

        $data = $this->search();

        // 20为一个page
        for ($i = 0; $i < self::NO_PARAMS_CACHE_PAGE; $i++) {
            $list     = $data['list'];
            $pageList = array_slice($list, $i * self::DEFAULT_PAGE_SIZE, self::DEFAULT_PAGE_SIZE);
            $page     = $i + 1;
            $total    = count($list);
            $saveData = [
                'list'        => $pageList,
                'pageSize'    => self::DEFAULT_PAGE_SIZE,
                'currentPage' => $page,
                'totalNum'    => $total,
            ];
            $cacheKey = $key . ':' . $page;
            Cache::set($cacheKey, json_encode($saveData));
        }
    }

    private function setKey()
    {
        $params = $this->params;
        if (count($params) > 0) {
            ksort($params);
            $stringKey = ArrayHelper::arrayToStringKey($params);
            $this->key = $stringKey;
        } else {
            $this->key = '0';
        }
    }

    /**
     * 其实我们做了两套缓存方案,一套是用于无参数的,一套是用于有参数的
     * 无参数的,我们只保留了800条数据,所以,如果用户在翻页的时候,我们就不需要去查数据库了,直接从缓存中取就好了,但是这个页数应该是只有前40页
     */
    private function getCacheData()
    {
        $params = $this->params;
        $sort   = $params['sort'];

        if ($params['page'] || $params['p']) {
            $page = $params['page'] ?: $params['p'];
            unset($params['page']);
            unset($params['pageSize']);
            unset($params['sort']);
            unset($params['memberId']);
            // 这个时候看看params里面还有没有参数
            if (count($params) <= 0) {
                // 有参数的话,就不用缓存了,看看page
                if ($page <= self::NO_PARAMS_CACHE_PAGE) {
                    // 拿缓存,
                    $key      = self::NO_PARAMS_CACHE_KEY . ':' . $sort . ':' . $page;
                    $dataJson = Cache::get($key);
                    $data     = json_decode($dataJson, true);

                    return $data;
                }

                return null;
            }
        }

        $cacheKey = Cache::ALL_COMPANY_LIST_PARAMS_LIST_KEY . '_' . $this->key;

        $cacheData = Cache::get($cacheKey);

        if ($cacheData) {
            $cacheData = json_decode($cacheData, true);
        } else {
            $cacheData = [
                'list'        => [],
                'pageSize'    => 20,
                'currentPage' => $this->page,
                'totalNum'    => 0,
            ];
        }

        return $cacheData;
    }

    private function getData()
    {
        $cacheData = $this->getCacheData();
        if ($cacheData['list']) {
            $data = $cacheData;
        } else {
            $this->total    = self::DEFAULT_PAGE_TOTAL;
            $this->pageSize = self::DEFAULT_PAGE_SIZE;
            $data           = $this->search();
        }
        // 处理收藏的信息
        $list = $data['list'];
        if (PLATFORM == 'PC') {
            foreach ($list as &$item) {
                //判断该单位是否被收藏了
                if (!empty($this->params['memberId'])) {
                    $isCollect = BaseCompanyCollect::checkIsCollect($this->params['memberId'], $item['companyId']);
                    if ($isCollect) {
                        $item['isCollect'] = BaseCompanyCollect::COLLECT_STATUS_YES;
                    } else {
                        $item['isCollect'] = BaseCompanyCollect::COLLECT_STATUS_NO;
                    }
                } else {
                    $item['isCollect'] = BaseCompanyCollect::COLLECT_STATUS_NO;
                }
            }
        }

        $data['list'] = $list;

        $this->returnData = $data;
    }

    /**
     * 置顶查询和职位列表查询统一放到这里
     * @param $params
     * @param $topJobIds
     * @return array
     */
    public function search()
    {
        $searchModel = BaseCompany::find()
            ->alias('c')
            ->innerJoin(['t' => BaseCompanyStatData::tableName()], 'c.id=t.company_id')
            ->innerJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()], 'c.group_score_system_id = cgss.id')
            ->where([
                'status'  => BaseCompany::STATUS_ACTIVE,
                'is_hide' => BaseCompany::IS_HIDE_NO,
            ]);

        if ($this->params['keyword']) {
            // 这里是关键词查询，对于meiliseach而言，有一些符号它是不拆的，所以如果keyword中含有这些符号，就会导致查询不到数据，这种情就直接走我们原来的like就可以了，
            if (strpbrk($this->params['keyword'], implode('', self::SPECIAL_SYMBOL))) {
                $searchModel->andFilterWhere([
                    'like',
                    'c.full_name',
                    $this->params['keyword'],
                ]);//关键词查询
            } else {
                // 这里是走meilisearch的
                $companyIds = self::searchKeyword();
                if ($companyIds) {
                    if (count($companyIds) >= 1000) {
                        $searchModel->andFilterWhere([
                            'like',
                            'c.full_name',
                            $this->params['keyword'],
                        ]);//关键词查询
                    } else {
                        $searchModel->andWhere(['c.id' => $companyIds]);
                    }
                } else {
                    $searchModel->andWhere(['c.id' => 0]);
                }
            }

            // $companyIds = self::searchKeyword();
            // if ($companyIds) {
            //     if (count($companyIds) >= 1000) {
            //         $searchModel->andFilterWhere([
            //             'like',
            //             'c.full_name',
            //             $this->params['keyword'],
            //         ]);//关键词查询
            //     } else {
            //         $searchModel->andWhere(['c.id' => $companyIds]);
            //     }
            // } else {
            //     $searchModel->andWhere(['c.id' => 0]);
            // }
            // $searchModel->andFilterWhere([
            //     'like',
            //     'c.full_name',
            //     $this->params['keyword'],
            // ]);//关键词查询
        }

        if ($this->params['areaId']) {
            // 城市的查询
            $searchModel->andWhere(['c.city_id' => $this->params['areaId']]);
        }

        $searchModel->andFilterWhere(['c.scale' => $this->params['companyScaleType']]);                       //单位规模查询
        $searchModel->andFilterWhere(['c.industry_id' => $this->params['industryId']]);
        $searchModel->andFilterWhere(['c.type' => $this->params['companyType']]);                       //单位类型查询
        $searchModel->andFilterWhere(['c.nature' => $this->params['companyNature']]);                       //单位性质

        //获取总数量
        $count = $searchModel->count();
        $count = $count > self::NO_PARAMS_CACHE_PAGE_SIZE ? self::NO_PARAMS_CACHE_PAGE_SIZE : $count;

        $pageSize = $this->params['pageSize'] ?: self::DEFAULT_PAGE_SIZE;

        $pages = BaseActiveRecord::setPage($count, $this->params['page'], $pageSize);

        $select = [
            'c.id',
            'c.id as companyId',
            "c.package_type as companyRole",
            'c.full_name as name',
            'c.logo_url',
            'c.scale',
            'c.nature',
            'c.industry_id',
            'c.member_id',
            'c.city_id as cityId',
            'c.type',
            'c.nature',
            'c.job_last_release_time',
            'c.is_cooperation as isCooperation',
            'c.welfare_label_ids',
            't.resume_view_rate',
            't.online_announcement_count',
            't.online_job_count',
        ];

        if ($this->companyIds) {
            // 先按照sort倒叙再按照companyIds的顺序来排序

            $sort = [
                'sort'                                                     => SORT_DESC,
                'FIELD(companyId,' . implode(',', $this->companyIds) . ')' => SORT_ASC,
            ];
        } else {
            //            switch ($this->sort) {
            //                case 'resume':
            //                    // 简历查看率
            //                    $sort = "t.resume_view_rate desc,sort desc,heat desc";
            //                    break;
            //                case 'update':
            //                    $sort = 'c.job_last_release_time desc,sort desc';
            //                    break;
            //                case 'default':
            //                    //推荐排序:单位热度=最近3个月人才投递次数*65%+单位主页累计收藏量*5%+当前在线职位数*30%；
            //                    $sort = "sort desc,heat desc";
            //                    break;
            //                default:
            //                    // 简历查看率
            //                    $sort = "t.resume_view_rate desc,sort desc,,heat desc";
            //                    break;
            //            }
            // 2.4 统一排序去除排序
            $sort = "cgss.score DESC,heat desc,c.id desc";
        }

        $list = $searchModel->select($select)
            ->orderBy($sort)
            ->asArray()
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->all();

        $memberLastLoginDate = [];
        if ($list) {
            $memberIds           = array_column($list, 'member_id');
            $memberLastLoginDate = BaseMember::find()
                ->andWhere(['id' => $memberIds])
                ->select([
                    'id',
                    'last_active_time',
                ])
                ->asArray()
                ->all();

            $memberLastLoginDate = array_column($memberLastLoginDate, 'last_active_time', 'id');
        }

        foreach ($list as $k => &$company) {
            // 这里格式化一下简历查看率
            //            if ($company['resume_view_rate'] < 0) {
            //                $company['viewingRate'] = '-';
            //            } elseif ($company['resume_view_rate'] >= '0.00') {
            //                $company['viewingRate'] = $company['resume_view_rate'] . '%';
            //            } else {
            //                $company['viewingRate'] = '-';
            //            }

            //
            //获取单位登录时间
            $lastLoginDate = $memberLastLoginDate[$company['member_id']];
            [
                ,
                $activeName, ,
            ] = BaseMember::getActiveText($lastLoginDate, BaseMember::ACTIVE_RULE_LIST_FOR_COMPANY);

            $company['lastLoginTime']      = $activeName;
            $company['announcementAmount'] = $company['online_announcement_count'] > 999 ? '999+' : ($company['online_announcement_count'] <= 0 ? '-' : $company['online_announcement_count']);
            $company['jobAmount']          = $company['online_job_count'] > 999 ? '999+' : ($company['online_job_count'] <= 0 ? '-' : $company['online_job_count']);
            //获取单位行业
            $company['industry'] = BaseTrade::getIndustryName($company['industry_id']);
            //获取单位规模
            $company['scale'] = BaseDictionary::getCompanyScaleName($company['scale']);
            //获取单位类型
            $company['type'] = BaseDictionary::getCompanyTypeName($company['type']);
            //获取单位性质
            $company['nature'] = BaseDictionary::getCompanyNatureName($company['nature']);

            //获取单位logo
            $company['logoUrl'] = BaseCompany::getLogoFullUrl($company['logo_url']);

            $company['areaName'] = BaseArea::getAreaName($company['cityId']);

            //获取单位详情url
            $company['url'] = UrlHelper::createCompanyDetailPath($company['companyId']);
        }

        $data = [
            'list'        => $list,
            'pageSize'    => $pageSize,
            'currentPage' => $this->page,
            'totalNum'    => $count * 1,
        ];

        $this->returnData = $data;
        $this->setCacheData($data);

        return $data;
    }

    private function setCacheData($data)
    {
        if ($this->page > 10) {
            return null;
        }

        $cacheKey = Cache::ALL_COMPANY_LIST_PARAMS_LIST_KEY . '_' . $this->key;

        Cache::set($cacheKey, json_encode($data), self::CACHE_TIME);
    }

    private function check()
    {
        // 这里主要是对于ip做一个限制,避免恶意刷,一分钟内,同一个ip,只能查询10次,超过就不允许了

        try {
            $ip = IpHelper::getIp();
        } catch (\Exception $e) {
            $ip = '';
        }

        $paramIpList = \Yii::$app->params['ipWhiteList'];

        // 非正式环境就直接返回true就可以了
        if (Yii::$app->params['environment'] != 'prod') {
            return true;
        }

        if (in_array($ip, $paramIpList)) {
            return true;
        }

        $key       = Cache::ALL_IP_WHITE_LIST_COMPANY_SEARCH_KEY . ':' . $ip;
        $minute    = date('YmdHi');
        $countData = json_decode(Cache::get($key), true);
        // 同一分钟不允许超过10次

        $value = $countData[$minute] + 1;

        if ($value > 60) {
            throw new MessageException('您搜索的太频繁了');
        }
        $countData[$minute] = $value;
        Cache::set($key, json_encode($countData), 3600);

        return true;
    }

    public function filter()
    {
        $originParams             = $this->originParams;
        $this->page               = $originParams['page'] ?? 1;
        $this->pageSize           = $originParams['pageSize'] ?? self::DEFAULT_PAGE_SIZE;
        $this->sort               = $originParams['sort'] ?? 'resume';//看代码没有排序默认是走new
        $this->params['page']     = $this->page;
        $this->params['sort']     = $this->sort;
        $this->params['pageSize'] = $this->pageSize;

        // 首先去掉一些不符合的搜索参数
        // 在这里重新组装一下参数,因为不是所有参数都是合法的
        // page
        // areaId
        // companyType
        // companyNature
        // companyScaleType
        // industryId
        // keyword
        // sort

        // 把需要的参数塞到params里面去
        foreach (self::PARAMS_KEY_LIST as $key => $item) {
            // 一个一个过滤数据
            switch ($key) {
                case 'keyword':
                    $keyword = $originParams['keyword'];
                    if (!empty($keyword)) {
                        $this->handelKeyword($keyword);
                    }
                    break;
                case 'areaId':
                    $areaId = $originParams['areaId'];
                    if (!empty($areaId)) {
                        $this->handelAreaId($areaId);
                    }
                    break;
                case 'companyType':
                    $companyType = $originParams['companyType'];
                    if (!empty($companyType)) {
                        $this->handelCompanyType($companyType);
                    }
                    break;
                case 'companyNature':
                    $companyNature = $originParams['companyNature'];
                    if (!empty($companyNature)) {
                        $this->handelCompanyNature($companyNature);
                    }
                    break;
                case 'companyScaleType':
                    $scale = $originParams['companyScaleType'];
                    if (!empty($scale)) {
                        $this->handelScale($scale);
                    }
                    break;
                case 'industryId':
                    $industryId = $originParams['industryId'];
                    if (!empty($industryId)) {
                        $this->handelIndustryId($industryId);
                    }
                    break;
                case 'memberId':
                    $memberId = $originParams['memberId'];
                    if (!empty($memberId)) {
                        $this->handelMemberId($memberId);
                    }
                    break;
                case 'page':
                    $page = $originParams['page'];
                    break;
            }
        }
    }

    private function searchKeyword()
    {
        $keyword     = $this->keyword;
        $meilisearch = new \common\service\meilisearch\company\SearchService();
        $companyIds  = $meilisearch->setKeyword($keyword)
            ->run();

        $this->companyIds = $companyIds;

        return $companyIds;
    }

    private function handelKeyword($keyword)
    {
        // 只能是数字或者字母或者中文字符?
        $keyword = trim($keyword);
        if (empty($keyword)) {
            return '';
        }

        $this->params['keyword'] = $keyword;
        $this->keyword           = $keyword;
    }

    private function handelAreaId($areaId)
    {
        // 先把逗号转成下划线
        $areaId = str_replace(',', '_', $areaId);
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id
        $areaIdArr = explode('_', $areaId);
        // 去掉空的和非数字的
        $areaIdArr = array_filter($areaIdArr, function ($item) {
            return is_numeric($item);
        });

        if (PLATFORM != 'MINI') {
            if (count($areaIdArr) > 5) {
                throw new MessageException('最多只能选择5个地区');
            }
        }

        // 把地区都下沉到第二级
        $areaIds = BaseArea::getCityIds($areaIdArr);

        $this->params['areaId'] = $areaIds;

        return $areaId;
    }

    private function handelCompanyNature($companyNature)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id
        $companyNature    = str_replace(',', '_', $companyNature);
        $companyNatureArr = explode('_', $companyNature);
        // 去掉空的和非数字的
        $companyNatureArr = array_filter($companyNatureArr, function ($item) {
            return is_numeric($item);
        });

        if (PLATFORM != 'MINI') {
            if (count($companyNatureArr) > 5) {
                throw new MessageException('最多只能选择5个单位性质');
            }
        }

        $this->params['companyNature'] = $companyNatureArr;

        return $companyNature;
    }

    private function handelCompanyType($companyType)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id
        $companyType    = str_replace(',', '_', $companyType);
        $companyTypeArr = explode('_', $companyType);
        // 去掉空的和非数字的
        $companyTypeArr = array_filter($companyTypeArr, function ($item) {
            return is_numeric($item);
        });

        if (PLATFORM != 'MINI') {
            if (count($companyTypeArr) > 5) {
                throw new MessageException('最多只能选择5个单位类型');
            }
        }

        $this->params['companyType'] = $companyTypeArr;

        return $companyType;
    }

    private function handelMajorId($majorId)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id
        $majorId    = str_replace(',', '_', $majorId);
        $majorIdArr = explode('_', $majorId);
        // 去掉空的和非数字的
        $majorIdArr = array_filter($majorIdArr, function ($item) {
            return is_numeric($item);
        });

        if (PLATFORM != 'MINI') {
            if (count($majorIdArr) > 5) {
                throw new MessageException('最多只能选择5个专业');
            }
        }

        $this->params['majorId'] = $majorIdArr;

        return $majorId;
    }

    private function handelEducationType($educationType)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id
        // $educationTypeArr = explode('_', $educationType);
        // // 去掉空的和非数字的
        // $educationTypeArr = array_filter($educationTypeArr, function ($item) {
        //     return is_numeric($item);
        // });
        //
        if (PLATFORM != 'MINI') {
            // if (count($educationTypeArr) > 5) {

            //     throw new MessageException('最多只能选择5个学历');
        }
        // }

        $educationTypeList = BaseDictionary::getEducationValueByKey($educationType);
        if (is_string($educationTypeList)) {
            $educationTypeList = explode(',', $educationTypeList);
        }

        $this->params['educationType'] = $educationTypeList;

        return $educationType;
    }

    private function handelIndustryId($industryId)
    {
        // 只能一个并且需要是数字
        if (!is_numeric($industryId)) {
            throw new MessageException('行业类别必须是数字');
        }

        // 行业类别是两级的,所以我们需要把下面的都下沉到第二级
        $trade = BaseTrade::findOne($industryId);

        if ($trade->level == 1) {
            $this->params['industryId'] = BaseTrade::find()
                ->select('id')
                ->where(['parent_id' => $industryId])
                ->column();
        } else {
            $this->params['industryId'] = $industryId;
        }

        return $industryId;
    }

    private function handelScale($scale)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id,并且只能是1~7
        $scale = str_replace(',', '_', $scale);

        $scaleArr = explode('_', $scale);
        // 去掉空的和非数字的
        $scaleArr = array_filter($scaleArr, function ($item) {
            return is_numeric($item) && $item >= 1 && $item <= 7;
        });

        if (count($scaleArr) > 7) {
            throw new MessageException('最多只能选择7个公司规模');
        }

        $this->params['companyScaleType'] = $scaleArr;

        return $scale;
    }

    private function handelMemberId($memberId)
    {
        $this->params['memberId'] = $memberId;

        return $memberId;
    }

}
