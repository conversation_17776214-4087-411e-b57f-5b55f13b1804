<?php

namespace common\service\search;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementAreaRelation;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleColumn;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyGroupScoreSystem;
use common\base\models\BaseDictionary;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseJob;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseMajor;
use common\base\models\BaseTrade;
use common\base\models\BaseWelfareLabel;
use common\components\MessageException;
use common\helpers\ArrayHelper;
use common\helpers\DebugHelper;
use common\helpers\IpHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\libs\Cache;
use Yii;
use yii\base\Exception;

class AnnouncementListService extends BaseService
{

    private $originParams;
    private $key;

    private $announcementList;
    private $sort            = 'new';
    private $total;
    private $limit;
    private $offset;
    private $publicSelectArr = [];

    const             CACHE_TIME                = 300;
    const             DEFAULT_PAGE_SIZE         = 20;
    const             NO_PARAMS_CACHE_PAGE      = 40;
    const             NO_PARAMS_CACHE_PAGE_SIZE = self::DEFAULT_PAGE_SIZE * self::NO_PARAMS_CACHE_PAGE;
    const             NO_PARAMS_CACHE_KEY       = Cache::ALL_ANNOUNCEMENT_LIST_NO_PARAMS_LIST_KEY;

    // public $testCompanyId;

    // 用于临时存放一些字典的名字,不用每次都去查
    private $tmpDictionaryNameList = [];

    // 现在实际上只有地区,学科,单位类型,公告类型,职位类型和关键字
    const PARAMS_KEY_LIST = [
        'majorId'          => [],
        'areaId'           => [],
        'companyType'      => [],
        'type'             => [],
        'keyword'          => [],
        'page'             => [],
        'jobType'          => [],
        'pageSize'         => [],
        'isEstablishment'  => [],
        'announcementHeat' => [],
        'sort'             => [],
        'educationType'    => [],
    ];

    public function run($params, $type)
    {
        $this->originParams = $params;

        $this->type = $type;
        $this->filter();
        $this->setKey();
        $this->check();
        $this->getData();

        $this->afterRun();

        return [
            'list' => $this->announcementList,
        ];
    }

    /**
     * 这个方法是用于保存一下职位列表的无参数缓存(暂时考虑缓存40页,定时更新一下,这样可以避免用户在单纯翻页的时候也需要实时获取)
     */
    public function setNoParamsListCache()
    {
        $this->pageSize = self::NO_PARAMS_CACHE_PAGE_SIZE;
        // sort保留两种就好了
        $this->sort               = 'new';
        $this->params['pageSize'] = $this->pageSize;
        $this->params['page']     = 1;
        $key                      = self::NO_PARAMS_CACHE_KEY . ':' . $this->sort;
        $list                     = $this->search();

        // 20为一个page
        for ($i = 0; $i < self::NO_PARAMS_CACHE_PAGE; $i++) {
            $pageList = array_slice($list, $i * self::DEFAULT_PAGE_SIZE, self::DEFAULT_PAGE_SIZE);
            $page     = $i + 1;
            $total    = count($list);
            $data     = [
                'list'  => $pageList,
                'total' => $total,
            ];
            $cacheKey = $key . ':' . $page;
            Cache::set($cacheKey, json_encode($data));
        }
    }

    private function setKey()
    {
        $params = $this->params;
        if (count($params) > 0) {
            ksort($params);
            $stringKey = ArrayHelper::arrayToStringKey($params);
            $this->key = $stringKey;
        } else {
            $this->key = '0';
        }
    }

    /**
     * 其实我们做了两套缓存方案,一套是用于无参数的,一套是用于有参数的
     * 无参数的,我们只保留了800条数据,所以,如果用户在翻页的时候,我们就不需要去查数据库了,直接从缓存中取就好了,但是这个页数应该是只有前40页
     */
    private function getCacheData()
    {
        $params = $this->params;
        $sort   = $params['sort'];
        unset($params['sort']);
        if ($params['page'] || $params['p']) {
            $page = $params['page'] ?: $params['p'];
            unset($params['page']);
            unset($params['pageSize']);
            // 这个时候看看params里面还有没有参数
            if (count($params) <= 0) {
                // 有参数的话,就不用缓存了,看看page
                if ($page <= self::NO_PARAMS_CACHE_PAGE) {
                    // 拿缓存,
                    $key      = self::NO_PARAMS_CACHE_KEY . ':' . $sort . ':' . $page;
                    $dataJson = Cache::get($key);
                    $data     = json_decode($dataJson, true);
                    $allList  = $data['list'];
                    $total    = $data['total'];
                    // 根据page来拿实际所需数据
                    $start = ($page - 1) * $this->pageSize;

                    return [
                        'list'  => $allList,
                        'total' => $total,
                    ];
                }

                return null;
            }
        }

        $cacheKey  = Cache::ALL_ANNOUNCEMENT_LIST_PARAMS_LIST_KEY . '_' . $this->key;
        $cacheData = Cache::get($cacheKey);

        if ($cacheData) {
            $cacheData = json_decode($cacheData, true);
        } else {
            $cacheData = [
                'list'  => [],
                'total' => 0,
            ];
        }

        return $cacheData;
    }

    private function setCacheData($data)
    {
        if ($this->page > 1) {
            return null;
        }
        if ($this->sort && $this->sort != 'new') {
            return null;
        }
        $cacheKey = Cache::ALL_ANNOUNCEMENT_LIST_PARAMS_LIST_KEY . '_' . $this->key;

        Cache::set($cacheKey, json_encode($data), self::CACHE_TIME);
    }

    private function setPublicSelectArr()
    {
        $this->publicSelectArr = [
            'a.id',
            'a.title',
            'b.status',
            'b.refresh_time',
            'c.full_name',
            'home_column_id',
            'a.highlights_describe',
        ];
    }

    private function getData()
    {
        $cacheData = $this->getCacheData();
        if ($cacheData['list']) {
            $list = $cacheData['list'];
        } else {
            $this->pageSize = self::DEFAULT_PAGE_SIZE;
            $list           = $this->search();
        }

        if (!$cacheData['list']) {
            $this->setCacheData([
                'list' => $list,
            ]);
        }

        $this->announcementList = $list;
    }

    /**
     * 置顶查询和职位列表查询统一放到这里
     * @param $params
     * @param $topJobIds
     * @return \yii\db\ActiveQuery
     */
    public function setPublicQuery($params = [], $topJobIds = [])
    {
        if (empty($this->params) && !empty($topJobIds)) {
            //从职位置顶那边过来的
            $this->params = $params;
        }
        $mainQuery = BaseAnnouncement::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'a.article_id = b.id')
            ->innerJoin(['c' => BaseCompany::tableName()], 'a.company_id = c.id')
            ->innerJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()], 'c.group_score_system_id = cgss.id')
            ->where([
                'b.is_show'   => BaseArticle::IS_SHOW_YES,
                'b.status'    => [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
            ]);

        if (Yii::$app->params['testCompanyId']) {
            $mainQuery->andWhere([
                'not in',
                'c.id',
                Yii::$app->params['testCompanyId'],
            ]);
        }

        if ($this->params['jobType'] || $this->params['educationType']) {
            $mainQuery->innerJoin(['j' => BaseJob::tableName()], 'a.id = j.announcement_id');
            $mainQuery->andWhere([
                'j.is_show' => BaseJob::IS_SHOW_YES,
                'j.status'  => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ]);
        }

        // 有地区中间表了
        if ($this->params['areaId']) {
            $mainQuery->innerJoin(['ra' => BaseAnnouncementAreaRelation::tableName()], 'a.id = ra.announcement_id');
            $mainQuery->andWhere([
                'ra.area_id' => $this->params['areaId'],
            ]);
        }

        // if ($this->params['areaId']) {
        //     $mainQuery->andFilterWhere(['j.city_id' => $this->params['areaId']]);
        // }

        if ($this->params['jobType']) {
            $mainQuery->andFilterWhere(['j.job_category_id' => $this->params['jobType']]);
        }

        if ($this->params['isEstablishment']) {
            $mainQuery->andWhere([
                'a.establishment_type' => [
                    BaseAnnouncement::ALL_ESTABLISHMENT,
                    BaseAnnouncement::PART_ESTABLISHMENT,
                ],
            ]);
        }

        // 2.4追加学历筛选
        if ($this->params['educationType']) {
            $mainQuery->andFilterWhere(['j.education_type' => $this->params['educationType']]);
        }

        if ($this->params['announcementHeat']) {
            $mainQuery->andFilterWhere(['a.announcement_heat_type' => $this->params['announcementHeat']]);
        }

        if ($this->params['majorId']) {
            $mainQuery->innerJoin(['d' => BaseJobMajorRelation::tableName()], 'd.announcement_id = a.id');
            $mainQuery->andFilterWhere(['d.major_id' => $this->params['majorId']]);
        }

        // 单位性质companyNature
        $mainQuery->andFilterWhere(['c.nature' => $this->params['companyNature']]);
        // 单位的类型
        $mainQuery->andFilterWhere(['c.type' => $this->params['companyType']]);
        // 关键字 like
        if ($this->params['keyword']) {
            $mainQuery->andFilterWhere([
                'like',
                'a.title',
                $this->params['keyword'],
            ]);
        }
        // type(主栏目)
        if ($this->params['type']) {
            $mainQuery->innerJoin(['e' => BaseArticleColumn::tableName()], 'b.id = e.article_id');
            $mainQuery->andWhere(['e.column_id' => $this->params['type']]);
        }

        // 小程序
        if ($this->type == self::TYPE_MINI_ANNOUNCEMENT_LIST) {
            $mainQuery->andFilterWhere(['a.is_miniapp' => BaseAnnouncement::IS_MINIAPP_YES]);
        }

        // 合作单位
        if ($this->params['isCooperation']) {
            $mainQuery->andFilterWhere(['c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES]);
        }

        return $mainQuery;
    }

    private function search()
    {
        $mainQuery = $this->setPublicQuery();

        //设置公告的查询字段
        $this->setPublicSelectArr();

        $this->limit  = $this->params['pageSize'] ?: self::DEFAULT_PAGE_SIZE;
        $this->offset = $this->params['pageSize'] * ($this->params['page'] - 1) ?: 0;

        // 有部分字段搜索的时候,需要关联表,就会导致需要分组才是对的数据
        if ($this->params['educationType'] || $this->params['areaId'] || $this->params['jobType'] || $this->params['majorId'] || $this->params['isEstablishment']) {
            $mainQuery->groupBy('a.id');
        }

        $list = $mainQuery->select($this->publicSelectArr)
            //            ->orderBy('b.status,b.refresh_date desc,c.sort desc,b.refresh_time desc,b.refresh_time desc')
            ->orderBy('b.status,b.refresh_date desc,a.is_first_release asc,cgss.score desc,a.id desc')
            ->asArray()
            ->offset($this->offset)
            ->limit($this->limit)
            ->all();

        foreach ($list as $k => &$item) {
            // 处理一下时间
            $item['time'] = TimeHelper::formatDateByYear($item['refresh_time']);
            // 找学历
            $item['establishmentType'] = BaseAnnouncement::getEstablishmentType($item['id']);
            $item['education']         = trim(BaseJob::getAnnouncementJobEducationType($item['id']));
            $item['recruitAmount']     = BaseJob::getAnnouncementJobRecruitAmount($item['id']) ?: 0;
            $item['area']              = BaseAnnouncement::getOnlineNewCityNameForMini($item['id']);
            $item['jobAmount']         = BaseJob::getAnnouncementJobAmount($item['id']) ?: 0;
        }

        return $list;
    }

    private function check()
    {
        // 这里主要是对于ip做一个限制,避免恶意刷,一分钟内,同一个ip,只能查询10次,超过就不允许了
        // $ip = $this->ip;

        // 搜索引擎的,就不需要
        // 找到当前执行的路由
        $url = Yii::$app->controller->action->uniqueId;

        if ($url == 'engine/index') {
            return true;
        }

        try {
            $ip = IpHelper::getIp();
        } catch (\Exception $e) {
            $ip = '';
        }

        $paramIpList = \Yii::$app->params['ipWhiteList'];

        // 非正式环境就直接返回true就可以了
        if (Yii::$app->params['environment'] != 'prod') {
            return true;
        }

        if (in_array($ip, $paramIpList)) {
            return true;
        }

        $key       = Cache::ALL_IP_WHITE_LIST_JOB_SEARCH_KEY . ':' . $ip;
        $minute    = date('YmdHi');
        $countData = json_decode(Cache::get($key), true);
        // 同一分钟不允许超过10次

        $value = $countData[$minute] + 1;

        if ($value > 60) {
            throw new MessageException('您搜索的太频繁了');
            // try {
            //     $ua = Yii::$app->request->getUserAgent();
            //     Cache::lPush('TMP:JOB_SEARCH_UA', json_encode([
            //         'ua'     => $ua,
            //         'ip'     => IpHelper::getIp(),
            //         'userId' => Yii::$app->user->id,
            //         'time'   => CUR_DATETIME,
            //         'url'    => \Yii::$app->request->url,
            //     ]));
            // } catch (\Exception $e) {
            //     // do nothing
            // }

        }
        $countData[$minute] = $value;
        Cache::set($key, json_encode($countData), 3600);

        return true;
    }

    public function filter()
    {
        $originParams = $this->originParams;

        $configKey = [
            'isEstablishment',
            'announcementHeat',
            'companyType',
        ];

        [
            $originParams,
            $groupTypeParamsNum,
        ] = ArrayHelper::groupParamsToRealParams($originParams, $configKey, true);
        // 小程序更多查询的数据只能弄到12个
        if ($groupTypeParamsNum > 12 && $this->type == AnnouncementListService::TYPE_MINI_ANNOUNCEMENT_LIST) {
            throw new MessageException('最多可选12项');
        }

        $this->page               = $originParams['page'] ?? 1;
        $this->pageSize           = $originParams['pageSize'] ?? self::DEFAULT_PAGE_SIZE;
        $this->params['page']     = $this->page;
        $this->params['sort']     = $this->sort;
        $this->params['pageSize'] = $this->pageSize;
        // 首先去掉一些不符合的搜索参数
        // 在这里重新组装一下参数,因为不是所有参数都是合法的
        // page
        // wageId
        // majorId
        // keyword
        // areaId
        // companyNature
        // companyType
        // educationType
        // natureType
        // scale
        // industryId
        // companyId
        // jobType
        // releaseTimeType
        // pageSize
        // sort

        // 把需要的参数塞到params里面去
        foreach (self::PARAMS_KEY_LIST as $key => $item) {
            // 一个一个过滤数据
            switch ($key) {
                case 'keyword':
                    $keyword = $originParams['keyword'];
                    if (!empty($keyword)) {
                        $this->handelKeyword($keyword);
                    }
                    break;
                case 'areaId':
                    $areaId = $originParams['areaId'];
                    if (!empty($areaId)) {
                        $this->handelAreaId($areaId);
                    }
                    break;
                case 'companyNature':
                    $companyNature = $originParams['companyNature'];
                    if (!empty($companyNature)) {
                        $this->handelCompanyNature($companyNature);
                    }
                    break;
                case 'companyType':
                    $companyType = $originParams['companyType'];
                    if (!empty($companyType)) {
                        $this->handelCompanyType($companyType);
                    }
                    break;
                case 'majorId':
                    $majorId = $originParams['majorId'];
                    if (!empty($majorId)) {
                        $this->handelMajorId($majorId);
                    }
                    break;
                case 'educationType':
                    $this->params['educationType'] = StringHelper::changeStrToFilterArr($originParams['educationType']);
                    break;
                case 'industryId':
                    $industryId = $originParams['industryId'];
                    if (!empty($industryId)) {
                        $this->handelIndustryId($industryId);
                    }
                    break;
                case 'experienceType':
                    $experienceType = $originParams['experienceType'];
                    if (!empty($experienceType)) {
                        $this->handelExperienceType($experienceType);
                    }
                    break;
                case 'releaseTimeType':
                    $releaseTimeType = $originParams['releaseTimeType'];
                    if (!empty($releaseTimeType)) {
                        $this->handelReleaseTimeType($releaseTimeType);
                    }
                    break;
                case 'wageId':
                    $wageId = $originParams['wageId'];
                    if (!empty($wageId)) {
                        $this->handelWageId($wageId);
                    }
                    break;
                case 'companyScaleType':
                    $scale = $originParams['companyScaleType'];
                    if (!empty($scale)) {
                        $this->handelScale($scale);
                    }
                    break;
                case 'natureType':
                    $natureType = $originParams['natureType'];
                    if (!empty($natureType)) {
                        $this->handelNatureType($natureType);
                    }
                    break;
                case 'titleType':
                    $titleType = $originParams['titleType'];
                    if (!empty($titleType)) {
                        $this->handelTitleType($titleType);
                    }
                    break;
                case 'welfareLabelId':
                    $welfareLabelId = $originParams['welfareLabelId'];
                    if (!empty($welfareLabelId)) {
                        $this->handelWelfareLabelId($welfareLabelId);
                    }
                    break;
                case 'jobType':
                    $jobType = $originParams['jobType'];
                    if (!empty($jobType)) {
                        $this->handelJobType($jobType);
                    }
                    break;
                case 'isFast':
                    $isFast = $originParams['isFast'];
                    if (!empty($isFast)) {
                        $this->handelIsFast();
                    }
                    break;
                case 'isFresh':
                    $isFresh = $originParams['isFresh'];
                    if (!empty($isFresh)) {
                        $this->handelIsFresh();
                    }
                    break;
                case 'isCooperation':
                    $isCooperation = $originParams['isCooperation'];
                    if (!empty($isCooperation)) {
                        $this->handelIsCooperation();
                    }
                    break;
                case 'isEstablishment':
                    $isCooperation = $originParams['isEstablishment'];
                    if (!empty($isCooperation)) {
                        $this->handelisEstablishment();
                    }
                    break;
                case 'announcementHeat':
                    $this->params['announcementHeat'] = $originParams['announcementHeat'];
                    break;
                case 'type':
                    $type = $originParams['type'];
                    if (!empty($type)) {
                        $this->handelType($type);
                    }
                    break;
                case 'page':
                    $page = $originParams['page'];
                    break;
                case 'memberId':
                    $memberId = $originParams['memberId'];
                    break;
            }
        }
    }

    // 获取和设置一些临时的字典名称
    private function getTmpDictionaryNameList($key, $name)
    {
        $value = $this->tmpDictionaryNameList[$key][$name];
        if ($value) {
            return $value;
        }

        switch ($key) {
            case 'experience':
                $value = BaseDictionary::getExperienceName($name);
                break;
            case 'education':
                $value = BaseDictionary::getEducationName($name);
                break;
            case 'jobCategory':
                $value = BaseCategoryJob::getName($name);
                break;
            case 'welfareTag':
                $value = array_slice(BaseWelfareLabel::getWelfareLabelNameList($name), 0, 2);
                break;
            case 'area':
                $value = BaseArea::getAreaName($name);
                break;
            case 'companyType':
                $value = BaseDictionary::getCompanyTypeName($name);
                break;
            case 'companyNature':
                $value = BaseDictionary::getCompanyNatureName($name);
                break;
            case 'major':
                $value       = '';
                $jobMajorIds = explode(',', $name);
                if (count($jobMajorIds) > 0) {
                    $value = BaseMajor::getAllMajorName($jobMajorIds);
                }
                break;
        }

        // aa($value);

        $this->tmpDictionaryNameList[$key][$name] = $value;

        return $value;
    }

    private function handelKeyword($keyword)
    {
        // 只能是数字或者字母或者中文字符?
        $keyword = trim($keyword);
        if (empty($keyword)) {
            return '';
        }

        $this->params['keyword'] = $keyword;
        $this->keyword           = $keyword;
    }

    private function handelAreaId($areaId)
    {
        // 先把逗号转成下划线
        $areaId = str_replace(',', '_', $areaId);
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id
        $areaIdArr = explode('_', $areaId);
        // 去掉空的和非数字的
        $areaIdArr = array_filter($areaIdArr, function ($item) {
            return is_numeric($item);
        });

        if (count($areaIdArr) > 5) {
            throw new MessageException('最多只能选择5个地区');
        }

        // 把地区都下沉到第二级
        $areaIds = BaseArea::getCityIds($areaIdArr);

        $this->params['areaId'] = $areaIds;

        return $areaId;
    }

    private function handelCompanyNature($companyNature)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id
        $companyNature    = str_replace(',', '_', $companyNature);
        $companyNatureArr = explode('_', $companyNature);
        // 去掉空的和非数字的
        $companyNatureArr = array_filter($companyNatureArr, function ($item) {
            return is_numeric($item);
        });

        if (count($companyNatureArr) > 5) {
            throw new MessageException('最多只能选择5个单位性质');
        }

        $this->params['companyNature'] = $companyNatureArr;

        return $companyNature;
    }

    private function handelCompanyType($companyType)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id
        $companyType    = str_replace(',', '_', $companyType);
        $companyTypeArr = explode('_', $companyType);
        // 去掉空的和非数字的
        $companyTypeArr = array_filter($companyTypeArr, function ($item) {
            return is_numeric($item);
        });

        if ($this->type != AnnouncementListService::TYPE_MINI_ANNOUNCEMENT_LIST && count($companyTypeArr) > 5) {
            throw new MessageException('最多只能选择5个单位类型');
        }

        $this->params['companyType'] = $companyTypeArr;

        return $companyType;
    }

    private function handelMajorId($majorId)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id
        $majorId    = str_replace(',', '_', $majorId);
        $majorIdArr = explode('_', $majorId);
        // 去掉空的和非数字的
        $majorIdArr = array_filter($majorIdArr, function ($item) {
            return is_numeric($item);
        });

        if (count($majorIdArr) > 5) {
            throw new MessageException('最多只能选择5个专业');
        }

        $this->params['majorId'] = $majorIdArr;

        return $majorId;
    }

    private function handelEducationType($educationType)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id
        // $educationTypeArr = explode('_', $educationType);
        // // 去掉空的和非数字的
        // $educationTypeArr = array_filter($educationTypeArr, function ($item) {
        //     return is_numeric($item);
        // });
        //
        // if (count($educationTypeArr) > 5) {
        //     throw new MessageException('最多只能选择5个学历');
        // }

        $this->params['educationType'] = BaseDictionary::getEducationValueByKey($educationType);

        return $educationType;
    }

    private function handelIndustryId($industryId)
    {
        // 只能一个并且需要是数字
        if (!is_numeric($industryId)) {
            throw new MessageException('行业类别必须是数字');
        }

        // 行业类别是两级的,所以我们需要把下面的都下沉到第二级
        $trade = BaseTrade::findOne($industryId);

        if ($trade->level == 1) {
            $this->params['industryId'] = BaseTrade::find()
                ->select('id')
                ->where(['parent_id' => $industryId])
                ->column();
        } else {
            $this->params['industryId'] = $industryId;
        }

        return $industryId;
    }

    private function handelExperienceType($experienceType)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id
        $experienceType = str_replace(',', '_', $experienceType);

        $experienceTypeArr = explode('_', $experienceType);
        // 去掉空的和非数字的
        $experienceTypeArr = array_filter($experienceTypeArr, function ($item) {
            return is_numeric($item);
        });

        if (count($experienceTypeArr) > 5) {
            throw new MessageException('最多只能选择5个工作经验');
        }

        $this->params['experienceType'] = $experienceTypeArr;

        return $experienceType;
    }

    private function handelReleaseTimeType($releaseTimeType)
    {
        $releaseTimeInfo = BaseDictionary::getReleaseTimeListInfo($releaseTimeType);

        if (empty($releaseTimeInfo)) {
            throw new MessageException('发布时间类型错误');
        }

        $this->params['releaseTimeBegin'] = $releaseTimeInfo;

        return $releaseTimeInfo;
    }

    private function handelWageId($wageId)
    {
        $wageInfo = BaseDictionary::getMinAndMaxWage($wageId);

        if (empty($wageInfo)) {
            throw new MessageException('薪资类型错误');
        }

        //累赘一个字段，后面判断面议用
        $this->params['wageId']    = $wageId;
        $this->params['wageBegin'] = $wageInfo['min'];
        $this->params['wageEnd']   = $wageInfo['max'];

        return $wageInfo;
    }

    private function handelScale($scale)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id,并且只能是1~7
        $scale = str_replace(',', '_', $scale);

        $scaleArr = explode('_', $scale);
        // 去掉空的和非数字的
        $scaleArr = array_filter($scaleArr, function ($item) {
            return is_numeric($item) && $item >= 1 && $item <= 7;
        });

        if (count($scaleArr) > 7) {
            throw new MessageException('最多只能选择7个公司规模');
        }

        $this->params['companyScaleType'] = $scaleArr;

        return $scale;
    }

    private function handelNatureType($natureType)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id,并且只能是1~7
        $natureType = str_replace(',', '_', $natureType);

        $natureTypeArr = explode('_', $natureType);
        // 去掉空的和非数字的
        $natureTypeArr = array_filter($natureTypeArr, function ($item) {
            return is_numeric($item) && $item >= 1 && $item <= 5;
        });

        if (count($natureTypeArr) > 5) {
            throw new MessageException('最多只能选择5个工作性质');
        }

        $this->params['natureType'] = $natureTypeArr;

        return $natureType;
    }

    private function handelJobType($jobType)
    {
        $jobType = str_replace(',', '_', $jobType);

        $jobTypeArr = explode('_', $jobType);
        // 去掉空的和非数字的

        if (count($jobTypeArr) > 5) {
            throw new MessageException('最多只能选择5个职位类型');
        }

        $this->params['jobType'] = $jobTypeArr;

        return $jobType;
    }

    private function handelType($type)
    {
        $type = str_replace(',', '_', $type);

        $typeArr = explode('_', $type);
        // 去掉空的和非数字的

        if (count($typeArr) > 5) {
            throw new MessageException('最多只能选择5个公告类型');
        }

        $typeArr = BaseHomeColumn::getFullId($typeArr);

        $this->params['type'] = $typeArr;

        return $typeArr;
    }

    private function handelIsFast()
    {
        // 去掉空的和非数字的

        $this->params['isFast'] = 1;

        return '1';
    }

    private function handelIsFresh()
    {
        // 去掉空的和非数字的

        $this->params['isFresh'] = 1;

        return '1';
    }

    private function handelIsCooperation()
    {
        // 去掉空的和非数字的

        $this->params['isCooperation'] = 1;

        return '1';
    }

    private function handelisEstablishment()
    {
        // 去掉空的和非数字的

        $this->params['isEstablishment'] = 1;

        return '1';
    }

    private function handelWelfareLabelId($welfareLabelId)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id,并且只能是1~7
        $welfareLabelId = str_replace(',', '_', $welfareLabelId);

        $welfareLabelIdArr = explode('_', $welfareLabelId);
        // 去掉空的和非数字的
        $welfareLabelIdArr = array_filter($welfareLabelIdArr, function ($item) {
            return is_numeric($item);
        });

        if (count($welfareLabelIdArr) > 5) {
            throw new MessageException('最多只能选择5个工作性质');
        }

        $this->params['welfareLabelId'] = $welfareLabelIdArr;

        return $welfareLabelId;
    }

    private function handelTitleType($titleType)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id,并且只能是1~7
        $titleType = str_replace(',', '_', $titleType);

        $titleTypeArr = explode('_', $titleType);
        // 去掉空的和非数字的
        $titleTypeArr = array_filter($titleTypeArr, function ($item) {
            return is_numeric($item) && $item >= 1 && $item <= 4;
        });

        if (count($titleTypeArr) > 4) {
            throw new MessageException('最多只能选择4个职称');
        }

        $this->params['titleType'] = $titleTypeArr;

        return $titleType;
    }

}
