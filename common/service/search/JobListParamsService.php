<?php

namespace common\service\search;

use admin\models\Trade;
use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseDictionary;
use common\base\models\BaseMajor;
use common\helpers\ArrayHelper;
use common\libs\Cache;
use frontendPc\models\Area;
use frontendPc\models\Dictionary;

class JobListParamsService extends BaseService
{
    const KEY = Cache::PC_HOME_JOB_LIST_PARAMS_KEY;

    public function get()
    {
        $params = Cache::get(self::KEY);
        if (!$params) {
            return $this->set();
        }

        return json_decode($params, true);
    }

    // 这里主要是给职位中心相关的参数做服务的
    public function set()
    {
        $educationList = Dictionary::getEducationSearchList();

        // 这里还多加一个参数,用于控制前端是否显示反馈快,应届生,认证单位等等选择
        $selectButtonList = [
            '1', // 反馈快
            '1', // 应届生
            '2', // 认证单位
            '1', // 编制
        ];

        $data = [
            'jobTypeList'       => BaseCategoryJob::getJobHome(),
            'hotAreaList'       => ArrayHelper::intToString(ArrayHelper::object2LV(Area::getNewSearchHotList())),
            'areaList'          => BaseArea::getHierarchyCityList(),
            'companyTypeList'   => ArrayHelper::array2LV(BaseDictionary::getCompanyTypeList()),
            'majorList'         => BaseMajor::getAllForJobSelect(),
            'educationList'     => ArrayHelper::array2LV($educationList),
            'companyNatureList' => ArrayHelper::array2LV(Dictionary::getCompanyNatureList()),
            'industryList'      => ArrayHelper::kV2LV(Trade::getTradeList()),
            'releaseList'       => ArrayHelper::array2LV(BaseDictionary::getReleaseTimeList()),
            'titleList'         => ArrayHelper::array2LV(BaseDictionary::getFirstTitleList()),
            'natureList'        => ArrayHelper::array2LV(BaseDictionary::getNatureList()),
            'selectButtonList'  => $selectButtonList,
        ];

        $data = ArrayHelper::intToString($data);

        Cache::set(self::KEY, json_encode($data), 3600);

        return $data;
    }

}