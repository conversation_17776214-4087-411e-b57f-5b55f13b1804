<?php

namespace common\service\match;

use common\base\models\BaseArea;
use common\base\models\BaseJob;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeIntention;
use yii\base\Exception;
use Yii;

/**
 * 匹配的规则
 */
class MatchCompleteService extends CommonMatchService
{
    /** 规则设定  */
    private $rule_key;
    /** 规则信息  */
    private $rule_info;
    /** 方案类型  */
    private $project;
    /** 职位ID  */
    private $job_id;
    /** 意向ID  */
    private $intention_id;
    /** 简历ID  */
    private $resume_id;
    /** 职位信息 */
    private $job_info;
    /** 意向信息 */
    private $intention_info;
    /** 简历信息 */
    private $resume_info;
    /** 简历信息 */
    private $resume_top_education_info;
    /** 简历意向列表信息 */
    private $resume_intention_list;
    /** 是否匹配职位类型定义 */
    private $is_match_job_category = 0;
    /** 是否匹配学历定义 */
    private $is_match_education = 0;
    /** 是否匹配专业定义 */
    private $is_match_major = 0;
    /** 是否匹配地区定义-城市匹配 */
    private $is_match_area_city = 0;
    /** 是否匹配地区定义-省份匹配 */
    private $is_match_area_province = 0;

    const PROJECT_TYPE_1 = 1;//意向与职位对比---针对人匹配岗位的规则
    const PROJECT_TYPE_2 = 2;//简历与职位对比---这种就是键里所有意向对比岗位的规则
    /** 特殊匹配ID */
    const SPECIAL_MATCH_0    = 23.5;//匹配0项
    const SPECIAL_MATCH_1    = 22.5;//匹配1项
    const SPECIAL_MATCH_2    = 16.5;//匹配2项
    const SPECIAL_MATCH_3    = 6.5;//匹配3项
    const SPECIAL_MATCH_LIST = [
        self::SPECIAL_MATCH_0,
        self::SPECIAL_MATCH_1,
        self::SPECIAL_MATCH_2,
        self::SPECIAL_MATCH_3,
    ];

    /**
     * 设置规则
     */
    public function setRuleKey($rule_key = self::PERSON_TO_JOB_RULE)
    {
        $this->rule_key = $rule_key;

        return $this;
    }

    /**
     * 设置规则情况
     */
    public function setProject($project)
    {
        $this->project = $project;

        return $this;
    }

    /**
     * 初始化
     * @param $init
     *              [
     *              'job_id' => '职位ID',
     *              'intention_id' =>'意向ID',
     *              'resume_id' => '简历ID',
     *              ]
     *              参数说明：
     *              job_id：职位ID一定要传入
     *              intention_id意向ID和resume_id简历ID二选一
     */
    public function init($init)
    {
        if (empty($this->rule_key)) {
            throw new Exception('规则未设置');
        }
        //判断初始化参数是否正确
        if (empty($init['job_id']) || (empty($init['intention_id']) && empty($init['resume_id'])) || (!empty($init['intention_id']) && !empty($init['resume_id']))) {
            throw new Exception('初始化参数错误');
        }
        //获取规则信息
        $this->rule_info = Yii::$app->params[$this->rule_key];
        //为了兼容跟多情况
        //初始化职位ID
        $this->job_id = $init['job_id'];
        //初始化意向ID
        $this->intention_id = $init['intention_id'] ?: 0;
        //初始化简历ID
        $this->resume_id = $init['resume_id'] ?: 0;
        //初始化职位信息
        $this->job_info = BaseJob::findOne($this->job_id);
        //初始化意向信息
        if ($this->intention_id > 0) {
            $this->intention_info            = BaseResumeIntention::findOne($this->intention_id);
            $this->resume_info               = BaseResume::findOne($this->intention_info['resume_id']);
            $this->resume_top_education_info = BaseResumeEducation::findOne($this->resume_info->last_education_id);
        }
        //初始化简历信息
        if ($this->resume_id > 0) {
            $this->resume_info               = BaseResume::findOne($this->resume_id);
            $this->resume_top_education_info = BaseResumeEducation::findOne($this->resume_info->last_education_id);
            //初始化简历意向列表信息
            $this->resume_intention_list = BaseResumeIntention::find()
                ->andWhere(['resume_id' => $this->resume_id])
                ->andWhere(['status' => BaseResumeIntention::STATUS_ACTIVE])
                ->asArray()
                ->all();
        }

        return $this;
    }

    /**
     * 匹配主程序
     */
    public function run()
    {
        switch ($this->project) {
            case self::PROJECT_TYPE_1:
                $complete_data = $this->projectType1();
                break;
            case self::PROJECT_TYPE_2:
                $complete_data = $this->projectType2();
                break;
            default:
                throw new Exception('匹配类型错误');
        }

        return $complete_data;
    }

    /**
     * 匹配类型1
     * 意向与职位对比
     */
    private function projectType1()
    {
        //获取职位职位类型、学历要求、专业、工作城市、工作省份
        $job_type      = $this->job_info->job_category_id;
        $job_education = $this->job_info->education_type;
        $job_major     = $this->job_info->major_id ? explode(',', $this->job_info->major_id) : [];
        $job_city      = $this->job_info->city_id;
        $job_province  = $this->job_info->province_id;

        //获取意向职位类型、学历要求、专业、工作城市
        $intention_type               = $this->intention_info['job_category_id'];
        $intention_education          = $this->resume_top_education_info->education_id;
        $intention_major              = empty($this->resume_top_education_info->major_id_level_2) ? 0 : $this->resume_top_education_info->major_id_level_2;
        $intention_area_arr           = $this->processingArea($this->intention_info['area_id']);
        $intention_city               = array_column($intention_area_arr['city'], 'id');
        $intention_city_province      = array_column($intention_area_arr['city_province'], 'id');
        $intention_city_province_city = array_column($intention_area_arr['city_province_city'], 'id');
        $intention_province           = array_column($intention_area_arr['province'], 'id');
        $intention_province_city      = array_column($intention_area_arr['province_city'], 'id');
        $intention_province_all       = array_unique(array_merge($intention_city_province, $intention_province));
        //是否城市匹配
        if (in_array($job_city, $intention_city)) {
            $this->is_match_area_city = 1;
        }
        //是否省份匹配
        if (in_array($job_province, $intention_province_all)) {
            $this->is_match_area_province = 1;
        }

        //循环规则信息
        foreach ($this->rule_info as $item) {
            //规则ID
            $rule_id = $item['id'];
            //职位类型对比
            $job_category_bool = $this->jobCategoryType($item['match']['job_category_id'], $job_type, $intention_type);
            //学历要求对比
            $education_bool = $this->educationType($item['match']['education_id'], $job_education,
                $intention_education);
            //专业对比
            $major_bool = $this->majorType($item['match']['major_id'], $job_major, $intention_major);
            //工作城市对比
            $city_bool = $this->cityType($item['match']['area_id'], $job_city, $intention_city,
                $intention_city_province_city, $intention_province_city);
            //判断是否完全命中规则
            if ($job_category_bool && $education_bool && $major_bool && $city_bool) {
                //保存命中规则数据返回数据
                //终止程序
                return [
                    'rule_id'                => $rule_id,
                    'rule_info'              => $item,
                    'is_match_job_category'  => $this->is_match_job_category,
                    'is_match_education'     => $this->is_match_education,
                    'is_match_major'         => $this->is_match_major,
                    'is_match_area_city'     => $this->is_match_area_city,
                    'is_match_area_province' => $this->is_match_area_province,
                ];
            }
        }

        //没有命中规则-去到.5规则
        $area_box = array_merge($intention_city, $intention_province, $intention_city_province);

        return $this->ruleMatchDecimal($area_box);
    }

    /**
     * 匹配类型2
     * 简历与职位对比
     */
    private function projectType2()
    {
        //获取职位职位类型
        $job_type = $this->job_info->job_category_id;
        //获取简历职位类型、学历要求、专业、工作城市
        $is_intention_bool = false;
        $intention_id      = [];
        //地区盒子
        $area_box = [];
        foreach ($this->resume_intention_list as $item) {
            if ($item['job_category_id'] == $job_type) {
                $is_intention_bool = true;
                array_push($intention_id, $item['id']);
            }
            $item_area = explode(',', $item['area_id']);
            foreach ($item_area as $area_id) {
                array_push($area_box, $area_id);
                $item_info = BaseArea::getAreaInfo($area_id);
                if ($item_info['level'] == 2) {
                    array_push($area_box, $item_info['parent_id']);
                }
            }
        }
        if ($is_intention_bool) {
            //初始化意向信息
            $this->intentionInit($intention_id);

            return $this->projectType1();
        } else {
            return $this->RuleMatchDecimal($area_box);
        }
    }

    /**
     * .5规则处理计算
     * @param $area_box array 地区盒子
     */
    private function ruleMatchDecimal(array $area_box)
    {
        //获取职位职位类型、学历要求、专业、工作城市
        $job_type         = $this->job_info->job_category_id;
        $job_education    = $this->job_info->education_type;
        $job_major        = $this->job_info->major_id ? explode(',', $this->job_info->major_id) : [];
        $job_city         = $this->job_info->city_id;
        $job_province     = $this->job_info->province_id;
        $resume_education = $this->resume_top_education_info->education_id;
        if ($job_education == BaseResumeEducation::EDUCATION_TYPE_OTHER_CODE) {
            $job_education = 0;
        }
        if ($this->resume_top_education_info->education_id == BaseResumeEducation::EDUCATION_TYPE_OTHER_CODE) {
            $resume_education = 0;
        }
        //匹配命中了几项
        $complete = 0;
        //学历要求对比-意向学历大于等于职位要求学历或者职位要求学历为不限
        if ($job_education <= 0 || $resume_education >= $job_education) {
            $this->is_match_education = 1;
            $complete++;
        }
        //专业要求对比
        //判断人的专业是否是为未知的
        $intention_major = empty($this->resume_top_education_info->major_id_level_2) ? 0 : $this->resume_top_education_info->major_id_level_2;
        if (!empty($intention_major) || !in_array($intention_major, [
                CommonMatchService::MAJOR_ID_NO_LIMIT,
                CommonMatchService::MAJOR_ID_UNKNOWN,
            ])) {
            if (in_array(CommonMatchService::MAJOR_ID_NO_LIMIT,
                    $job_major) || in_array(CommonMatchService::MAJOR_ID_UNKNOWN, $job_major)) {
                $this->is_match_major = 1;
                //求职者的意向未知以及职位里含未知
                $complete++;
            }
            if (in_array($intention_major, $job_major)) {
                $this->is_match_major = 1;
                $complete++;
            }
        } else {
            if (in_array(CommonMatchService::MAJOR_ID_NO_LIMIT,
                    $job_major) || in_array(CommonMatchService::MAJOR_ID_UNKNOWN, $job_major)) {
                $this->is_match_major = 1;
                //求职者的意向未知以及职位里含未知
                $complete++;
            }
        }
        if (empty($job_major)) {
            $this->is_match_major = 2;
        }
        //地区要求对比
        if (in_array($job_city, $area_box) || in_array($job_province, $area_box)) {
            if (in_array($job_city, $area_box)) {
                $this->is_match_area_city = 1;
            }
            if (in_array($job_province, $area_box)) {
                $this->is_match_area_province = 1;
            }
            $complete++;
        }
        $rule_id = self::SPECIAL_MATCH_LIST[$complete] ?: self::SPECIAL_MATCH_0;

        return [
            'rule_id'                => $rule_id,
            'rule_info'              => [],
            'is_match_job_category'  => $this->is_match_job_category,
            'is_match_education'     => $this->is_match_education,
            'is_match_major'         => $this->is_match_major,
            'is_match_area_city'     => $this->is_match_area_city,
            'is_match_area_province' => $this->is_match_area_province,
        ];
    }

    /**
     * 意向信息初始化
     * @param $intention_id
     * @return bool
     * @throws Exception
     */
    private function intentionInit($intention_id)
    {
        if (count($intention_id) <= 0) {
            throw new Exception('意向ID错误');
        }
        //初始化意向信息
        $data                 = BaseResumeIntention::find()
            ->where(['id' => $intention_id])
            ->asArray()
            ->all();
        $this->intention_info = [
            'resume_id'       => implode(',', array_unique(array_column($data, 'resume_id'))),
            'job_category_id' => implode(',', array_unique(array_column($data, 'job_category_id'))),
            'area_id'         => implode(',', array_unique(array_column($data, 'area_id'))),
        ];
        if (empty($this->intention_info)) {
            throw new Exception('意向信息错误');
        }

        return true;
    }

    /**
     * 职位类型对比
     * 0不用匹配 1匹配
     */
    private function jobCategoryType($rule_type, $job_category_type, $resume_category_type)
    {
        switch ($rule_type) {
            case 0:
                //不用匹配
                if ($job_category_type != $resume_category_type) {
                    return true;
                }
                break;
            case 1:
                //包含匹配
                if ($job_category_type == $resume_category_type) {
                    $this->is_match_job_category = 1;

                    return true;
                }
                break;
        }

        return false;
    }

    /**
     * 学历要求对比
     * 0不用匹配 1匹配 2职位学历要求低于人才最高学历
     */
    private function educationType($rule_type, $job_education, $resume_education)
    {
        //特殊处理一下 其他
        if ($job_education == BaseResumeEducation::EDUCATION_TYPE_OTHER_CODE) {
            $job_education = 0;
        }
        if ($resume_education == BaseResumeEducation::EDUCATION_TYPE_OTHER_CODE) {
            $resume_education = 0;
        }
        switch ($rule_type) {
            case 0:
                //不用匹配
                if ($job_education != $resume_education) {
                    return true;
                }
                break;
            case 1:
                //包含匹配
                if ($job_education == $resume_education) {
                    $this->is_match_education = 1;

                    return true;
                }
                break;
            case 2:
                //职位学历要求低于人才最高学历
                if ($job_education < $resume_education) {
                    $this->is_match_education = 1;

                    return true;
                }
                break;
        }

        return false;
    }

    /**
     * 专业对比
     * 0不用匹配 1匹配专业 2专业不限 3专业未分类 4空 5专业未分类或空值
     */
    private function majorType($rule_type, $job_major, $resume_major)
    {
        switch ($rule_type) {
            case 0:
                //不用匹配
                if (!in_array($resume_major, $job_major)) {
                    return true;
                }
                break;
            case 1:
                //包含匹配
                if (in_array($resume_major, $job_major)) {
                    $this->is_match_major = 1;

                    return true;
                }
                break;
            case 2:
                //专业不限
                if (in_array(CommonMatchService::MAJOR_ID_NO_LIMIT, $job_major)) {
                    $this->is_match_major = 1;

                    return true;
                }
                break;
            case 3:
                //专业未分类
                if (in_array(CommonMatchService::MAJOR_ID_UNKNOWN, $job_major)) {
                    $this->is_match_major = 1;

                    return true;
                }
                break;
            case 4:
                //空
                if (empty($job_major)) {
                    $this->is_match_major = 2;

                    return true;
                }
                break;
            case 5:
                //专业未分类或空值
                if (empty($job_major) || in_array(CommonMatchService::MAJOR_ID_UNKNOWN, $job_major)) {
                    $this->is_match_major = 1;
                    if (empty($job_major)) {
                        $this->is_match_major = 2;
                    }

                    return true;
                }
                break;
        }

        return false;
    }

    /**
     * 工作城市对比
     * 0不用匹配 1匹配 2匹配城市 3匹配省份 4匹配重点城市 5匹配非重点城市 6意向城市所属省份匹配 7意向地区或所属省份匹配 8意向地区所属省份不匹配
     */
    private function cityType(
        $rule_type,
        $job_city,
        $resume_city = [],
        $resume_city_province_city = [],
        $resume_province_city = []
    ) {
        switch ($rule_type) {
            case 0:
                //不用匹配
                if (!in_array($job_city, array_unique(array_merge($resume_city, $resume_province_city)))) {
                    return true;
                }
                break;
            case 1:
                //包含匹配
                if (in_array($job_city, array_unique(array_merge($resume_city, $resume_province_city)))) {
                    return true;
                }
                break;
            case 2:
                //匹配城市
                if (in_array($job_city, $resume_city)) {
                    return true;
                }
                break;
            case 3:
                //匹配省份
                if (in_array($job_city, $resume_province_city)) {
                    return true;
                }
                break;
            case 4:
                //匹配重点城市
                if (in_array($job_city, CommonMatchService::TOP_AREA_KEY_CITY)) {
                    return true;
                }
                break;
            case 5:
                //匹配非重点城市
                if (!in_array($job_city, CommonMatchService::TOP_AREA_KEY_CITY)) {
                    return true;
                }
                break;
            case 6:
                //意向城市所属省份匹配
                if (in_array($job_city, $resume_city_province_city)) {
                    return true;
                }
                break;
            case 7:
                //意向地区或所属省份匹配
                if (in_array($job_city, array_unique(array_merge($resume_city_province_city, $resume_province_city)))) {
                    return true;
                }
                break;
            case 8:
                //意向地区所属省份不匹配
                if (!in_array($job_city, $resume_province_city)) {
                    return true;
                }
                break;
        }

        return false;
    }

    /**
     * 地区ID处理
     ** 原因：人才地区选择了多个有城市、省份
     ** 处理结果：将人才地区id处理成集合，城市(key:city)与省份(key:province)拆成两个集合
     */
    private function processingArea($area_id)
    {
        $area_id_arr = explode(',', $area_id);
        //获取所有地区信息
        $area_data = BaseArea::find()
            ->andWhere([
                'in',
                'id',
                $area_id_arr,
            ])
            ->asArray()
            ->all();
        //区分
        $city               = [];//城市
        $city_province      = [];//城市所属省份
        $city_province_city = [];//城市所属省份下的所有城市
        $province           = [];//省份
        $province_city      = [];//省份下的城市
        foreach ($area_data as $item) {
            if ($item['level'] == 1) {
                $province[$item['id']] = $item;
            }
            if ($item['level'] == 2) {
                $city[$item['id']] = $item;
            }
        }
        $city_ids = [];
        //对意向城市处理
        if (count($city) > 0) {
            $city_ids           = array_keys($city);
            $city_parent_ids    = array_unique(array_column($city, 'parent_id'));
            $city_province_data = BaseArea::find()
                ->andWhere([
                    'id'    => $city_parent_ids,
                    'level' => 1,
                ])
                ->asArray()
                ->all();
            foreach ($city_province_data as $city_province_item) {
                $city_province[$city_province_item['id']] = $city_province_item;
            }
            if (count($city_province) > 0) {
                $city_province_ids       = array_keys($city_province);
                $city_province_city_data = BaseArea::find()
                    ->andWhere([
                        'parent_id' => $city_province_ids,
                        'level'     => 2,
                    ])
                    ->asArray()
                    ->all();
                foreach ($city_province_city_data as $city_province_city_item) {
                    $city_province_city[$city_province_city_item['id']] = $city_province_city_item;
                }
            }
        }
        //省份不为空时候获取省份下的所有城市，同时剔除意向城市
        if (count($province) > 0) {
            $province_ids       = array_keys($province);
            $province_city_data = BaseArea::find()
                ->andWhere([
                    'parent_id' => $province_ids,
                    'level'     => 2,
                ])
                ->asArray()
                ->all();
            foreach ($province_city_data as $province_item) {
                if (!in_array($province_item['id'], $city_ids)) {
                    $province_city[$province_item['id']] = $province_item;
                }
            }
        }

        return [
            'city'               => $city,
            'city_province'      => $city_province,
            'city_province_city' => $city_province_city,
            'province'           => $province,
            'province_city'      => $province_city,
        ];
    }

}