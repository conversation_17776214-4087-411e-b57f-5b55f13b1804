<?php

namespace common\service\chat;

use common\base\models\BaseChatCommonGreeting;
use common\base\models\BaseChatCommonGreetingSystem;
use common\base\models\BaseChatCommonPhrase;
use common\base\models\BaseMember;
use common\libs\Aliyun\Green;
use yii\base\Exception;

/**
 * 处理打招呼语
 */
class CommonGreeting extends BaseService
{
    //自定义打招呼语数量
    const MAX_COUNT = 10;
    //单位默认招呼语的系统提示
    const COMPANY_DEFAULT_GREETING_SYSTEM_MESSAGE = '点击修改打招呼语';
    const RESUME_DEFAULT_GREETING_SYSTEM_MESSAGE  = '点击修改打招呼语';

    /**
     * 获取招呼语列表
     * @param $memberId
     * @return array
     */
    public function getList($memberId)
    {
        //获取当前账号信息
        $memberInfo = BaseMember::findOne($memberId);
        //获取系统的招呼语
        $systemList = BaseChatCommonGreetingSystem::find()
            ->select([
                'id',
                'content',
            ])
            ->where([
                'type' => $memberInfo->type,
            ])
            ->orderBy('id desc')
            ->asArray()
            ->all();
        foreach ($systemList as &$item) {
            //打招呼语类型 1：系统 2：自定义
            $item['type']   = BaseMember::GREETING_TYPE_SYSTEM;
            $item['active'] = $memberInfo->greeting_type == BaseMember::GREETING_TYPE_SYSTEM && $item['id'] == $memberInfo->greeting_default_id;
        }

        $customList = BaseChatCommonGreeting::find()
            ->select([
                'id',
                'content',
            ])
            ->where(['member_id' => $memberId])
            ->orderBy('id desc')
            ->asArray()
            ->all();
        foreach ($customList as &$item) {
            $item['type']   = BaseMember::GREETING_TYPE_CUSTOM;
            $item['active'] = $memberInfo->greeting_type == BaseMember::GREETING_TYPE_CUSTOM && $item['id'] == $memberInfo->greeting_default_id;
        }

        //合并系统招呼语和自定义招呼语
        $list = array_merge($systemList, $customList);

        return [
            'list'        => $list,
            'isGreeting'  => $memberInfo->is_greeting,
            'systemCount' => count($systemList),
            'customCount' => count($customList),
            'maxCount'    => self::MAX_COUNT,
        ];
    }

    /**
     * 删除打招呼语
     * @param $memberId
     * @param $id
     * @return false|int
     * @throws \Throwable
     * @throws \yii\db\StaleObjectException
     */
    public function delete($memberId, $id)
    {
        $model = BaseChatCommonGreeting::findOne($id);
        if (!$model) {
            throw new \Exception('打招呼语不存在');
        }

        $res = $model->delete();
        //获取当前账号信息
        $memberInfo = BaseMember::findOne($memberId);
        if ($res && $memberInfo->greeting_type == BaseMember::GREETING_TYPE_CUSTOM && $id == $memberInfo->greeting_default_id) {
            //执行一个更新默认打招呼语的ID为系统打招呼语第一条
            //获取系统第一条
            $systemId = BaseChatCommonGreetingSystem::find()
                ->select('id')
                ->andWhere(['type' => $memberInfo->type])
                ->orderBy('id desc')
                ->asArray()
                ->scalar();
            //更新当前账号的默认打招呼语ID
            $memberInfo->greeting_default_id = $systemId;
            $memberInfo->greeting_type       = BaseMember::GREETING_TYPE_SYSTEM;
            $memberInfo->save();
        }

        return $res;
    }

    /**
     * 添加或编辑打招呼语
     * @param $memberId
     * @param $content
     * @param $id
     * @return bool
     * @throws \Exception
     */
    public function addOrEdit($memberId, $content, $id)
    {
        // content最多200个字
        if (mb_strlen($content) > 200) {
            throw new Exception('常用语最多200个字');
        }

        // 不能为空
        if (empty(trim($content))) {
            throw new Exception('打招呼语不能为空');
        }
        //检查录入的打招呼是否合法
        $green = new Green();
        if (!$green->checkText($content)) {
            throw new Exception('内容包含' . $green->reason . '等敏感信息，请修改！');
        }

        if (!$id) {
            // 检查一下是否超过最大数量
            $count = BaseChatCommonGreeting::find()
                ->where(['member_id' => $memberId])
                ->count();

            if ($count >= self::MAX_COUNT) {
                throw new Exception('最多只能添加' . self::MAX_COUNT . '条打招呼语');
            }

            $model            = new BaseChatCommonGreeting();
            $model->member_id = $memberId;
        } else {
            $model = BaseChatCommonGreeting::findOne([
                'id'        => $id,
                'member_id' => $memberId,
            ]);
            if (!$model) {
                throw new \Exception('打招呼语不存在');
            }
        }
        $model->content = $content;
        $res            = $model->save();

        return $res;
    }

    /**
     * 修改打招呼语配置开关
     * @param $memberId
     * @return bool
     * @throws Exception
     */
    public function editIsGreeting($memberId)
    {
        //获取用户信息
        $memberInfo = BaseMember::findOne($memberId);
        //如果是开的那就关闭它，如果是关的那就打开它
        if ($memberInfo->is_greeting == BaseMember::IS_GREETING_YES) {
            $memberInfo->is_greeting = BaseMember::IS_GREETING_NO;
        } else {
            //打开的时候去看一下打招呼语默认ID是否为0---保底措施
            if ($memberInfo->greeting_default_id == 0) {
                //获取系统默认的第一条
                $memberInfo->greeting_type       = BaseMember::GREETING_TYPE_SYSTEM;
                $memberInfo->greeting_default_id = BaseChatCommonGreetingSystem::getDefaultId($memberInfo->type);
            }
            $memberInfo->is_greeting = BaseMember::IS_GREETING_YES;
        }

        if (!$memberInfo->save()) {
            throw new Exception($memberInfo->getFirstErrorsMessage());
        }

        return true;
    }

    /**
     * 修改默认打招呼语
     * @param $memberId
     * @param $id
     * @param $type
     * @return true
     * @throws Exception
     */
    public function editDefaultGreeting($memberId, $id, $type)
    {
        //获取用户信息
        $memberInfo = BaseMember::findOne($memberId);
        //先看参数合法性
        if ($type == BaseMember::GREETING_TYPE_SYSTEM) {
            //看打招呼语是否存在
            $greetingInfo = BaseChatCommonGreetingSystem::findOne($id);
            if (!$greetingInfo) {
                throw new Exception('打招呼语不存在');
            }
        } elseif ($type == BaseMember::GREETING_TYPE_CUSTOM) {
            //看打招呼语是否存在
            $greetingInfo = BaseChatCommonGreeting::findOne($id);
            if (!$greetingInfo) {
                throw new Exception('打招呼语不存在');
            }
        } else {
            throw new Exception('参数错误');
        }

        //修改默认打招呼语
        $memberInfo->greeting_default_id = $id;
        $memberInfo->greeting_type       = $type;

        if (!$memberInfo->save()) {
            throw new Exception($memberInfo->getFirstErrorsMessage());
        }

        return true;
    }
}
