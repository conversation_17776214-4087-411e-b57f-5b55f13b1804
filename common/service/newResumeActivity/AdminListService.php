<?php

namespace common\service\newResumeActivity;

use common\base\BaseActiveRecord;
use common\base\models\BaseMember;
use common\base\models\BaseNewResumeActivityAccept;
use common\base\models\BaseNewResumeActivityShare;
use common\base\models\BaseResume;
use common\helpers\TimeHelper;

/**
 * 运营后台查询服务
 */
class AdminListService extends BaseService
{

    // 人才检索
    // 注册时间
    // 权益发放时间
    // 分享状态
    // 权益发放状态
    public $originalParams = [];

    public $page;
    public $pageSize;

    public $data = [];

    public $isExcel = false;

    /**
     * 分享人ID
     * 分享人姓名
     * 分享人手机号
     * 受邀人手机号
     * 受邀人ID
     * 受邀人姓名
     * 注册时间
     * 分享状态
     * 权益发放时间
     * 权益发放状态
     */
    public $headers = [
        '分享人ID',
        '分享人姓名',
        '分享人手机号',
        '受邀人手机号',
        '受邀人ID',
        '受邀人姓名',
        '注册时间',
        '分享状态',
        '权益达标时间',
        '分享权益发放状态',
        '受邀权益发放状态',
    ];

    /**
     * 创建拉新活动
     */
    public function run($params)
    {
        $this->originalParams = $params;

        $this->handelParams();

        $this->getListData();

        return $this->data;
    }

    public function getListData()
    {
        // 一切都是基于
        $query = BaseNewResumeActivityAccept::find()
            ->alias('a')
            ->innerJoin(['b' => BaseNewResumeActivityShare::tableName()], 'b.id = a.share_id');

        $query->select([
            'a.id',
            'acceptResumeId' => 'a.resume_id',
            'shareResumeId'  => 'b.resume_id',
            'a.add_time',
            'success_time',
            'a.status',
            'a.share_issue_status',
            'a.accept_issue_status',
        ]);

        if ($this->originalParams['keyword']) {
            // 被分享人
            $query->innerJoin(['c' => BaseResume::tableName()], 'c.id = a.resume_id');
            $query->innerJoin(['d' => BaseMember::tableName()], 'd.id = c.member_id');

            // 分享人
            $query->innerJoin(['e' => BaseResume::tableName()], 'e.id = b.resume_id');
            $query->innerJoin(['f' => BaseMember::tableName()], 'f.id = e.member_id');

            // 分享人被分享人的名字、uuid、手机号名字（全等）
            $query->andWhere([
                'or',
                ['c.name' => $this->originalParams['keyword']],
                ['c.uuid' => $this->originalParams['keyword']],
                ['d.mobile' => $this->originalParams['keyword']],
                ['e.name' => $this->originalParams['keyword']],
                ['e.uuid' => $this->originalParams['keyword']],
                ['f.mobile' => $this->originalParams['keyword']],
            ]);

            $query->addSelect([
                'acceptName'   => 'c.name',
                'acceptUuid'   => 'c.uuid',
                'acceptMobile' => 'd.mobile',
                'shareName'    => 'e.name',
                'shareUuid'    => 'e.uuid',
                'shareMobile'  => 'f.mobile',
            ]);
        }

        // 注册开始结束时间add_time
        if ($this->originalParams['addTimeStart']) {
            $query->andWhere([
                '>=',
                'a.add_time',
                TimeHelper::dayToBeginTime($this->originalParams['addTimeStart']),
            ]);
        }
        if ($this->originalParams['addTimeEnd']) {
            $query->andWhere([
                '<=',
                'a.add_time',
                TimeHelper::dayToEndTime($this->originalParams['addTimeEnd']),
            ]);
        }

        // 权益发放开始结束时间success_time
        if ($this->originalParams['successTimeStart']) {
            $query->andWhere([
                '>=',
                'success_time',
                TimeHelper::dayToBeginTime($this->originalParams['successTimeStart']),
            ]);
        }
        if ($this->originalParams['successTimeEnd']) {
            $query->andWhere([
                '<=',
                'success_time',
                TimeHelper::dayToEndTime($this->originalParams['successTimeEnd']),
            ]);
        }

        // 分享状态
        if ($this->originalParams['status']) {
            $query->andWhere([
                'a.status' => $this->originalParams['status'],
            ]);
        }

        // 权益发放状态
        if ($this->originalParams['issueStatus']) {
            // accept_issue_status or share_issue_status
            $query->andWhere([
                'or',
                ['a.accept_issue_status' => $this->originalParams['issueStatus']],
                ['a.share_issue_status' => $this->originalParams['issueStatus']],
            ]);
        }

        // 权益发放状态
        // if ($this->originalParams['acceptIssueStatus']) {
        //     $query->andWhere([
        //         'a.accept_issue_status' => $this->originalParams['acceptIssueStatus'],
        //     ]);
        // }

        if (!$this->isExcel) {
            $count = $query->count();

            $pages = BaseActiveRecord::setPage($count, $this->page, $this->pageSize);

            $list = $query->offset($pages['offset'])
                ->limit($pages['limit'])
                ->orderBy('a.add_time desc')
                ->asArray()
                ->all();
        } else {
            $list = $query->orderBy('a.add_time desc')
                ->asArray()
                ->all();
        }

        $excelList = [];
        foreach ($list as &$v) {
            if (!$this->originalParams['keyword']) {
                $shareResumeInfo   = $this->getResumeInfo($v['shareResumeId']);
                $acceptResumeInfo  = $this->getResumeInfo($v['acceptResumeId']);
                $v['shareName']    = $shareResumeInfo['name'];
                $v['shareUuid']    = $shareResumeInfo['uuid'];
                $v['shareMobile']  = $shareResumeInfo['mobile'];
                $v['acceptName']   = $acceptResumeInfo['name'];
                $v['acceptUuid']   = $acceptResumeInfo['uuid'];
                $v['acceptMobile'] = $acceptResumeInfo['mobile'];
            }

            $v['statusTxt']            = BaseNewResumeActivityAccept::STATUS_LIST[$v['status']];
            $v['shareIssueStatusTxt']  = BaseNewResumeActivityAccept::ISSUE_STATUS_LIST[$v['share_issue_status']];
            $v['acceptIssueStatusTxt'] = BaseNewResumeActivityAccept::ISSUE_STATUS_LIST[$v['accept_issue_status']];

            // 如果status=1，但是实际上shareIssueStatus和acceptIssueStatus都是-1，那么successTime = -
            if ($v['status'] == 1 && $v['share_issue_status'] == -1 && $v['accept_issue_status'] == -1) {
                $v['success_time'] = '-';
            }

            if ($v['status'] != BaseNewResumeActivityAccept::STATUS_SUCCESS) {
                $v['success_time'] = '-';
            }

            // 组成excel所需数据
            if ($this->isExcel) {
                $excelList[] = $this->itemToExcel($v);
            }
        }

        if ($this->isExcel) {
            $this->data = $this->listToExcel($excelList);

            return true;
        }

        $pages = [
            'count' => (int)$count,
            'limit' => (int)$this->pageSize,
            'page'  => (int)$this->page,
        ];

        $this->data = [
            'list'  => $list,
            'pages' => $pages,
        ];
    }

    protected function getResumeInfo($resumeId)
    {
        // 找uuid，名字，手机号
        $resumeInfo = BaseResume::find()
            ->alias('c')
            ->innerJoin(['b' => BaseMember::tableName()], 'b.id = c.member_id')
            ->select([
                'name',
                'uuid',
                'mobile',
            ])
            ->where([
                'c.id' => $resumeId,
            ])
            ->asArray()
            ->one();

        return $resumeInfo;
    }

    protected function handelParams()
    {
        $page     = $this->originalParams['page'] ?? 1;
        $pageSize = $this->originalParams['pageSize'] ?? 20;

        $this->page     = $page;
        $this->pageSize = $pageSize;

        if ($this->originalParams['export']) {
            $this->isExcel = true;
        }
    }

    public function itemToExcel($item)
    {
        return [
            $item['shareUuid'],
            $item['shareName'],
            $item['shareMobile'],
            $item['acceptMobile'],
            $item['acceptUuid'],
            $item['acceptName'],
            $item['add_time'],
            $item['statusTxt'],
            $item['success_time'],
            $item['shareIssueStatusTxt'],
            $item['acceptIssueStatusTxt'],
        ];
    }

    protected function listToExcel($list)
    {
        return [
            'headers' => $this->headers,
            'data'    => $list,
        ];
    }
}
