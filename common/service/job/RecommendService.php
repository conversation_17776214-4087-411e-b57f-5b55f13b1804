<?php

namespace common\service\job;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseMajor;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEducation;
use common\helpers\DebugHelper;
use common\helpers\TimeHelper;
use yii\base\Exception;

class RecommendService extends BaseService
{
    private $majorId;
    private $memberId;
    private $surplusNum;
    private $jobInfo;
    private $fieldList;
    private $jobIdList;
    private $publicQuery;

    /**
     * 设置初始数据
     * @param $params
     * @return $this
     * @throws Exception
     */
    public function setData($params)
    {
        $this->memberId   = $params['memberId'];
        $this->surplusNum = $params['limit'];
        if ($params['memberId']) {
            //获取用户最高学历id
            $memberTopEducationId = BaseResume::findOneVal(['member_id' => $params['memberId']], 'last_education_id');
            //获取最高学历需求专业
            //            $topEducationMajorId = BaseResumeEducation::findOneVal(['id' => $memberTopEducationId], 'major_id');
            $topEducationMajorInfo = BaseResumeEducation::findOne($memberTopEducationId);
            //由于求职者专业是选到3级的，但是职位是到2级到，需要选上一级
            //            $this->majorId = BaseMajor::findOneVal(['id' => $topEducationMajorId], 'parent_id');
            $this->majorId = $topEducationMajorInfo->major_id_level_2;
        }
        //获取职位信息
        $this->jobInfo     = BaseJob::findOne(['id' => $params['jobId']]);
        $this->jobIdList[] = $params['jobId'];

        return $this;
    }

    /**
     * 获取推荐列表
     * @return array
     * @throws \Exception
     */
    public function getRecommendList()
    {
        // 记录开始时间
        $startTime = microtime(true);

        $allList = [];
        if ($this->memberId && $this->majorId) {
            //如果登陆了，且有专业id，那么使用登陆后的规则进行推荐，规则条数为6条
            $actionName = 'loginList';
            $ruleNum    = 6;
            $mode       = '登录用户推荐';
        } else {
            //如果未登陆，或者没有专业id，那么使用没登陆的规则进行推荐，规则条数为5条
            $actionName = 'unLoginList';
            $ruleNum    = 5;
            $mode       = '未登录用户推荐';
        }

        // 记录开始日志
        DebugHelper::jobRecommend([
            'action'     => 'start',
            'job_id'     => $this->jobInfo->id,
            'member_id'  => $this->memberId,
            'limit'      => $this->surplusNum,
            'mode'       => $mode,
            'major_id'   => $this->majorId,
            'start_time' => date('Y-m-d H:i:s'),
        ]);

        $this->getSelectField();
        $ruleStats = []; // 记录每个规则的统计信息

        for ($i = 1; $i < $ruleNum + 1; $i++) {
            $fullActionName = $actionName . $i;
            if ($this->surplusNum > 0) {
                // 记录规则开始时间
                $ruleStartTime = microtime(true);

                //调用公告的查询方法
                $this->getPublicQuery();
                //调用具体的查询方法
                $list = self::$fullActionName();

                // 记录规则执行时间和数据量
                $ruleEndTime       = microtime(true);
                $ruleExecutionTime = round(($ruleEndTime - $ruleStartTime) * 1000, 2); // 转换为毫秒
                $dataCount         = count($list);

                $ruleStats[] = [
                    'rule'              => "规则{$i}",
                    'method'            => $fullActionName,
                    'data_count'        => $dataCount,
                    'execution_time_ms' => $ruleExecutionTime,
                ];

                foreach ($list as &$item) {
                    $this->jobIdList[] = $item['id'];
                    $item['sort']      = $i;
                }
                $allList[] = $list;
            } else {
                break;
            }
        }
        //去除数组里的空数组
        $jobList = [];
        foreach ($allList as $list) {
            $jobList = array_merge($jobList, $list);
        }

        // 记录规则执行完成时间
        $rulesEndTime       = microtime(true);
        $rulesExecutionTime = round(($rulesEndTime - $startTime) * 1000, 2);

        //补充字段
        $fullInfoStartTime     = microtime(true);
        $result                = $this->getFullInfo($jobList);
        $fullInfoEndTime       = microtime(true);
        $fullInfoExecutionTime = round(($fullInfoEndTime - $fullInfoStartTime) * 1000, 2);

        // 记录结束时间和总体统计
        $endTime            = microtime(true);
        $totalExecutionTime = round(($endTime - $startTime) * 1000, 2); // 转换为毫秒

        // 记录完成日志
        DebugHelper::jobRecommend([
            'action'                     => 'complete',
            'job_id'                     => $this->jobInfo->id,
            'total_data_count'           => count($result),
            'rule_stats'                 => $ruleStats,
            'rules_execution_time_ms'    => $rulesExecutionTime,
            'fullinfo_execution_time_ms' => $fullInfoExecutionTime,
            'total_execution_time_ms'    => $totalExecutionTime,
            'end_time'                   => date('Y-m-d H:i:s'),
        ]);

        // 性能监控：如果执行时间超过1秒，记录性能警告
        if ($totalExecutionTime > 1000) {
            // 构建用户信息
            $userInfo = $this->memberId ? "用户ID:{$this->memberId}" : "未登录用户";
            if ($this->majorId) {
                $userInfo .= ", 专业ID:{$this->majorId}";
            }

            // 构建查询条件摘要
            $querySummary = "单位ID:{$this->jobInfo->company_id}, ";
            $querySummary .= "省份ID:{$this->jobInfo->province_id}, ";
            $querySummary .= "学历要求:{$this->jobInfo->education_type}, ";
            $querySummary .= "职位类型:{$this->jobInfo->job_category_id}";

            // 添加数据处理时间到规则统计中
            $enhancedRuleStats   = $ruleStats;
            $enhancedRuleStats[] = [
                'rule'              => '数据处理',
                'method'            => 'getFullInfo',
                'data_count'        => count($result),
                'execution_time_ms' => $fullInfoExecutionTime,
            ];

            DebugHelper::performanceAlert([
                'algorithm_type'             => '职位',
                'target_id'                  => $this->jobInfo->id,
                'total_execution_time_ms'    => $totalExecutionTime,
                'rules_execution_time_ms'    => $rulesExecutionTime,
                'fullinfo_execution_time_ms' => $fullInfoExecutionTime,
                'total_data_count'           => count($result),
                'rule_stats'                 => $enhancedRuleStats,
                'user_info'                  => $userInfo,
                'query_summary'              => $querySummary,
                'timestamp'                  => date('Y-m-d H:i:s'),
            ]);
        }

        return $result;
    }

    /**
     * 获取完整的信息列表
     * @param $jobList
     * @return array
     * @throws \Exception
     */
    private function getFullInfo($jobList)
    {
        if (empty($jobList)) {
            return [];
        }

        // 记录详细的性能监控
        $detailStats    = [];
        $totalStartTime = microtime(true);

        // 1. 批量获取地区信息 - 详细监控
        $areaStartTime = microtime(true);
        $cityIds       = array_unique(array_column($jobList, 'city_id'));
        $areaMap       = [];
        $areaCallStats = [];

        foreach ($cityIds as $cityId) {
            $singleAreaStart  = microtime(true);
            $areaMap[$cityId] = BaseArea::getAreaName($cityId);
            $singleAreaEnd    = microtime(true);
            $areaCallStats[]  = [
                'city_id' => $cityId,
                'time_ms' => round(($singleAreaEnd - $singleAreaStart) * 1000, 2),
            ];
        }

        $areaEndTime               = microtime(true);
        $detailStats['area_query'] = [
            'operation'     => '地区名称查询',
            'count'         => count($cityIds),
            'time_ms'       => round(($areaEndTime - $areaStartTime) * 1000, 2),
            'avg_per_call'  => round((($areaEndTime - $areaStartTime) * 1000) / count($cityIds), 2),
            'slowest_calls' => array_slice($this->getSlowestCalls($areaCallStats), 0, 3),
        ];

        // 2. 批量获取单位信息 - 详细监控
        $companyStartTime = microtime(true);
        $companyIds       = array_unique(array_column($jobList, 'company_id'));
        $companyMap       = [];
        $companyCallStats = [];
        $companyDbTime    = 0;

        foreach ($companyIds as $companyId) {
            $singleCompanyStart = microtime(true);
            $companyInfo        = BaseCompany::findOne(['id' => $companyId]);
            $singleCompanyEnd   = microtime(true);
            $singleCallTime     = round(($singleCompanyEnd - $singleCompanyStart) * 1000, 2);
            $companyDbTime      += $singleCallTime;

            if ($companyInfo) {
                $companyMap[$companyId] = [
                    'full_name' => $companyInfo['full_name'],
                    'type'      => $companyInfo['type'],
                    'nature'    => $companyInfo['nature'],
                ];
            }

            $companyCallStats[] = [
                'company_id' => $companyId,
                'time_ms'    => $singleCallTime,
                'found'      => $companyInfo ? 'yes' : 'no',
            ];
        }

        $companyEndTime               = microtime(true);
        $detailStats['company_query'] = [
            'operation'     => '单位信息查询',
            'count'         => count($companyIds),
            'time_ms'       => round(($companyEndTime - $companyStartTime) * 1000, 2),
            'avg_per_call'  => round((($companyEndTime - $companyStartTime) * 1000) / count($companyIds), 2),
            'db_time_total' => $companyDbTime,
            'slowest_calls' => array_slice($this->getSlowestCalls($companyCallStats), 0, 3),
        ];

        // 3. 批量获取字典数据 - 详细监控
        $dictStartTime   = microtime(true);
        $educationTypes  = array_unique(array_column($jobList, 'education_type'));
        $experienceTypes = array_unique(array_column($jobList, 'experience_type'));
        $companyTypes    = array_unique(array_column($companyMap, 'type'));
        $companyNatures  = array_unique(array_column($companyMap, 'nature'));

        // 学历字典查询监控
        $educationStart     = microtime(true);
        $educationMap       = [];
        $educationCallStats = [];
        foreach ($educationTypes as $type) {
            $singleStart          = microtime(true);
            $educationMap[$type]  = BaseDictionary::getEducationName($type);
            $singleEnd            = microtime(true);
            $educationCallStats[] = [
                'type'    => $type,
                'time_ms' => round(($singleEnd - $singleStart) * 1000, 2),
            ];
        }
        $educationEnd = microtime(true);

        // 经验字典查询监控
        $experienceStart     = microtime(true);
        $experienceMap       = [];
        $experienceCallStats = [];
        foreach ($experienceTypes as $type) {
            $singleStart           = microtime(true);
            $experienceMap[$type]  = BaseDictionary::getExperienceName($type);
            $singleEnd             = microtime(true);
            $experienceCallStats[] = [
                'type'    => $type,
                'time_ms' => round(($singleEnd - $singleStart) * 1000, 2),
            ];
        }
        $experienceEnd = microtime(true);

        // 公司类型字典查询监控
        $companyTypeStart     = microtime(true);
        $companyTypeMap       = [];
        $companyTypeCallStats = [];
        foreach ($companyTypes as $type) {
            $singleStart            = microtime(true);
            $companyTypeMap[$type]  = BaseDictionary::getCompanyTypeName($type);
            $singleEnd              = microtime(true);
            $companyTypeCallStats[] = [
                'type'    => $type,
                'time_ms' => round(($singleEnd - $singleStart) * 1000, 2),
            ];
        }
        $companyTypeEnd = microtime(true);

        // 公司性质字典查询监控
        $companyNatureStart     = microtime(true);
        $companyNatureMap       = [];
        $companyNatureCallStats = [];
        foreach ($companyNatures as $nature) {
            $singleStart               = microtime(true);
            $companyNatureMap[$nature] = BaseDictionary::getCompanyNatureName($nature);
            $singleEnd                 = microtime(true);
            $companyNatureCallStats[]  = [
                'nature'  => $nature,
                'time_ms' => round(($singleEnd - $singleStart) * 1000, 2),
            ];
        }
        $companyNatureEnd = microtime(true);

        $dictEndTime               = microtime(true);
        $detailStats['dict_query'] = [
            'operation'      => '字典数据查询',
            'total_count'    => count($educationTypes) + count($experienceTypes) + count($companyTypes) + count($companyNatures),
            'total_time_ms'  => round(($dictEndTime - $dictStartTime) * 1000, 2),
            'education'      => [
                'count'         => count($educationTypes),
                'time_ms'       => round(($educationEnd - $educationStart) * 1000, 2),
                'avg_per_call'  => count($educationTypes) > 0 ? round((($educationEnd - $educationStart) * 1000) / count($educationTypes),
                    2) : 0,
                'slowest_calls' => array_slice($this->getSlowestCalls($educationCallStats), 0, 2),
            ],
            'experience'     => [
                'count'         => count($experienceTypes),
                'time_ms'       => round(($experienceEnd - $experienceStart) * 1000, 2),
                'avg_per_call'  => count($experienceTypes) > 0 ? round((($experienceEnd - $experienceStart) * 1000) / count($experienceTypes),
                    2) : 0,
                'slowest_calls' => array_slice($this->getSlowestCalls($experienceCallStats), 0, 2),
            ],
            'company_type'   => [
                'count'         => count($companyTypes),
                'time_ms'       => round(($companyTypeEnd - $companyTypeStart) * 1000, 2),
                'avg_per_call'  => count($companyTypes) > 0 ? round((($companyTypeEnd - $companyTypeStart) * 1000) / count($companyTypes),
                    2) : 0,
                'slowest_calls' => array_slice($this->getSlowestCalls($companyTypeCallStats), 0, 2),
            ],
            'company_nature' => [
                'count'         => count($companyNatures),
                'time_ms'       => round(($companyNatureEnd - $companyNatureStart) * 1000, 2),
                'avg_per_call'  => count($companyNatures) > 0 ? round((($companyNatureEnd - $companyNatureStart) * 1000) / count($companyNatures),
                    2) : 0,
                'slowest_calls' => array_slice($this->getSlowestCalls($companyNatureCallStats), 0, 2),
            ],
        ];

        // 4. 批量获取公告信息 - 详细监控
        $announcementStartTime = microtime(true);
        $announcementIds       = array_filter(array_unique(array_column($jobList, 'announcement_id')));

        // 监控SQL查询时间
        $sqlStart        = microtime(true);
        $announcementMap = BaseAnnouncement::find()
            ->select(['title'])
            ->where(['id' => $announcementIds])
            ->indexBy('id')
            ->asArray()
            ->all();
        $sqlEnd          = microtime(true);

        $announcementEndTime               = microtime(true);
        $detailStats['announcement_query'] = [
            'operation'   => '公告信息查询',
            'count'       => count($announcementIds),
            'time_ms'     => round(($announcementEndTime - $announcementStartTime) * 1000, 2),
            'sql_time_ms' => round(($sqlEnd - $sqlStart) * 1000, 2),
            'found_count' => count($announcementMap),
            'query_type'  => 'batch_sql',
        ];

        // 5. 数据组装和格式化 - 详细监控
        $formatStartTime = microtime(true);
        $formatStats     = [
            'wage_format_time' => 0,
            'url_format_time'  => 0,
            'time_format_time' => 0,
            'data_lookup_time' => 0,
        ];

        foreach ($jobList as $k => &$job) {
            $itemStart = microtime(true);

            // 数据查找时间
            $lookupStart                     = microtime(true);
            $job['areaName']                 = $areaMap[$job['city_id']] ?? '';
            $companyInfo                     = $companyMap[$job['company_id']] ?? [];
            $job['companyName']              = $companyInfo['full_name'] ?? '';
            $job['companyType']              = $companyTypeMap[$companyInfo['type']] ?? '';
            $job['companyNature']            = $companyNatureMap[$companyInfo['nature']] ?? '';
            $job['education']                = $educationMap[$job['education_type']] ?? '';
            $job['experience']               = $experienceMap[$job['experience_type']] ?? '';
            $job['announcement']             = $announcementMap[$job['announcement_id']]['title'] ?? '';
            $lookupEnd                       = microtime(true);
            $formatStats['data_lookup_time'] += ($lookupEnd - $lookupStart) * 1000;

            // 薪资格式化时间
            $wageStart                       = microtime(true);
            $job['wage']                     = BaseJob::formatWage($job['min_wage'], $job['max_wage'],
                $job['wage_type']);
            $wageEnd                         = microtime(true);
            $formatStats['wage_format_time'] += ($wageEnd - $wageStart) * 1000;

            // URL生成时间
            $urlStart                       = microtime(true);
            $job['url']                     = BaseJob::getDetailUrl($job['id']);
            $urlEnd                         = microtime(true);
            $formatStats['url_format_time'] += ($urlEnd - $urlStart) * 1000;

            // 时间格式化时间
            $timeStart                       = microtime(true);
            $job['refreshTime']              = TimeHelper::formatDateByYear($job['refresh_time']);
            $timeEnd                         = microtime(true);
            $formatStats['time_format_time'] += ($timeEnd - $timeStart) * 1000;
        }

        $formatEndTime              = microtime(true);
        $detailStats['data_format'] = [
            'operation'     => '数据组装格式化',
            'count'         => count($jobList),
            'total_time_ms' => round(($formatEndTime - $formatStartTime) * 1000, 2),
            'avg_per_item'  => round((($formatEndTime - $formatStartTime) * 1000) / count($jobList), 2),
            'breakdown'     => [
                'data_lookup_ms' => round($formatStats['data_lookup_time'], 2),
                'wage_format_ms' => round($formatStats['wage_format_time'], 2),
                'url_format_ms'  => round($formatStats['url_format_time'], 2),
                'time_format_ms' => round($formatStats['time_format_time'], 2),
            ],
        ];

        // 记录总体统计
        $totalEndTime = microtime(true);
        $totalTime    = round(($totalEndTime - $totalStartTime) * 1000, 2);

        // 只有超过1000ms才记录详细性能日志到performanceAlert.log
        if ($totalTime > 1000) {
            DebugHelper::performanceAlert([
                'algorithm_type'          => '职位推荐',
                'detail_type'             => 'getFullInfo_detail',
                'target_id'               => $this->jobInfo->id,
                'data_count'              => count($jobList),
                'total_execution_time_ms' => $totalTime,
                'detail_stats'            => $detailStats,
            ]);
        }

        return $jobList;
    }

    /**
     * 设置要获取的字段
     * @return void
     */
    private function getSelectField()
    {
        $this->fieldList = [
            'j.id',
            'j.name',
            'j.wage_type',
            'j.min_wage',
            'j.max_wage',
            'j.experience_type',
            'j.education_type',
            'j.amount',
            'j.city_id',
            'j.announcement_id',
            'j.company_id',
            'j.refresh_time',
        ];
    }

    private function getPublicQuery()
    {
        $this->publicQuery = BaseJob::find()
            ->alias('j')
            ->where(['j.status' => BaseJob::STATUS_ONLINE])
            ->andFilterWhere([
                'not in',
                'j.id',
                $this->jobIdList,
            ])
            ->select($this->fieldList)
            ->limit($this->surplusNum)
            ->asArray();
    }

    /**
     * 用户登陆情况的第一种情况
     * 当前浏览职位所属单位的其他职位，满足：
     * 职位工作地点所属省份、学历要求、职位类型同当前所浏览的职位信息匹配，且需求专业包含求职者需求专业（指求职者最高学历的需求专业；须上溯至二级专业）的职位；
     * @return array|\yii\db\ActiveRecord[]
     */
    private function loginList1()
    {
        // 记录规则详细信息
        DebugHelper::jobRecommend([
            'rule_detail'     => '登录规则1执行',
            'job_id'          => $this->jobInfo->id,
            'member_id'       => $this->memberId,
            'rule_name'       => '同单位同省份同学历同类型匹配专业职位',
            'company_id'      => $this->jobInfo->company_id,
            'province_id'     => $this->jobInfo->province_id,
            'education_type'  => $this->jobInfo->education_type,
            'job_category_id' => $this->jobInfo->job_category_id,
            'major_id'        => $this->majorId,
        ]);

        $list = $this->publicQuery->andWhere([
            'province_id'     => $this->jobInfo->province_id,
            'company_id'      => $this->jobInfo->company_id,
            'education_type'  => $this->jobInfo->education_type,
            'job_category_id' => $this->jobInfo->job_category_id,
        ])
            ->innerJoin(['r' => BaseJobMajorRelation::tableName()], 'r.job_id=j.id')
            ->andWhere(['r.major_id' => $this->majorId])
            ->all();

        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * 用户登陆情况的第2种情况
     * 其他合作单位的职位：职位工作地点所属省份、职位类型同当前所浏览的职位匹配，且需求专业包含求职者需求专业（最高学历的需求专业；二级专业）的职位；
     * @return array|\yii\db\ActiveRecord[]
     */
    private function loginList2()
    {
        // 记录规则详细信息
        DebugHelper::jobRecommend([
            'rule_detail'        => '登录规则2执行',
            'job_id'             => $this->jobInfo->id,
            'member_id'          => $this->memberId,
            'rule_name'          => '其他合作单位同省份同类型匹配专业职位',
            'province_id'        => $this->jobInfo->province_id,
            'job_category_id'    => $this->jobInfo->job_category_id,
            'major_id'           => $this->majorId,
            'cooperation_status' => BaseCompany::COOPERATIVE_UNIT_YES,
        ]);

        $list = $this->publicQuery->andWhere([
            'j.province_id'        => $this->jobInfo->province_id,
            'j.job_category_id'    => $this->jobInfo->job_category_id,
            'j.is_job_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
        ])
            ->innerJoin(['r' => BaseJobMajorRelation::tableName()], 'r.job_id=j.id')
            ->andWhere(['r.major_id' => $this->majorId])
            ->all();

        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * 用户登陆情况的第3种情况
     * 非合作单位的职位：职位工作地点所属省份、职位类型同当前所浏览的职位匹配，且需求专业包含求职者需求专业（最高学历的需求专业；二级专业）的职位；
     * @return array|\yii\db\ActiveRecord[]
     */
    private function loginList3()
    {
        // 记录规则详细信息
        DebugHelper::jobRecommend([
            'rule_detail'        => '登录规则3执行',
            'job_id'             => $this->jobInfo->id,
            'member_id'          => $this->memberId,
            'rule_name'          => '非合作单位同省份同类型匹配专业职位',
            'province_id'        => $this->jobInfo->province_id,
            'job_category_id'    => $this->jobInfo->job_category_id,
            'major_id'           => $this->majorId,
            'cooperation_status' => BaseCompany::COOPERATIVE_UNIT_NO,
        ]);

        $list = $this->publicQuery->andWhere([
            'j.province_id'        => $this->jobInfo->province_id,
            'j.job_category_id'    => $this->jobInfo->job_category_id,
            'j.is_job_cooperation' => BaseCompany::COOPERATIVE_UNIT_NO,
        ])
            ->innerJoin(['r' => BaseJobMajorRelation::tableName()], 'r.job_id=j.id')
            ->andWhere(['r.major_id' => $this->majorId])
            ->all();

        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * 用户登陆情况的第4种情况
     * 随机推荐需求专业包含求职者需求专业（最高学历的需求专业；二级专业）的职位
     * @return array|\yii\db\ActiveRecord[]
     */
    private function loginList4()
    {
        // 记录规则详细信息
        DebugHelper::jobRecommend([
            'rule_detail' => '登录规则4执行',
            'job_id'      => $this->jobInfo->id,
            'member_id'   => $this->memberId,
            'rule_name'   => '随机匹配专业职位',
            'major_id'    => $this->majorId,
        ]);

        $list             = $this->publicQuery->innerJoin(['r' => BaseJobMajorRelation::tableName()], 'r.job_id=j.id')
            ->andWhere(['r.major_id' => $this->majorId])
            ->all();
        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * 用户登陆情况的第5种情况
     * 随机推荐职位类型同当前所浏览职位相同的职位
     * @return array|\yii\db\ActiveRecord[]
     */
    private function loginList5()
    {
        // 记录规则详细信息
        DebugHelper::jobRecommend([
            'rule_detail'     => '登录规则5执行',
            'job_id'          => $this->jobInfo->id,
            'member_id'       => $this->memberId,
            'rule_name'       => '随机同类型职位',
            'job_category_id' => $this->jobInfo->job_category_id,
        ]);

        $list             = $this->publicQuery->andWhere(['job_category_id' => $this->jobInfo->job_category_id])
            ->all();
        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * 用户登陆情况的第6种情况
     * 随机推荐职位工作地点所属省份同当前所浏览职位匹配的职位
     * @return array|\yii\db\ActiveRecord[]
     */
    private function loginList6()
    {
        // 记录规则详细信息
        DebugHelper::jobRecommend([
            'rule_detail' => '登录规则6执行',
            'job_id'      => $this->jobInfo->id,
            'member_id'   => $this->memberId,
            'rule_name'   => '随机同省份职位',
            'province_id' => $this->jobInfo->province_id,
        ]);

        $list             = $this->publicQuery->andWhere(['province_id' => $this->jobInfo->province_id])
            ->all();
        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * 用户未登陆情况的第1种情况
     * 职位工作地点所属省份、学历要求、职位类型同当前所浏览的职位匹配的职位；
     * @return array|\yii\db\ActiveRecord[]
     */
    private function unLoginList1()
    {
        // 记录规则详细信息
        DebugHelper::jobRecommend([
            'rule_detail'     => '未登录规则1执行',
            'job_id'          => $this->jobInfo->id,
            'rule_name'       => '同单位同省份同学历同类型职位',
            'company_id'      => $this->jobInfo->company_id,
            'province_id'     => $this->jobInfo->province_id,
            'education_type'  => $this->jobInfo->education_type,
            'job_category_id' => $this->jobInfo->job_category_id,
        ]);

        $list             = $this->publicQuery->andWhere([
            'province_id'     => $this->jobInfo->province_id,
            'company_id'      => $this->jobInfo->company_id,
            'education_type'  => $this->jobInfo->education_type,
            'job_category_id' => $this->jobInfo->job_category_id,
        ])
            ->all();
        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * 用户未登陆情况的第2种情况
     * 其他合作单位的职位：职位工作地点所属省份、职位类型同当前所浏览的职位匹配的职位；
     * @return array|\yii\db\ActiveRecord[]
     */
    private function unLoginList2()
    {
        // 记录规则详细信息
        DebugHelper::jobRecommend([
            'rule_detail'        => '未登录规则2执行',
            'job_id'             => $this->jobInfo->id,
            'rule_name'          => '其他合作单位同省份同类型职位',
            'province_id'        => $this->jobInfo->province_id,
            'job_category_id'    => $this->jobInfo->job_category_id,
            'cooperation_status' => BaseCompany::COOPERATIVE_UNIT_YES,
        ]);

        $list             = $this->publicQuery->andWhere([
            'j.province_id'        => $this->jobInfo->province_id,
            'j.job_category_id'    => $this->jobInfo->job_category_id,
            'j.is_job_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
        ])
            ->all();
        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * 用户未登陆情况的第3种情况
     * 非合作单位的职位：职位工作地点所属省份、职位类型同当前所浏览的职位匹配的职位；
     * @return array|\yii\db\ActiveRecord[]
     */
    private function unLoginList3()
    {
        // 记录规则详细信息
        DebugHelper::jobRecommend([
            'rule_detail'        => '未登录规则3执行',
            'job_id'             => $this->jobInfo->id,
            'rule_name'          => '非合作单位同省份同类型职位',
            'province_id'        => $this->jobInfo->province_id,
            'job_category_id'    => $this->jobInfo->job_category_id,
            'cooperation_status' => BaseCompany::COOPERATIVE_UNIT_NO,
        ]);

        $list             = $this->publicQuery->andWhere([
            'j.province_id'        => $this->jobInfo->province_id,
            'j.job_category_id'    => $this->jobInfo->job_category_id,
            'j.is_job_cooperation' => BaseCompany::COOPERATIVE_UNIT_NO,
        ])
            ->all();
        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * 用户未登陆情况的第4种情况
     * 随机推荐职位类型同当前所浏览职位相同的职位
     * @return array|\yii\db\ActiveRecord[]
     */
    private function unLoginList4()
    {
        // 记录规则详细信息
        DebugHelper::jobRecommend([
            'rule_detail'     => '未登录规则4执行',
            'job_id'          => $this->jobInfo->id,
            'rule_name'       => '随机同类型职位',
            'job_category_id' => $this->jobInfo->job_category_id,
        ]);

        $list             = $this->publicQuery->andWhere(['job_category_id' => $this->jobInfo->job_category_id])
            ->all();
        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * 用户未登陆情况的第5种情况
     * 随机推荐职位工作地点所属省份同当前所浏览职位匹配的职位
     * @return array|\yii\db\ActiveRecord[]
     */
    private function unLoginList5()
    {
        // 记录规则详细信息
        DebugHelper::jobRecommend([
            'rule_detail' => '未登录规则5执行',
            'job_id'      => $this->jobInfo->id,
            'rule_name'   => '随机同省份职位',
            'province_id' => $this->jobInfo->province_id,
        ]);

        $list             = $this->publicQuery->andWhere(['province_id' => $this->jobInfo->province_id])
            ->all();
        $this->surplusNum -= count($list);

        return $list;
    }

    /**
     * 获取最慢的调用记录（按时间倒序排列）
     * @param array $callStats
     * @return array
     */
    private function getSlowestCalls($callStats)
    {
        usort($callStats, function ($a, $b) {
            return $b['time_ms'] <=> $a['time_ms'];
        });

        return $callStats;
    }

}