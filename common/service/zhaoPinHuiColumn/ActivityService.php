<?php
/**
 * create user：shannon
 * create time：2025/2/12 上午9:24
 */
namespace common\service\zhaoPinHuiColumn;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArticle;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseHwActivityAnnouncement;
use common\base\models\BaseHwActivityCompanyHot;
use common\base\models\BaseJob;
use common\base\models\BaseShowcase;
use common\helpers\FileHelper;
use common\helpers\FormatConverter;
use common\helpers\UrlHelper;
use common\service\search\SearchParamsService;
use Yii;
use common\base\models\BaseActivityFormOptionSign;
use common\base\models\BaseHwActivity;
use common\base\models\BaseHwActivityFeatureTagRelation;
use common\base\models\BaseHwActivitySession;
use common\base\models\BaseHwSpecialActivity;
use common\base\models\BaseHwSpecialActivityRelation;
use common\helpers\IpHelper;
use common\helpers\TimeHelper;
use queue\ClickJob;
use queue\Producer;
use yii\helpers\ArrayHelper;

/**
 * 处理活动服务类
 * Class ActivityService
 * @package common\service\zhaoPinHuiColumn
 */
class ActivityService extends BaseService
{
    private static $instance;
    private        $activityId;

    public static function getInstance()
    {
        if (empty(self::$instance)) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    private function setActivityId($activityId)
    {
        if (!$activityId) {
            //去404
            $this->redirect404();
        }
        $this->activityId = $activityId;
        //没有设置平台就补充一下
        if (!$this->operationPlatform) {
            switch (PLATFORM) {
                case 'PC':
                    $this->operationPlatform = self::PLATFORM_WEB;
                    break;
                case 'H5';
                    $this->operationPlatform = self::PLATFORM_H5;
                    break;
                case 'ADMIN':
                    $this->operationPlatform = self::PLATFORM_ADMIN;
                    break;
                case 'MINI':
                    $this->operationPlatform = self::PLATFORM_MINI;
                    break;
                case 'BOSHIHOU';
                    $this->operationPlatform = self::PLATFORM_BOSHIHOU;
                    break;
                case 'ZHAOPINHUI':
                    $this->operationPlatform = self::PLATFORM_ZHAOPINHUI;
                    break;
            }
        }

        return $this;
    }

    /**
     * 活动详情页
     */
    public function runDetail($params)
    {
        $this->setActivityId($params['activityId']);
        $this->setUser();
        $activityInfo = BaseHwActivity::findOne($this->activityId);
        if (in_array($activityInfo->series_type, BaseHwActivity::ZHAOPINHUI_UN_TYPE)) {
            $this->redirect404();
        }
        $sessionInfo           = BaseHwActivitySession::findOne(['activity_id' => $this->activityId]);
        $startDatetime         = $sessionInfo->start_time ? ($sessionInfo->start_date . ' ' . substr_replace($sessionInfo->start_time,
                ':', 2, 0) . ':00') : TimeHelper::dayToBeginTime($sessionInfo->start_date);
        $activityCountDownTime = ($startDatetime != TimeHelper::ZERO_DATE) ? (strtotime($startDatetime) - time()) : 0;
        $activityCountDownTime = $activityCountDownTime > 0 ? $activityCountDownTime : 0;
        if ($activityCountDownTime <= 0) {
            BaseHwActivity::updateActivityBySession($this->activityId);
        }

        //获取活动详情
        $activityInfo = BaseHwActivity::find()
            ->select([
                'id',
                'series_type as seriesType',
                'type',
                'name',
                'longitude',
                'latitude',
                'to_hold_type as toHoldType',
                'custom_feature_tag as customFeatureTag',
                'activity_benefits as activityBenefits',
                'activity_benefits_content as activityBenefitsContent',
                'attendance_notes as attendanceNotes',
                'image_pc_banner_id as imagePcBannerId',
                'image_mini_banner_id as imageMiniBannerId',
                'image_service_code_id as imageServiceCodeId',
                'image_notice_id as imageNoticeId',
                'activity_child_status as activityChildStatus',
                'activity_highlights as activityHighlights',
                'activity_highlights_title as activityHighlightsTitle',
                'activity_organization as activityOrganization',
                'template_id as templateId',
                'participation_company_amount as participationCompanyAmount',
                'apply_link_person_type as applyLinkPersonType',
                'apply_link_person_form_id as applyLinkPersonFormId',
                'apply_link_person_form_option_id as applyLinkPersonFormOptionId',
                'apply_person_time as applyPersonTime',
                'apply_link_company as applyLinkCompany',
                'apply_company_time as applyCompanyTime',
                'activity_detail as activityDetail',
                'participation_method as participationMethod',
                'wonderful_review as wonderfulReview',
                'activity_number as activityNumber',
                'participation_company_amount as participationCompanyAmount',
            ])
            ->where(['id' => $this->activityId])
            ->asArray()
            ->one();
        if (!$activityInfo) {
            //去404
            $this->redirect404();
        }
        //统计点击量
        $this->click($activityInfo['id']);
        //活动详情数据一些字段数据处理
        //banner图
        $activityInfo['imagePcBannerUrl']       = FileHelper::getFullPathById($activityInfo['imagePcBannerId']);
        $activityInfo['imageMiniBannerUrl']     = FileHelper::getFullPathById($activityInfo['imageMiniBannerId']);
        $activityInfo['imageServiceCodeUrl']    = FileHelper::getFullPathById($activityInfo['imageServiceCodeId']);
        $activityInfo['imageNoticeUrl']         = FileHelper::getFullPathById($activityInfo['imageNoticeId']);
        $activityInfo['activityHighlightsText'] = strip_tags($activityInfo['activityHighlights']);
        //专场名称显示
        $activityInfo['specialName'] = $this->getActivitySpecialName($activityInfo['id']);
        //子状态文案
        $activityInfo['activityChildStatusText'] = BaseHwActivity::ACTIVITY_CHILD_STATUS_TEXT_LIST[$activityInfo['seriesType']][$activityInfo['activityChildStatus']];
        //举办方式为文案
        $activityInfo['toHoldTypeText'] = $activityInfo['toHoldType'] ? BaseHwActivity::TO_HOLD_TYPE_TEXT[$activityInfo['toHoldType']] : '';
        //活动系列类型文案
        $activityInfo['tagList']   = [];
        $activityInfo['typeText']  = BaseHwActivity::TYPE_TEXT_LIST[$activityInfo['type']];
        $activityInfo['tagList'][] = [
            'type'  => 1,
            'value' => $activityInfo['toHoldTypeText'],
        ];
        //(系统)特色标签文案-数组
        $activityInfo['featureTagArray'] = BaseHwActivityFeatureTagRelation::getFeatureTagName($activityInfo['id']);
        foreach ($activityInfo['featureTagArray'] as $featureTag) {
            $activityInfo['tagList'][] = [
                'type'  => 2,
                'value' => $featureTag,
            ];
        }
        //自定义 特色标签-数组
        $activityInfo['customFeatureTagArray'] = $activityInfo['customFeatureTag'] ? explode(',',
            $activityInfo['customFeatureTag']) : [];
        foreach ($activityInfo['customFeatureTagArray'] as $customFeatureTag) {
            $activityInfo['tagList'][] = [
                'type'  => 3,
                'value' => $customFeatureTag,
            ];
        }
        //活动时间：读取活动所录入的“活动时间” 段：
        //1、若为 选择时间录入：展示所选时间段：
        //（1）若开始日期=结束日期，显示格式： {开始日期} {开始日期星期} {开始时间} -{结束时间}；例:2025年04月15日（周二） 14:30-17:30
        //（2）若开始日期<结束日期，显示格式为： {开始日期}  ～ {结束日期}；例:2025年04月15日~2025年04月16日
        //（3）其中，开始&结束日期，显示为 X年X月X日；开始&结束时间，显示为 时:分；无则不显示；
        //2、若为 自定义录入：直接显示自定义录入的文本；完整展示。
        $activityInfo['activityDateTime'] = !$sessionInfo->custom_time ? BaseService::formatActivityDateText(2,
            $sessionInfo->start_date, $sessionInfo->end_date, $sessionInfo->start_time,
            $sessionInfo->end_time) : $sessionInfo->custom_time;
        // 活动地点:读取所录入的“活动地点”字段内容，完整展示:
        // 1、若为 自定义录入，则直接展示自定义录入文本内容;
        // 2、若为 选择地点录入，显示格式:{所选城市}{详细地址};完整展示(1)所选城市:
        // ① 显示所选的城市名称(PS:国内地区城市为三级，海外地区城市为四级);
        // ② 若为海外地区，且四级选中了“全国”，则需显示为对应的国家名称;(如:选中了“日本-全国”，则此处展示为“日本”)
        // ③ 多个地点时，用““”隔开，完整展示;(2)详细地址未录入则不展示;亦不拼接“.”
        // 3、若活动“举办方式”仅选择了“线上”，则“活动地点”字段标题文案改为“活动平台”;
        $activityInfo['activityArea']          = BaseHwActivity::getAllAddressBySeries($activityInfo['id']) . ($sessionInfo->detail_address ? '·' . $sessionInfo->detail_address : '');
        $activityInfo['activityDetailAddress'] = $sessionInfo->detail_address;
        // “参会福利”提示栏：
        // 1、若活动“参会福利”字段非空，则本提示栏展示“参会福利”字段内容；最多展示1行，超出“…”展示；
        //       ① 若“福利详情”字段非空，展示“     ”；点击提示栏，当前页弹出福利详情提示框，展示“福利详情”字段内容；完整展示。
        //       ② 若“福利详情”字段为空，不展示“    ”按钮，点击提示栏无交互；、
        // 2、若“参会福利”字段为空，则不展示该提示栏；“基本信息”模块布局自适应展示；
        //// 格式化参会详情---一级标题
        $activityInfo['activityBenefitsContentFormat'] = $this->operationPlatform != self::PLATFORM_MINI ? self::formatContentV1($activityInfo['activityBenefitsContent']) : self::formatContentMiniV1($activityInfo['activityBenefitsContent']);
        unset($activityInfo['activityBenefitsContent']);
        // 倒计时
        // 若活动子状态为“即将开始”，则展示倒计时悬浮栏：
        // 展示格式:  “距活动开始还有： X天X时X分X秒”
        // 计算逻辑：由当前时间开始至活动开始时间结束，动态计算并展示倒计时，精确到秒。
        // 2、若活动子状态非“即将开始”，不展示该倒计时悬浮栏
        $activityInfo['activityCountDownTime'] = $activityCountDownTime;

        //2、需求人数（02.13新增字段）：展示本场活动需求人数：
        //① 优先展示活动的“需求人数”字段内容；
        //② 若“需求人数”字段为空，则 计算本活动关联公告在线&下线（不含隐藏）职位“招聘人数”字段之和（若“招聘人数”=若干，则“若干”取50进行计算）：
        //    展示文案判断：若 数值＜100，则展示文案：更新中 ；
        //    若 数值≥100，则展示具体需求人数数据；
        if (!$activityInfo['activityNumber']) {
            $activityInfo['activityNumber'] = $this->getActivityNumber($activityInfo['id']);
        }

        // 热门单位
        // 1、读取活动【热门单位】模块添加单位；排序号越大越靠前。字段说明见卡片标注。
        // 2.一行展示不完，展示轮播条，每隔5s自动切换至下一页；支持手动切换查看。
        // 3、若添加单位数量为0，则整个模块隐藏；
        // 4、鼠标移入时，卡片展示投影效果；
        // 5、点击卡片，读取该单位“落地链接”配置：
        //  若为“公告详情”，则新页面打开单位本活动关联公告详情页，取初始发布时间最早的那条；
        //  若为“单位主页”，则新页面打开单位主页-单位介绍tab；
        $companyHotList = $this->getCompanyHotList($activityInfo['id']);

        //相关活动推荐
        //1、展示及交互
        //  1.1 若本活动有所属专场，且所属专场关联活动数量>2，则展示本推荐模块:
        //      (1)展示符合推荐规则的活动。若一行展示不完，底部展示滚动条，支持用户滑动滚动条查看更多活动,
        //      点击整张活动卡片，新页面打开对应的【活动详情页】。
        //      (2)常显展示右侧“更多"按钮，点击按钮热区，新页面打开【活动汇总页】并定位至“最新活动”模块。
        //  1.2 若本活动无所属专场 或 所属专场关联活动数量≤2，则不展示本模块，页面布局自适应展示。
        //2、活动推荐 & 排序规则:
        //  所属专场下其他待举办的活动>所属专场下其他进行中的活动>其他待举办的招聘会系列活动>所属专场下其他已结束的活动即:
        //  (1)优先展示该活动所属专场下其他“待举办"&“即将开始”子状态的活动:
        //    ① 先展示有明确活动时间的活动，按活动开始时间顺序展示;
        //    ② 再展示未明确活动时间(“活动时间”为自定义录入)的活动，按活动ID倒序排。
        //  (2)其次展示所属专场下其他“进行中"状态的活动，按活动开始时间顺序展示;
        //  (3)再次展示其他招聘会系列活动“待举办"&“即将开始”子状态的活动;排序规则同(1);
        //  (4)最后展示所属专场下其他“已结束”状态的活动:
        //    ① 按活动开始时间倒序展示;
        //    ② 开始时间相同，按活动ID倒序排。
        //////+++++++++++++++++++++++++++++++++++++++++
        /// 逻辑说明
        /// 1、先看下活动是否有所属专场，有所属专场看专场下活动数量是否大于2，大于2展示推荐模块，否则不展示推荐模块。
        $specialIds            = BaseHwSpecialActivityRelation::find()
            ->select('special_id')
            ->where(['activity_id' => $activityInfo['id']])
            ->asArray()
            ->column();
        $isRecommendActivity   = count($specialIds) > 0 && (BaseHwSpecialActivityRelation::find()
                    ->where(['special_id' => $specialIds])
                    ->count() > 2);
        $recommendActivityList = [];
        //推荐的时候
        if ($isRecommendActivity) {
            $recommendActivityList = $this->getRecommendActivityList($activityInfo['id']);
        }

        // 人才报名
        // 1、若人才尚未报名本场活动；点击热区，新页面打开“人才报名链接”；
        // 2、若人才已报名本场活动；展示已报名样式，点击热区无跳转
        $activityInfo['applyStatus']   = 2;//1=已报名 2=未报名 3=不显示
        $applyLinkInfo                 = BaseHwActivity::getPersonApplyLinkUrl($activityInfo['id']);
        $activityInfo['applyLink']     = $applyLinkInfo['link'];
        $activityInfo['applyLinkType'] = $applyLinkInfo['linkType'];
        if ($activityInfo['applyLinkPersonType'] != BaseHwSpecialActivity::APPLY_LINK_PERSON_TYPE_OTHER && !empty($this->resumeId)) {
            $activityInfo['applyStatus'] = BaseActivityFormOptionSign::getResumeActivityApplyStatus($this->resumeId,
                $activityInfo['applyLinkPersonFormId'], $activityInfo['applyLinkPersonFormOptionId']);
        }
        if (!$activityInfo['applyLink'] && $activityInfo['applyStatus'] != 1) {
            $activityInfo['applyStatus'] = 3;
        }

        // 单位报名
        // 若有设置“单位报名链接”且当前时间点尚未过或未设置“单位报名截止日期”，展示该单位报名icon1.点击热区，新页面打开“单位报名链接”;
        // 若当前时间点已过“单位报名截止日期”/未设置单位报名链接，不展示该单位报名icon:悬浮栏高度自适应展示。
        // apply_link_company、apply_company_time
        $activityInfo['applyLinkCompanyIsShow'] = 2; //1=展示 2=隐藏
        if ($activityInfo['applyLinkCompany'] && (($activityInfo['applyCompanyTime'] == TimeHelper::ZERO_DATE) || (time() < strtotime(TimeHelper::dayToEndTime($activityInfo['applyCompanyTime']))))) {
            $activityInfo['applyLinkCompanyIsShow']     = 1;
            $applyLinkCompanyInfo                       = UrlHelper::formatLinkInfo($activityInfo['applyLinkCompany']);
            $activityInfo['applyLinkCompany']           = $applyLinkCompanyInfo['newLink'];
            $activityInfo['applyLinkCompanyType'] = $applyLinkCompanyInfo['linkType'];
        }
        if ($activityInfo['applyStatus'] == 1) {
            $activityInfo['applyLinkCompanyIsShow'] = 2;
        }

        //根据模板输出
        $activityInfo['activityDetail']      = $this->templateContentFormat($activityInfo['templateId'],
            $activityInfo['activityDetail']);
        $activityInfo['participationMethod'] = $this->templateContentFormat($activityInfo['templateId'],
            $activityInfo['participationMethod']);
        $activityInfo['wonderfulReview']     = $this->templateContentFormat($activityInfo['templateId'],
            $activityInfo['wonderfulReview']);

        // 参会单位
        $cityParams               = self::getActivityCompanySearchCityParams(0, $activityInfo['id']);
        $companyParticipatingList = [
            'searchList' => [
                'cityParams'        => $cityParams ?: [],
                'companyTypeParams' => self::getActivityCompanySearchCompanyTypeParams(0, $activityInfo['id']),
                'majorSelect'       => self::getActivityCompanySearchMajorParams(0, $activityInfo['id']),
                'jobTypeList'       => BaseCategoryJob::getJobHome(),
            ],
            'dataList'   => SpecialActivityService::getActivityCompany($params, $this->resumeId),
        ];
        //返回页面上所有数据
        //注释每个位置的内容
        return [
            // 活动数据
            'activityInfo'             => $activityInfo,
            // 热门单位
            'companyHotList'           => $companyHotList,
            // 相关活动推荐
            'isRecommendActivity'      => $isRecommendActivity,
            'recommendActivityList'    => $recommendActivityList,
            //参会单位
            'companyParticipatingList' => $companyParticipatingList,
        ];
    }

    /**
     * 获取参会单位列表
     * @param $params
     * @return array
     * @throws \common\components\MessageException
     */
    public function runCompanyList($params)
    {
        $this->setUser();

        return SpecialActivityService::getActivityCompany($params, $this->resumeId);
    }

    /**
     * 推荐活动
     * 活动推荐 & 排序规则:
     * 所属专场下其他待举办的活动>所属专场下其他进行中的活动>其他待举办的招聘会系列活动>所属专场下其他已结束的活动即:
     * (1)优先展示该活动所属专场下其他“待举办"&“即将开始”子状态的活动:
     *   ① 先展示有明确活动时间的活动，按活动开始时间顺序展示;
     *   ② 再展示未明确活动时间(“活动时间”为自定义录入)的活动，按活动ID倒序排。
     * (2)其次展示所属专场下其他“进行中"状态的活动，按活动开始时间顺序展示;
     * (3)再次展示其他招聘会系列活动“待举办"&“即将开始”子状态的活动;排序规则同(1);
     * (4)最后展示所属专场下其他“已结束”状态的活动:
     *   ① 按活动开始时间倒序展示;
     *   ② 开始时间相同，按活动ID倒序排。
     */
    private function getRecommendActivityList($activityId)
    {
        // start_date asc ,start_time asc ,id desc-场次表
        //来到这里直接根据规则拿数据就好了
        //1、获取该活动所属所有专场ID
        $specialIds = BaseHwSpecialActivityRelation::find()
            ->select('special_id')
            ->where(['activity_id' => $activityId])
            ->column();
        //获取所有所属专场下的活动除开本活动得推荐活动Id集合
        $activityIdSet = BaseHwSpecialActivityRelation::find()
            ->select('activity_id')
            ->where(['special_id' => $specialIds])
            ->andWhere([
                '!=',
                'activity_id',
                $activityId,
            ])
            ->column();
        $activityIdSet = array_unique($activityIdSet);
        $commonQuery   = BaseHwActivity::find()
            ->select([
                'ha.id',
                'ha.activity_child_status',
                'ha.series_type',
                'ha.name',
                'ha.activity_start_date',
                'ha.activity_end_date',
                'ha.main_img_file_id',
                'ha.image_mini_master_id',
                'ha.activity_link',
                'ha.click',
                'has.custom_time',
                'has.start_date',
                'has.start_time',
            ])
            ->alias('ha')
            ->innerJoin(['has' => BaseHwActivitySession::tableName()], 'ha.id=has.activity_id')
            // 只要上架的
            ->where([
                'grounding_status' => [
                    BaseHwActivity::GROUNDING_STATUS_ON,
                ],
            ]);
        //获取(1)数据
        if ($activityIdSet) {
            $list1 = (clone $commonQuery)->andWhere([
                'ha.activity_child_status' => [
                    BaseHwActivity::ACTIVITY_CHILD_STATUS_NOT_START,
                    BaseHwActivity::ACTIVITY_CHILD_STATUS_BE_ABOUT_TO_START,
                ],
            ])
                ->addSelect(["IF(has.start_date='0000-00-00',10,100) as sort1"])
                ->andWhere(['ha.id' => $activityIdSet])
                ->orderBy('sort1 desc,has.start_date asc,has.start_time asc,ha.id desc')
                ->asArray()
                ->all();
        }
        //获取(2)数据
        if ($activityIdSet) {
            $list2 = (clone $commonQuery)->andWhere(['ha.activity_child_status' => BaseHwActivity::ACTIVITY_CHILD_STATUS_PROGRESS])
                ->andWhere(['ha.id' => $activityIdSet])
                ->orderBy('has.start_date asc,has.start_time asc,ha.id desc')
                ->asArray()
                ->all();
        }

        //获取(3)数据
        $list3Query = clone $commonQuery;
        if ($activityIdSet) {
            $list3Query->andWhere([
                'not in',
                'ha.id',
                $activityIdSet,
            ]);
        }
        $list3 = $list3Query->addSelect(["IF(has.start_date='0000-00-00',10,100) as sort1"])
            ->andWhere([
                '!=',
                'ha.id',
                $activityId,
            ])
            ->andWhere([
                'ha.activity_child_status' => [
                    BaseHwActivity::ACTIVITY_CHILD_STATUS_NOT_START,
                    BaseHwActivity::ACTIVITY_CHILD_STATUS_BE_ABOUT_TO_START,
                ],
                'ha.series_type'           => BaseHwActivity::ZHAOPINHUI_TYPE,
            ])
            ->orderBy('sort1 desc,has.start_date asc,has.start_time asc,ha.id desc')
            ->asArray()
            ->all();
        //获取(4)数据
        if ($activityIdSet) {
            $list4 = (clone $commonQuery)->andWhere(['ha.activity_child_status' => BaseHwActivity::ACTIVITY_CHILD_STATUS_END])
                ->andWhere(['ha.id' => $activityIdSet])
                ->orderBy('has.start_date desc,has.start_time desc,ha.id desc')
                ->asArray()
                ->all();
        }
        $list = $activityIdSet ? array_merge($list1, $list2, $list3, $list4) : $list3;
        foreach ($list as &$item) {
            $item['activityChildStatusText'] = BaseHwActivity::ACTIVITY_CHILD_STATUS_TEXT_LIST[$item['series_type']][$item['activity_child_status']];
            $item['mainImgUrl']              = FileHelper::getFullPathById($item['main_img_file_id']);
            $item['imageMiniMasterUrl']      = FileHelper::getFullPathById($item['image_mini_master_id']);
            $item['area']                    = BaseHwActivity::getAllAddressBySeries($item['id']);
            $item['activityDetailUrl']       = BaseHwActivity::getActivityLinkUrl($item['series_type'],
                $item['activity_link']);
            $item['activityTime']            = !$item['custom_time'] ? ($item['activity_start_date'] == $item['activity_end_date'] ? TimeHelper::getLong($item['activity_start_date']) : TimeHelper::getLong($item['activity_start_date']) . '~' . TimeHelper::getLong($item['activity_end_date'])) : $item['custom_time'];
            //若数值＜1w，则四舍五入取整后展示具体数据；若数值≥1w，则以w为单位取值，四舍五入，最多展示到小数点后两位
            $click         = $item['click'] * 26.1;
            $item['click'] = $click < 10000 ? round($click) : round($click / 10000, 2) . 'w';
        }

        return $list;
    }

    /**
     * 热门单位
     * 1、读取活动【热门单位】模块添加单位；排序号越大越靠前。字段说明见卡片标注。
     * 2.一行展示不完，展示轮播条，每隔5s自动切换至下一页；支持手动切换查看。
     * 3、若添加单位数量为0，则整个模块隐藏；
     * 4、鼠标移入时，卡片展示投影效果；
     * 5、点击卡片，读取该单位“落地链接”配置：
     *  若为“公告详情”，则新页面打开单位本活动关联公告详情页，取初始发布时间最早的那条；
     *  若为“单位主页”，则新页面打开单位主页-单位介绍tab；
     */
    private function getCompanyHotList($activityId)
    {
        $list = BaseHwActivityCompanyHot::find()
            ->alias('hac')
            ->select([
                'hac.id',
                'hac.activity_id as activityId',
                'hac.company_id as companyId',
                'hac.sort',
                'hac.link_type as linkType',
                'c.full_name as companyName',
                'c.short_name as shortName',
                'c.logo_url as companyLogo',
                'c.type as companyType',
                'c.nature as companyNature',
            ])
            ->innerJoin(['c' => BaseCompany::tableName()], 'hac.company_id=c.id')
            ->where(['hac.activity_id' => $activityId])
            ->orderBy('hac.sort desc,hac.id desc')
            ->asArray()
            ->all();
        foreach ($list as &$item) {
            //单位类型文案
            $item['companyTypeText'] = BaseDictionary::getCompanyTypeName($item['companyType']);
            //单位性质文案
            $item['companyNatureText'] = BaseDictionary::getCompanyNatureName($item['companyNature']);
            //单位Logo
            $item['companyLogo'] = BaseCompany::getLogoFullUrl($item['companyLogo']);
            $item['companyName'] = $item['shortName'] ?: $item['companyName'];
            // 处理落地链接
            $item['targetLinkType'] = BaseShowcase::TARGET_LINK_TYPE_STATION;//内部跳转
            if ($item['linkType'] == BaseHwActivityCompanyHot::LINK_TYPE_ANNOUNCEMENT) {
                //若为"公告详情"，则新页面打开单位本活动关联公告详情页，取初始发布时间最早的那条；
                $announcementId = BaseHwActivityAnnouncement::find()
                    ->alias('haa')
                    ->select('haa.announcement_id')
                    ->innerJoin(['a' => BaseAnnouncement::tableName()], 'haa.announcement_id=a.id')
                    ->innerJoin(['art' => BaseArticle::tableName()], 'a.article_id=art.id')
                    ->where([
                        'haa.activity_id' => $item['activityId'],
                        'haa.company_id'  => $item['companyId'],
                        'haa.status'      => 1,
                    ])
                    ->orderBy('art.first_release_time asc')
                    ->limit(1)
                    ->scalar();
                if ($announcementId) {
                    $item['linkUrl'] = PLATFORM == 'MINI' ? UrlHelper::createMiniAnnouncementDetailPath($announcementId) : UrlHelper::createPcAnnouncementDetailPath($announcementId);
                } else {
                    $item['linkUrl']        = '';
                    $item['targetLinkType'] = 0;
                }
            } else {
                //落地单位详情
                $item['linkUrl'] = PLATFORM == 'MINI' ? UrlHelper::createMiniCompanyDetailPath($item['companyId']) : UrlHelper::createPcCompanyDetailPath($item['companyId']);
            }
        }

        return $list;
    }

    /**
     * 获取活动专场名称
     * 1.0 关联专场:展示格式:本场活动关联专场名称·第N场;N=该活动在该专场下的排序序位号(顺序)。例如右图:深圳站 N=2最多展示一行，超出“…"展示;点击专场名称，新页面打开对应的专场详情页;
     * (1)若活动关联多个专场，多个专场名称“;”隔开;各专场展示顺序:
     *  ①“进行中”状态的专场>“即将开始”/“待举办”状态的专场>“已结束”状态的专场;
     *  ② 若专场举办状态相同，优先展示与当前活动“举办方式”相同的专场，再展示举办方式不同的专场。
     * (2)若无关联专场，则不展示该模块;
     */
    private function getActivitySpecialName($activityId)
    {
        //先获取活动所有关联的专场
        $specialList = BaseHwSpecialActivityRelation::find()
            ->alias('hsar')
            ->select([
                'hsa.name as specialName',
                'hsa.special_link as specialLink',
                'hsar.special_id as specialId',
            ])
            ->innerJoin(['hsa' => BaseHwSpecialActivity::tableName()], 'hsar.special_id = hsa.id')
            ->innerJoin(['ha' => BaseHwActivity::tableName()], 'hsar.activity_id = ha.id')
            ->where(['hsar.activity_id' => $activityId])
            ->orderBy([
                new \yii\db\Expression('CASE WHEN hsa.status = 3 THEN 1 WHEN hsa.status IN (2, 1) THEN 2 WHEN hsa.status = 4 THEN 3 ELSE 4   END'),
                new \yii\db\Expression('CASE WHEN hsa.to_hold_type = ha.to_hold_type THEN 1  ELSE 2  END'),
                new \yii\db\Expression('hsar.special_id desc'),
            ])
            ->asArray()
            ->all();
        //如果专场ID为空则没有关联专场
        if (!$specialList) {
            return '';
        }
        //关联了专场，获取专场名称
        $specialNameArr = [];
        foreach ($specialList as $item) {
            //获取专场下的活动
            $specialActivityIds = BaseHwSpecialActivityRelation::find()
                ->alias('hasr')
                ->leftJoin(['hsa' => BaseHwSpecialActivity::tableName()], 'hasr.special_id = hsa.id')
                ->leftJoin(['ha' => BaseHwActivity::tableName()], 'hasr.activity_id = ha.id')
                ->select('activity_id')
                ->where(['special_id' => $item['specialId']])
                ->orderBy([
                    new \yii\db\Expression("CASE WHEN ha.activity_start_date = '0000=00-00' THEN 1  ELSE 2  END desc"),
                    new \yii\db\Expression('ha.activity_start_date asc'),
                    new \yii\db\Expression('hasr.id asc'),
                ])
                ->column();
            //获取数组中与活动ID相同的下标-由于获取的下标是从0开始所以获得的下标需要全部+1
            $key = array_search($activityId, $specialActivityIds);
            //$key这里肯定是搜索的到的
            //拼接规则：本场活动关联专场名称·第N场
            $specialNameArr[] = [
                'name' => $item['specialName'] . '·第' . ($key + 1) . '场',
                'url'  => self::getSpecialLink($item['specialLink']),
                'id'   => $item['specialId'],
            ];
        }

        return $specialNameArr;
    }

    /**
     * 点击量
     * @param $id
     * @return false|void
     * @throws \yii\base\NotSupportedException
     */
    private function click($id)
    {
        if (!$_COOKIE[\Yii::$app->params['userCookiesKey']] && $this->operationPlatform != self::PLATFORM_MINI) {
            // 过滤爬虫
            return false;
        }
        $data = [
            'type'        => ClickJob::TYPE_ACTIVITY,
            'mainId'      => $id,
            'ip'          => IpHelper::getIpInt(),
            'source'      => PLATFORM == 'MINI' ? 3 : 1,
            'useragent'   => Yii::$app->request->headers['user-agent'] ?? '',
            'userCookies' => $_COOKIE[Yii::$app->params['userCookiesKey']] ?? '',
            'memberId'    => $this->memberId ?? 0,
        ];
        Producer::click($data);
    }

    /**
     * 针对模板类型输出内容
     * @param $templateId
     * @param $content
     * @return mixed|string
     */
    private function templateContentFormat($templateId, $content)
    {
        if (empty($content)) {
            return $content;
        }
        switch ($templateId) {
            case BaseHwActivity::TEMPLATE_TYPE_DEFAULT:
                $content = '<div class="not-style-wrapper">' . $content . '</div>';
                break;
            case BaseHwActivity::TEMPLATE_TYPE_GENERAL:
                $content = $this->operationPlatform != self::PLATFORM_MINI ? self::formatContentV3($content) : self::formatContentMiniV2($content);
                break;
        }

        return $content;
    }

    /**
     * 获取活动需求人数
     */
    public function getActivityNumber($activityId)
    {
        //获取关联活动公告ID
        $announcementIds = BaseHwActivityAnnouncement::find()
            ->select('announcement_id')
            ->where(['activity_id' => $activityId])
            ->andWhere(['status' => 1])
            ->column();

        return BaseJob::find()
                   ->select("SUM(CASE WHEN amount = '若干' THEN 50 ELSE amount END) as total_amount")
                   ->where(['announcement_id' => $announcementIds])
                   ->asArray()
                   ->one()['total_amount'] ?? 0;
    }
}