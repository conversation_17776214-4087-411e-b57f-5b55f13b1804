<?php

namespace common\service\column;

use admin\models\Area;
use admin\models\CategoryJob;
use admin\models\HomeColumn;
use admin\models\Major;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomeColumnDictionaryRelationship;
use common\helpers\ArrayHelper;
use common\service\CommonService;
use Yii;
use yii\base\Exception;

/**
 * 栏目服务类
 * 处理栏目相关的业务逻辑
 */
class HomeColumnService extends CommonService
{
    /**
     * 获取列表参数
     * @return array
     */
    public function getListParams()
    {
        $this->line('获取栏目列表参数');
        
        return [
            'isHideList'           => ArrayHelper::obj2Arr(HomeColumn::IS_HIDE_LIST),
            'contentTypeList'      => ArrayHelper::obj2Arr(HomeColumn::CONTENT_TYPE_LIST),
            'templateTypeList'     => ArrayHelper::obj2Arr(HomeColumn::TEMPLATE_TYPE_LIST),
            'detailTypeList'       => ArrayHelper::obj2Arr(HomeColumn::DETAIL_TYPE_LIST),
            'list'                 => HomeColumn::getLevel1List(),
            'majorList'            => ArrayHelper::arr2KV(Major::find()
                ->select([
                    'id',
                    'name',
                ])
                ->where([
                    'level'  => 2,
                    'status' => 1,
                ])
                ->asArray()
                ->all(), 'id', 'name'),
            'areaList'             => Area::getAreaList(),
            'categoryList'         => CategoryJob::getAllCategoryJobList(),
            'operateAttributeList' => ArrayHelper::obj2Arr(BaseHomeColumn::OPERATE_ATTRIBUTE_LIST),
        ];
    }

    /**
     * 获取栏目列表
     * @param array $params 查询参数
     * @return array
     */
    public function getList($params = [])
    {
        $this->line('获取栏目列表，参数：' . json_encode($params));
        
        return HomeColumn::getList($params);
    }

    /**
     * 获取资讯列表
     * @return array
     */
    public function getNewsList()
    {
        $this->line('获取资讯列表');
        
        return HomeColumn::getNewsList();
    }

    /**
     * 获取字典列表
     * @param int $id 栏目ID
     * @return array
     */
    public function getDictionaryList($id)
    {
        $this->line('获取字典列表，栏目ID：' . $id);
        
        if (!$id) {
            throw new Exception('栏目ID不能为空');
        }
        
        return HomeColumn::getDictionaryList($id);
    }

    /**
     * 新增栏目
     * @param array $data 栏目数据
     * @param int $adminId 管理员ID
     * @return bool
     * @throws Exception
     */
    public function add($data, $adminId)
    {
        $this->line('新增栏目，管理员ID：' . $adminId . '，数据：' . json_encode($data));
        
        if (!$adminId) {
            throw new Exception('管理员ID不能为空');
        }
        
        $model = new HomeColumn();
        $model->create($data, $adminId);
        
        $this->line('栏目创建成功');
        
        return true;
    }

    /**
     * 设置字典关联
     * @param array $data 字典数据
     * @return bool
     * @throws Exception
     */
    public function setDictionary($data)
    {
        $this->line('设置字典关联，数据：' . json_encode($data));
        
        $columnId   = $data['id'] ?? null;
        $majorId    = $data['majorId'] ?? null;
        $areaId     = $data['areaId'] ?? null;
        $categoryId = $data['categoryId'] ?? null;

        if (!$columnId) {
            throw new Exception('请选择栏目');
        }

        BaseHomeColumnDictionaryRelationship::setMajor($columnId, $majorId);
        BaseHomeColumnDictionaryRelationship::setArea($columnId, $areaId);
        BaseHomeColumnDictionaryRelationship::setCategory($columnId, $categoryId);

        $this->line('字典关联设置成功');
        
        return true;
    }
}