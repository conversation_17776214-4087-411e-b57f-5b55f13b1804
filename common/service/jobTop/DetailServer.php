<?php

namespace common\service\jobTop;

use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseJob;
use common\base\models\BaseJobTopConfig;
use common\base\models\BaseJobTopConfigLog;
use common\models\CategoryJob;
use yii\base\Exception;

class DetailServer extends BaseService
{
    private $id;

    public function run($id)
    {
        $this->id = $id;
        $this->check();

        return $this->getDetail();
    }

    private function check()
    {
        //判断记录状态
        $info = BaseJobTopConfig::findOne(['id' => $this->id]);
        if (empty($info)) {
            throw new Exception('记录不存在');
        }
        if ($info['is_delete'] == BaseJobTopConfig::IS_DELETE_YES) {
            throw new Exception('记录已被移除');
        }
    }

    private function getDetail()
    {
        $info = BaseJobTopConfig::find()
            ->where(['id' => $this->id])
            ->select([
                'job_id',
                'date',
                'type',
                'job_category_id as jobCategoryId',
                'area_id',
                'sort',
                'remark',
                'status',
            ])
            ->asArray()
            ->one();
        //获取职位名称
        $info['jobName'] = BaseJob::findOneVal(['id' => $info['job_id']], 'name');
        //判断是否常规置顶
        $info['routineTopId'] = '';
        if ($info['type'] == BaseJobTopConfig::TYPE_ROUTINE) {
            $info['routineTopId'] = '1';
        }
        //获取职位类型名称
        $info['jobCategoryId']   = $info['jobCategoryId'] ?: '';
        $info['jobCategoryText'] = BaseCategoryJob::getFullLevelName($info['jobCategoryId']);
        $info['cityId']          = '';
        $info['cityText']        = '';
        $info['provinceId']      = '';
        $info['provinceText']    = '';
        if (!empty($info['area_id'])) {
            $level = BaseArea::findOneVal(['id' => $info['area_id']], 'level');
            if ($level == 1) {
                $info['provinceId']   = $info['area_id'];
                $info['provinceText'] = BaseArea::getAreaName($info['area_id']);
            } elseif ($level == 2) {
                $info['cityId']   = $info['area_id'];
                $info['cityText'] = BaseArea::getAreaName($info['area_id']);
            }
        } else {
            $info['area_id'] = '';
        }
        $info['isCanEdit'] = (string)self::checkEdit($info['status']);

        unset($info['type'], $info['area_id']);

        return $info;
    }

}