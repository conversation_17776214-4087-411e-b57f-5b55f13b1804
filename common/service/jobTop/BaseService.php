<?php

namespace common\service\jobTop;

use common\base\models\BaseArea;
use common\base\models\BaseJob;
use common\base\models\BaseJobTopConfig;
use common\helpers\TimeHelper;
use common\models\CategoryJob;
use yii\base\Exception;

class BaseService
{

    const LIMIT_DAILY_AMOUNT   = 5;
    const LIMIT_JOB_AMOUNT     = 1;
    const LIMIT_COMPANY_AMOUNT = 2;

    /**
     * 判断记录是否可以移除
     * @param $params
     * @return bool
     */
    public function checkRemove($id)
    {
        //判断记录是否有置顶记录，有的话，不能移除
        $info = BaseJobTopConfig::findOne(['id' => $id]);
        if ($info->is_run == BaseJobTopConfig::IS_RUN_YES || $info->run_time != TimeHelper::ZERO_TIME) {
            return BaseJobTopConfig::CAN_REMOVE_NO;
        } else {
            return BaseJobTopConfig::CAN_REMOVE_YES;
        }
    }

    /**
     * 判断记录是否可以编辑
     * @param $params
     * @return bool
     */
    public function checkEdit($status)
    {
        if ($status == BaseJobTopConfig::STATUS_TOP_ING || $status == BaseJobTopConfig::STATUS_OVER) {
            return BaseJobTopConfig::CAN_EDIT_NO;
        } else {
            return BaseJobTopConfig::CAN_EDIT_YES;
        }
    }

    public function checkResetTop($id)
    {
        $info = BaseJobTopConfig::findOne(['id' => $id]);

        //获取职位状态
        $jobStatus = BaseJob::findOneVal(['id' => $info->job_id], 'status');
        if ($jobStatus == BaseJob::STATUS_OFFLINE) {
            return BaseJobTopConfig::TOP_BUTTON_DISABLED_YES;
        }
        //判断资源问题
        try {
            $this->checkRecordAmount($info);
        } catch (Exception $e) {
            return BaseJobTopConfig::TOP_BUTTON_DISABLED_YES;
        }

        return BaseJobTopConfig::TOP_BUTTON_DISABLED_NO;
    }

    /**
     * 针对条数进行判断
     * @param $model
     * @return void
     * @throws Exception
     */
    protected function checkRecordAmount($model)
    {
        $publicWhere    = [
            'is_delete' => BaseJobTopConfig::IS_DELETE_NO,
            'date'      => $model->date,
        ];
        $publicAndWhere = [
            '<>',
            'status',
            BaseJobTopConfig::STATUS_OVER,
        ];

        //判断是否超出日限制
        $dailyAmountQuery = BaseJobTopConfig::find()
            ->where($publicWhere)
            ->andWhere($publicAndWhere)
            ->andFilterWhere([
                '<>',
                'id',
                $model->id,
            ]);
        //判断是否超出公司限制
        $companyAmountQuery = BaseJobTopConfig::find()
            ->where($publicWhere)
            ->andWhere(['company_id' => $model->company_id])
            ->andWhere($publicAndWhere)
            ->andFilterWhere([
                '<>',
                'id',
                $model->id,
            ]);
        //判断同一职位是否超出限制
        $jobAmountQuery = BaseJobTopConfig::find()
            ->where($publicWhere)
            ->andWhere(['job_id' => $model->job_id])
            ->andWhere($publicAndWhere)
            ->andFilterWhere([
                '<>',
                'id',
                $model->id,
            ]);

        if ($model->type == BaseJobTopConfig::TYPE_ROUTINE) {
            $where         = ['type' => BaseJobTopConfig::TYPE_ROUTINE];
            $companyAmount = $companyAmountQuery->andWhere($where)
                ->count();
            $jobAmount     = $jobAmountQuery->andWhere($where)
                ->count();
            $dailyAmount   = $dailyAmountQuery->andWhere($where)
                ->count();
            if ($dailyAmount >= self::LIMIT_DAILY_AMOUNT) {
                throw new Exception("{$model->date}日常规置顶资源已满");
            }
        } elseif ($model->type == BaseJobTopConfig::TYPE_SEARCH) {
            //如果是置顶类型判断，要通过2个条件判断
            $andFilterWhere1 = ['job_category_id' => $model->job_category_id];
            $andFilterWhere2 = ['area_id' => $model->area_id];
            $companyAmount   = $companyAmountQuery->andFilterWhere($andFilterWhere1)
                ->andFilterWhere($andFilterWhere2)
                ->count();
            $jobAmount       = $jobAmountQuery->andFilterWhere($andFilterWhere1)
                ->andFilterWhere($andFilterWhere2)
                ->count();

            if (!empty($model->job_category_id)) {
                $dailyAmount = $dailyAmountQuery->andFilterWhere($andFilterWhere1)
                    ->count();
                if ($dailyAmount >= self::LIMIT_DAILY_AMOUNT) {
                    $jobCateText = CategoryJob::findOneVal(['id' => $model->job_category_id], 'name');
                    throw new Exception("{$model->date}“{$jobCateText}”职位类型置顶资源已满");
                }
            }
            if (!empty($model->area_id)) {
                $dailyAmount = $dailyAmountQuery->andFilterWhere($andFilterWhere2)
                    ->count();
                if ($dailyAmount >= self::LIMIT_DAILY_AMOUNT) {
                    $jobCateText = BaseArea::getAreaName($model->area_id);
                    throw new Exception("{$model->date}“{$jobCateText}”置顶资源已满");
                }
            }
        }
        if ($companyAmount >= self::LIMIT_COMPANY_AMOUNT) {
            throw new Exception("{$model->date}日该位置单位置顶职位数量超限");
        }

        if ($jobAmount >= self::LIMIT_JOB_AMOUNT) {
            throw new Exception("请勿重复置顶该职位");
        }
    }

    /**
     * 判断置顶按钮状态
     * @param $params
     * @return array|int|void
     */
    public function getTopButtonInfo($id)
    {
        $status = BaseJobTopConfig::findOneVal(['id' => $id], 'status');
        switch ($status) {
            case BaseJobTopConfig::STATUS_NOT_START:
                //未开始,暂停置顶只读置灰
                return [
                    'topButtonType'           => BaseJobTopConfig::TOP_BUTTON_STOP,
                    'topButtonDisabledStatus' => BaseJobTopConfig::TOP_BUTTON_DISABLED_YES,
                ];
            case BaseJobTopConfig::STATUS_TOP_ING:
                //置顶中,暂停置顶
                return [
                    'topButtonType'           => BaseJobTopConfig::TOP_BUTTON_STOP,
                    'topButtonDisabledStatus' => BaseJobTopConfig::TOP_BUTTON_DISABLED_NO,
                ];
            case BaseJobTopConfig::STATUS_OVER:
                //置顶结束了,重启置顶只读置灰
                return [
                    'topButtonType'           => BaseJobTopConfig::TOP_BUTTON_RESTART,
                    'topButtonDisabledStatus' => $this->checkResetTop($id),
                ];
            case BaseJobTopConfig::STATUS_TOP_STOP:
                //重启置顶
                return [
                    'topButtonType'           => BaseJobTopConfig::TOP_BUTTON_RESTART,
                    'topButtonDisabledStatus' => $this->checkResetTop($id),
                ];
        }
    }

}
