<?php

namespace common\service\jobTop;

use common\base\models\BaseJobTopConfig;
use yii\base\Exception;

class ActionServer extends BaseService
{

    private function getModel($id)
    {
        $model = BaseJobTopConfig::findOne(['id' => $id]);
        if (empty($model)) {
            throw new Exception('记录不存在');
        }

        return $model;
    }

    public function sort($params)
    {
        if (empty($params['id']) || strlen($params['sort']) < 1) {
            throw new Exception('请求参数错误');
        }
        $model       = $this->getModel($params['id']);
        $model->sort = $params['sort'];
        if (!$model->save()) {
            throw new Exception('保存失败' . $model->getFirstErrorsMessage());
        }
    }

    public function changeTopStatus($params)
    {
        if (empty($params['id'])) {
            throw new Exception('请求参数错误');
        }
        $model = $this->getModel($params['id']);
        if ($model->status == BaseJobTopConfig::STATUS_TOP_STOP || $model->status == BaseJobTopConfig::STATUS_OVER) {
            //判断是否可以置顶
            $btnStatus = $this->checkResetTop($params['id']);
            if($btnStatus != BaseJobTopConfig::TOP_BUTTON_DISABLED_NO){
                throw new Exception('置顶信息错误');
            }
            $model->status = BaseJobTopConfig::STATUS_TOP_ING;
            $model->is_run = BaseJobTopConfig::IS_RUN_YES;
        } elseif ($model->status == BaseJobTopConfig::STATUS_TOP_ING) {
            $model->status = BaseJobTopConfig::STATUS_TOP_STOP;
        }
        if (!$model->save()) {
            throw new Exception('保存失败' . $model->getFirstErrorsMessage());
        }
    }

    public function remove($params)
    {
        if (empty($params['id'])) {
            throw new Exception('请求参数错误');
        }
        $model = $this->getModel($params['id']);
        //判断状态
        if ($model->is_run == BaseJobTopConfig::IS_RUN_YES) {
            throw new Exception('记录置顶中，不能移除');
        }
        if ($model->is_delete == BaseJobTopConfig::IS_DELETE_YES) {
            throw new Exception('记录已被移除，请勿重复操作');
        }
        $model->is_delete = BaseJobTopConfig::IS_DELETE_YES;
        if (!$model->save()) {
            throw new Exception('保存失败' . $model->getFirstErrorsMessage());
        }
    }

    /**
     * 根据职位状态，结束置顶状态
     * @param $jobIds
     * @return void
     */
    public function updateStatusByJobStatus($jobIds)
    {
        $list = BaseJobTopConfig::find()
            ->where(['job_id' => $jobIds])
            ->select([
                'is_run',
                'id',
            ])
            ->asArray()
            ->all();
        foreach ($list as $item) {
            if ($item['is_run'] == BaseJobTopConfig::IS_RUN_YES) {
                //只要把置顶中的结束掉即可
                $model         = BaseJobTopConfig::findOne(['id' => $item['id']]);
                $model->status = BaseJobTopConfig::STATUS_OVER;
                $model->is_run = BaseJobTopConfig::IS_RUN_NO;
                $model->save();
            }
        }
    }
}