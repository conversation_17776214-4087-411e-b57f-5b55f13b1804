<?php
/**
 * create user：伍彦川
 * create time：2025/9/11 11:58
 */
namespace common\service\v2\resume;

use admin\models\Area;
use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseDictionary;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAcademicIndex;
use common\base\models\BaseResumeComplete;
use common\base\models\BaseResumeConferencePage;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeEducationMajorRelation;
use common\base\models\BaseResumeEducationMentorRelation;
use common\base\models\BaseResumeIntention;
use common\base\models\BaseResumeIntentionAreaRelation;
use common\base\models\BaseResumeOverseasExperience;
use common\base\models\BaseResumePostdoctorWork;
use common\base\models\BaseResumePostdoctorWorkMentorTitleRelation;
use common\base\models\BaseResumePostdoctorWorkProjectRelation;
use common\base\models\BaseResumePostdoctorWorkResearchFieldRelation;
use common\base\models\BaseResumeResearchDirection;
use common\base\models\BaseResumeResearchDirectionResearchFieldRelation;
use common\base\models\BaseResearchField;
use common\base\models\BaseResumeResearchField;
use common\base\models\BaseResumeTitleRelation;
use common\base\models\BaseResumeWork;
use common\base\models\BaseResumeWorkJobTitleRelation;
use common\base\models\BaseResumeWorkResearchFieldRelation;
use common\base\models\BaseResumeWorkTypeRelation;
use common\components\MessageException;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\libs\Cache;
use common\service\CommonService;
use Faker\Provider\Base;
use yii\db\conditions\AndCondition;

class BaseResumeService extends CommonService
{
    protected            $memberId; // 用户ID
    protected BaseMember $memberModel; // 用户model
    protected BaseResume $resumeModel; // 简历model
    protected            $templateType; // 对应模版
    protected            $resumeModule; // 用户未完善模块-当前模块
    protected            $params; // 用户提交信息
    protected            $companyId; // 单位ID

    protected $resumeCompleteModule = [
        'templateType'         => 0,
        // 对应模版
        'isComplete'           => false,
        // 是否已经完善了简历
        'resumeModule'         => [],
        // 用户所有模块
        'currentModule'        => '',
        // 当前模块下标
        'resumeCompleteModule' => 0,
    ]; // 当前用户所有模块，最高学历和基础信息是公共的，后续的模块根据不同选项追加不同模块

    const STEP_FIRST  = '1';
    const STEP_SECOND = '2';
    const STEP_THIRD  = '3';

    const TOP_EDUCATION_MODULE                = 'top_education_module'; // 最高学历模块
    const BASIC_INFO_MODULE                   = 'basic_info_module'; // 基本信息模块
    const EDUCATION_MODULE                    = 'education_module'; // 教育经历模块
    const POSTDOCTOR_MODULE                   = 'postdoctor_module'; // 博士后-工作经历模块
    const WORK_MODULE                         = 'work_module'; // 工作经历模块
    const INTENTION_MODULE                    = 'intention_module'; // 求职意向模块
    const ACHIEVEMENT_MODULE                  = 'achievement_module'; // 五年研究成果模块
    const ADVANTAGE_MODULE                    = 'advantage_module'; // 个人优势模块
    const RESEARCH_DIRECTION_MODULE           = 'research_direction_module'; // 研究方向模块
    const POSTDOCTOR_WORK_MODULE              = 'postdoctor_work_module'; // 博士后经历模块
    const OVERSEAS_EXPERIENCE_MODULE          = 'overseas_experience_module'; // 其他海外经历模块
    const ACADEMIC_PAGE_MODULE                = 'academic_page_module'; // 期刊论文模块
    const CONFERENCE_PAGE_MODULE              = 'conference_page_module'; // 会议论文模块
    const ACADEMIC_PATENT_MODULE              = 'academic_patent_module'; // 专利模块
    const ACADEMIC_BOOK_MODULE                = 'academic_book_module';// 著作模块
    const RESEARCH_PROJECT_MODULE             = 'research_project_module'; // 项目经历模块
    const ACADEMIC_CONFERENCE_ACTIVITY_MODULE = 'academic_conference_activity_module';// 学术会议/活动模块
    const ASSOCIATION_POSITION_MODULE         = 'association_position_module';// 学会/协会任职模块
    const JOURNAL_POSITION_MODULE             = 'journal_position_module'; // 期刊/论文职务模块
    const SOCIAL_POSITION_MODULE              = 'social_position_module';// 社会兼职/顾问模块
    const PROJECT_REVIEW_MODULE               = 'project_review_module'; // 项目/基金评审模块
    const THESIS_COMMITTEE_MODULE             = 'thesis_committee_module'; // 学位答辩评委模块
    const TEACHING_COURSE_MODULE              = 'teaching_course_module'; // 主讲课程模块
    const COMPETITION_GUIDANCE_MODULE         = 'competition_guidance_module'; // 竞赛指导模块
    const EDUCATION_REFORM_MODULE             = 'education_reform_module'; // 教改实践模块
    const TEXTBOOK_WRITING_MODULE             = 'textbook_writing_module'; // 教材编写模块
    const REWARD_MODULE                       = 'reward_module'; // 奖励/荣誉/称号/头衔模块
    const CERTIFICATE_MODULE                  = 'certificate_module'; // 技能特长-资质证书模块
    const OTHER_SKILL_MODULE                  = 'other_skill_module';  // 技能特长
    const ADDITIONAL_INFO_MODULE              = 'additional_info_module'; // 附加信息

    const RESUME_UN_COMPLETE_MODULE = [
        self::TOP_EDUCATION_MODULE,
        self::BASIC_INFO_MODULE,
        self::EDUCATION_MODULE,
        self::POSTDOCTOR_MODULE,
        self::WORK_MODULE,
        self::INTENTION_MODULE,
    ];

    /** @var string[] 模块顺序；只能一步一步往下走 */

    public function setTemplateType($templateType)
    {
        $this->templateType = intval($templateType);

        return $this;
    }

    public function setResumeModule($resumeModule)
    {
        $this->resumeModule = strval($resumeModule);

        return $this;
    }

    public function setCompanyId($companyId)
    {
        $this->companyId = $companyId;

        return $this;
    }

    public function setParams($params)
    {
        $this->params = $params;

        return $this;
    }

    public function setMemberModel($memberId)
    {
        $this->memberId    = $memberId;
        $this->memberModel = BaseMember::findOne($memberId);
        $this->resumeModel = BaseResume::findOne(['member_id' => $memberId]);

        return $this;
    }

    public function setResumeModel($resumeId)
    {
        $this->resumeModel = BaseResume::findOne($resumeId);
        $this->memberId    = $this->resumeModel->member_id;
        $this->memberModel = BaseMember::findOne($this->memberId);

        return $this;
    }

    /**
     * 获取当前用户所属模块
     * @param $memberId
     * @return void
     */
    public function getUnCompleteResumeModule($memberId)
    {
        $resumeInfo = BaseResume::find()
            ->alias('r')
            ->leftJoin(['m' => BaseMember::tableName()], 'r.member_id = m.id')
            ->where(['r.member_id' => $memberId])
            ->select([
                'r.status',
                'r.template_type',
                'r.top_education_code',
                'r.name',
                'r.gender',
                'r.birthday',
                'm.email',
                'm.mobile',
                'm.mobile_code',
                'r.work_status',
                'r.postdoctor_work_status',
                'r.identity_type',
                'r.begin_work_date',
            ])
            ->asArray()
            ->one();

        if ($resumeInfo['status'] != BaseResume::STATUS_WAIT_AUDIT) {
            $this->resumeCompleteModule['isComplete'] = true;

            return $this->resumeCompleteModule;
        }

        // 追加需要填写模块
        $this->setResumeCompleteModule(self::TOP_EDUCATION_MODULE);
        $this->setResumeCompleteModule(self::BASIC_INFO_MODULE);
        $this->setResumeCompleteModule(self::EDUCATION_MODULE);

        // 【最高学历】
        if (!$resumeInfo['top_education_code']) {
            return $this->formatResumeModule();
        } else {
            // 前进一个模块
            $this->resumeModuleAdvance();
        }

        $this->resumeCompleteModule['templateType'] = $resumeInfo['template_type'];
        $resumeCompleteInfo                         = BaseResumeComplete::find()
            ->andWhere([
                'and',
                [
                    '=',
                    'member_id',
                    $memberId,
                ],
                [
                    '=',
                    'template_type',
                    $resumeInfo['template_type'],
                ],
                [
                    '<>',
                    'status',
                    BaseResumeComplete::STATUS_DELETE,
                ],
            ])
            ->asArray()
            ->one();
        if (!$resumeCompleteInfo) {
            return $this->formatResumeModule();
        }

        // 【基础信息】
        if ($resumeCompleteInfo['basic'] < 1) {
            return $this->formatResumeModule();
        } else {
            // 步数加一
            $this->resumeModuleAdvance();

            // 根据学业就业状态追加模块
            if ($resumeInfo['template_type'] == BaseResume::TEMPLATE_TYPE_ACADEMIC) {
                // 教育经历
                if ($resumeCompleteInfo['education'] < 2) {
                    return $this->formatResumeModule();
                } else {
                    // 步数加一
                    $this->resumeModuleAdvance();
                }

                switch ($resumeInfo['postdoctor_work_status']) {
                    case BaseResume::POSTDOCTOR_WORK_STATUS_STUDYING:
                    case BaseResume::POSTDOCTOR_WORK_STATUS_FRESH_GRADUATE_JOB_SEEKING:
                    case BaseResume::POSTDOCTOR_WORK_STATUS_OTHER:

                        // 补充模块
                        $this->setResumeCompleteModule(self::INTENTION_MODULE);
                        $this->setResumeCompleteModule(self::ACHIEVEMENT_MODULE);

                        if ($resumeCompleteInfo['intention'] <= 0) {
                            return $this->formatResumeModule();
                        } else {
                            $this->resumeModuleAdvance();
                        }

                        return $this->formatResumeModule(true);
                        break;
                    case BaseResume::POSTDOCTOR_WORK_STATUS_EMPLOYED_NOT_SEEKING:

                        // 补充模块
                        $this->setResumeCompleteModule(self::WORK_MODULE);
                        $this->setResumeCompleteModule(self::ACHIEVEMENT_MODULE);

                        if ($resumeCompleteInfo['work'] <= 0) {
                            return $this->formatResumeModule();
                        } else {
                            $this->resumeModuleAdvance();
                        }

                        return $this->formatResumeModule(true);
                        break;
                    case BaseResume::POSTDOCTOR_WORK_STATUS_EMPLOYED_SEEKING_BETTER:
                    case BaseResume::POSTDOCTOR_WORK_STATUS_UNEMPLOYED_JOB_SEEKING:
                    case BaseResume::POSTDOCTOR_WORK_STATUS_RETIRED:
                        // 补充模块
                        $this->setResumeCompleteModule(self::WORK_MODULE);
                        $this->setResumeCompleteModule(self::INTENTION_MODULE);
                        if ($resumeCompleteInfo['work'] <= 0) {
                            return $this->formatResumeModule();
                        } else {
                            $this->resumeModuleAdvance();
                        }
                        if ($resumeCompleteInfo['intention'] <= 0) {
                            return $this->formatResumeModule();
                        }

                        // 追加论文成果模块，
                        $this->setResumeCompleteModule(self::ACHIEVEMENT_MODULE);

                        return $this->formatResumeModule(true);
                        break;
                    case BaseResume::POSTDOCTOR_WORK_STATUS_RESEARCHER:

                        // 补充模块
                        $this->setResumeCompleteModule(self::POSTDOCTOR_MODULE);
                        $this->setResumeCompleteModule(self::INTENTION_MODULE);
                        $this->setResumeCompleteModule(self::ACHIEVEMENT_MODULE);

                        if ($resumeCompleteInfo['postdoctor_experience'] <= 0) {
                            return $this->formatResumeModule();
                        } else {
                            $this->resumeModuleAdvance();
                        }
                        if ($resumeCompleteInfo['intention'] <= 0) {
                            return $this->formatResumeModule();
                        } else {
                            $this->resumeModuleAdvance();
                        }

                        return $this->formatResumeModule(true);
                        break;
                    default:
                        throw new MessageException('用户学业/就业状态不对');
                }
            } else {
                if ($resumeInfo['template_type'] == BaseResume::TEMPLATE_TYPE_COMMON) {
                    // 教育经历，硕士有两个，本科以下只有一个
                    if ($resumeInfo['top_education_code'] == BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE) {
                        if ($resumeCompleteInfo['education'] < 2) {
                            return $this->formatResumeModule();
                        } else {
                            // 步数加一
                            $this->resumeModuleAdvance();
                        }
                    } else {
                        if ($resumeCompleteInfo['education'] < 1) {
                            return $this->formatResumeModule();
                        } else {
                            // 步数加一
                            $this->resumeModuleAdvance();
                        }
                    }

                    // 身份=“职场人”且“参加工作时间”≠“暂无工作经历”
                    if ($resumeInfo['identity_type'] == BaseResume::IDENTITY_TYPE_WORKER && $resumeInfo['begin_work_date'] != TimeHelper::ZERO_DATE) {
                        $this->setResumeCompleteModule(self::WORK_MODULE);
                        $this->setResumeCompleteModule(self::INTENTION_MODULE);
                        if ($resumeCompleteInfo['work'] <= 0) {
                            return $this->formatResumeModule();
                        } else {
                            $this->resumeModuleAdvance();
                        }
                        if ($resumeCompleteInfo['intention'] <= 0) {
                            return $this->formatResumeModule();
                        }

                        return $this->formatResumeModule(true);
                    } else {
                        // 身份=“在校/应届生” 或 身份=“职场人”但“参加工作时间”＝“暂无工作经历”
                        if (($resumeInfo['identity_type'] == BaseResume::IDENTITY_TYPE_GRADUATE || $resumeInfo['identity_type'] == BaseResume::IDENTITY_TYPE_WORKER) && $resumeInfo['begin_work_date'] == TimeHelper::ZERO_DATE) {
                            $this->setResumeCompleteModule(self::INTENTION_MODULE);
                            $this->setResumeCompleteModule(self::ADVANTAGE_MODULE);

                            if ($resumeCompleteInfo['intention'] <= 0) {
                                return $this->formatResumeModule();
                            } else {
                                $this->resumeModuleAdvance();
                            }

                            return $this->formatResumeModule(true);
                        }
                    }
                }
            }
        }

        throw new MessageException('数据不正确，请确认状态是否正常');
    }

    /**
     * 追加填写模块
     */
    public function setResumeCompleteModule($module)
    {
        if ($this->operationPlatform == 'H5') {
            if ($module == self::ADVANTAGE_MODULE) {
                return false;
            }
        }
        $this->resumeCompleteModule['resumeModule'][] = $module;
    }

    /**
     * 模块加一
     * @return void
     */
    public function resumeModuleAdvance()
    {
        $this->resumeCompleteModule['resumeCompleteModule']++;
    }

    public function formatResumeModule($isSetResumeActive = false)
    {
        if ($isSetResumeActive) {
            // 已完成
            $this->resumeCompleteModule['isComplete'] = true;
            $this->setResumeActive();
        }

        $this->resumeCompleteModule['currentModule'] = $this->resumeCompleteModule['resumeModule'][$this->resumeCompleteModule['resumeCompleteModule']] ?? '';

        $resumeModuleKey                          = array_search(empty($this->resumeModule) ? $this->resumeCompleteModule['currentModule'] : $this->resumeModule,
            $this->resumeCompleteModule['resumeModule']);
        $this->resumeCompleteModule['nextModule'] = $resumeModuleKey === false ? '' : ($this->resumeCompleteModule['resumeModule'][$resumeModuleKey + 1] ?? '');
        $this->resumeCompleteModule['backModule'] = $this->resumeCompleteModule['resumeModule'][$resumeModuleKey - 1] ?? '';

        return $this->resumeCompleteModule;
    }

    public function getEmailStatus()
    {
        // 定义允许的邮箱后缀，使用正则转义特殊字符
        $allowedSuffixes = [
            '\.edu\.cn',
            '\.ac\.cn',
            '\.edu',
            '\.gov\.cn',
            '\.org\.cn',
            '\.org',
            '\.int',
            '\.gov',
        ];

        // 构建正则表达式模式
        $pattern = '/(' . implode('|', $allowedSuffixes) . ')$/i';

        $showEmail         = 2;
        $showAcademicEmail = 2;
        if ($this->memberModel->email_register_status != BaseMember::EMAIL_REGISTER_STATUS_NORMAL) {
            $showEmail         = 1;
            $showAcademicEmail = 1;
        }
        if ($this->memberModel->email_register_status == BaseMember::EMAIL_REGISTER_STATUS_NORMAL) {
            if (preg_match($pattern, $this->memberModel->email) !== 1) {
                $showAcademicEmail = 1;
            }
        }

        return [
            'showEmail'         => $showEmail,
            'showAcademicEmail' => $showAcademicEmail,
        ];
    }

    /**
     * 用户信息回到初始
     */
    public function reloadResume()
    {
    }

    /**
     * 将用户从未完善用户转为完善用户
     * @return void
     */
    public function setResumeActive()
    {
        $this->resumeModel->status                = BaseResume::STATUS_ACTIVE;
        $this->resumeModel->display_template_type = $this->resumeModel->template_type;

        $this->resumeModel->save();

        // 根据用户选择，映射identity_type和work_status
        if ($this->resumeModel->template_type == BaseResume::TEMPLATE_TYPE_ACADEMIC) {
            switch ($this->resumeModel->postdoctor_work_status) {
                case BaseResume::POSTDOCTOR_WORK_STATUS_STUDYING:
                    $this->resumeModel->identity_type = BaseResume::IDENTITY_TYPE_GRADUATE;
                    break;
                case BaseResume::POSTDOCTOR_WORK_STATUS_FRESH_GRADUATE_JOB_SEEKING:
                    $this->resumeModel->work_status   = BaseResume::WORK_STATUS_JOB_SEEKING;
                    $this->resumeModel->identity_type = BaseResume::IDENTITY_TYPE_GRADUATE;
                    break;
                case BaseResume::POSTDOCTOR_WORK_STATUS_RESEARCHER:
                case BaseResume::POSTDOCTOR_WORK_STATUS_UNEMPLOYED_JOB_SEEKING:
                    $this->resumeModel->identity_type = BaseResume::IDENTITY_TYPE_WORKER;
                    $this->resumeModel->work_status   = BaseResume::WORK_STATUS_JOB_SEEKING;
                    break;
                case BaseResume::POSTDOCTOR_WORK_STATUS_EMPLOYED_SEEKING_BETTER:
                    $this->resumeModel->identity_type = BaseResume::IDENTITY_TYPE_WORKER;
                    $this->resumeModel->work_status   = BaseResume::WORK_STATUS_WAITING;
                    break;
                case BaseResume::POSTDOCTOR_WORK_STATUS_EMPLOYED_NOT_SEEKING:
                    $this->resumeModel->identity_type = BaseResume::IDENTITY_TYPE_WORKER;
                    $this->resumeModel->work_status   = BaseResume::WORK_STATUS_NOT_SEEKING;
                    break;
                case BaseResume::POSTDOCTOR_WORK_STATUS_RETIRED:
                    $this->resumeModel->identity_type = BaseResume::IDENTITY_TYPE_WORKER;
                    $this->resumeModel->work_status   = BaseResume::WORK_STATUS_RETIRED;
                    break;
                default:
                    $this->resumeModel->work_status   = 0;
                    $this->resumeModel->identity_type = BaseResume::IDENTITY_TYPE_DELETION;
                    break;
            }
        }

        // 删除教育经历，求职意向，博士后经历，工作经历
        $deleteResumeComplete = BaseResumeComplete::findOne([
            'and',
            [
                '=',
                'resume_id',
                $this->resumeModel->id,
            ],
            [
                '<>',
                'template_type',
                $this->resumeModel->template_type,
            ],
        ]);
        if ($deleteResumeComplete) {
            $deleteResumeComplete->status = BaseResumeComplete::STATUS_DELETE;
            $deleteResumeComplete->save();
        }

        // 教育经历
        // 最高学历为博士，保留学术版-博士教育经历和学术版-本科教育经历
        // 最高学历为硕士，保留通用版-硕士经历和通用版-本科教育经历
        // 最高学历为本科，保留通用版-本科教育经历
        // 最高学历为大专，保留通用版-大专/其他教育经历
        if ($this->resumeModel->top_education_code == BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE) {
            $educationWhere = [
                [
                    '=',
                    'template_type',
                    $this->resumeModel->template_type,
                ],
            ];
        } else {
            if ($this->resumeModel->top_education_code == BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE) {
                $educationWhere = [
                    [
                        '=',
                        'template_type',
                        $this->resumeModel->template_type,
                    ],
                    [
                        'in',
                        'education_id',
                        [
                            BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE,
                            BaseResumeEducation::EDUCATION_TYPE_UNDERGRADUATE_CODE,
                        ],
                    ],
                ];
            } else {
                if ($this->resumeModel->top_education_code == BaseResumeEducation::EDUCATION_TYPE_UNDERGRADUATE_CODE) {
                    $educationWhere = [
                        [
                            '=',
                            'template_type',
                            $this->resumeModel->template_type,
                        ],
                        [
                            'in',
                            'education_id',
                            [
                                BaseResumeEducation::EDUCATION_TYPE_UNDERGRADUATE_CODE,
                            ],
                        ],
                    ];
                } else {
                    $educationWhere = [
                        [
                            '=',
                            'template_type',
                            $this->resumeModel->template_type,
                        ],
                        [
                            'in',
                            'education_id',
                            [
                                BaseResumeEducation::EDUCATION_TYPE_JUNIOR_CODE,
                                BaseResumeEducation::EDUCATION_TYPE_OTHER_CODE,
                            ],
                        ],
                    ];
                }
            }
        }
        $educationWhere[]    = [
            '=',
            'resume_id',
            $this->resumeModel->id,
        ];
        $educationWhere[]    = [
            '=',
            'status',
            BaseResumeEducation::STATUS_ACTIVE,
        ];
        $saveEducationList   = BaseResumeEducation::find()
            ->andWhere(new AndCondition($educationWhere))
            ->select(['id'])
            ->column();
        $deleteEducationList = BaseResumeEducation::find()
            ->where([
                'and',
                [
                    '=',
                    'resume_id',
                    $this->resumeModel->id,
                ],
                [
                    'not in',
                    'id',
                    $saveEducationList,
                ],
            ])
            ->asArray()
            ->all();

        if ($deleteEducationList) {
            $deleteEducationId = array_column($deleteEducationList, 'id');

            $mentorRelations = BaseResumeEducationMentorRelation::find()
                ->where(['resume_education_id' => $deleteEducationId])
                ->all();
            foreach ($mentorRelations as $mentorRelation) {
                $mentorRelation->status = BaseResumeEducationMentorRelation::STATUS_DELETE;
                $mentorRelation->save();
            }

            $majorRelations = BaseResumeEducationMajorRelation::find()
                ->where(['resume_education_id' => $deleteEducationId])
                ->all();
            foreach ($majorRelations as $majorRelation) {
                $majorRelation->status = BaseResumeEducationMajorRelation::STATUS_DELETE;
                $majorRelation->save();
            }

            $educations = BaseResumeEducation::find()
                ->where(['id' => $deleteEducationId])
                ->all();
            foreach ($educations as $education) {
                $education->status = BaseResumeEducation::STATUS_FREEZE;
                $education->save();
            }
        }

        $deleteIntentionList = BaseResumeIntention::find()
            ->where([
                'and',
                [
                    '=',
                    'resume_id',
                    $this->resumeModel->id,
                ],
                [
                    '<>',
                    'template_type',
                    $this->resumeModel->template_type,
                ],
            ])
            ->asArray()
            ->all();
        if ($deleteIntentionList) {
            $deleteIntentionId = array_column($deleteIntentionList, 'id');

            $intentionAreaRelations = BaseResumeIntentionAreaRelation::find()
                ->where(['intention_id' => $deleteIntentionId])
                ->all();
            foreach ($intentionAreaRelations as $intentionAreaRelation) {
                $intentionAreaRelation->status = BaseResumeIntention::STATUS_DELETE;
                $intentionAreaRelation->save();
            }

            $intentions = BaseResumeIntention::find()
                ->where(['id' => $deleteIntentionId])
                ->all();
            foreach ($intentions as $intention) {
                $intention->status = BaseResumeIntention::STATUS_DELETE;
                $intention->save();
            }
        }

        $postdoctorWorkList = BaseResumePostdoctorWork::find()
            ->where([
                'and',
                [
                    '=',
                    'resume_id',
                    $this->resumeModel->id,
                ],
                [
                    '<>',
                    'template_type',
                    $this->resumeModel->template_type,
                ],
            ])
            ->asArray()
            ->all();

        if ($postdoctorWorkList) {
            $deletePostdoctorWorkId = array_column($postdoctorWorkList, 'id');

            $researchFieldRelations = BaseResumePostdoctorWorkResearchFieldRelation::find()
                ->where(['resume_postdoctor_work_id' => $deletePostdoctorWorkId])
                ->all();
            foreach ($researchFieldRelations as $researchFieldRelation) {
                $researchFieldRelation->status = BaseResumePostdoctorWorkResearchFieldRelation::STATUS_DELETE;
                $researchFieldRelation->save();
            }

            $mentorTitleRelations = BaseResumePostdoctorWorkMentorTitleRelation::find()
                ->where(['resume_postdoctor_work_id' => $deletePostdoctorWorkId])
                ->all();
            foreach ($mentorTitleRelations as $mentorTitleRelation) {
                $mentorTitleRelation->status = BaseResumePostdoctorWorkMentorTitleRelation::STATUS_DELETE;
                $mentorTitleRelation->save();
            }

            $projectRelations = BaseResumePostdoctorWorkProjectRelation::find()
                ->where(['resume_postdoctor_work_id' => $deletePostdoctorWorkId])
                ->all();
            foreach ($projectRelations as $projectRelation) {
                $projectRelation->status = BaseResumePostdoctorWorkProjectRelation::STATUS_DELETE;
                $projectRelation->save();
            }

            $postdoctorWorks = BaseResumePostdoctorWork::find()
                ->where(['id' => $deletePostdoctorWorkId])
                ->all();
            foreach ($postdoctorWorks as $postdoctorWork) {
                $postdoctorWork->status = BaseResumePostdoctorWork::STATUS_FREEZE;
                $postdoctorWork->save();
            }
        }

        $workList = BaseResumeWork::find()
            ->where([
                'and',
                [
                    '=',
                    'resume_id',
                    $this->resumeModel->id,
                ],
                [
                    '<>',
                    'template_type',
                    $this->resumeModel->template_type,
                ],
            ])
            ->asArray()
            ->all();

        if ($workList) {
            $deleteWorkId = array_column($workList, 'id');

            $jobTitleRelations = BaseResumeWorkJobTitleRelation::find()
                ->where(['resume_work_id' => $deleteWorkId])
                ->all();
            foreach ($jobTitleRelations as $jobTitleRelation) {
                $jobTitleRelation->status = BaseResumeWorkJobTitleRelation::STATUS_DELETE;
                $jobTitleRelation->save();
            }

            $workResearchFieldRelations = BaseResumeWorkResearchFieldRelation::find()
                ->where(['resume_work_id' => $deleteWorkId])
                ->all();
            foreach ($workResearchFieldRelations as $workResearchFieldRelation) {
                $workResearchFieldRelation->status = BaseResumeWorkResearchFieldRelation::STATUS_DELETE;
                $workResearchFieldRelation->save();
            }

            $workTypeRelations = BaseResumeWorkTypeRelation::find()
                ->where(['resume_work_id' => $deleteWorkId])
                ->all();
            foreach ($workTypeRelations as $workTypeRelation) {
                $workTypeRelation->status = BaseResumeWorkTypeRelation::STATUS_DELETE;
                $workTypeRelation->save();
            }

            $works = BaseResumeWork::find()
                ->where(['id' => $deleteWorkId])
                ->all();
            foreach ($works as $work) {
                $work->status = BaseResumeWork::STATUS_FREEZE;
                $work->save();
            }
        }

        // 查找工作经历里面的职称
        $resumeWorkTitleList = BaseResumeWorkJobTitleRelation::find()
            ->where([
                'resume_id' => $this->resumeModel->id,
            ])
            ->asArray()
            ->all();

        if ($resumeWorkTitleList) {
            foreach ($resumeWorkTitleList as $resumeWorkTitle) {
                $resumeTitleRelationInfo            = new BaseResumeTitleRelation();
                $resumeTitleRelationInfo->member_id = $this->memberId;
                $resumeTitleRelationInfo->resume_id = $this->resumeModel->id;
                $resumeTitleRelationInfo->title_id  = $resumeWorkTitle['dict_code'];
                $resumeTitleRelationInfo->get_year  = $resumeWorkTitle['get_year'];
                $resumeTitleRelationInfo->status    = BaseResumeTitleRelation::STATUS_ACTIVE;
                $resumeTitleRelationInfo->save();
            }
            $resumeWorkTitleIds          = array_column($resumeWorkTitleList, 'dict_code');
            $this->resumeModel->title_id = explode(',', $resumeWorkTitleIds);
        }

        // 将研究领域标签设置为用户标签
        $resumePostdoctorWorkResearchFieldRelationList = BaseResumePostdoctorWorkResearchFieldRelation::find()
            ->where([
                'resume_id' => $this->resumeModel->id,
                'status'    => BaseResumePostdoctorWorkResearchFieldRelation::STATUS_ACTIVE,
            ])
            ->asArray()
            ->all();

        foreach ($resumePostdoctorWorkResearchFieldRelationList as $resumePostdoctorWorkResearchFieldRelation) {
            $researchField                    = new BaseResumeResearchField();
            $researchField->research_field_id = $resumePostdoctorWorkResearchFieldRelation['research_field_id'];
            $researchField->resume_id         = $this->resumeModel->id;
            $researchField->member_id         = $this->memberId;
            $researchField->status            = BaseResumeResearchField::STATUS_ACTIVE;
            $researchField->save();
        }

        // 执行一下完善简历后执行的更新逻辑
        (new ResumeCompleteSaveAfterService())->setResumeModel($this->resumeModel->id)
            ->updateResumeIsPostdoctor()
            ->updateTopEducationId()
            ->updateResumeIsAbroad()
            ->updateLastUpdateTime();

        return $this;
    }

    public function getResumeCount()
    {
        $result['postdoctorCount'] = intval(Cache::get(Cache::POSTDOCTOR_COUNT));
        $result['resumeCount']     = intval(Cache::get(Cache::RESUME_COUNT));

        $result['postdoctorCountTenThousand'] = ceil($result['postdoctorCount'] / 10000);
        $result['resumeCountTenThousand']     = ceil($result['resumeCountTenThousand'] / 10000);

        return $result;
    }

    public function postdoctorIdCard()
    {
        $defaultData             = [];
        $postdoctorEducationInfo = BaseResumeEducation::findOne([
            'resume_id'    => $this->resumeModel->id,
            'education_id' => BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE,
            'status'       => BaseResumeEducation::STATUS_ACTIVE,
        ]);

        $postdoctorWorkInfo = $this->getPostdoctorWorkPosition();
        $cardData           = [];

        if ($postdoctorWorkInfo['resume_postdoctor_work_id']) {
            if ($postdoctorWorkInfo['position']) {
                $cardData[] = [
                    'type' => 1,
                    'desc' => $postdoctorWorkInfo['position'],
                ];
            }
            if ($postdoctorWorkInfo['research_direction']) {
                $cardData[] = [
                    'type' => 2,
                    'desc' => $postdoctorWorkInfo['research_direction'],
                ];
            }
        } else {
            $workInfo = $this->getWorkPosition();
            if ($workInfo['position']) {
                $cardData[] = [
                    'type' => 1,
                    'desc' => $workInfo['position'],
                ];
            }

            // 获取用户求职意向对应category名称
            $intentionInfo = BaseResumeIntention::find()
                ->alias('i')
                ->leftJoin(['cj' => BaseCategoryJob::tableName()], 'i.job_category_id = cj.id')
                ->select([
                    'cj.name',
                ])
                ->where([
                    'i.resume_id' => $this->resumeModel->id,
                    'i.status'    => BaseResumeIntention::STATUS_ACTIVE,
                ])
                ->asArray()
                ->one();

            if ($intentionInfo['name']) {
                $cardData[] = [
                    'type' => 3,
                    'desc' => $intentionInfo['name'],
                ];
            }
        }

        $defaultData['resume_id_card'] = [
            'name'             => $this->resumeModel->name,
            'degree_type'      => $postdoctorEducationInfo->degree_type,
            'school'           => $postdoctorEducationInfo->school,
            'major'            => $postdoctorEducationInfo->major_custom,
            'card_data'        => $cardData,
            'complete'         => $this->resumeModel->complete,
            'last_update_time' => $this->resumeModel->last_update_time,
        ];

        return $defaultData;
    }

    /**
     * 获取博士后工作经历职务信息
     */
    public function getPostdoctorWorkPosition()
    {
        $resumePosition     = '';
        $postdoctorWorkInfo = BaseResumePostdoctorWork::find()
            ->select([
                'id',
                new \yii\db\Expression('CASE WHEN is_now = 1 THEN CURDATE() ELSE end_date END AS end_date',),
                'join_training_institution',
                'research_direction',
            ])
            ->where([
                'resume_id' => $this->resumeModel->id,
                'status'    => BaseResumePostdoctorWork::STATUS_ACTIVE,
            ])
            ->orderBy('end_date desc, id desc')
            ->asArray()
            ->one();

        if ($postdoctorWorkInfo) {
            $postdoctorWorkEndTime = strtotime($postdoctorWorkInfo['end_date']);
            switch (true) {
                case ($postdoctorWorkEndTime > time()) :
                    $resumePosition = $postdoctorWorkInfo['join_training_institution'] . '博士后研究员';
                    break;
                case ($postdoctorWorkEndTime <= time()) :
                default:
                    $resumePosition = '前' . $postdoctorWorkInfo['join_training_institution'] . '博士后研究员';
                    break;
            }
        }

        return [
            'resume_postdoctor_work_id' => $postdoctorWorkInfo['id'],
            'position'                  => $resumePosition,
            'research_direction'        => $postdoctorWorkInfo['research_direction'] ?? '',
        ];
    }

    /**
     * 获取简历工作经历职务
     */
    public function getWorkPosition()
    {
        $resumePosition = '';
        $resumeWorkInfo = BaseResumeWork::find()
            ->select([
                // 使用条件表达式：如果is_now=1则取今天日期，否则取原end_date
                new \yii\db\Expression('CASE WHEN is_now = 1 THEN CURDATE() ELSE end_date END AS end_date',),
                'company',
                'job_name',
                'is_now',
            ])
            ->where([
                'resume_id' => $this->resumeModel->id,
                'status'    => BaseResumeWork::STATUS_ACTIVE,
            ])
            // 按处理后的end_date降序排序，相同情况下按id降序
            ->orderBy([
                'end_date' => SORT_DESC,
                'id'       => SORT_DESC,
            ])
            ->asArray()
            ->one();

        if ($resumeWorkInfo) {
            $resumeWorkInfoEndTime = strtotime($resumeWorkInfo['end_date']);

            // 判断显示哪一个，谁晚
            switch (true) {
                case ($resumeWorkInfo['is_now'] == BaseResumeWork::IS_NOW_YES) :
                case ($resumeWorkInfoEndTime > time()) :
                    $resumePosition = $resumeWorkInfo['company'] . ' ' . $resumeWorkInfo['job_name'];
                    break;
                case ($resumeWorkInfoEndTime <= time()) :
                default:
                    $resumePosition = '前' . $resumeWorkInfo['company'] . ' ' . $resumeWorkInfo['job_name'];
                    break;
            }
        }

        return [
            'resume_work_id' => $resumeWorkInfo['id'],
            'company'        => $resumeWorkInfo['company'] ?? '',
            'position'       => $resumePosition,
        ];
    }
}