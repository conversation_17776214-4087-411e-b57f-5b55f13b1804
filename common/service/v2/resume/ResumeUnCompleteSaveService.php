<?php
/**
 * 编辑-未完善
 * create user：伍彦川
 * create time：2025/9/22 11:12
 */
namespace common\service\v2\resume;

use common\base\models\BaseResumeEducation;
use common\components\MessageException;
use common\service\CommonService;
use common\service\v2\resume\unCompleteSaveImpl\ResumeBasicInfoService;
use common\service\v2\resume\unCompleteSaveImpl\ResumeEducationService;
use common\service\v2\resume\unCompleteSaveImpl\ResumeIntentionService;
use common\service\v2\resume\unCompleteSaveImpl\ResumePostdoctorWorkService;
use common\service\v2\resume\unCompleteSaveImpl\ResumeTopEducationService;
use common\service\v2\resume\unCompleteSaveImpl\ResumeWorkService;

class ResumeUnCompleteSaveService
{
    public function saveInfo($memberId, $resumeModule, $params = [])
    {
        $impl = null;
        switch ($resumeModule) {
            case BaseResumeService::TOP_EDUCATION_MODULE:
                $impl = new ResumeTopEducationService();
                break;
            case BaseResumeService::BASIC_INFO_MODULE:
                $impl = new ResumeBasicInfoService();
                break;
            case BaseResumeService::EDUCATION_MODULE:
                $impl = new ResumeEducationService();
                break;
            case BaseResumeService::POSTDOCTOR_MODULE:
                $impl = new ResumePostdoctorWorkService();
                break;
            case BaseResumeService::WORK_MODULE:
                $impl = new ResumeWorkService();
                break;
            case BaseResumeService::INTENTION_MODULE:
                $impl = new ResumeIntentionService();
                break;
            default:
                throw new MessageException('没有该模块');
                break;
        }

        $data = $impl->setParams($params)
            ->setMemberModel($memberId)
            ->setPlatform(PLATFORM == 'PC' ? CommonService::PLATFORM_WEB : CommonService::PLATFORM_H5)
            ->setResumeModule($resumeModule)
            ->validate()
            ->saveBefore()
            ->save()
            ->saveAfter()
            ->getResumeModule();

        if (!in_array($resumeModule, $data['resumeModule']['resumeModule'])) {
            throw new MessageException('不允许操作当前模块');
        }

        return $data;
    }
}