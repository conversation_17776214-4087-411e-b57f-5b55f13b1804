<?php
/**
 * 编辑内容
 * create user：伍彦川
 * create time：2025/9/22 11:12
 */
namespace common\service\v2\resume;

use common\components\MessageException;
use common\helpers\ArrayHelper;
use common\service\v2\resume\completeSaveImpl\ResumeAcademicBookService;
use common\service\v2\resume\completeSaveImpl\ResumeAcademicConferenceActivityService;
use common\service\v2\resume\completeSaveImpl\ResumeAcademicPageService;
use common\service\v2\resume\completeSaveImpl\ResumeAcademicPatentService;
use common\service\v2\resume\completeSaveImpl\ResumeAchievementService;
use common\service\v2\resume\completeSaveImpl\ResumeAdditionalInfoService;
use common\service\v2\resume\completeSaveImpl\ResumeAdvantageService;
use common\service\v2\resume\completeSaveImpl\ResumeAssociationPositionService;
use common\service\v2\resume\completeSaveImpl\ResumeBasicInfoService;
use common\service\v2\resume\completeSaveImpl\ResumeCertificateService;
use common\service\v2\resume\completeSaveImpl\ResumeCompetitionGuidanceService;
use common\service\v2\resume\completeSaveImpl\ResumeCompleteSaveImpl;
use common\service\v2\resume\completeSaveImpl\ResumeConferencePageService;
use common\service\v2\resume\completeSaveImpl\ResumeEducationReformService;
use common\service\v2\resume\completeSaveImpl\ResumeEducationService;
use common\service\v2\resume\completeSaveImpl\ResumeIntentionService;
use common\service\v2\resume\completeSaveImpl\ResumeJournalPositionService;
use common\service\v2\resume\completeSaveImpl\ResumeOtherSkillService;
use common\service\v2\resume\completeSaveImpl\ResumeOverseasExperienceService;
use common\service\v2\resume\completeSaveImpl\ResumePostdoctorWorkService;
use common\service\v2\resume\completeSaveImpl\ResumeProjectReviewService;
use common\service\v2\resume\completeSaveImpl\ResumeResearchDirectionService;
use common\service\v2\resume\completeSaveImpl\ResumeResearchProjectService;
use common\service\v2\resume\completeSaveImpl\ResumeRewardService;
use common\service\v2\resume\completeSaveImpl\ResumeSocialPositionService;
use common\service\v2\resume\completeSaveImpl\ResumeTeachingCourseService;
use common\service\v2\resume\completeSaveImpl\ResumeTextbookWritingService;
use common\service\v2\resume\completeSaveImpl\ResumeThesisCommitteeService;
use common\service\v2\resume\completeSaveImpl\ResumeWorkService;

class ResumeCompleteSaveService
{
    public function saveInfo($memberId, $resumeModule, $params = [])
    {
        /** @var ResumeCompleteSaveImpl $impl */
        $impl = null;
        switch ($resumeModule) {
            case BaseResumeService::BASIC_INFO_MODULE:
                // 基本信息模块：处理个人基础信息（如姓名、性别、联系方式等）的保存逻辑
                $impl = new ResumeBasicInfoService();
                break;
            case BaseResumeService::ADVANTAGE_MODULE:
                // 个人优势模块
                $impl = new ResumeAdvantageService();
                break;
            case BaseResumeService::INTENTION_MODULE:
                // 求职意向模块
                $impl = new ResumeIntentionService();
                break;
            case BaseResumeService::RESEARCH_DIRECTION_MODULE:
                // 研究方向模块
                $impl = new ResumeResearchDirectionService();
                break;
            case BaseResumeService::EDUCATION_MODULE:
                // 教育经历模块
                $impl = new ResumeEducationService();
                break;
            case BaseResumeService::WORK_MODULE:
                // 工作经历模块
                $impl = new ResumeWorkService();
                break;
            case BaseResumeService::ACHIEVEMENT_MODULE:
                // 五年研究成果模块
                $impl = new ResumeAchievementService();
                break;
            case BaseResumeService::POSTDOCTOR_WORK_MODULE:
                // 博士后经历模块
                $impl = new ResumePostdoctorWorkService();
                break;
            case BaseResumeService::OVERSEAS_EXPERIENCE_MODULE:
                // 其他海外经历模块
                $impl = new ResumeOverseasExperienceService();
                break;
            case BaseResumeService::ACADEMIC_PAGE_MODULE:
                // 期刊论文模块
                $impl = new ResumeAcademicPageService();
                break;
            case BaseResumeService::CONFERENCE_PAGE_MODULE:
                // 会议论文模块
                $impl = new ResumeConferencePageService();
                break;
            case BaseResumeService::ACADEMIC_PATENT_MODULE:
                // 专利模块
                $impl = new ResumeAcademicPatentService();
                break;
            case BaseResumeService::ACADEMIC_BOOK_MODULE:
                // 出版著作
                $impl = new ResumeAcademicBookService();
                break;
            case BaseResumeService::RESEARCH_PROJECT_MODULE:
                // 项目经历
                $impl = new ResumeResearchProjectService();
                break;
            case BaseResumeService::ACADEMIC_CONFERENCE_ACTIVITY_MODULE:
                // 学术会议/活动模块
                $impl = new ResumeAcademicConferenceActivityService();
                break;
            case BaseResumeService::ASSOCIATION_POSITION_MODULE:
                // 学会/协会任职模块
                $impl = new ResumeAssociationPositionService();
                break;
            case BaseResumeService::JOURNAL_POSITION_MODULE:
                // 期刊/论文职务模块
                $impl = new ResumeJournalPositionService();
                break;
            case BaseResumeService::SOCIAL_POSITION_MODULE:
                // 社会兼职/顾问模块
                $impl = new ResumeSocialPositionService();
                break;
            case BaseResumeService::PROJECT_REVIEW_MODULE:
                // 项目/基金评审模块
                $impl = new ResumeProjectReviewService();
                break;
            case BaseResumeService::THESIS_COMMITTEE_MODULE:
                // 学位答辩评委模块
                $impl = new ResumeThesisCommitteeService();
                break;
            case BaseResumeService::TEACHING_COURSE_MODULE:
                // 主讲课程模块
                $impl = new ResumeTeachingCourseService();
                break;
            case BaseResumeService::COMPETITION_GUIDANCE_MODULE:
                // 竞赛指导模块
                $impl = new ResumeCompetitionGuidanceService();
                break;
            case BaseResumeService::EDUCATION_REFORM_MODULE:
                // 教改实践模块
                $impl = new ResumeEducationReformService();
                break;
            case BaseResumeService::TEXTBOOK_WRITING_MODULE:
                // 教材编写模块
                $impl = new ResumeTextbookWritingService();
                break;
            case BaseResumeService::REWARD_MODULE:
                // 奖励/荣誉/称号/头衔模块
                $impl = new ResumeRewardService();
                break;
            case BaseResumeService::CERTIFICATE_MODULE:
                // 技能特长-资质证书模块
                $impl = new ResumeCertificateService();
                break;
            case BaseResumeService::OTHER_SKILL_MODULE:
                // 技能特长模块
                $impl = new ResumeOtherSkillService();
                break;
            case BaseResumeService::ADDITIONAL_INFO_MODULE:
                // 附加信息模块
                $impl = new ResumeAdditionalInfoService();
                break;
            default:
                throw new MessageException('没有该模块');
                break;
        }

        $data = $impl->setParams($params)
            ->setMemberModel($memberId)
            ->setPlatform(PLATFORM)
            ->setResumeModule($resumeModule)
            ->validate()
            ->saveBefore()
            ->save()
            ->saveAfter()
            ->getMessageData();

        return ArrayHelper::intToString($data);
    }
}