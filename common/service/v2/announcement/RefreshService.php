<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\announcement;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseAnnouncementLog;
use common\base\models\BaseCompanyPackageChangeLog;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseJob;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\service\companyPackage\CompanyPackageApplication;
use common\service\v2\job\RefreshService as JobRefreshService;
use common\base\models\BaseJobHandleLog;
use common\components\MessageException;
use common\helpers\IpHelper;
use yii\base\Exception;

/**
 * 公告刷新
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class RefreshService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 数据验证
     */
    public function validate()
    {
        if (empty($this->params['announcementId'])) {
            throw new MessageException('公告ID不能为空');
        }

        $operateRes = (new OperateService())->run('refresh', $this->articleInfo->is_show,
            $this->announcementInfo->status, $this->announcementInfo->audit_status);
        if ($operateRes['disabled'] != 1) {
            throw new MessageException('公告状态不允许刷新');
        }

        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            $this->baseCheckMemberPackage();

            // 找到现在还有多少公告刷新次数
            $remainAmount = $this->companyPackageConfigModel->announcement_refresh_amount;
            if ($remainAmount <= 0) {
                throw new MessageException('公告刷新次数已达上限');
            }
            // 找到上次刷新的时间
            $lastRefreshTime = $this->articleInfo->real_refresh_time;

            $subDay = TimeHelper::computeDaySub($lastRefreshTime, CUR_DATETIME);

            if ($subDay <= $this->companyPackageConfigModel->announcement_refresh_interval_day) {
                throw new MessageException('公告刷新间隔时间未到');
            }
        }

        return $this;
    }

    public function refresh($params)
    {
        $this->params = $params;
        $this->initInfo();
        $this->setAnnouncement($this->params['announcementId'])
            ->validate()
            ->run()
            ->companyPackage()
            ->log(BaseAnnouncementLog::TYPE_REFRESH)
            ->afterAnnouncementUpdateJob();
    }

    public function refreshBatch($params)
    {
        $successIds  = [];
        $failMessage = [];
        $this->initInfo();
        $ids = StringHelper::changeStrToFilterArr($params['announcementId'] ?? '');
        foreach ($ids as $id) {
            try {
                $this->params['announcementId'] = $id;
                $this->setAnnouncement($id)
                    ->validate()
                    ->run()
                    ->companyPackage()
                    ->log(BaseAnnouncementLog::TYPE_REFRESH_BATCH)
                    ->afterAnnouncementUpdateJob();

                if (!empty($this->announcementInfo->id)) {
                    $successIds[] = $this->announcementInfo->id;
                }
            } catch (MessageException $exception) {
                $failMessage[] = '公告ID：' . $id . '，刷新失败，原因：' . $exception->getMessage();
            }
        }

        return [
            'successIds'  => $successIds,
            'failMessage' => implode('<br>', $failMessage),
        ];
    }

    private function companyPackage()
    {
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            $companyPackageApplication = new CompanyPackageApplication();
            $handleType                = BaseCompanyPackageChangeLog::HANDLE_TYPE_ANNOUNCEMENT_REFRESH;
            $remark                    = BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[$handleType];
            $companyPackageApplication->announcementRefresh($this->companyInfo->id, 1, $remark);
        }

        return $this;
    }

    public function run()
    {
        $handleLog = [
            'add_time'        => CUR_DATETIME,
            'handle_type'     => strval(BaseAnnouncementHandleLog::HANDLE_TYPE_REFRESH),
            'handler_type'    => strval($this->params['platformType']),
            'handler_id'      => $this->params['userId'],
            'handler_name'    => $this->params['username'],
            'handle_before'   => json_encode([
                '刷新时间' => $this->articleInfo->refresh_time,
            ], JSON_UNESCAPED_UNICODE),
            'handle_after'    => json_encode([
                '刷新时间' => CUR_DATETIME,
            ]),
            'ip'              => IpHelper::getIpInt(),
            'announcement_id' => $this->announcementId,
        ];

        // 更新日期
        $this->articleInfo->refresh_time      = CUR_DATETIME;
        $this->articleInfo->refresh_date      = CUR_DATE;
        $this->articleInfo->real_refresh_time = CUR_DATETIME;
        if (!$this->articleInfo->save()) {
            throw new MessageException($this->articleInfo->getFirstErrorsMessage());
        }
        $this->announcementInfo->is_first_release = BaseAnnouncement::IS_FIRST_RELEASE_NO;
        if (!$this->announcementInfo->save()) {
            throw new MessageException($this->announcementInfo->getFirstErrorsMessage());
        }

        // 记录一下log数据
        BaseAnnouncementHandleLog::createInfo($handleLog);

        // 更新对应职位
        $jobIds = BaseJob::find()
            ->select(['id'])
            ->where([
                'and',
                [
                    '=',
                    'announcement_id',
                    $this->announcementId,
                ],
                [
                    '=',
                    'status',
                    BaseJob::STATUS_ONLINE,
                ],
                [
                    '=',
                    'is_show',
                    BaseJob::IS_SHOW_YES,
                ],
            ])
            ->column();
        if ($jobIds) {
            $jobIds            = implode(',', $jobIds);
            $jobRefreshService = new JobRefreshService();

            $jobRefreshService->setPlatform($this->operationPlatform)
                ->setId($jobIds)
                ->setBatch()
                ->run();
        }

        return $this;
    }
}