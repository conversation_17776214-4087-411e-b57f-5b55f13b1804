<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\announcement;

use admin\models\Announcement;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseAnnouncementLog;
use common\base\models\BaseArticle;
use common\base\models\BaseCompanyPackageChangeLog;
use common\base\models\BaseHwActivityAnnouncement;
use common\base\models\BaseJob;
use common\components\MessageException;
use common\helpers\IpHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\service\company\PushEditMessageService;
use common\service\companyPackage\CompanyPackageApplication;
use common\service\zhaoPinHuiColumn\SpecialActivityService;
use Faker\Provider\Base;
use yii\base\Exception;

/**
 * 公告再发布
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class RepublishService extends BaseService
{
    public $handleLogData = [];
    /** @var 发短信内容 */
    private $messageData = [];

    public $jobIds;

    public function __construct()
    {
        parent::__construct();
    }

    public function repubilshBatch($params)
    {
        $failMessage = [];
        $this->initInfo();
        $ids = StringHelper::changeStrToFilterArr($params['announcementId'] ?? '');
        foreach ($ids as $id) {
            try {
                $this->params['announcementId'] = $id;
                $this->setAnnouncement($this->params['announcementId'])
                    ->validate()
                    ->beforeHandle()
                    ->run()
                    ->afterHandle()
                    ->companyPackage()
                    ->log(BaseAnnouncementLog::TYPE_REPUBLISH_BATCH)
                    ->afterAnnouncementUpdateJob();

                $checkMajor = Announcement::majorMessageNotice($this->params['announcementId']);
                if ($checkMajor['res'] === false) {
                    $failMessage[] = $checkMajor['msg'];
                }
            } catch (MessageException $exception) {
                $failMessage[] = '公告ID：' . $id . '，再发布失败，原因：' . $exception->getMessage();
            }
        }

        return [
            'failMessage' => implode('<br>', $failMessage),
        ];
    }

    public function repubilsh($params)
    {
        $this->params = $params;
        $this->initInfo();
        $this->setAnnouncement($this->params['announcementId'])
            ->validate()
            ->beforeHandle()
            ->run()
            ->afterHandle()
            ->companyPackage()
            ->log(BaseAnnouncementLog::TYPE_REPUBLISH)
            ->afterAnnouncementUpdateJob();

        return Announcement::majorMessageNotice($this->params['announcementId']);
    }

    private function beforeHandle()
    {
        // 记录一下log数据
        $this->handleLogData = [
            'add_time'        => CUR_DATETIME,
            'job_id'          => 0,
            'handle_type'     => strval(BaseAnnouncementHandleLog::HANDLE_TYPE_RELEASE_AGAIN),
            'handler_type'    => strval($this->params['platformType']),
            'handler_id'      => $this->params['userId'],
            'handler_name'    => $this->params['username'],
            'handle_before'   => json_encode([
                '公告状态' => BaseAnnouncement::STATUS_LIST[$this->announcementInfo->status],
                '更新时间' => $this->announcementInfo->update_time,
            ], JSON_UNESCAPED_UNICODE),
            'handle_after'    => '',
            'ip'              => IpHelper::getIpInt(),
            'announcement_id' => $this->announcementInfo->id,
        ];

        $this->messageData = [
            'announcementId'   => $this->announcementInfo->id,
            'beforeStatusText' => BaseArticle::STATUS_LIST[$this->announcementInfo->status],
            'afterStatusText'  => BaseArticle::STATUS_LIST[$this->announcementInfo->status],
        ];

        return $this;
    }

    /**
     * 在执行后
     * @throws \yii\base\NotSupportedException
     */
    public function afterHandle()
    {
        $this->handleLogData['handle_after'] = json_encode([
            '公告状态' => BaseAnnouncement::STATUS_LIST[$this->announcementInfo->status],
            '更新时间' => CUR_DATETIME,
        ], JSON_UNESCAPED_UNICODE);
        // 消耗套餐,写日志等等
        BaseAnnouncementHandleLog::createInfo($this->handleLogData);

        $this->messageData['afterStatusText'] = BaseArticle::STATUS_LIST[$this->announcementInfo->status];
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            (new PushEditMessageService())->republish($this->messageData);
        }

        return $this;
    }

    public function run()
    {
        $this->announcementInfo->period_date  = TimeHelper::ZERO_TIME;
        $this->announcementInfo->offline_type = 0;
        $this->announcementInfo->status       = BaseAnnouncement::STATUS_ONLINE;
        if (!$this->announcementInfo->save()) {
            throw new MessageException($this->announcementInfo->getFirstErrorsMessage());
        }

        $dateTime                        = CUR_DATETIME;
        $this->articleInfo->status       = BaseArticle::STATUS_ONLINE;
        $this->articleInfo->refresh_time = $dateTime; //刷新时间
        $this->articleInfo->refresh_date = CUR_DATE; //刷新时间
        $this->articleInfo->release_time = $dateTime; //发布时间
        if (!$this->articleInfo->save()) {
            throw new MessageException($this->articleInfo->getFirstErrorsMessage());
        }

        $jobRepublishService = new \common\service\v2\job\RepublishService();
        $jobRepublishService->setBatch();
        $jobRepublishService->setPlatform($this->operationPlatform);
        $jobRepublishService->setJobId(implode(',', $this->jobIds));
        $jobRepublishService->run();

        // 需要将涉及到的活动重新设置
        // 追加逻辑，需要将关联这个公告的活动重新设置一下
        $allActivity = BaseHwActivityAnnouncement::find()
            ->select(['activity_id'])
            ->where(['announcement_id' => $this->announcementId])
            ->asArray()
            ->all();

        if ($allActivity) {
            $allActivityIds = array_column($allActivity, 'activity_id');
            SpecialActivityService::multipleActivityRelationSingleAnnouncement($this->announcementInfo->company_id,
                implode(',', $allActivityIds), $this->announcementId);
        }

        return $this;
    }

    public function companyPackage()
    {
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            // 在这里处理会员套餐的消费
            $companyPackageApplication = new CompanyPackageApplication();
            $handleType                = BaseCompanyPackageChangeLog::HANDLE_TYPE_ANNOUNCEMENT_RELEASE;
            $remark                    = BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[$handleType];
            $companyPackageApplication->announcementCreate($this->companyInfo->id, 1, $remark);
        }

        return $this;
    }

    /**
     * 验证数据
     */
    public function validate()
    {
        if (empty($this->params['announcementId'])) {
            throw new \Exception('公告ID不能为空');
        }

        $operateRes = (new OperateService())->run('republish', $this->articleInfo->is_show,
            $this->announcementInfo->status, $this->announcementInfo->audit_status);
        if ($operateRes['disabled'] != 1) {
            throw new MessageException('公告状态不允许再发布');
        }

        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            if ($this->announcementInfo->offline_type == BaseAnnouncement::OFFLINE_TYPE_VIOLATION) {
                throw new \Exception('当前内容违规，请联系运营人员');
            }

            $this->baseCheckMemberPackage();
            $this->checkRelease();
        }

        $this->jobIds = BaseJob::find()
            ->select(['id'])
            ->where([
                'announcement_id' => $this->announcementId,
                'status'          => BaseJob::STATUS_OFFLINE,
                'is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->asArray()
            ->column();

        if (empty($this->jobIds)) {
            throw new MessageException('没有可发布的职位');
        }

        return $this;
    }
}