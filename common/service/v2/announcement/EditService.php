<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\announcement;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementEdit;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseAnnouncementLog;
use common\base\models\BaseArticle;
use common\base\models\BaseCompanyPackageChangeLog;
use common\base\models\BaseJob;
use common\base\models\BaseJobTemp;
use common\components\MessageException;
use common\helpers\IpHelper;
use common\helpers\TimeHelper;
use common\service\company\PushEditMessageService;
use common\service\companyPackage\CompanyPackageApplication;
use Yii;
use yii\base\Exception;

/**
 * 公告编辑
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class EditService extends BaseService
{
    protected $editorType;

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 公告编辑
     */
    public function run()
    {
        $this->params = Yii::$app->request->post();
        if (!isset($this->params['announcementId']) || empty($this->params['announcementId'])) {
            throw new Exception('参数错误');
        }
        //初始化一些数据
        $this->initInfo();
        $this->announcementId = $this->params['announcementId'];
        $this->setOldAnnouncementInfo();
        $this->articleId = $this->oldAnnouncementInfo->article_id;
        $this->setOldArticleInfo();
        //下线  删除 待审核 职位不允许编辑
        if ($this->oldAnnouncementInfo->status == BaseAnnouncement::STATUS_DELETE || $this->oldAnnouncementInfo->status == BaseAnnouncement::STATUS_OFFLINE || $this->oldAnnouncementInfo->audit_status == BaseAnnouncement::STATUS_AUDIT_AWAIT) {
            throw new Exception('职位待审核或者已下线、已删除，不允许编辑');
        }
        //检验数据合法性
        $this->dataVerify(false);
        //验证资源套餐
        $this->companySourcePackageCheck();
        //特殊验证--放dataVerify后面要使用到deliveryType
        $this->specialVerify();
        //设置一下公告是否进入审核状态
        if ($this->params['submitType'] == self::SUBMIT_TYPE_BUTTON_PUBLISH) {
            if ($this->oldAnnouncementInfo->status == BaseAnnouncement::STATUS_STAGING) {
                $this->isAudit = true;
            } else {
                $this->setIsAudit();
            }
        }
        //更新公告信息
        $this->saveTable(false);
        //保存职位信息
        $this->saveJobTable();
        //after
        $this->after();
        //日志
        $this->handleLog();
        $this->log(BaseAnnouncementLog::TYPE_EDIT);

        return [
            'id' => $this->announcementId,
        ];
    }

    /**
     * 处理所有后置逻辑
     * @throws Exception
     */
    private function after()
    {
        $this->updateAnnouncementPiFlag();
        $this->updateAnnouncementMiniAppType();
        $this->updateJobAnnouncementAmount();
        $this->updateAnnouncementBoShiHouTable();
        $this->updateJobAnnouncementRelation();
        $this->updateAnnouncementEstablishment();
        $this->updateStatAnnouncementCount();
        $this->runAutoColumnAfter();
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY && $this->params['submitType'] == self::SUBMIT_TYPE_BUTTON_PUBLISH && $this->isAudit) {
            (new PushEditMessageService())->publish(['announcementId' => $this->announcementId]);
        }
    }

    /**
     * 特殊检验--一般是编辑出现的检验
     * 公告层面
     * @throws Exception
     */
    private function specialVerify()
    {
        if ($this->oldAnnouncementInfo->status == BaseAnnouncement::STATUS_STAGING) {
            return true;
        }
        //判断职位投递性质
        if ($this->oldAnnouncementInfo->delivery_type != $this->params['deliveryType'] && $this->oldAnnouncementInfo->delivery_type > 0) {
            throw new Exception('你编辑使公告投递类型站内外发生变化，导致修改失败');
        }
    }

    /**
     * 原始公告信息
     * @return void
     * @throws Exception
     */
    private function setOldAnnouncementInfo()
    {
        $this->oldAnnouncementInfo = BaseAnnouncement::findOne($this->announcementId);
        if (!$this->oldAnnouncementInfo) {
            throw new Exception('公告信息不存在');
        }
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY && $this->companyInfo->id != $this->oldAnnouncementInfo->company_id) {
            throw new Exception('您的单位不存在该公告数据，无法编辑！');
        }
    }

    /**
     * 原始文章信息
     * @throws Exception
     */
    private function setOldArticleInfo()
    {
        $this->oldArticleInfo = BaseArticle::findOne($this->articleId);
        if (!$this->oldArticleInfo) {
            throw new Exception('公告文章信息不存在');
        }
    }

    /**
     * 公告下职位是否导致公告进入审核流程
     * @throws Exception
     */
    private function setIsAudit()
    {
        $isEditAnnouncement = false;
        $isEditJob          = false;
        $isAddTempJob       = false;
        //        if ($this->oldAnnouncementInfo->status == BaseAnnouncement::STATUS_STAGING && $this->params['submitType'] == self::SUBMIT_TYPE_BUTTON_PUBLISH) {
        //            $this->isAudit = true;
        //        }
        //1、看自身内容是否进入审核流程
        //  公告详情
        if ($this->oldArticleInfo->content != $this->params['content']) {
            $this->isAudit                    = true;
            $isEditAnnouncement               = true;
            $this->isAuditAnnouncementCentent = true;
        }
        //  附件--单位端才需要审核
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            $fileIdsArr    = $this->params['fileIds'] ? explode(',', $this->params['fileIds']) : [];
            $oldFileIdsArr = $this->oldAnnouncementInfo->file_ids ? explode(',',
                $this->oldAnnouncementInfo->file_ids) : [];
            if (count(array_diff($fileIdsArr, $oldFileIdsArr)) > 0) {
                $this->isAudit                    = true;
                $isEditAnnouncement               = true;
                $this->isAuditAnnouncementFileIds = true;
            }
        }

        //2、看新增职位没有
        if ($this->jobTempIds) {
            //有新职位，进入审核流程
            $this->isAudit = true;
            $isAddTempJob  = true;
        }
        //3、有职位是编辑 但是进入审核流程的
        if ($this->jobAndTempIds) {
            //这时候要看下这些职位数据与原职位的一些字段是否有变化
            //$isEditJob = true;
            foreach ($this->jobAndTempData as $jobAndTempItem) {
                if ($jobAndTempItem['id'] <= 0 || $jobAndTempItem['jobId'] <= 0) {
                    throw new Exception('数据好像出错了，请联系开发人员！！！');
                }
                $jobInfo = BaseJob::findOne($jobAndTempItem['jobId']);
                if ($jobInfo->status == BaseJob::STATUS_WAIT) {
                    if (!$this->isAudit) {
                        $this->isAudit = true;
                    }
                    if (!$isAddTempJob) {
                        $isAddTempJob = true;
                    }
                } else {
                    $res = $this->filterAudit($jobAndTempItem['id'], $jobAndTempItem['jobId']);
                    if ($res) {
                        if (!$this->isAudit) {
                            $this->isAudit = true;
                        }
                        if (!$isEditJob) {
                            $isEditJob = true;
                        }
                    }
                }
            }
        }
        //4、正式职位
        if ($this->jobIds && $this->jobData) {
            foreach ($this->jobData as $jobItem) {
                if ($jobItem['status'] == BaseJob::STATUS_WAIT && $jobItem['auditStatus'] != BaseJob::AUDIT_STATUS_WAIT_AUDIT) {
                    if (!$this->isAudit) {
                        $this->isAudit = true;
                    }
                    if (!$isAddTempJob) {
                        $isAddTempJob = true;
                    }
                }
            }
        }
        if ($isEditAnnouncement && $isEditJob && $isAddTempJob) {
            //修改了公告+修改了职位+新增了职位
            $this->editorType = BaseAnnouncement::TYPE_EDITOR_ANNOUNCEMENT_JOB_ADD_JOB;
        } elseif ($isEditJob && $isAddTempJob) {
            //修改了职位+新增了职位
            $this->editorType = BaseAnnouncement::TYPE_EDITOR_ADD_JOB;
        } elseif ($isEditAnnouncement && $isAddTempJob) {
            //修改了公告+新增了职位
            $this->editorType = BaseAnnouncement::TYPE_EDITOR_ANNOUNCEMENT_ADD_JOB;
        } elseif ($isEditAnnouncement && $isEditJob) {
            //修改了公告+修改了职位
            $this->editorType = BaseAnnouncement::TYPE_EDITOR_ANNOUNCEMENT_JOB;
        } elseif ($isAddTempJob) {
            //新增了职位
            $this->editorType = BaseAnnouncement::TYPE_ADD_JOB;
        } elseif ($isEditJob) {
            //修改了职位
            $this->editorType = BaseAnnouncement::TYPE_EDITOR_JOB;
        } elseif ($isEditAnnouncement) {
            //修改了公告
            $this->editorType = BaseAnnouncement::TYPE_EDITOR_ANNOUNCEMENT;
        }
    }

    /**
     * 对于职位编辑的一些字段进行对比进入审核流程
     * @throws Exception
     */
    private function filterAudit($tempJobId, $jobId)
    {
        $tempJobInfo = BaseJobTemp::findOne($tempJobId);
        $jobInfo     = BaseJob::findOne($jobId);
        if (!$tempJobInfo || !$jobInfo) {
            throw new Exception('数据好像出错了，请联系开发人员！！！');
        }
        //编辑前的内容【岗位职责、任职要求、其他说明】
        $oldDuty        = $jobInfo->duty;
        $oldRequirement = $jobInfo->requirement;
        $oldRemark      = $jobInfo->remark;
        //编辑后新的内容
        $newDuty        = $tempJobInfo->duty;
        $newRequirement = $tempJobInfo->requirement;
        $newRemark      = $tempJobInfo->remark;
        //对比内容
        $duty        = $oldDuty != $newDuty ? 1 : 0;
        $requirement = $oldRequirement != $newRequirement ? 1 : 0;
        $remark      = $oldRemark != $newRemark ? 1 : 0;
        //组装一个结构 ['jobId'=>['isAudit'=>true/false,'editContent'=>[],'before'=>[],'after'=>[]],'jobId'=>[],'jobId'=>[]]
        //如果有一项不一样就进入审核流程
        if ($duty || $requirement || $remark) {
            return true;
        }

        return false;
    }

    /**
     * 公告日志
     * @return void
     * @throws \yii\base\Exception
     * @throws \yii\base\NotSupportedException
     */
    private function handleLog()
    {
        $before   = [
            '修改类型' => BaseAnnouncementHandleLog::HANDLE_TYPE_NAME[BaseAnnouncementHandleLog::HANDLE_TYPE_EDIT],
        ];
        $after    = [
            '修改类型' => BaseAnnouncementHandleLog::HANDLE_TYPE_NAME[BaseAnnouncementHandleLog::HANDLE_TYPE_EDIT],
        ];
        $editData = [];
        if ($this->isAuditAnnouncementCentent) {
            $before['公告详情']  = $this->oldArticleInfo->content;
            $after['公告详情']   = $this->params['content'];
            $editData['content'] = $this->params['content'];
        }

        if ($this->isAuditAnnouncementFileIds) {
            $before['职位附件']   = $this->oldAnnouncementInfo->file_ids;
            $after['职位附件']    = $this->params['fileIds'];
            $editData['file_ids'] = $this->params['fileIds'];
        }

        if ($this->isAudit) {
            // 如果之前就有在编辑中的内容,那么是要直接编辑的
            $announcementEditModel                  = BaseAnnouncementEdit::findOne(['announcement_id' => $this->announcementId]) ?: new BaseAnnouncementEdit();
            $announcementEditModel->status          = BaseAnnouncementEdit::STATUS_ONLINE;
            $announcementEditModel->announcement_id = $this->announcementId;
            $announcementEditModel->edit_content    = json_encode($editData);
            $announcementEditModel->editor          = $this->params['username'];
            $announcementEditModel->editor_type     = $this->params['platformType'];
            $announcementEditModel->editor_id       = $this->params['userId'];
            if (!$announcementEditModel->save()) {
                throw new Exception($announcementEditModel->getFirstErrorsMessage());
            }
        }

        $handleLogArr = [
            'add_time'        => CUR_DATETIME,
            'announcement_id' => $this->announcementId,
            'handle_type'     => BaseAnnouncementHandleLog::HANDLE_TYPE_EDIT,
            'editor_type'     => $this->editorType,
            'handler_type'    => $this->params['platformType'],
            'handler_id'      => $this->params['userId'],
            'handler_name'    => $this->params['username'],
            'handle_before'   => json_encode($before),
            'handle_after'    => json_encode($after),
            'ip'              => IpHelper::getIpInt(),
        ];
        BaseAnnouncementHandleLog::createInfo($handleLogArr);
    }

    /**
     * 检查资源
     * @throws Exception
     */
    private function companySourcePackageCheck()
    {
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY) {
            if ($this->params['submitType'] == self::SUBMIT_TYPE_BUTTON_PUBLISH) {
                if ($this->oldAnnouncementInfo->company_id != $this->companyInfo->id) {
                    throw new Exception('公告不属于当前单位');
                }
                if ($this->oldAnnouncementInfo->status == BaseJob::STATUS_WAIT) {
                    $this->baseCheckMemberPackage();
                    if ($this->companyPackageConfigModel->announcement_amount <= 0) {
                        throw new Exception('公告发布数量已达上限');
                    }
                    if ($this->oldAnnouncementInfo->status != BaseAnnouncement::STATUS_ONLINE) {
                        $releaseTime = $this->articleInfo->release_time;
                        // 未曾发布成功过
                        if ($releaseTime && $releaseTime != TimeHelper::ZERO_TIME) {
                            $subDay = TimeHelper::computeDaySub($releaseTime, CUR_DATETIME);

                            if ($subDay <= $this->companyPackageConfigModel->announcement_release_interval_day) {
                                throw new Exception('公告发布必须间隔' . $this->companyPackageConfigModel->announcement_release_interval_day . '天才能发布');
                            }
                        }
                    }
                    $companyPackageApplication = new CompanyPackageApplication();
                    $handleType                = BaseCompanyPackageChangeLog::HANDLE_TYPE_ANNOUNCEMENT_RELEASE;
                    $remark                    = BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[$handleType];
                    $companyPackageApplication->announcementCreate($this->companyInfo->id, 1, $remark);
                }
            }
        }
    }
}