<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseCompanyPackageChangeLog;
use common\base\models\BaseJob;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseJobLog;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseMajor;
use common\helpers\IpHelper;
use common\helpers\TimeHelper;
use common\service\company\PushEditMessageService;
use common\service\companyPackage\CompanyPackageApplication;
use common\service\v2\announcement\AfterService;
use queue\Producer;
use yii\base\Exception;
use Yii;

/**
 * 职位再发布
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class RepublishService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    public function setJobId($jobId)
    {
        $this->jobId = $jobId;
    }

    /**
     * 检查会员套餐
     * @throws Exception|void
     */
    private function checkMemberPackage()
    {
        // 职位过来只需要扣公告那边，职位无需操作
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY && $this->jobInfo->announcement_id <= 0) {
            $this->baseCheckMemberPackage();

            if ($this->companyPackageConfigModel->job_amount <= 0) {
                throw new Exception('职位数量已达上限');
            }
            // 再次发布职位
            if (TimeHelper::reduceDates(CUR_DATETIME,
                    $this->jobInfo->refresh_time) < $this->companyPackageConfigModel->job_release_interval_day) {
                throw new Exception('职位距离上次提交发布还未满：' . $this->companyPackageConfigModel->job_release_interval_day . '天');
            }
        }
    }

    /**
     * 职位再发布、批量再发布
     * @throws Exception
     */
    public function run()
    {
        if (!$this->jobId) {
            $id = Yii::$app->request->post('jobId');
        } else {
            $id = $this->jobId;
        }
        if (empty($id)) {
            throw new Exception('参数错误');
        }
        $this->initInfo();
        $ids = explode(',', $id);
        $msg = '';
        if (count($ids) > 0 && $this->isBatch) {
            //批量
            foreach ($ids as $idItem) {
                $itemRes = $this->republishOne($idItem);
                if ($itemRes !== true) {
                    $msg .= $itemRes;
                }
            }
        } else {
            //单个
            $msg = $this->republishOne($id);
        }
        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY && count($ids) > 0) {
            foreach ($ids as $idItem2) {
                (new PushEditMessageService())->republish(['jobId' => $idItem2]);
            }
        }

        return $msg ?: true;
    }

    /**
     * 单条再发布
     * @param $id
     * @throws Exception
     */
    private function republishOne($id)
    {
        $this->setJob($id);

        $this->checkMemberPackage();

        if ($this->jobInfo->status != BaseJob::STATUS_OFFLINE || $this->jobInfo->is_show != BaseJob::IS_SHOW_YES) {
            if ($this->isBatch) {
                return '职位ID：' . $this->jobInfo->uuid . ',非下线状态或者隐藏，不允许操作再发布<br />';
            } else {
                throw new Exception('当前职位是非下线状态或者隐藏，不允许操作再发布！');
            }
        }
        //初始化一写数据
        $this->jobId   = $id;
        $oldPeriodDate = $this->jobInfo->period_date;
        $oldStatus     = $this->jobInfo->status;
        $this->setCompany($this->jobInfo->company_id);
        //此处关联公告，若公告为在线状态，再发布成功后，职位即变成”在线“状态；若公告下线，提示公告要先上线，
        //才能再发布职位；若公告处于待发布状态，提示公告需要审核通过后，才能再发布职位；
        if ($this->jobInfo->announcement_id > 0) {
            $announcementInfo = BaseAnnouncement::findOne($this->jobInfo->announcement_id);
            if ($announcementInfo->status != BaseAnnouncement::STATUS_ONLINE) {
                if ($this->isBatch) {
                    return '职位ID：' . $this->jobInfo->uuid . ',职位关联的公告ID：' . $announcementInfo->uuid . '需先上线<br />';
                } else {
                    throw new Exception('职位关联的公告需先上线');
                }
            }
        }
        $periodDate                  = Date("Y-m-d H-i-s", (strtotime('+' . BaseJob::ADD_RELEASE_AGAIN_DAY . ' day')));
        $this->jobInfo->status       = BaseJob::STATUS_ONLINE;
        $this->jobInfo->period_date  = TimeHelper::ZERO_TIME;
        $this->jobInfo->refresh_time = CUR_DATETIME;
        $this->jobInfo->refresh_date = CUR_DATE;
        //        $jobModel->audit_status = BaseJob::AUDIT_STATUS_PASS_AUDIT;
        $this->jobInfo->offline_type = 0;
        $this->jobInfo->offline_time = TimeHelper::ZERO_TIME;
        if (!$this->jobInfo->save()) {
            throw new Exception('再发布失败' . $this->jobInfo->getFirstErrorsMessage());
        }
        //记录操作日志
        BaseJobHandleLog::createInfo([
            'add_time'        => date('Y-m-d H:i:s'),
            'job_id'          => $id,
            'handle_type'     => BaseJobHandleLog::HANDLE_TYPE_RELEASE_AGAIN,
            'handler_type'    => $this->params['platformType'],
            'handler_id'      => $this->params['userId'],
            'handler_name'    => $this->params['username'],
            'handle_before'   => json_encode([
                '职位有效期' => $oldPeriodDate,
                '职位状态'   => BaseJob::JOB_STATUS_NAME[$oldStatus],
            ]),
            'handle_after'    => json_encode([
                '职位有效期' => $periodDate,
                '职位状态'   => BaseJob::JOB_STATUS_NAME[BaseJob::STATUS_ONLINE],
            ]),
            'ip'              => IpHelper::getIpInt(),
            'announcement_id' => $this->oldJobInfo->announcement_id ?: 0,
        ]);
        //写一下日志
        $this->log($this->isBatch ? BaseJobLog::TYPE_REPUBLISH_BATCH : BaseJobLog::TYPE_REPUBLISH);
        $result = $this->majorMessageNotice();

        if ($this->operationPlatform == self::PLATFORM_WEB_COMPANY && $this->jobInfo->announcement_id <= 0) {
            $companyPackageApplication = new CompanyPackageApplication();
            $handleType                = BaseCompanyPackageChangeLog::HANDLE_TYPE_JOB_RELEASE;
            $remark                    = BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[$handleType];
            // 职位过来只需要扣公告那边，职位无需操作
            $companyPackageApplication->jobRelease($this->companyInfo->id, 1, $remark);
        }

        //后置处理
        $this->after();

        return $result;
    }

    /**
     * 后置处理
     * @return void
     */
    private function after()
    {
        $this->updateStatInfo();
        $this->runAutoColumnAfter();
        if ($this->jobInfo->announcement_id) {
            (new AfterService())->setPlatform($this->operationPlatform)
                ->run($this->jobInfo->announcement_id);
        }
    }

    /**
     * 隐藏专业提醒
     * @return string|true
     */
    private function majorMessageNotice()
    {
        $majorId = BaseMajor::HIDE_MAJOR_ID;
        if (BaseJobMajorRelation::find()
            ->where([
                'job_id'   => $this->jobId,
                'major_id' => $majorId,
            ])
            ->exists()) {
            $this->updateJobMajorRelationTable();

            return '该职位关联学科【电子信息】已不存在,职位ID:' . $this->jobInfo->uuid;
        }

        return true;
    }
}