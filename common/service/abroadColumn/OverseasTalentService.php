<?php

namespace common\service\abroadColumn;

use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseHwActivity;
use common\base\models\BaseHwActivitySession;
use common\base\models\BaseHwActivitySessionArea;
use common\base\models\BaseNews;
use common\helpers\FileHelper;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use haiWai\models\Seo;
use yii\helpers\ArrayHelper;

class OverseasTalentService extends BaseService
{
    public $activityLimit = 10;
    public $searchData;

    /**
     * @throws \yii\base\Exception
     */
    public function getAll($isUpdateCache = false)
    {
        if (!$isUpdateCache) {
            $data = $this->getCache(self::CACHE_KEY_CHYINCAI);

            if ($data) {
                return $data;
            }
        }

        $banner             = $this->getBanner();
        $topShowcaseList    = $this->getTopShowcaseList();
        $bottomShowcaseList = $this->getBottomShowCase();
        $moreActivityList   = $this->getMoreActivityList();
        $newsList           = $this->getNewsList();
        $searchParams       = $this->getSearchParamsList();

        $data = [
            'banner'             => $banner,
            'topShowcaseList'    => $topShowcaseList,
            'bottomShowcaseList' => $bottomShowcaseList,
            'moreActivityList'   => $moreActivityList,
            'newsList'           => $newsList,
            'searchParams'       => $searchParams,
        ];

        $this->setCache(self::CACHE_KEY_CHYINCAI, $data);

        return $data;
    }

    private function getBanner()
    {
        $key = 'hw_chuhaiyincai_HF';

        return $this->getCommonShowcaseList($key);
    }

    /**
     * 广告位-上2
     * @return array
     */
    private function getTopShowcaseList()
    {
        $key = 'hw_chuhaiyincai_A1';

        return $this->getCommonShowcaseList($key, 2);
    }

    /**
     * 广告位-下4
     * @return array
     */
    private function getBottomShowCase()
    {
        $key = 'hw_chuhaiyincai_B1';

        return $this->getCommonShowcaseList($key);
    }

    private function getSearchParamsList()
    {
        return Seo::getSeoUrl([], Seo::TYPE_CHUHAI)['searchList'];
    }

    /**
     * 获取活动列表
     * @return array
     * @throws \yii\base\Exception
     */
    public function getActivityList($params)
    {
        $typeList    = BaseHwActivity::SERIES_TYPE_RELATION_LIST[BaseHwActivity::SERIES_OVERSEAS_RECRUITMENT];
        $searchModel = BaseHwActivity::find()
            ->alias('a')
            ->where([
                'a.grounding_status' => BaseHwActivity::GROUNDING_STATUS_ON,
                'a.type'             => $typeList,
                'a.status'           => BaseHwActivity::STATUS_ACTIVE,
            ]);
        //地区查询
        if ($params['areaId']) {
            $areaInfo = BaseArea::findOne($params['areaId']);
            if (($areaInfo->level == 1 && $areaInfo->is_china = BaseArea::IS_CHINA_YES) || ($areaInfo->level == 3 && $areaInfo->is_china = BaseArea::IS_CHINA_NO)) {
                $params['areaId'] = BaseArea::find()
                    ->select('id')
                    ->where([
                        'parent_id' => $params['areaId'],
                        'status'    => BaseArea::STATUS_ACTIVE,
                    ])
                    ->column();
            }
            $searchModel->innerJoin(['sa' => BaseHwActivitySessionArea::tableName()], 'sa.activity_id = a.id')
                ->andWhere(['sa.status' => BaseHwActivitySessionArea::STATUS_ACTIVE])
                ->andWhere(['sa.area_id' => $params['areaId']]);
        }
        //类型查询
        $searchModel->andFilterWhere(['a.type' => $params['activityType']]);
        //活动时间查询
        if ($params['activityTime']) {
            $timeList            = $this->getActivityTimeRangeByType($params['activityTime']);
            $params['startTime'] = $timeList['startTime'];
            $params['endTime']   = $timeList['endTime'];
        }
        if ($params['activityCustomTime'] && !isset($params['startTime']) && empty($params['startTime'])) {
            //2024-08,2025-06
            $activityCustomTimeArr = explode(',', $params['activityCustomTime']);
            $startTime             = $activityCustomTimeArr[0] . '-01 00:00:00';
            $endTime               = $activityCustomTimeArr[1] . '-01 00:00:00';
            $maxDay                = date('t', strtotime($endTime));
            $endTime               = $activityCustomTimeArr[1] . '-' . $maxDay . ' 23:59:59';
            $params['startTime']   = $startTime;
            $params['endTime']     = $endTime;
        }
        if ($params['startTime'] && $params['endTime']) {
            //可能是类型获取的，也可能是自定义的
            $searchModel->andWhere([
                'or',
                [
                    'between',
                    'a.activity_start_date',
                    $params['startTime'],
                    $params['endTime'],
                ],
                [
                    'between',
                    'a.activity_end_date',
                    $params['startTime'],
                    $params['endTime'],
                ],
                [
                    'and',
                    [
                        '<=',
                        'a.activity_start_date',
                        $params['startTime'],
                    ],
                    [
                        '>=',
                        'a.activity_end_date',
                        $params['endTime'],
                    ],
                ],
            ]);
            //            [
            //                '<=',
            //                'activity_start_date',
            //                $params['endTime'],
            //            ])
            //                ->andWhere([
            //                '>=',
            //                'activity_end_date',
            //                $params['startTime'],
            //            ])
        }
        //活动状态查询
        $searchModel->andFilterWhere(['a.activity_status' => $params['activityStatus']]);
        $searchModel->groupBy('a.id');
        //获取总数量
        $count = $searchModel->count();

        $pages = BaseHwActivity::setPage($count, $params['page'] ?: 1, $this->activityLimit);

        $list = $searchModel->select([
            'a.id',
            'a.name',
            'a.type',
            'a.introduce as description',
            'a.activity_start_date',
            'a.activity_end_date',
            'a.detail_url as url',
            'a.main_img_file_id',
            'a.activity_status',
            'a.review_img_file_ids',
            'a.is_outside_url',
            'a.activity_child_status',
            'a.sign_up_url',

        ])
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('a.activity_status asc,a.sort desc,a.activity_start_date desc,a.id desc')
            ->asArray()
            ->all();

        if (!$list) {
            $list = [];
        } else {
            foreach ($list as &$item) {
                $item['logo']      = BaseHwActivity::getLogo($item['id']);
                $item['imageInfo'] = [];

                if ($item['activity_status'] == BaseHwActivity::ACTIVITY_STATUS_PROGRESS) {
                    $item['imageInfo']['isMain'] = 1;
                    $item['imageInfo']['tag']    = '';
                    $item['imageInfo']['list']   = [FileHelper::getFullPathById($item['main_img_file_id'])];
                } elseif ($item['activity_status'] == BaseHwActivity::ACTIVITY_STATUS_END) {
                    $reviewImgFileList = json_decode($item['review_img_file_ids'], true);

                    // 如果有活动图集就显示，没有就是主图
                    if ($reviewImgFileList) {
                        $item['imageInfo']['isMain'] = 2;
                        $item['imageInfo']['tag']    = '活动图集';

                        $fileIdList                = array_column($reviewImgFileList, 'fileId');
                        $item['imageInfo']['list'] = [];
                        foreach ($fileIdList as $reviewImgFileId) {
                            $item['imageInfo']['list'][] = FileHelper::getFullPathById($reviewImgFileId);
                        }
                    } else {
                        $item['imageInfo']['isMain'] = 1;
                        $item['imageInfo']['tag']    = '';
                        $item['imageInfo']['list']   = [FileHelper::getFullPathById($item['main_img_file_id'])];
                    }
                }
                $item['tag'] = BaseHwActivity::TYPE_TEXT_LIST[$item['type']];
                //获取时间类型文案
                $timeType     = BaseHwActivity::getTimeTypeText($item['id']);
                $item['date'] = BaseHwActivity::getActivityDate($item['id']);
                if ($timeType) {
                    $item['date'] .= '(' . $timeType . ')';
                }
                $item['address']     = BaseHwActivity::getAddressBySeries($item['id']);
                $item['signEndDate'] = BaseHwActivity::getSignEndDate($item['id']);
                $item['btnDisabled'] = $item['activity_child_status'] == BaseHwActivity::ACTIVITY_CHILD_STATUS_END;
                $item['btnText']     = BaseHwActivity::ACTIVITY_BTN_TEXT_LIST[$item['activity_child_status']];
                if ($item['is_outside_url'] == BaseHwActivity::IS_OUTSIDE_URL_YES) {
                    $item['rel'] = 'nofollow';
                } else {
                    $item['rel'] = '';
                }
                //场次列表
                $item['sessionList'] = BaseHwActivitySession::getSessionInfoList($item['id']);
            }
        }

        return [
            'list'  => $list,
            'limit' => $pages['limit'],
            'page'  => $pages['page'],
            'count' => (int)$count,
        ];
    }

    /**
     * 获取更多活动列表
     * @return array
     */
    private function getMoreActivityList()
    {
        $typeList = BaseHwActivity::SERIES_TYPE_RELATION_LIST[BaseHwActivity::SERIES_MORE_ACTIVITY];
        $list     = BaseHwActivity::find()
            ->where([
                'status'           => BaseHwActivity::STATUS_ACTIVE,
                'grounding_status' => BaseHwActivity::GROUNDING_STATUS_ON,
                'type'             => $typeList,
            ])
            ->select([
                'id',
                'name',
                'activity_start_date',
                'activity_end_date',
                'detail_url as url',
                'is_outside_url',
                'add_time',
            ])
            ->limit(18)
            ->orderBy('activity_status asc,sort desc,activity_start_date desc,id desc')
            ->asArray()
            ->all();

        if (!$list) {
            return [];
        } else {
            $newList = [];
            // year  monthDay  day title
            foreach ($list as $k => &$item) {
                $item['refreshDate'] = date('m.d', strtotime($item['add_time']));
                $item['refreshYear'] = date('Y', strtotime($item['add_time']));
                $item['date']        = BaseHwActivity::getActivityDate($item['id']);
                $item['address']     = BaseHwActivity::getAllAddressBySeries($item['id']);
                if ($item['is_outside_url'] == BaseHwActivity::IS_OUTSIDE_URL_YES) {
                    $item['rel'] = 'nofollow';
                } else {
                    $item['rel'] = '';
                }
                $pageKey = floor($k / 6);

                // 年 月 日 标题
                $item['year']     = $item['refreshYear'];
                $item['monthDay'] = $item['refreshDate'];
                $item['day']      = $item['date'];
                $item['title']    = $item['name'];

                $newList[$pageKey][] = $item;
            }
        }

        return $newList;
    }

    //获取资讯列表
    private function getNewsList()
    {
        $list     = BaseArticle::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticleAttribute::tableName()], 'a.id=b.article_id')
            ->innerJoin(['c' => BaseNews::tableName()], 'c.article_id=a.id')
            ->where([
                'a.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.type'      => BaseArticleAttribute::ATT_ABROAD_ACTIVITY,
                'a.status'    => BaseArticle::STATUS_ONLINE,
                'a.is_show'   => BaseArticle::IS_SHOW_YES,
                'a.type'      => BaseArticle::TYPE_NEWS,

            ])
            ->select([
                'title',
                'cover_thumb',
                'seo_description',
                'c.id',
                'a.refresh_date',
                'a.link_url',
            ])
            ->orderBy('a.refresh_time desc')
            ->limit(10)
            ->asArray()
            ->all();
        $pageList = [];
        $topNews  = [];
        foreach ($list as $k => &$item) {
            $item['url']          = $item['link_url'] ?: UrlHelper::createPcNewsDetailPath($item['id']);
            $item['cover_thumb']  = FileHelper::getFullUrl($item['cover_thumb']);
            $item['refresh_date'] = TimeHelper::formatDateByYear($item['refresh_date'], '.');
            if ($k == 0) {
                //第一个需要固定置顶
                $item['isTop'] = true;
                $topNews       = $item;
            } else {
                $newKey               = $k - 1;
                $item['isTop']        = false;
                $pageKey              = floor($newKey / 3);
                $pageList[$pageKey][] = $item;
            }
        }

        return [
            'topNews'  => $topNews,
            'pageList' => $pageList,
        ];
    }

}
