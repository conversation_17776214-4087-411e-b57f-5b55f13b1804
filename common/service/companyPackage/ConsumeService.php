<?php

namespace common\service\companyPackage;

use common\base\models\BaseAdmin;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyPackageChangeLog;
use common\base\models\BaseCompanyPackageSystemConfig;
use common\base\models\BaseMember;
use Yii;
use yii\base\Exception;

class ConsumeService extends BaseService
{
    // 新增操作类型
    const HANDLE_TYPE_JOB_RELEASE                   = BaseCompanyPackageChangeLog::HANDLE_TYPE_JOB_RELEASE;
    const HANDLE_TYPE_ANNOUNCEMENT_RELEASE          = BaseCompanyPackageChangeLog::HANDLE_TYPE_ANNOUNCEMENT_RELEASE;
    const HANDLE_TYPE_JOB_REFUSE                    = BaseCompanyPackageChangeLog::HANDLE_TYPE_JOB_REFUSE;
    const HANDLE_TYPE_ANNOUNCEMENT_REFUSE           = BaseCompanyPackageChangeLog::HANDLE_TYPE_ANNOUNCEMENT_REFUSE;
    const HANDLE_TYPE_JOB_REFRESH                   = BaseCompanyPackageChangeLog::HANDLE_TYPE_JOB_REFRESH;
    const HANDLE_TYPE_ANNOUNCEMENT_REFRESH          = BaseCompanyPackageChangeLog::HANDLE_TYPE_ANNOUNCEMENT_REFRESH;
    const HANDLE_TYPE_RESUME_DOWNLOAD               = BaseCompanyPackageChangeLog::HANDLE_TYPE_RESUME_DOWNLOAD;
    const HANDLE_TYPE_SMS_SEND                      = BaseCompanyPackageChangeLog::HANDLE_TYPE_SMS_SEND;
    const HANDLE_TYPE_SMS_SEND_FAIL                 = BaseCompanyPackageChangeLog::HANDLE_TYPE_SMS_SEND_FAIL;
    const HANDLE_TYPE_EXPIRATION                    = BaseCompanyPackageChangeLog::HANDLE_TYPE_EXPIRATION;
    const HANDLE_TYPE_SYSTEM_ADD_RESUME_DOWNLOAD    = BaseCompanyPackageChangeLog::HANDLE_TYPE_SYSTEM_ADD_RESUME_DOWNLOAD;
    const HANDLE_TYPE_SYSTEM_REDUCE_RESUME_DOWNLOAD = BaseCompanyPackageChangeLog::HANDLE_TYPE_SYSTEM_REDUCE_RESUME_DOWNLOAD;
    const HANDLE_TYPE_SYSTEM_CHANGE_RESUME_CHAT     = BaseCompanyPackageChangeLog::HANDLE_TYPE_SYSTEM_CHANGE_RESUME_CHAT;
    const HANDLE_TYPE_INITIATE_CHAT                 = BaseCompanyPackageChangeLog::HANDLE_TYPE_INITIATE_CHAT;
    const HANDLE_TYPE_SYSTEM_SET_SMS_AMOUNT         = BaseCompanyPackageChangeLog::HANDLE_TYPE_SYSTEM_SET_SMS_AMOUNT;

    const ALGORITHM_ADD = 1; //添加套餐
    const ALGORITHM_CUT = 2; //减少套餐

    private   $handleType;
    private   $num;
    private   $remark;
    private   $algorithm;
    protected $companyPackageChangeLogId;
    public    $adminId;

    /**
     * @throws Exception
     * @throws \yii\db\Exception
     */
    public function run($companyId, $handleType, $num, $remark, $algorithm = self::ALGORITHM_CUT)
    {
        $this->handleType = $handleType;
        $this->num        = $num;
        $this->remark     = $remark;
        $this->algorithm  = $algorithm;
        $this->setCompany($companyId);
        $this->setPackage();
        $this->create();

        return $this->companyPackageChangeLogId;
    }

    /**
     * @throws Exception
     */
    public function check()
    {
        switch ($this->handleType) {
            case self::HANDLE_TYPE_JOB_RELEASE:
                if ($this->companyPackageConfigModel->job_amount - $this->num < 0) {
                    throw new Exception('职位数量不足');
                }

                return true;
            case self::HANDLE_TYPE_ANNOUNCEMENT_RELEASE:
                if ($this->companyPackageConfigModel->announcement_amount - $this->num < 0) {
                    throw new Exception('公告数量不足');
                }

                return true;
            case self::HANDLE_TYPE_JOB_REFRESH:
                if ($this->companyPackageConfigModel->job_refresh_amount - $this->num < 0) {
                    throw new Exception('职位刷新数量不足');
                }

                return true;
            case self::HANDLE_TYPE_ANNOUNCEMENT_REFRESH:
                if ($this->companyPackageConfigModel->announcement_refresh_amount - $this->num < 0) {
                    throw new Exception('公告刷新数量不足');
                }

                return true;
            case self::HANDLE_TYPE_RESUME_DOWNLOAD:
            case self::HANDLE_TYPE_SYSTEM_REDUCE_RESUME_DOWNLOAD:
                if ($this->companyPackageConfigModel->resume_download_amount - $this->num < 0) {
                    throw new Exception('简历下载数量不足');
                }

                return true;
            case self::HANDLE_TYPE_SYSTEM_ADD_RESUME_DOWNLOAD:
                // 系统管理员对点数进行加点,
                if ($this->companyPackageConfigModel->resume_download_amount + $this->num > 2000) {
                    throw new Exception('下载点数最多为2000点');
                }

                return true;
            case self::HANDLE_TYPE_INITIATE_CHAT:
                if ($this->companyPackageConfigModel->chat_amount - $this->num < 0) {
                    throw new Exception('直聊点数不足');
                }

                return true;
        }
    }

    /**
     * @throws Exception
     * @throws \Exception
     */
    public function create()
    {
        $key            = '';
        $type           = 0;
        $oldAmount      = 0;
        $oldCycleAmount = 0;

        switch ($this->handleType) {
            case self::HANDLE_TYPE_JOB_RELEASE:
            case self::HANDLE_TYPE_JOB_REFUSE:
                $oldAmount = $this->companyPackageConfigModel->job_amount;
                $type      = BaseCompanyPackageChangeLog::TYPE_JOB_RELEASE;
                $key       = 'job_amount';
                break;
            case self::HANDLE_TYPE_ANNOUNCEMENT_RELEASE:
            case self::HANDLE_TYPE_ANNOUNCEMENT_REFUSE:
                $oldAmount = $this->companyPackageConfigModel->announcement_amount;
                $key       = 'announcement_amount';
                $type      = BaseCompanyPackageChangeLog::TYPE_ANNOUNCEMENT_RELEASE;
                break;
            case self::HANDLE_TYPE_JOB_REFRESH:
                $oldAmount = $this->companyPackageConfigModel->job_refresh_amount;
                $type      = BaseCompanyPackageChangeLog::TYPE_JOB_REFRESH;
                $key       = 'job_refresh_amount';
                break;
            case self::HANDLE_TYPE_ANNOUNCEMENT_REFRESH:
                $oldAmount = $this->companyPackageConfigModel->announcement_refresh_amount;
                $key       = 'announcement_refresh_amount';
                $type      = BaseCompanyPackageChangeLog::TYPE_ANNOUNCEMENT_REFRESH;
                break;
            case self::HANDLE_TYPE_RESUME_DOWNLOAD:
                $oldAmount = $this->companyPackageConfigModel->resume_download_amount;
                $key       = 'resume_download_amount';
                $type      = BaseCompanyPackageChangeLog::TYPE_RESUME_DOWN;
                break;
            case self::HANDLE_TYPE_SYSTEM_REDUCE_RESUME_DOWNLOAD:
            case self::HANDLE_TYPE_SYSTEM_ADD_RESUME_DOWNLOAD:
                $oldAmount      = $this->companyPackageConfigModel->resume_download_amount;
                $oldCycleAmount = $this->companyPackageConfigModel->cycle_resume_download_amount;
                $key            = 'resume_download_amount';
                $type           = BaseCompanyPackageChangeLog::TYPE_RESUME_DOWN;
                break;
            case self::HANDLE_TYPE_SYSTEM_CHANGE_RESUME_CHAT:
            case self::HANDLE_TYPE_INITIATE_CHAT:
                $oldAmount      = $this->companyPackageConfigModel->chat_amount;
                $oldCycleAmount = $this->companyPackageConfigModel->cycle_chat_amount;
                $key            = 'chat_amount';
                $type           = BaseCompanyPackageChangeLog::TYPE_CHAT_ACCOUNT;
                break;
            case self::HANDLE_TYPE_SYSTEM_SET_SMS_AMOUNT:
            case self::HANDLE_TYPE_SMS_SEND:
                // 后台将新增统一配置为系统配置短信
                $oldAmount      = $this->companyPackageConfigModel->sms_amount; // 剩余数量
                $oldCycleAmount = $this->companyPackageConfigModel->cycle_sms_amount;  // 总量
                $key            = 'sms_amount';
                $type           = BaseCompanyPackageChangeLog::TYPE_SMS_SEND;
                break;
        }

        if ($this->algorithm == self::ALGORITHM_CUT) {
            $surplus = $oldAmount - $this->num;

            if ($surplus < 0) {
                throw new \Exception('您好，您当前操作资源不足');
            }
            $handleId = Yii::$app->user->id;
            $handler  = BaseCompanyMemberInfo::findOneVal(['member_id' => $handleId], 'contact');
        } else {
            $surplus  = $oldAmount + $this->num;
            $handleId = Yii::$app->user->id;
            $handler  = BaseAdmin::findOneVal(['id' => $handleId], 'name');
        }

        // 更新公司套餐配置
        $this->companyPackageConfigModel->setAttribute($key, $surplus);
        if (in_array($this->handleType, [
            self::HANDLE_TYPE_SYSTEM_ADD_RESUME_DOWNLOAD,
            self::HANDLE_TYPE_SYSTEM_REDUCE_RESUME_DOWNLOAD,
            self::HANDLE_TYPE_SYSTEM_CHANGE_RESUME_CHAT,
            self::HANDLE_TYPE_SYSTEM_SET_SMS_AMOUNT,
        ])) {
            // 这个幺重新配置，如果是后台编辑
            $handleId = Yii::$app->user->id;
            $handler  = BaseAdmin::findOneVal(['id' => $handleId], 'name');

            if ($this->algorithm == self::ALGORITHM_CUT) {
                $cycleSurplus = $oldCycleAmount - $this->num;
            } else {
                $cycleSurplus = $oldCycleAmount + $this->num;
            }
            $this->companyPackageConfigModel->setAttribute('cycle_' . $key, $cycleSurplus);
        }
        $this->companyPackageConfigModel->save();

        $member = BaseMember::findOne($this->companyModel->member_id);

        $packageConfigName = BaseCompanyPackageSystemConfig::PACKAGE_CONFIG_NAME;

        $packageSurplus = [
            $packageConfigName['job_amount']                  => $this->companyPackageConfigModel->job_amount,
            $packageConfigName['announcement_amount']         => $this->companyPackageConfigModel->announcement_amount,
            $packageConfigName['job_refresh_amount']          => $this->companyPackageConfigModel->job_refresh_amount,
            $packageConfigName['announcement_refresh_amount'] => $this->companyPackageConfigModel->announcement_refresh_amount,
            $packageConfigName['resume_download_amount']      => $this->companyPackageConfigModel->resume_download_amount,
            $packageConfigName['chat_amount']                 => $this->companyPackageConfigModel->chat_amount,
            $packageConfigName['sms_amount']                  => $this->companyPackageConfigModel->sms_amount,
        ];
        $packageSurplus = json_encode($packageSurplus);

        $data = [
            'type'            => $type,
            'identify'        => $this->algorithm,
            'change_amount'   => $this->num,
            'surplus'         => $surplus,
            'package_surplus' => $packageSurplus,
            'member_id'       => $this->companyModel->member_id,
            'member_name'     => $member->username ?: '',
            'company_id'      => $this->companyModel->id ?: '',
            'company_name'    => $this->companyModel->full_name ?: '',
            'handle_before'   => $oldAmount . '',
            'handle_after'    => $surplus . '',
            'handler_id'      => $handleId,
            'handler'         => $handler ?: '',
            'content'         => BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[$this->handleType],
            'remark'          => $this->remark,
            'handle_type'     => $this->handleType,
        ];

        $this->companyPackageChangeLogId = BaseCompanyPackageChangeLog::createCompanyPackageChangeLog($data);
    }

    /**
     * 服务到期
     * @throws Exception
     * @throws \yii\db\Exception
     */
    public function serviceExpiration($companyId, $handleType, $adminId = '')
    {
        $this->handleType = $handleType;
        $this->remark     = BaseCompanyPackageChangeLog::HANDLER_TYPE_NAME[$handleType];
        $this->algorithm  = self::ALGORITHM_CUT;
        $this->adminId    = $adminId;
        $this->setCompany($companyId);
        //这里做个校验
        if ($this->companyModel->package_type == BaseCompany::PACKAGE_TYPE_OVER) {
            return '套餐已置空';
        }
        $this->setPackage(false);
        //到期套餐置空，简历下载点数，直聊点数，短信条数
        $this->expirationConfig();
        //到期套餐记录
        $this->expirationChangeLog();
        //这里更改单位会员类型
        $this->expirationCompany();
    }

    /**
     * 服务到期
     * 套餐置空
     * @throws Exception
     * @throws \Exception
     */
    public function expirationConfig()
    {
        //单位套餐置空
        $relevantList = BaseCompanyPackageChangeLog::RELEVANT_TYPE_LIST;
        $configData   = [];
        foreach ($relevantList as $v) {
            $configData[$v['field']] = 0;
        }
        $configData['cycle_resume_download_amount'] = 0; // 当前周期简历下载点数
        $configData['cycle_chat_amount']            = 0; // 当前周期直聊沟通点数
        $configData['cycle_sms_amount']             = 0; // 当前周期短信条数

        $this->changPackageConfig($configData, $this->companyModel->id);
    }

    /**
     * 服务到期
     * 套餐置空记录日志
     * @throws Exception
     * @throws \Exception
     */
    public function expirationChangeLog()
    {
        //单位套餐置空
        $relevantList      = BaseCompanyPackageChangeLog::RELEVANT_TYPE_LIST;
        $packageConfigName = BaseCompanyPackageSystemConfig::PACKAGE_CONFIG_NAME;
        $packageSurplus    = [
            $packageConfigName['job_amount']                  => 0,
            $packageConfigName['announcement_amount']         => 0,
            $packageConfigName['job_refresh_amount']          => 0,
            $packageConfigName['announcement_refresh_amount'] => 0,
            $packageConfigName['resume_download_amount']      => 0,
            $packageConfigName['sms_amount']                  => 0,
        ];
        $packageSurplus    = json_encode($packageSurplus);
        $member            = BaseMember::findOne($this->companyModel->member_id);
        if ($this->adminId) {
            $handlerType = BaseCompanyPackageChangeLog::HANDLER_TYPE_PLATFORM;
            $handlerId   = $this->adminId;
            $handler     = BaseAdmin::findOneVal(['id' => $this->adminId], 'name');
        } else {
            $handlerType = BaseCompanyPackageChangeLog::HANDLER_TYPE_PERSON;
            $handlerId   = $this->companyModel->member_id;
            $handler     = $this->companyModel->full_name ?: '';
        }
        $data = [
            'identify'        => BaseCompanyPackageChangeLog::IDENTIFY_REDUCE,
            'surplus'         => 0,
            'package_surplus' => $packageSurplus,
            'member_id'       => $this->companyModel->member_id,
            'member_name'     => $member->username ?: '',
            'company_id'      => $this->companyModel->id ?: '',
            'company_name'    => $this->companyModel->full_name ?: '',
            'handle_after'    => '0',
            'handler_type'    => $handlerType,
            'handler_id'      => $handlerId,
            'handler'         => $handler ?: '',
            'content'         => $this->remark,
            'remark'          => $this->remark,
            'handle_type'     => $this->handleType,
            'type'            => '',
            'change_amount'   => 0,
            'handle_before'   => '',
        ];
        foreach ($relevantList as $v) {
            $oldAmount             = $this->companyPackageConfigModel[$v['field']];
            $data['type']          = $v['type'];
            $data['change_amount'] = $oldAmount . '';
            $data['handle_before'] = $oldAmount . '';
            BaseCompanyPackageChangeLog::createCompanyPackageChangeLog($data);
        }
    }

    /**
     * 服务到期
     * 套餐置空
     * @throws Exception
     * @throws \Exception
     */
    public function expirationCompany()
    {
        //单位会员类型
        try {
            $this->companyModel->package_type = BaseCompany::PACKAGE_TYPE_OVER;
            $this->companyModel->save();
            // 同时应该更新单位的分
            BaseCompany::updateSort($this->companyModel->id);
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}
