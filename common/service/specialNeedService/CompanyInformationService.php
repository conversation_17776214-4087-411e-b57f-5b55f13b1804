<?php

namespace common\service\specialNeedService;

use common\base\models\BaseJob;
use common\base\models\BaseSpecialNeedConfig;

/**
 * 单位信息特殊需求处理服务
 * 重构后支持数据库配置，向后兼容原有硬编码逻辑
 */
class CompanyInformationService extends BaseService
{
    public function handelCompanyDetail($data, $companyId)
    {
        // 优先使用新的配置系统
        $data = $this->applyCompanyConfigs($data, $companyId);

        // 向后兼容：如果新系统未启用或无配置，使用原有逻辑
        if (!$this->isOpen) {
            return $data;
        }

        if ($companyId == $this->config['944']['companyId']) {
            // 正式进入逻辑，拿专业信息
            switch (PLATFORM) {
                case 'H5':
                    break;
                case 'PC':
                    foreach ($data['announcementHotList'] as &$datum) {
                        $datum['recruitAmount'] = '3~5';
                    }
                    $newJobHotList1 = [];
                    $newJobHotList2 = [];
                    // 排序和 amount都要修改
                    $jobRanking = $this->config['944']['jobRanking'];

                    foreach ($jobRanking as $jobId) {
                        foreach ($data['jobHotList'] as $item) {
                            if ($item['jobId'] == $jobId && $item['status'] == BaseJob::STATUS_ONLINE) {
                                $item['amount']   = '1~2';
                                $newJobHotList1[] = $item;
                            }
                        }
                    }

                    // 剩余的职位放到newJobHotList2
                    $tmpIds = array_column($newJobHotList1, 'jobId');
                    foreach ($data['jobHotList'] as $item) {
                        if (!in_array($item['jobId'], $tmpIds)) {
                            $item['amount']   = '1~2';
                            $newJobHotList2[] = $item;
                        }
                    }

                    $data['jobHotList'] = [
                        ...$newJobHotList1,
                        ...$newJobHotList2,
                    ];

                    break;
                case 'MINI':
                default:
                    break;
            }
        }

        if ($companyId == 95881) {
            // 只有PC端有这个需求
            if (PLATFORM === 'PC') {
                $data['announcementHotList'][0]['recruitAmount'] = '不超过15';
                $data['jobHotList'][0]['amount']                 = '不超过15';
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1191
        // 公告ID：325258，单位ID：72526（上海商学院）
        // 公告标题及链接：上海商学院2025年公开招聘商务信息学院院长公告https://www.gaoxiaojob.com/announcement/detail/325258.html
        // 修改内容：PC端、H5端、小程序端口公告详情页、职位详情页、职位所属公告的职位列表、单位主页-职位列表 的需求专业，修改为指定文字。
        // 需求专业：管理科学与工程、计算机科学与技术、电子信息、智能科学与技术
        if ($companyId == 72526) {
            // 处理单位详情页的职位热门列表
            if (isset($data['jobHotList']) && is_array($data['jobHotList'])) {
                foreach ($data['jobHotList'] as &$item) {
                    if (isset($item['announcementId']) && $item['announcementId'] == 325258) {
                        $item['major'] = '管理科学与工程、计算机科学与技术、电子信息、智能科学与技术';
                    }
                }
            }
        }

        return $data;
    }

    public function handelCompanyAnnouncementList($data, $companyId)
    {
        // 优先使用新的配置系统
        $data = $this->applyAnnouncementListConfigs($data, $companyId);

        // 向后兼容：如果新系统未启用或无配置，使用原有逻辑
        if (!$this->isOpen) {
            return $data;
        }

        if ($companyId == $this->config['944']['companyId']) {
            // 正式进入逻辑，拿专业信息
            switch (PLATFORM) {
                case 'H5':
                case 'PC':
                    foreach ($data['announcementList'] as &$datum) {
                        if ($datum['id'] == $this->config['944']['announcementId']) {
                            $datum['recruitAmount'] = '3~5';
                        }
                    }
                    break;
                case 'MINI':
                    foreach ($data['list'] as &$datum) {
                        $datum['announcement_recruit_amount'] = '3~5';
                    }
                    break;
                default:
                    break;
            }
        }

        if ($companyId == 510) {
            // 获取当前控制
            // 小程序
            if (PLATFORM === 'MINI') {
                // foreach ($data['list'] as &$datum) {
                //     $datum['educationName']    = '详见招聘简章';
                //     $datum['minEducationName'] = '详见招聘简章';
                // }
            }
        }

        // 15人改成不超过15人
        if ($companyId == 95881) {
            // 获取当前控制
            $controller = \Yii::$app->controller->id;

            //  需求专业字段信息，统一展示为“详见招聘简章”
            if ($controller === 'api/person/company') {
                // api

                // $data['list'][0]['announcement_recruit_amount'] = '不超过15';
            } else {
                // 实际页面
                $data['announcementList'][0]['recruitAmount'] = '不超过15';

                // 小程序
                if (PLATFORM === 'MINI') {
                    $data['list'][0]['announcement_recruit_amount'] = '不超过15';
                }
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1191
        // 公告ID：325258，单位ID：72526（上海商学院）
        // 公告标题及链接：上海商学院2025年公开招聘商务信息学院院长公告https://www.gaoxiaojob.com/announcement/detail/325258.html
        // 修改内容：PC端、H5端、小程序端口公告详情页、职位详情页、职位所属公告的职位列表、单位主页-职位列表 的需求专业，修改为指定文字。
        // 需求专业：管理科学与工程、计算机科学与技术、电子信息、智能科学与技术
        // 注意：此方法主要处理单位公告列表，不直接涉及职位专业字段，所以这里不需要特殊处理

        return $data;
    }

    public function handelCompanyJobList($data, $companyId)
    {
        // 优先使用新的配置系统
        $data = $this->applyJobListConfigs($data, $companyId);

        // 向后兼容：如果新系统未启用或无配置，使用原有逻辑
        if (!$this->isOpen) {
            return $data;
        }

        if ($companyId == $this->config['944']['companyId']) {
            // 正式进入逻辑，拿专业信息
            switch (PLATFORM) {
                case 'H5':
                case 'PC':
                    // 排序、专业、招聘人数都要修改
                    $jobRanking = $this->config['944']['jobRanking'];

                    $newJobList1 = [];
                    $newJobList2 = [];

                    foreach ($jobRanking as $jobId) {
                        foreach ($data['jobList'] as $item) {
                            if ($item['jobId'] == $jobId && $item['status'] == BaseJob::STATUS_ONLINE) {
                                $item['amount'] = '1~2';
                                $item['major']  .= ",{$this->config['944']['jobAddMajor']}";
                                $newJobList1[]  = $item;
                            }
                        }
                    }

                    // 剩余的职位放到newJobList2
                    $tmpIds = array_column($newJobList1, 'jobId');
                    foreach ($data['jobList'] as $item) {
                        if (!in_array($item['jobId'], $tmpIds)) {
                            $newJobList2[] = $item;
                        }
                    }

                    $data['jobList'] = [
                        ...$newJobList1,
                        ...$newJobList2,
                    ];
                    break;
                case 'MINI':
                    $jobRanking  = $this->config['944']['jobRanking'];
                    $newJobList1 = [];
                    $newJobList2 = [];

                    foreach ($jobRanking as $jobId) {
                        foreach ($data['list'] as $item) {
                            if ($item['id'] == $jobId && $item['status'] == BaseJob::STATUS_ONLINE) {
                                $item['amount'] = '1~2';
                                $newJobList1[]  = $item;
                            }
                        }
                    }

                    $tmpIds = array_column($newJobList1, 'id');
                    foreach ($data['list'] as $item) {
                        if (!in_array($item['id'], $tmpIds)) {
                            $newJobList2[] = $item;
                        }
                    }

                    $newJobList = [
                        ...$newJobList1,
                        ...$newJobList2,
                    ];

                    $data['list'] = $newJobList;
                    break;
                default:
                    break;
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1042&version=0&param=0&storyType=story
        if ($companyId == 510) {
            // 获取当前控制
            $controller = \Yii::$app->controller->id;

            //  需求专业字段信息，统一展示为“详见招聘简章”
            if ($controller === 'api/person/company') {
                // api
                foreach ($data['list'] as &$datum) {
                    $datum['major'] = '详见招聘简章';
                    // $datum['education'] = '详见招聘简章';
                }
            } else {
                // 实际页面
                foreach ($data['jobList'] as &$datum) {
                    $datum['major'] = '详见招聘简章';
                    // $datum['education'] = '详见招聘简章';
                }

                // 小程序
                if (PLATFORM === 'MINI') {
                    foreach ($data['list'] as &$datum) {
                        $datum['major'] = '详见招聘简章';
                        // $datum['educationTypeName'] = '详见招聘简章';
                    }
                }
            }
        }

        if ($companyId == 100086) {
            $majorConfig = [
                1558458 => "医药类及相关专业",
                1558459 => "医药类及相关专业",
                1558460 => "医药类及相关专业",
                1558461 => "医药类及相关专业",
                1558462 => "医药类及相关专业",
                1558463 => "医药类及相关专业",
                1558464 => "医药类及相关专业",
                1558465 => "医药类及相关专业",
                1558466 => "医药类及相关专业",
                1558467 => "医药类及相关专业",
                1558468 => "医药类及相关专业",
                1558469 => "医药类、管理类",
                1558470 => "人文社会科学（包括哲学、经济学、法学、教育学、文学、历史学、管理学、艺术学等学科门类和心理学、社会学等专业）",
                1558471 => "医学大类（基础医学类、临床医学类、口腔医学类、公共卫生与预防医学类专业优先）",
                1558472 => "英语类（含医学英语）",
                1558473 => "体育类",
                1558474 => "医学大类（护理类专业优先）",
                1558475 => "医学大类（中医学类、医学技术类、中西医结合类、临床医学类专业优先）",
                1558476 => "医学大类（医学技术类、临床医学类、口腔医学类专业优先）",
                1558477 => "医学大类（药学类、中药学类专业优先）",
                1558478 => "医学大类、管理大类（健康服务与管理、养老服务管理等专业优先）",
                1558479 => "计算机类专业、电子信息类专业",
                1558480 => "教育学、心理学、思想政治教育、管理学、医学及相关专业",
                1613689 => "文史类、管理类相关专业优先",
                1613690 => "文史类、管理类相关专业优先",
            ];
            // 正式进入逻辑，拿专业信息
            foreach ($data['jobList'] as &$item) {
                $item['major'] = $majorConfig[$item['jobId']];
            }

            // 正式进入逻辑，拿专业信息
            foreach ($data['list'] as &$item2) {
                $item2['major'] = $majorConfig[$item2['jobId']];
            }
        }

        //https://zentao.jugaocai.com/index.php?m=story&f=view&id=1182
        // // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1190&version=0&param=0&storyType=story
        //
        // /**
        //  * 职位ID：1645988
        //  *
        //  * 职位名称及链接：国际交流处负责人https://www.gaoxiaojob.com/job/detail/1645988.html
        //  *
        //  * 修改内容：PC端、H5端、小程序端口职位详情页、职位所属公告的职位列表、单位主页-职位列表 的职位类型、需求专业，修改为指定文字。
        //  *
        //  * 职位类型修改为：行政管理
        //  *
        //  * 需求专业修改为：国际关系、教育管理、外语类等相关专业
        //  */
        if ($companyId == 289323) {
            $majorConfig = [
                1623838 => "人力资源管理、心理学、高等教育学、管理学、法学等相关专业",
                1623837 => "人力资源管理、心理学、高等教育学、管理学、法学等相关专业",
                1645988 => "国际关系、教育管理、外语类等相关专业",
            ];

            $educationConfig = [
                1645988 => "硕士及以上学历",
            ];

            // 正式进入逻辑，拿专业信息
            foreach ($data['jobList'] as &$item) {
                if (!isset($majorConfig[$item['jobId']])) {
                    continue;
                }
                $item['major'] = $majorConfig[$item['jobId']];
            }

            // 正式进入逻辑，拿专业信息
            foreach ($data['list'] as &$item2) {
                if (!isset($majorConfig[$item2['jobId']])) {
                    continue;
                }
                $item2['major'] = $majorConfig[$item2['jobId']];
            }
            // 正式进入逻辑，拿学历
            foreach ($data['jobList'] as &$item) {
                if (!isset($educationConfig[$item['jobId']])) {
                    continue;
                }
                $item['education'] = $educationConfig[$item['jobId']];
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1073

        /**
         * 需求描述
         * 公告ID：301480
         *
         * 公告：事业编制，电子科技大学2025年专职辅导员招聘公告https://www.gaoxiaojob.com/announcement/detail/301480.html
         *
         * PC端+H5端+小程序端的公告页+职位页+单位页，均需按以下需求修改：
         *
         * 1.“招15人”改为“招不超15过人”
         *
         * 2.需求学科改为：“马克思主义理论、心理学或学校其他学科专业背景”
         */
        if ($companyId == 95881) {
            // 获取当前控制
            $controller = \Yii::$app->controller->id;

            //  需求专业字段信息，统一展示为“详见招聘简章”
            if ($controller === 'api/person/company') {
                // api
                foreach ($data['list'] as &$datum) {
                    // $datum['major']     = '详见招聘简章';
                    // $datum['education'] = '详见招聘简章';
                }
            } else {
                // 实际页面
                $data['jobList'][0]['amount'] = '不超过15';
                $data['jobList'][0]['major']  = '马克思主义理论、心理学或学校其他学科专业背景';

                // 小程序
                if (PLATFORM === 'MINI') {
                    $data['list'][0]['amount'] = '不超过15';
                    $data['list'][0]['major']  = '马克思主义理论、心理学或学校其他学科专业背景';
                }
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1185
        // 公告ID：316039
        // companyId =84680
        // 公告：南京城市职业学院（南京开放大学）2025年公开招聘高层次人才公告（第一批）https://www.gaoxiaojob.com/announcement/detail/316039.html

        /**
         * 2.职位列表、职位详情页、单位主页：各个职位的学科展示见下表
         *
         * 职位名称
         *
         * 学科
         *
         * 职位ID
         *
         * 教学质量管理与研究
         *
         * 教育类，计算机类
         *
         * 31635167
         *
         * 产品艺术设计专业教师
         *
         * 设计学（产品设计方向），艺术设计（产品设计方向）
         *
         * 31635159
         *
         * 健康管理专业教师
         *
         * 公共卫生类，医学类
         *
         * 31635151
         *
         * 实验实训员
         *
         * 计算机类
         *
         * 31635166
         *
         * 数字媒体艺术设计专业教师
         *
         * 设计学（数字媒体艺术方向），艺术设计（数字媒体艺术方向）
         *
         * 31635158
         *
         * 专业带头人
         *
         * 公共管理类，医学类，电子信息类，交通工程类，机械工程类，计算机类，艺术类，工商管理类，经济类，商务贸易类
         *
         * 31635150
         *
         * 体育教师
         *
         * 体育教育训练学，体育教学，运动训练，体育人文社会学
         *
         * 31635165
         *
         * 视觉传达设计专业教师
         *
         * 设计艺术学（平面设计方向）
         *
         * 31635157
         *
         * 思政教师2
         *
         * 马克思主义哲学，马克思主义基本原理，马克思主义理论，马克思主义中国化研究，思想政治教育
         *
         * 31635164
         *
         * 传播与策划专业教师
         *
         * 电影学，广播电视艺术学
         *
         * 31635156
         *
         * 云计算技术应用专业教师
         *
         * 网络空间安全，网络与信息安全，信息安全，大数据技术与工程，人工智能，通信工程（含宽带网络，移动通信等），信息与通信工程，通信与信息系统
         *
         * 31635155
         *
         * 思政教师1
         *
         * 马克思主义哲学，马克思主义基本原理，马克思主义理论，马克思主义中国化研究，思想政治教育
         *
         * 31635163
         *
         * 跨境电子商务专业教师
         *
         * 经济类，管理科学与工程
         *
         * 31635162
         *
         * 软件技术专业教师
         *
         * 计算机（软件）类
         *
         * 31635154
         *
         * 商务数据分析专业教师
         *
         * 统计类
         *
         * 31635161
         *
         * 智能网联汽车技术专业教师
         *
         * 电子信息类，交通工程类，机械工程类
         *
         * 31635153
         *
         * 智慧健康养老服务与管理专业教师
         *
         * 公共卫生类，医学类，计算机类，电子信息类
         *
         * 31635152
         *
         * 环境艺术设计专任教师
         *
         * 设计学(环境设计方向），艺术设计(环境设计方向）
         *
         * 31635160
         */
        if ($companyId == 84680) {
            $majorConfig = [
                1635167 => "教育类，计算机类",
                1635159 => "设计学（产品设计方向），艺术设计（产品设计方向）",
                1635151 => "公共卫生类，医学类",
                1635166 => "计算机类",
                1635158 => "设计学（数字媒体艺术方向），艺术设计（数字媒体艺术方向）",
                1635150 => "公共管理类，医学类，电子信息类，交通工程类，机械工程类，计算机类，艺术类，工商管理类，经济类，商务贸易类",
                1635165 => "体育教育训练学，体育教学，运动训练，体育人文社会学",
                1635157 => "设计艺术学（平面设计方向）",
                1635164 => "马克思主义哲学，马克思主义基本原理，马克思主义理论，马克思主义中国化研究，思想政治教育",
                1635156 => "电影学，广播电视艺术学",
                1635155 => "网络空间安全，网络与信息安全，信息安全，大数据技术与工程，人工智能，通信工程（含宽带网络，移动通信等），信息与通信工程，通信与信息系统",
                1635163 => "马克思主义哲学，马克思主义基本原理，马克思主义理论，马克思主义中国化研究，思想政治教育",
                1635162 => "经济类，管理科学与工程",
                1635154 => "计算机（软件）类",
                1635161 => "统计类",
                1635153 => "电子信息类，交通工程类，机械工程类",
                1635152 => "公共卫生类，医学类，计算机类，电子信息类",
                1635160 => "设计学(环境设计方向），艺术设计(环境设计方向）",
            ];
            // 正式进入逻辑
            foreach ($data['jobList'] as &$item) {
                if (!isset($majorConfig[$item['jobId']])) {
                    continue;
                }
                $item['major'] = $majorConfig[$item['jobId']];
            }

            // 正式进入逻辑
            foreach ($data['list'] as &$item2) {
                if (!isset($majorConfig[$item2['jobId']])) {
                    continue;
                }
                $item2['major'] = $majorConfig[$item['jobId']];
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&id=1203

        /**
         * 需求描述
         * 共计需要修改4个职位
         *
         * 修改内容：PC端、H5端、小程序端口职位详情页、职位所属公告的职位列表、单位主页-职位列表 的职位类型、学历要求、需求专业，修改为指定文字。
         *
         * 请6月18日下午3点前处理完毕，谢谢
         *
         * 职位ID
         *
         * 职位类型
         *
         * 学历要求
         *
         * 需求专业
         *
         * 1682494
         *
         * 教学科研岗
         *
         * 硕士研究生及以上
         *
         * 英语专业毕业，应用语言学、文学、翻译、商务英语等相关专业；第一学历为全日制本科，本硕专业一致或相近
         *
         * 1682495
         *
         * 教学科研岗
         *
         * 硕士研究生及以上
         *
         * 数学专业毕业，统计学等数学相关专业；第一学历为全日制本科，本硕专业一致或相近
         *
         * 1682496
         *
         * 教学科研岗
         *
         * 硕士研究生及以上
         *
         * 体育教育、运动训练等体育学类专业；第一学历为全日制本科，本硕专业一致或相近
         *
         * 1682497
         *
         * 教学科研岗
         *
         * 硕士研究生及以上
         *
         * 计算机科学与技术、软件工程、数据科学与大数据技术、人工智能、数字媒体技术、物联网工程、虚拟现实与增强现实等相关专业
         *
         * 公告id 289323
         *
         * 按以上要求新增处理一个职位
         * 职位ID：1682498
         * 职位类型：教学科研岗
         * 学历要求：硕士研究生及以上
         * 需求专业：档案学、信息资源管理等相关专业
         */
        if ($companyId == 289323) {
            $jobConfig = [
                1682494 => [
                    'jobType'   => '教学科研岗',
                    'education' => '硕士研究生及以上',
                    'major'     => '英语专业毕业，应用语言学、文学、翻译、商务英语等相关专业；第一学历为全日制本科，本硕专业一致或相近',
                ],
                1682495 => [
                    'jobType'   => '教学科研岗',
                    'education' => '硕士研究生及以上',
                    'major'     => '数学专业毕业，统计学等数学相关专业；第一学历为全日制本科，本硕专业一致或相近',
                ],
                1682496 => [
                    'jobType'   => '教学科研岗',
                    'education' => '硕士研究生及以上',
                    'major'     => '体育教育、运动训练等体育学类专业；第一学历为全日制本科，本硕专业一致或相近',
                ],
                1682497 => [
                    'jobType'   => '教学科研岗',
                    'education' => '硕士研究生及以上',
                    'major'     => '计算机科学与技术、软件工程、数据科学与大数据技术、人工智能、数字媒体技术、物联网工程、虚拟现实与增强现实等相关专业',
                ],
                1682498 => [
                    'jobType'   => '教学科研岗',
                    'education' => '硕士研究生及以上',
                    'major'     => '档案学、信息资源管理等相关专业',
                ],
            ];

            // 正式进入逻辑
            foreach ($data['jobList'] as &$item) {
                if (!isset($jobConfig[$item['jobId']])) {
                    continue;
                }
                $item['jobType']   = $jobConfig[$item['jobId']]['jobType'];
                $item['education'] = $jobConfig[$item['jobId']]['education'];
                $item['major']     = $jobConfig[$item['jobId']]['major'];
            }

            // 正式进入逻辑
            foreach ($data['list'] as &$item2) {
                if (!isset($jobConfig[$item2['jobId']])) {
                    continue;
                }
                $item2['jobType']   = $jobConfig[$item2['jobId']]['jobType'];
                $item2['education'] = $jobConfig[$item2['jobId']]['education'];
                $item2['major']     = $jobConfig[$item2['jobId']]['major'];
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1191
        // 公告ID：325258，单位ID：72526（上海商学院）
        // 公告标题及链接：上海商学院2025年公开招聘商务信息学院院长公告https://www.gaoxiaojob.com/announcement/detail/325258.html
        // 修改内容：PC端、H5端、小程序端口公告详情页、职位详情页、职位所属公告的职位列表、单位主页-职位列表 的需求专业，修改为指定文字。
        // 需求专业：管理科学与工程、计算机科学与技术、电子信息、智能科学与技术
        if ($companyId == 72526) {
            // 获取当前控制器
            $controller = \Yii::$app->controller->id;

            // 处理不同数据格式
            if ($controller === 'api/person/company') {
                // API接口
                if (isset($data['list']) && is_array($data['list'])) {
                    foreach ($data['list'] as &$item) {
                        if (isset($item['announcementId']) && $item['announcementId'] == 325258) {
                            $item['major'] = '管理科学与工程、计算机科学与技术、电子信息、智能科学与技术';
                        }
                    }
                }
            } else {
                // 实际页面
                if (isset($data['jobList']) && is_array($data['jobList'])) {
                    foreach ($data['jobList'] as &$item) {
                        if (isset($item['announcementId']) && $item['announcementId'] == 325258) {
                            $item['major'] = '管理科学与工程、计算机科学与技术、电子信息、智能科学与技术';
                        }
                    }
                }

                // 小程序端
                if (PLATFORM === 'MINI') {
                    if (isset($data['list']) && is_array($data['list'])) {
                        foreach ($data['list'] as &$item) {
                            if (isset($item['announcementId']) && $item['announcementId'] == 325258) {
                                $item['major'] = '管理科学与工程、计算机科学与技术、电子信息、智能科学与技术';
                            }
                        }
                    }
                }
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1212
        // 公告ID：289323，单位ID：80360（长沙科技学院）
        // 修改内容：PC端、H5端、小程序端口职位详情页、职位所属公告的职位列表、单位主页-职位列表 的职位类型、学历要求、职称要求、需求专业
        if ($companyId == 80360) {
            // 长沙科技学院14个职位的特殊配置
            // 注意：部分职位的职称要求列为空白，按需求"表格空白处保留原样"不修改学历要求
            $changShaJobConfig = [
                1700793 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '智能装备与系统、轨道交通信号与控制、能源与动力工程等相关专业',
                ],
                1700800 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '网络与新媒体、新闻学、广播电视学、广告学、编辑出版学等相关专业',
                ],
                1700792 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '智能交互设计、机械电子工程、机器人工程等相关专业',
                ],
                1700799 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '跨境电子商务、电子商务及法律、电子商务等相关专业',
                ],
                1700791 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '智能制造工程、机械设计制造及自动化、车辆工程等相关专业',
                ],
                1700798 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '供应链管理、物流管理、物流工程、采购管理等相关专业',
                ],
                1700790 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '博士或教授以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '轨道交通电气与控制、智能交通技术、轮机工程等相关专业',
                ],
                1700797 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '经济学、国际经济与贸易、财政学、金融学等相关专业',
                ],
                1700789 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '博士或教授以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '智慧交通、交通工程、交通运输等相关专业',
                ],
                1700796 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '博士或教授以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '物联网工程、智能科学与技术、人工智能等相关专业',
                ],
                1700788 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '教授、副教授、博士及以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '电气工程及其自动化、智能电网信息工程、电气工程与智能控制等相关专业',
                ],
                1700787 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '教授、副教授、博士及以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '储能科学与工程、能源与动力工程、新能源科学与工程等相关专业',
                ],
                1700795 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '博士或教授以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '数字媒体技术、计算机科学与技术、软件工程、智能科学与技术、电子信息工程等相关专业',
                ],
                1700794 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '博士或教授以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '人工智能、电子信息工程、通信工程、自动化、机器人工程、计算机科学与技术等相关专业',
                ],
            ];

            // 获取当前控制器
            $controller = \Yii::$app->controller->id;

            // 处理不同数据格式
            if ($controller === 'api/person/company') {
                // API接口
                if (isset($data['list']) && is_array($data['list'])) {
                    foreach ($data['list'] as &$item) {
                        $jobId = isset($item['jobId']) ? $item['jobId'] : (isset($item['id']) ? $item['id'] : null);
                        if ($jobId && isset($changShaJobConfig[$jobId])) {
                            $config = $changShaJobConfig[$jobId];
                            if (isset($config['education'])) {
                                $item['education'] = $config['education'];
                            }
                            if (isset($config['title'])) {
                                $item['title'] = $config['title'];
                            }
                            if (isset($config['major'])) {
                                $item['major'] = $config['major'];
                            }
                            if (isset($config['jobCategory'])) {
                                $item['jobCategory'] = $config['jobCategory'];
                            }
                        }
                    }
                }
            } else {
                // 实际页面
                if (isset($data['jobList']) && is_array($data['jobList'])) {
                    foreach ($data['jobList'] as &$item) {
                        $jobId = isset($item['jobId']) ? $item['jobId'] : (isset($item['id']) ? $item['id'] : null);
                        if ($jobId && isset($changShaJobConfig[$jobId])) {
                            $config = $changShaJobConfig[$jobId];
                            if (isset($config['education'])) {
                                $item['education'] = $config['education'];
                            }
                            if (isset($config['title'])) {
                                $item['title'] = $config['title'];
                            }
                            if (isset($config['major'])) {
                                $item['major'] = $config['major'];
                            }
                            if (isset($config['jobCategory'])) {
                                $item['jobCategory'] = $config['jobCategory'];
                            }
                        }
                    }
                }

                // 小程序端
                if (PLATFORM === 'MINI') {
                    if (isset($data['list']) && is_array($data['list'])) {
                        foreach ($data['list'] as &$item) {
                            $jobId = isset($item['job_id']) ? $item['job_id'] : (isset($item['id']) ? $item['id'] : null);
                            if ($jobId && isset($changShaJobConfig[$jobId])) {
                                $config = $changShaJobConfig[$jobId];
                                if (isset($config['education'])) {
                                    $item['education_name'] = $config['education'];
                                }
                                if (isset($config['major'])) {
                                    $item['major_name'] = $config['major'];
                                }
                                if (isset($config['jobCategory'])) {
                                    $item['job_category_name'] = $config['jobCategory'];
                                }
                            }
                        }
                    }
                }
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1212
        // 公告ID：289323，单位ID：80360（长沙科技学院）
        // 修改内容：PC端、H5端、小程序端口职位详情页、职位所属公告的职位列表、单位主页-职位列表 的职位类型、学历要求、职称要求、需求专业
        if ($companyId == 80360) {
            // 长沙科技学院14个职位的特殊配置
            // 注意：部分职位的职称要求列为空白，按需求"表格空白处保留原样"不修改学历要求
            $changShaJobConfig = [
                1700793 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '智能装备与系统、轨道交通信号与控制、能源与动力工程等相关专业',
                ],
                1700800 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '网络与新媒体、新闻学、广播电视学、广告学、编辑出版学等相关专业',
                ],
                1700792 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '智能交互设计、机械电子工程、机器人工程等相关专业',
                ],
                1700799 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '跨境电子商务、电子商务及法律、电子商务等相关专业',
                ],
                1700791 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '智能制造工程、机械设计制造及自动化、车辆工程等相关专业',
                ],
                1700798 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '供应链管理、物流管理、物流工程、采购管理等相关专业',
                ],
                1700790 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '博士或教授以上',
                    'title'       => '博士或教授以上',
                    'major'       => '轨道交通电气与控制、智能交通技术、轮机工程等相关专业',
                ],
                1700797 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '经济学、国际经济与贸易、财政学、金融学等相关专业',
                ],
                1700789 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '博士或教授以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '智慧交通、交通工程、交通运输等相关专业',
                ],
                1700796 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '博士或教授以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '物联网工程、智能科学与技术、人工智能等相关专业',
                ],
                1700788 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '教授、副教授、博士及以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '电气工程及其自动化、智能电网信息工程、电气工程与智能控制等相关专业',
                ],
                1700787 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '教授、副教授、博士及以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '储能科学与工程、能源与动力工程、新能源科学与工程等相关专业',
                ],
                1700795 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '博士或教授以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '数字媒体技术、计算机科学与技术、软件工程、智能科学与技术、电子信息工程等相关专业',
                ],
                1700794 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '博士或教授以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '人工智能、电子信息工程、通信工程、自动化、机器人工程、计算机科学与技术等相关专业',
                ],
            ];

            // 获取当前控制器
            $controller = \Yii::$app->controller->id;

            // 处理不同数据格式
            if ($controller === 'api/person/company') {
                // API接口
                if (isset($data['list']) && is_array($data['list'])) {
                    foreach ($data['list'] as &$item) {
                        $jobId = isset($item['jobId']) ? $item['jobId'] : (isset($item['id']) ? $item['id'] : null);
                        if ($jobId && isset($changShaJobConfig[$jobId])) {
                            $config = $changShaJobConfig[$jobId];
                            if (isset($config['education'])) {
                                $item['education'] = $config['education'];
                            }
                            if (isset($config['title'])) {
                                $item['title'] = $config['title'];
                            }
                            if (isset($config['major'])) {
                                $item['major'] = $config['major'];
                            }
                            if (isset($config['jobCategory'])) {
                                $item['jobCategory'] = $config['jobCategory'];
                            }
                        }
                    }
                }
            } else {
                // 实际页面
                if (isset($data['jobList']) && is_array($data['jobList'])) {
                    foreach ($data['jobList'] as &$item) {
                        $jobId = isset($item['jobId']) ? $item['jobId'] : (isset($item['id']) ? $item['id'] : null);
                        if ($jobId && isset($changShaJobConfig[$jobId])) {
                            $config = $changShaJobConfig[$jobId];
                            if (isset($config['education'])) {
                                $item['education'] = $config['education'];
                            }
                            if (isset($config['major'])) {
                                $item['major'] = $config['major'];
                            }
                            if (isset($config['jobCategory'])) {
                                $item['jobCategory'] = $config['jobCategory'];
                            }
                        }
                    }
                }

                // 处理单位详情页的职位热门列表
                if (isset($data['jobHotList']) && is_array($data['jobHotList'])) {
                    foreach ($data['jobHotList'] as &$item) {
                        $jobId = isset($item['jobId']) ? $item['jobId'] : (isset($item['id']) ? $item['id'] : null);
                        if ($jobId && isset($changShaJobConfig[$jobId])) {
                            $config = $changShaJobConfig[$jobId];
                            if (isset($config['education'])) {
                                $item['education'] = $config['education'];
                            }
                            if (isset($config['major'])) {
                                $item['major'] = $config['major'];
                            }
                            if (isset($config['jobCategory'])) {
                                $item['jobCategory'] = $config['jobCategory'];
                            }
                        }
                    }
                }

                // 小程序端
                if (PLATFORM === 'MINI') {
                    if (isset($data['list']) && is_array($data['list'])) {
                        foreach ($data['list'] as &$item) {
                            $jobId = isset($item['job_id']) ? $item['job_id'] : (isset($item['id']) ? $item['id'] : null);
                            if ($jobId && isset($changShaJobConfig[$jobId])) {
                                $config = $changShaJobConfig[$jobId];
                                if (isset($config['education'])) {
                                    $item['education_name'] = $config['education'];
                                }
                                if (isset($config['major'])) {
                                    $item['major_name'] = $config['major'];
                                }
                                if (isset($config['jobCategory'])) {
                                    $item['job_category_name'] = $config['jobCategory'];
                                }
                            }
                        }
                    }
                }
            }
        }

        return $data;
    }

    /**
     * 应用单位配置（支持优先级：职位配置 > 公告配置 > 单位配置）
     * @param array $data
     * @param int   $companyId
     * @return array
     */
    protected function applyCompanyConfigs($data, $companyId)
    {
        // 检查新系统是否启用
        if (!BaseSpecialNeedConfig::isEnabled()) {
            return $data;
        }

        // 获取当前平台
        $platform = defined('PLATFORM') ? PLATFORM : 'PC';

        // 获取单位级别的配置（最低优先级）
        $companyConfigs = BaseSpecialNeedConfig::getConfigs(BaseSpecialNeedConfig::TYPE_COMPANY, $companyId, $platform);

        // 应用配置到职位热门列表（jobHotList）
        if (isset($data['jobHotList']) && is_array($data['jobHotList'])) {
            foreach ($data['jobHotList'] as &$item) {
                // 1. 先应用单位级别配置
                if (!empty($companyConfigs)) {
                    $item = BaseSpecialNeedConfig::applyConfigs($item, $companyConfigs);
                }

                // 2. 再应用公告级别配置
                if (isset($item['announcementId'])) {
                    $announcementConfigs = BaseSpecialNeedConfig::getConfigs(BaseSpecialNeedConfig::TYPE_ANNOUNCEMENT,
                        $item['announcementId'], $platform);
                    if (!empty($announcementConfigs)) {
                        $item = BaseSpecialNeedConfig::applyConfigs($item, $announcementConfigs);
                    }
                }

                // 3. 最后应用职位级别配置（最高优先级）
                if (isset($item['jobId'])) {
                    $jobConfigs = BaseSpecialNeedConfig::getConfigs(BaseSpecialNeedConfig::TYPE_JOB, $item['jobId'],
                        $platform);
                    if (!empty($jobConfigs)) {
                        $item = BaseSpecialNeedConfig::applyConfigs($item, $jobConfigs);
                    }
                }
            }
        }

        // 应用配置到公告热门列表（announcementHotList）
        if (isset($data['announcementHotList']) && is_array($data['announcementHotList'])) {
            foreach ($data['announcementHotList'] as &$item) {
                // 1. 先应用单位级别配置
                if (!empty($companyConfigs)) {
                    $item = BaseSpecialNeedConfig::applyConfigs($item, $companyConfigs);
                }

                // 2. 再应用公告级别配置（高优先级）
                if (isset($item['id'])) {
                    $announcementConfigs = BaseSpecialNeedConfig::getConfigs(BaseSpecialNeedConfig::TYPE_ANNOUNCEMENT,
                        $item['id'], $platform);
                    if (!empty($announcementConfigs)) {
                        $item = BaseSpecialNeedConfig::applyConfigs($item, $announcementConfigs);
                    }
                }
            }
        }

        // 应用配置到单位详情本身
        if (!empty($companyConfigs)) {
            $data = BaseSpecialNeedConfig::applyConfigs($data, $companyConfigs);
        }

        return $data;
    }

    /**
     * 应用公告列表配置（支持优先级：公告配置 > 单位配置）
     * @param array $data
     * @param int   $companyId
     * @return array
     */
    protected function applyAnnouncementListConfigs($data, $companyId)
    {
        // 检查新系统是否启用
        if (!BaseSpecialNeedConfig::isEnabled()) {
            return $data;
        }

        // 获取当前平台
        $platform = defined('PLATFORM') ? PLATFORM : 'PC';

        // 获取单位级别的配置（低优先级）
        $companyConfigs = BaseSpecialNeedConfig::getConfigs(BaseSpecialNeedConfig::TYPE_COMPANY, $companyId, $platform);

        // 应用配置到公告列表
        if (isset($data['announcementList']) && is_array($data['announcementList'])) {
            foreach ($data['announcementList'] as &$item) {
                // 先应用单位级别配置
                if (!empty($companyConfigs)) {
                    $item = BaseSpecialNeedConfig::applyConfigs($item, $companyConfigs);
                }

                // 再应用公告级别配置（高优先级，会覆盖单位配置）
                if (isset($item['id'])) {
                    $announcementConfigs = BaseSpecialNeedConfig::getConfigs(BaseSpecialNeedConfig::TYPE_ANNOUNCEMENT,
                        $item['id'], $platform);
                    if (!empty($announcementConfigs)) {
                        $item = BaseSpecialNeedConfig::applyConfigs($item, $announcementConfigs);
                    }
                }
            }
        }

        // 应用配置到list字段（小程序等使用）
        if (isset($data['list']) && is_array($data['list'])) {
            foreach ($data['list'] as &$item) {
                // 先应用单位级别配置
                if (!empty($companyConfigs)) {
                    $item = BaseSpecialNeedConfig::applyConfigs($item, $companyConfigs);
                }

                // 再应用公告级别配置（高优先级，会覆盖单位配置）
                // 小程序端可能使用不同的字段名
                $announcementId = $item['id'] ?? $item['announcement_id'] ?? null;
                if ($announcementId) {
                    $announcementConfigs = BaseSpecialNeedConfig::getConfigs(BaseSpecialNeedConfig::TYPE_ANNOUNCEMENT,
                        $announcementId, $platform);
                    if (!empty($announcementConfigs)) {
                        $item = BaseSpecialNeedConfig::applyConfigs($item, $announcementConfigs);
                    }
                }
            }
        }

        return $data;
    }

    /**
     * 应用职位列表配置（支持优先级：职位配置 > 公告配置 > 单位配置）
     * @param array $data
     * @param int   $companyId
     * @return array
     */
    protected function applyJobListConfigs($data, $companyId)
    {
        // 检查新系统是否启用
        if (!BaseSpecialNeedConfig::isEnabled()) {
            return $data;
        }

        // 获取当前平台
        $platform = defined('PLATFORM') ? PLATFORM : 'PC';

        // 获取单位级别的配置（最低优先级）
        $companyConfigs = BaseSpecialNeedConfig::getConfigs(BaseSpecialNeedConfig::TYPE_COMPANY, $companyId, $platform);

        // 应用配置到职位列表
        if (isset($data['jobList']) && is_array($data['jobList'])) {
            foreach ($data['jobList'] as &$item) {
                // 1. 先应用单位级别配置（最低优先级）
                if (!empty($companyConfigs)) {
                    $item = BaseSpecialNeedConfig::applyConfigs($item, $companyConfigs,
                        BaseSpecialNeedConfig::TYPE_COMPANY);
                }

                // 2. 再应用公告级别配置（中等优先级）
                if (isset($item['announcementId'])) {
                    $announcementConfigs = BaseSpecialNeedConfig::getConfigs(BaseSpecialNeedConfig::TYPE_ANNOUNCEMENT,
                        $item['announcementId'], $platform);
                    if (!empty($announcementConfigs)) {
                        $item = BaseSpecialNeedConfig::applyConfigs($item, $announcementConfigs,
                            BaseSpecialNeedConfig::TYPE_ANNOUNCEMENT);
                    }
                }

                // 3. 最后应用职位级别配置（最高优先级，会覆盖前面的配置）
                if (isset($item['jobId'])) {
                    $jobConfigs = BaseSpecialNeedConfig::getConfigs(BaseSpecialNeedConfig::TYPE_JOB, $item['jobId'],
                        $platform);
                    if (!empty($jobConfigs)) {
                        $item = BaseSpecialNeedConfig::applyConfigs($item, $jobConfigs,
                            BaseSpecialNeedConfig::TYPE_JOB);
                    }
                }
            }
        }

        // 应用配置到list字段（小程序等使用）
        if (isset($data['list']) && is_array($data['list'])) {
            foreach ($data['list'] as &$item) {
                // 1. 先应用单位级别配置（最低优先级）
                if (!empty($companyConfigs)) {
                    $item = BaseSpecialNeedConfig::applyConfigs($item, $companyConfigs,
                        BaseSpecialNeedConfig::TYPE_COMPANY);
                }

                // 2. 再应用公告级别配置（中等优先级）
                // 小程序端可能使用不同的字段名
                $announcementId = $item['announcementId'] ?? $item['announcement_id'] ?? null;
                if ($announcementId) {
                    $announcementConfigs = BaseSpecialNeedConfig::getConfigs(BaseSpecialNeedConfig::TYPE_ANNOUNCEMENT,
                        $announcementId, $platform);
                    if (!empty($announcementConfigs)) {
                        $item = BaseSpecialNeedConfig::applyConfigs($item, $announcementConfigs,
                            BaseSpecialNeedConfig::TYPE_ANNOUNCEMENT);
                    }
                }

                // 3. 最后应用职位级别配置（最高优先级，会覆盖前面的配置）
                $jobId = $item['jobId'] ?? $item['id'] ?? null;
                if ($jobId) {
                    $jobConfigs = BaseSpecialNeedConfig::getConfigs(BaseSpecialNeedConfig::TYPE_JOB, $jobId, $platform);
                    if (!empty($jobConfigs)) {
                        $item = BaseSpecialNeedConfig::applyConfigs($item, $jobConfigs,
                            BaseSpecialNeedConfig::TYPE_JOB);
                    }
                }
            }
        }

        return $data;
    }

}
