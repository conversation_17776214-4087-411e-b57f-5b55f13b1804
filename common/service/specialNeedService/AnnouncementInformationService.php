<?php

namespace common\service\specialNeedService;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseSpecialNeedConfig;

/**
 * 公告信息特殊需求处理服务
 * 重构后支持数据库配置，向后兼容原有硬编码逻辑
 */
class AnnouncementInformationService extends BaseService
{
    public function handelMajorRanking($data)
    {
        if (!$this->isOpen) {
            return $data;
        }

        //  处理逻辑 http://zentao.jugaocai.com/index.php?m=story&f=view&storyID=769&version=0&param=&storyType=story#app=product

        // 老师墙裂要求，公告页面学科排序要按其要求排列：
        //
        // https://www.gaoxiaojob.com/announcement/detail/206013.html
        // H5端，小程序端，学科排序：药学、基础医学、临床医学、生物学

        // 公告id 206013
        // 1. 获取公告id
        switch (PLATFORM) {
            case 'H5':
                $announcementId = $data['id'];
                break;
            case 'PC':
                $announcementId = $data['id'];
                break;
            case 'MINI':
            default:
                $announcementId = $data['announcement_id'];
                break;
        }

        if ($announcementId == 206013) {
            // 正式进入逻辑，拿专业信息
            switch (PLATFORM) {
                case 'H5':
                    $data['majorName'] = '药学,基础医学,临床医学,生物学';
                    break;
                case 'PC':

                    break;
                case 'MINI':
                    $data['major_name'] = '药学,基础医学,临床医学,生物学';
                default:
                    break;
            }
        }

        return $data;
    }

    public function handelJobList($data, $announcementId)
    {
        // 优先使用新的配置系统
        $data = $this->applyJobListConfigs($data, $announcementId);

        // 向后兼容：如果新系统未启用或无配置，使用原有逻辑
        if (!$this->isOpen) {
            return $data;
        }

        //  处理逻辑 http://zentao.jugaocai.com/index.php?m=story&f=view&id=816

        if ($announcementId == 212795) {
            // 正式进入逻辑，拿专业信息
            foreach ($data['list'] as &$item) {
                $item['education'] = '博士研究生或正高职称';
                $item['major']     = '与拟聘二级学院专业设置或发展方向相关的专业';
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=storyView&storyID=1023
        if ($announcementId == 282268) {
            // 正式进入逻辑，拿专业信息
            foreach ($data['list'] as &$item) {
                if ($item['education'] === '硕士研究生') {
                    $item['education'] = '硕士研究生，高级职称可放宽至本科';
                }
            }
        }

        if ($announcementId == 212812) {
            // 正式进入逻辑，拿专业信息
            foreach ($data['list'] as &$item) {
                $item['education'] = '';
                $item['major']     = '与拟聘二级学院专业设置或发展方向相关的专业';
            }
        }

        if ($announcementId == 225958) {
            $majorConfig = [
                1129053 => '材料加工工程，材料科学与工程，机械电子工程，机械制造及其自动化，电气工程，机器人工程，智能制造技术，控制理论与控制工程等相关专业',
                1129054 => '机械制造及其自动化，机械电子工程，机械工程，模式识别与智能系统，电气工程，控制理论与控制工程等相关专业',
                1129055 => '电子科学与技术，信息与通信工程，电子信息，新一代电子信息技术，集成电路工程，应用电子技术，物理电子学，检测技术与自动化装置等相关专业',
                1129056 => '载运工具运用工程，车辆工程，智能车辆工程，新能源汽车工程，汽车维修工程教育等相关专业',
                1129057 => '计算机科学与技术，软件工程，网络空间安全，电子信息，大数据技术与工程，智能科学与技术，教育技术学，设计学等相关专业',
                1129058 => '制冷与低温工程，供热、供燃气、通风及空调工程，控制理论与工程，检测技术与自动化装置，电气工程及其自动化，清洁能源技术，化学工程与技术，室内设计，土木工程等相关专业',
                1129059 => '电子商务，跨境电子商务，物流工程，大数据技术与工程，新闻与传播，数字媒体技术，包装设计，摄影摄像等相关专业',
                1129060 => '设计艺术学，设计学，美术，艺术设计硕士（专业硕士），工业设计工程硕士（专业硕士），建筑学，木材科学与技术，广播电视艺术学等相关专业',
                1129061 => '汉语言文学（专业硕士或博士），财务管理，人力资源管理，旅游管理，工商管理(专业硕士)，电影学，会计等相关专业',
                1129062 => '材料学，冶金物理与化学，机械制造及其自动化，电机与电器，电力系统及其自动化等相关专业',
                1129063 => '应用数学，基础数学，体育教育训练学，体育教学硕士（专业硕士），运动训练硕士（专业硕士），中国语言文学，英语语言文学，外国语言学等相关专业',
            ];
            // 正式进入逻辑，拿专业信息
            foreach ($data['list'] as &$item) {
                $item['education'] = '硕士研究生，具有高级职称的可放宽至本科学历、学士及以上学位';
                $item['major']     = $majorConfig[$item['jobId']];
            }
        }

        if ($announcementId == 306054) {
            $majorConfig = [
                1558458 => "医药类及相关专业",
                1558459 => "医药类及相关专业",
                1558460 => "医药类及相关专业",
                1558461 => "医药类及相关专业",
                1558462 => "医药类及相关专业",
                1558463 => "医药类及相关专业",
                1558464 => "医药类及相关专业",
                1558465 => "医药类及相关专业",
                1558466 => "医药类及相关专业",
                1558467 => "医药类及相关专业",
                1558468 => "医药类及相关专业",
                1558469 => "医药类、管理类",
                1558470 => "人文社会科学（包括哲学、经济学、法学、教育学、文学、历史学、管理学、艺术学等学科门类和心理学、社会学等专业）",
                1558471 => "医学大类（基础医学类、临床医学类、口腔医学类、公共卫生与预防医学类专业优先）",
                1558472 => "英语类（含医学英语）",
                1558473 => "体育类",
                1558474 => "医学大类（护理类专业优先）",
                1558475 => "医学大类（中医学类、医学技术类、中西医结合类、临床医学类专业优先）",
                1558476 => "医学大类（医学技术类、临床医学类、口腔医学类专业优先）",
                1558477 => "医学大类（药学类、中药学类专业优先）",
                1558478 => "医学大类、管理大类（健康服务与管理、养老服务管理等专业优先）",
                1558479 => "计算机类专业、电子信息类专业",
                1558480 => "教育学、心理学、思想政治教育、管理学、医学及相关专业",
            ];
            // 正式进入逻辑，拿专业信息
            foreach ($data['list'] as &$item) {
                $item['major'] = $majorConfig[$item['jobId']];
            }
        }

        if ($announcementId == 313171) {
            $majorConfig = [
                1613689 => "文史类、管理类相关专业优先",
                1613690 => "文史类、管理类相关专业优先",
            ];
            // 正式进入逻辑，拿专业信息
            foreach ($data['list'] as &$item) {
                $item['major'] = $majorConfig[$item['jobId']];
            }
        }

        //        https://zentao.jugaocai.com/index.php?m=story&f=view&id=1182
        if ($announcementId == 289323) {
            $majorConfig     = [
                1623838 => "人力资源管理、心理学、高等教育学、管理学、法学等相关专业",
                1623837 => "人力资源管理、心理学、高等教育学、管理学、法学等相关专业",
            ];
            $educationConfig = [
                1645988 => "硕士及以上学历",
            ];
            // 正式进入逻辑，拿专业信息
            foreach ($data['list'] as &$item) {
                if ($majorConfig[$item['jobId']]) {
                    $item['major'] = $majorConfig[$item['jobId']];
                }
                if ($educationConfig[$item['jobId']]) {
                    $item['education'] = $educationConfig[$item['jobId']];
                }
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&id=944
        if ($announcementId == $this->config['944']['announcementId']) {
            switch (PLATFORM) {
                case 'H5':
                    break;
                case 'PC':
                    $jobAddMajor = $this->config['944']['jobAddMajor'];

                    // ①领军人才 →  ②学科带头人  →  ③青年拔尖人才
                    // $data['list'] 重新排序，jobId 按照下面的配置排序
                    $jobRanking = $this->config['944']['jobRanking'];
                    $newList1   = [];
                    $newList2   = [];
                    foreach ($jobRanking as $jobId) {
                        foreach ($data['list'] as $item) {
                            if ($item['jobId'] == $jobId) {
                                $item['major']  .= ",{$jobAddMajor}";
                                $item['amount'] = '1~2';
                                $newList1[]     = $item;
                            }
                        }
                    }

                    $tmpIds = array_column($newList1, 'jobId');
                    foreach ($data['list'] as $item) {
                        if (!in_array($item['jobId'], $tmpIds)) {
                            $item['major']  .= ",{$jobAddMajor}";
                            $item['amount'] = '1~2';
                            $newList2[]     = $item;
                        }
                    }

                    $newList = [
                        ...$newList1,
                        ...$newList2,
                    ];

                    $data['list'] = $newList;
                    break;
                case 'MINI':
                    $jobAddMajor = $this->config['944']['jobAddMajor'];
                    $jobRanking  = $this->config['944']['jobRanking'];
                    $newList1    = [];
                    $newList2    = [];
                    foreach ($jobRanking as $jobId) {
                        foreach ($data['data'] as $item) {
                            if ($item['id'] == $jobId) {
                                $item['major_name'] .= ",{$jobAddMajor}";
                                $item['amount']     = '1~2';
                                $newList1[]         = $item;
                            }
                        }
                    }

                    $tmpIds = array_column($newList1, 'id');
                    foreach ($data['data'] as $item) {
                        if (!in_array($item['id'], $tmpIds)) {
                            $item['major_name'] .= ",{$jobAddMajor}";
                            $item['amount']     = '1~2';
                            $newList2[]         = $item;
                        }
                    }

                    $newList = [
                        ...$newList1,
                        ...$newList2,
                    ];

                    $data['data'] = $newList;
                    break;
                default:
                    break;
            }
        }

        if ($announcementId == 289744) {
            //   需求专业字段信息，统一展示为“详见招聘简章”
            //   报名方式字段信息，统一展示为“详见正文”
            //   学历字段信息，统一展示为“详见招聘简章”
            foreach ($data['list'] as &$item) {
                $item['major'] = '详见招聘简章';
                // $item['education'] = '详见招聘简章';
            }

            foreach ($data['data'] as &$item) {
                $item['major_name'] = '详见招聘简章';
                // $item['education']      = '详见招聘简章';
                // $item['education_name'] = '详见招聘简章';
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1073

        /**
         * 需求描述
         * 公告ID：301480
         *
         * 公告：事业编制，电子科技大学2025年专职辅导员招聘公告https://www.gaoxiaojob.com/announcement/detail/301480.html
         *
         * PC端+H5端+小程序端的公告页+职位页+单位页，均需按以下需求修改：
         *
         * 1.“招15人”改为“招不超过15人”
         *
         * 2.需求学科改为：“马克思主义理论、心理学或学校其他学科专业背景”
         */

        if ($announcementId == 301480) {
            switch (PLATFORM) {
                case 'H5':
                    foreach ($data['list'] as &$item) {
                        $item['amount'] = '不超过15';
                        $item['major']  = '马克思主义理论、心理学或学校其他学科专业背景';
                    }
                    break;
                case 'PC':
                    foreach ($data['list'] as &$item) {
                        $item['amount'] = '招不超过15人';
                        $item['major']  = '马克思主义理论、心理学或学校其他学科专业背景';
                    }
                    break;
                case 'MINI':
                    foreach ($data['data'] as &$item) {
                        $item['amount']     = '不超过15';
                        $item['major_name'] = '马克思主义理论、心理学或学校其他学科专业背景';
                    }
                    break;
                default:
                    break;
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1185
        // 公告ID：316039
        //
        // 公告：南京城市职业学院（南京开放大学）2025年公开招聘高层次人才公告（第一批）https://www.gaoxiaojob.com/announcement/detail/316039.html

        /**
         * 2.职位列表、职位详情页、单位主页：各个职位的学科展示见下表
         *
         * 职位名称
         *
         * 学科
         *
         * 职位ID
         *
         * 教学质量管理与研究
         *
         * 教育类，计算机类
         *
         * 31635167
         *
         * 产品艺术设计专业教师
         *
         * 设计学（产品设计方向），艺术设计（产品设计方向）
         *
         * 31635159
         *
         * 健康管理专业教师
         *
         * 公共卫生类，医学类
         *
         * 31635151
         *
         * 实验实训员
         *
         * 计算机类
         *
         * 31635166
         *
         * 数字媒体艺术设计专业教师
         *
         * 设计学（数字媒体艺术方向），艺术设计（数字媒体艺术方向）
         *
         * 31635158
         *
         * 专业带头人
         *
         * 公共管理类，医学类，电子信息类，交通工程类，机械工程类，计算机类，艺术类，工商管理类，经济类，商务贸易类
         *
         * 31635150
         *
         * 体育教师
         *
         * 体育教育训练学，体育教学，运动训练，体育人文社会学
         *
         * 31635165
         *
         * 视觉传达设计专业教师
         *
         * 设计艺术学（平面设计方向）
         *
         * 31635157
         *
         * 思政教师2
         *
         * 马克思主义哲学，马克思主义基本原理，马克思主义理论，马克思主义中国化研究，思想政治教育
         *
         * 31635164
         *
         * 传播与策划专业教师
         *
         * 电影学，广播电视艺术学
         *
         * 31635156
         *
         * 云计算技术应用专业教师
         *
         * 网络空间安全，网络与信息安全，信息安全，大数据技术与工程，人工智能，通信工程（含宽带网络，移动通信等），信息与通信工程，通信与信息系统
         *
         * 31635155
         *
         * 思政教师1
         *
         * 马克思主义哲学，马克思主义基本原理，马克思主义理论，马克思主义中国化研究，思想政治教育
         *
         * 31635163
         *
         * 跨境电子商务专业教师
         *
         * 经济类，管理科学与工程
         *
         * 31635162
         *
         * 软件技术专业教师
         *
         * 计算机（软件）类
         *
         * 31635154
         *
         * 商务数据分析专业教师
         *
         * 统计类
         *
         * 31635161
         *
         * 智能网联汽车技术专业教师
         *
         * 电子信息类，交通工程类，机械工程类
         *
         * 31635153
         *
         * 智慧健康养老服务与管理专业教师
         *
         * 公共卫生类，医学类，计算机类，电子信息类
         *
         * 31635152
         *
         * 环境艺术设计专任教师
         *
         * 设计学(环境设计方向），艺术设计(环境设计方向）
         *
         * 31635160
         */
        if ($announcementId == 316039) {
            $majorConfig = [
                1635167 => "教育类，计算机类",
                1635159 => "设计学（产品设计方向），艺术设计（产品设计方向）",
                1635151 => "公共卫生类，医学类",
                1635166 => "计算机类",
                1635158 => "设计学（数字媒体艺术方向），艺术设计（数字媒体艺术方向）",
                1635150 => "公共管理类，医学类，电子信息类，交通工程类，机械工程类，计算机类，艺术类，工商管理类，经济类，商务贸易类",
                1635165 => "体育教育训练学，体育教学，运动训练，体育人文社会学",
                1635157 => "设计艺术学（平面设计方向）",
                1635164 => "马克思主义哲学，马克思主义基本原理，马克思主义理论，马克思主义中国化研究，思想政治教育",
                1635156 => "电影学，广播电视艺术学",
                1635155 => "网络空间安全，网络与信息安全，信息安全，大数据技术与工程，人工智能，通信工程（含宽带网络，移动通信等），信息与通信工程，通信与信息系统",
                1635163 => "马克思主义哲学，马克思主义基本原理，马克思主义理论，马克思主义中国化研究，思想政治教育",
                1635162 => "经济类，管理科学与工程",
                1635154 => "计算机（软件）类",
                1635161 => "统计类",
                1635153 => "电子信息类，交通工程类，机械工程类",
                1635152 => "公共卫生类，医学类，计算机类，电子信息类",
                1635160 => "设计学(环境设计方向），艺术设计(环境设计方向）",
            ];
            // 正式进入逻辑，拿专业信息
            foreach ($data['list'] as &$item) {
                $item['major'] = $majorConfig[$item['jobId']];
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1190&version=0&param=0&storyType=story

        /**
         * 职位ID：1645988
         *
         * 职位名称及链接：国际交流处负责人https://www.gaoxiaojob.com/job/detail/1645988.html
         *
         * 修改内容：PC端、H5端、小程序端口职位详情页、职位所属公告的职位列表、单位主页-职位列表 的职位类型、需求专业，修改为指定文字。
         *
         * 职位类型修改为：行政管理
         *
         * 需求专业修改为：国际关系、教育管理、外语类等相关专业
         *
         * 学历要求：硕士及以上学历
         */
        if ($announcementId == 289323) {
            $majorConfig     = [
                1645988 => "国际关系、教育管理、外语类等相关专业",
            ];
            $educationConfig = [
                1645988 => "硕士及以上学历",
            ];

            // 正式进入逻辑，拿专业信息
            foreach ($data['list'] as &$item) {
                if ($majorConfig[$item['jobId']]) {
                    $item['major'] = $majorConfig[$item['jobId']];
                }

                if ($educationConfig[$item['jobId']]) {
                    $item['education'] = $educationConfig[$item['jobId']];
                }
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&id=1203

        /**
         * 需求描述
         * 共计需要修改4个职位
         *
         * 修改内容：PC端、H5端、小程序端口职位详情页、职位所属公告的职位列表、单位主页-职位列表 的职位类型、学历要求、需求专业，修改为指定文字。
         *
         * 请6月18日下午3点前处理完毕，谢谢
         *
         * 职位ID
         *
         * 职位类型
         *
         * 学历要求
         *
         * 需求专业
         *
         * 1682494
         *
         * 教学科研岗
         *
         * 硕士研究生及以上
         *
         * 英语专业毕业，应用语言学、文学、翻译、商务英语等相关专业；第一学历为全日制本科，本硕专业一致或相近
         *
         * 1682495
         *
         * 教学科研岗
         *
         * 硕士研究生及以上
         *
         * 数学专业毕业，统计学等数学相关专业；第一学历为全日制本科，本硕专业一致或相近
         *
         * 1682496
         *
         * 教学科研岗
         *
         * 硕士研究生及以上
         *
         * 体育教育、运动训练等体育学类专业；第一学历为全日制本科，本硕专业一致或相近
         *
         * 1682497
         *
         * 教学科研岗
         *
         * 硕士研究生及以上
         *
         * 计算机科学与技术、软件工程、数据科学与大数据技术、人工智能、数字媒体技术、物联网工程、虚拟现实与增强现实等相关专业
         *
         * 公告id 289323
         *
         * 按以上要求新增处理一个职位
         * 职位ID：1682498
         * 职位类型：教学科研岗
         * 学历要求：硕士研究生及以上
         * 需求专业：档案学、信息资源管理等相关专业
         */
        if ($announcementId == 289323) {
            $majorConfig     = [
                1682494 => "英语专业毕业，应用语言学、文学、翻译、商务英语等相关专业；第一学历为全日制本科，本硕专业一致或相近",
                1682495 => "数学专业毕业，统计学等数学相关专业；第一学历为全日制本科，本硕专业一致或相近",
                1682496 => "体育教育、运动训练等体育学类专业；第一学历为全日制本科，本硕专业一致或相近",
                1682497 => "计算机科学与技术、软件工程、数据科学与大数据技术、人工智能、数字媒体技术、物联网工程、虚拟现实与增强现实等相关专业",
                1682498 => "档案学、信息资源管理等相关专业",
            ];
            $educationConfig = [
                1682494 => "硕士研究生及以上",
                1682495 => "硕士研究生及以上",
                1682496 => "硕士研究生及以上",
                1682497 => "硕士研究生及以上",
                1682498 => "硕士研究生及以上",
            ];

            // 正式进入逻辑，拿专业信息
            foreach ($data['list'] as &$item) {
                if ($majorConfig[$item['jobId']]) {
                    $item['major'] = $majorConfig[$item['jobId']];
                }

                if ($educationConfig[$item['jobId']]) {
                    $item['education'] = $educationConfig[$item['jobId']];
                }
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1191
        // 公告ID：325258
        // 公告标题及链接：上海商学院2025年公开招聘商务信息学院院长公告https://www.gaoxiaojob.com/announcement/detail/325258.html
        // 修改内容：PC端、H5端、小程序端口公告详情页、职位详情页、职位所属公告的职位列表、单位主页-职位列表 的需求专业，修改为指定文字。
        // 需求专业：管理科学与工程、计算机科学与技术、电子信息、智能科学与技术
        if ($announcementId == 325258) {
            // 正式进入逻辑，拿专业信息
            // 处理不同数据结构
            if (isset($data['list']) && is_array($data['list'])) {
                // H5端和PC端使用list字段
                foreach ($data['list'] as &$item) {
                    $item['major'] = '管理科学与工程、计算机科学与技术、电子信息、智能科学与技术';
                }
            }
            if (isset($data['data']) && is_array($data['data'])) {
                // 小程序端使用data字段
                foreach ($data['data'] as &$item) {
                    $item['major_name'] = '管理科学与工程、计算机科学与技术、电子信息、智能科学与技术';
                }
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1212
        // 公告ID：289323，单位ID：80360，长沙科技学院特殊修改需求 - 公告职位列表
        // 修改内容：PC端、H5端、小程序端口职位详情页、职位所属公告的职位列表、单位主页-职位列表 的职位类型、学历要求、职称要求、需求专业
        if ($announcementId == 289323) {
            // 长沙科技学院14个职位的特殊配置
            // 注意：部分职位的职称要求列为空白，按需求"表格空白处保留原样"不修改学历要求
            $changShaJobConfig = [
                1700793 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '智能装备与系统、轨道交通信号与控制、能源与动力工程等相关专业',
                ],
                1700800 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '网络与新媒体、新闻学、广播电视学、广告学、编辑出版学等相关专业',
                ],
                1700792 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '智能交互设计、机械电子工程、机器人工程等相关专业',
                ],
                1700799 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '跨境电子商务、电子商务及法律、电子商务等相关专业',
                ],
                1700791 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '智能制造工程、机械设计制造及自动化、车辆工程等相关专业',
                ],
                1700798 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '供应链管理、物流管理、物流工程、采购管理等相关专业',
                ],
                1700790 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '博士或教授以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '轨道交通电气与控制、智能交通技术、轮机工程等相关专业',
                ],
                1700797 => [
                    'jobCategory' => '教学科研岗',
                    // 'education'   => '', // 学历要求列为空，不修改
                    'title'       => '教授',
                    // 职称要求修改为教授
                    'major'       => '经济学、国际经济与贸易、财政学、金融学等相关专业',
                ],
                1700789 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '博士或教授以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '智慧交通、交通工程、交通运输等相关专业',
                ],
                1700796 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '博士或教授以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '物联网工程、智能科学与技术、人工智能等相关专业',
                ],
                1700788 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '教授、副教授、博士及以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '电气工程及其自动化、智能电网信息工程、电气工程与智能控制等相关专业',
                ],
                1700787 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '教授、副教授、博士及以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '储能科学与工程、能源与动力工程、新能源科学与工程等相关专业',
                ],
                1700795 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '博士或教授以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '数字媒体技术、计算机科学与技术、软件工程、智能科学与技术、电子信息工程等相关专业',
                ],
                1700794 => [
                    'jobCategory' => '教学科研岗',
                    'education'   => '博士或教授以上',
                    // 'title'       => '', // 职称要求不修改
                    'major'       => '人工智能、电子信息工程、通信工程、自动化、机器人工程、计算机科学与技术等相关专业',
                ],
            ];

            // 处理不同数据结构
            if (isset($data['list']) && is_array($data['list'])) {
                // H5端和PC端使用list字段
                foreach ($data['list'] as &$item) {
                    $jobId = isset($item['jobId']) ? $item['jobId'] : (isset($item['id']) ? $item['id'] : null);
                    if ($jobId && isset($changShaJobConfig[$jobId])) {
                        $config = $changShaJobConfig[$jobId];
                        if (isset($config['education'])) {
                            $item['education'] = $config['education'];
                        }
                        if (isset($config['title'])) {
                            $item['title'] = $config['title'];
                        }
                        if (isset($config['major'])) {
                            $item['major'] = $config['major'];
                        }
                        if (isset($config['jobCategory'])) {
                            $item['jobCategory'] = $config['jobCategory'];
                        }
                    }
                }
            }
            if (isset($data['data']) && is_array($data['data'])) {
                // 小程序端使用data字段
                foreach ($data['data'] as &$item) {
                    $jobId = isset($item['job_id']) ? $item['job_id'] : (isset($item['id']) ? $item['id'] : null);
                    if ($jobId && isset($changShaJobConfig[$jobId])) {
                        $config = $changShaJobConfig[$jobId];
                        if (isset($config['education'])) {
                            $item['education_name'] = $config['education'];
                        }
                        if (isset($config['title'])) {
                            $item['title_name'] = $config['title'];
                        }
                        if (isset($config['major'])) {
                            $item['major_name'] = $config['major'];
                        }
                        if (isset($config['jobCategory'])) {
                            $item['job_category_name'] = $config['jobCategory'];
                        }
                    }
                }
            }
        }

        return $data;
    }

    public function handelDetailInfo($data, $announcementId)
    {
        // 优先使用新的配置系统
        $data = $this->applyDetailInfoConfigs($data, $announcementId);

        // 向后兼容：如果新系统未启用或无配置，使用原有逻辑
        // https://zentao.jugaocai.com/index.php?m=story&f=view&id=944
        if ($announcementId == $this->config['944']['announcementId']) {
            switch (PLATFORM) {
                case 'H5':
                    $data['jobRecruitAmount'] = '3~5';
                    $data['majorName']        .= ",{$this->config['944']['jobAddMajor']}";
                    $jobRanking               = $this->config['944']['jobRanking'];
                    $newList1                 = [];
                    $newList2                 = [];

                    foreach ($jobRanking as $jobId) {
                        foreach ($data['jobList'] as $item) {
                            if ($item['jobId'] == $jobId) {
                                $item['major'] .= ",{$this->config['944']['jobAddMajor']}";
                                // 人数
                                $item['amount'] = '1~2';
                                $newList1[]     = $item;
                            }
                        }
                    }

                    $tmpIds = array_column($newList1, 'jobId');
                    foreach ($data['jobList'] as $item) {
                        if (!in_array($item['jobId'], $tmpIds)) {
                            $item['major'] .= ",{$this->config['944']['jobAddMajor']}";
                            // 人数
                            $item['amount'] = '1~2';
                            $newList2[]     = $item;
                        }
                    }

                    $newList         = [
                        ...$newList1,
                        ...$newList2,
                    ];
                    $data['jobList'] = $newList;
                    break;
                case 'PC':
                    $data['jobRecruitAmount'] = '3~5';
                    break;
                case 'MINI':
                    $data['major_name']                  .= ",{$this->config['944']['jobAddMajor']}";
                    $data['announcement_recruit_amount'] = '3~5';
                    break;
                default:
                    break;
            }
        }

        if ($announcementId == 271935) {
            if ($data['applyTypeText'] === '网上系统') {
                $data['applyTypeText'] = '网上报名';
            }

            if ($data['apply_type_text'] === '网上系统') {
                $data['apply_type_text'] = '网上报名';
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1042
        if ($announcementId == 289744) {
            // 需求专业字段信息，统一展示为“详见招聘简章”
            if (PLATFORM === "PC") {
                $data['majorName'] = [
                    [
                        'major' => '详见招聘简章',
                        'url'   => '#',
                    ],
                ];
                // 报名方式字段信息，统一展示为“详见正文”
                $data['applyTypeText'] = '详见正文';
                // 学历字段信息，统一展示为“详见招聘简章”
                $data['minEducation'] = '详见招聘简章';
            }

            if (PLATFORM === "H5") {
                $data['majorName']     = '详见招聘简章';
                $data['applyTypeText'] = '详见正文';
                $data['educationText'] = '详见招聘简章';
                foreach ($data['jobList'] as &$datum) {
                    $datum['education'] = '详见招聘简章';
                }
            }

            if (PLATFORM === "MINI") {
                $data['major_name']       = '详见招聘简章';
                $data['apply_type_text']  = '详见正文';
                $data['minEducationName'] = '详见招聘简章';
                $data['educationName']    = '详见招聘简章';
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1073

        /**
         * 需求描述
         * 公告ID：301480
         *
         * 公告：事业编制，电子科技大学2025年专职辅导员招聘公告https://www.gaoxiaojob.com/announcement/detail/301480.html
         *
         * PC端+H5端+小程序端的公告页+职位页+单位页，均需按以下需求修改：
         *
         * 1.“招15人”改为“招不超过15人”
         *
         * 2.需求学科改为：“马克思主义理论、心理学或学校其他学科专业背景”
         */
        if ($announcementId == 301480) {
            switch (PLATFORM) {
                case 'H5':
                    $data['jobRecruitAmount'] = '不超过15';
                    $data['majorName']        = '马克思主义理论、心理学或学校其他学科专业背景';
                    // $data['jobList']
                    foreach ($data['jobList'] as &$item) {
                        $item['major']  = '马克思主义理论、心理学或学校其他学科专业背景';
                        $item['amount'] = '不超过15';
                    }
                    break;
                case 'PC':
                    $data['jobRecruitAmount'] = '不超过15';

                    $data['majorName'] = [
                        [
                            'major' => '马克思主义理论、心理学或学校其他学科专业背景',
                            'url'   => '#',
                        ],
                    ];
                    break;
                case 'MINI':
                    $data['announcement_recruit_amount'] = '不超过15';
                    $data['major_name']                  = '马克思主义理论、心理学或学校其他学科专业背景';
                    break;
                default:
                    break;
            }
        }

        if ($announcementId == 306054) {
            $major = "哲学、经济学、法学、教育学、文学、历史学、管理学、艺术学、心理学、社会学、基础医学、临床医学、口腔医学、公共卫生与预防医学、英语、体育学、电子科学技术、信息与通信工程、计算机科学技术、护理学、中医学、中西医结合、药学、中药学、医学技术、健康服务与管理、养老服务与管理、思想政治教育等专业";
            // 需求专业字段信息，统一展示为“详见招聘简章”
            if (PLATFORM === "PC") {
                $data['majorName'] = [
                    [
                        'major' => $major,
                        'url'   => '#',
                    ],
                ];
            }
            if (PLATFORM === "H5") {
                $data['majorName'] = $major;
            }
            if (PLATFORM === "MINI") {
                $data['major_name'] = $major;
            }
        }

        if ($announcementId == 313171) {
            $major = "文史类、管理类相关专业优先";
            // 需求专业字段信息，统一展示为“详见招聘简章”
            if (PLATFORM === "PC") {
                $data['majorName'] = [
                    [
                        'major' => $major,
                        'url'   => '#',
                    ],
                ];
            }
            if (PLATFORM === "H5") {
                $data['majorName'] = $major;
            }
            if (PLATFORM === "MINI") {
                $data['major_name'] = $major;
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&id=1185
        // 316039
        // PC端+H5端+小程序端的公告页+职位页+单位页，均需按以下需求修改：
        //
        // 1.公告详情页：
        //
        // 需求专业（供参考）：公共管理类、医学类、电子信息类、交通工程类、机械工程类、计算机类、艺术类、工商管理类、经济类、商务贸易类、公共卫生类、计算机（软件）类、网络空间安全、网络与信息安全、信息安全、大数据技术与工程、人工智能、通信工程（含宽带网络、移动通信等）、信息与通信工程、通信与信息系统、电影学、广播电视艺术学、设计艺术学（平面设计方向）、设计学（数字媒体艺术方向）、艺术设计（数字媒体艺术方向）、设计学（产品设计方向）、艺术设计（产品设计方向）、设计学(环境设计方向）、艺术设计(环境设计方向）、统计类、管理科学与工程、马克思主义哲学、马克思主义基本原理、马克思主义理论、马克思主义中国化研究、思想政治教育、体育教育训练学、体育教学、运动训练、体育人文社会学、教育类
        if ($announcementId == 316039) {
            $major = "公共管理类、医学类、电子信息类、交通工程类、机械工程类、计算机类、艺术类、工商管理类、经济类、商务贸易类、公共卫生类、计算机（软件）类、网络空间安全、网络与信息安全、信息安全、大数据技术与工程、人工智能、通信工程（含宽带网络、移动通信等）、信息与通信工程、通信与信息系统、电影学、广播电视艺术学、设计艺术学（平面设计方向）、设计学（数字媒体艺术方向）、艺术设计（数字媒体艺术方向）、设计学（产品设计方向）、艺术设计（产品设计方向）、设计学(环境设计方向）、艺术设计(环境设计方向）、统计类、管理科学与工程、马克思主义哲学、马克思主义基本原理、马克思主义理论、马克思主义中国化研究、思想政治教育、体育教育训练学、体育教学、运动训练、体育人文社会学、教育类";
            // 需求专业字段信息，统一展示为“详见招聘简章”
            if (PLATFORM === "PC") {
                $data['majorName'] = [
                    [
                        'major' => $major,
                        'url'   => '#',
                    ],
                ];
            }
            if (PLATFORM === "H5") {
                $data['majorName'] = $major;
            }
            if (PLATFORM === "MINI") {
                $data['major_name'] = $major;
            }
        }

        if ($announcementId == 289323) {
            $educationConfig = [
                1645988 => "硕士及以上学历",
            ];

            // 正式进入逻辑，拿教育
            foreach ($data['list'] as &$item) {
                if ($educationConfig[$item['jobId']]) {
                    $item['education'] = $educationConfig[$item['jobId']];
                }
            }
        }

        // https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=1191
        // 公告ID：325258
        // 公告标题及链接：上海商学院2025年公开招聘商务信息学院院长公告https://www.gaoxiaojob.com/announcement/detail/325258.html
        // 修改内容：PC端、H5端、小程序端口公告详情页、职位详情页、职位所属公告的职位列表、单位主页-职位列表 的需求专业，修改为指定文字。
        // 需求专业：管理科学与工程、计算机科学与技术、电子信息、智能科学与技术
        if ($announcementId == 325258) {
            switch (PLATFORM) {
                case 'H5':
                    $data['majorName'] = '管理科学与工程、计算机科学与技术、电子信息、智能科学与技术';
                    // 处理公告详情页中的职位列表
                    if (isset($data['jobList']) && is_array($data['jobList'])) {
                        foreach ($data['jobList'] as &$item) {
                            $item['major'] = '管理科学与工程、计算机科学与技术、电子信息、智能科学与技术';
                        }
                    }
                    break;
                case 'PC':
                    $data['majorName'] = [
                        [
                            'major' => '管理科学与工程、计算机科学与技术、电子信息、智能科学与技术',
                            'url'   => '#',
                        ],
                    ];
                    // 处理公告详情页中的职位列表
                    if (isset($data['jobList']) && is_array($data['jobList'])) {
                        foreach ($data['jobList'] as &$item) {
                            $item['major'] = '管理科学与工程、计算机科学与技术、电子信息、智能科学与技术';
                        }
                    }
                    break;
                case 'MINI':
                    $data['major_name'] = '管理科学与工程、计算机科学与技术、电子信息、智能科学与技术';
                    // 处理公告详情页中的职位列表
                    if (isset($data['jobList']) && is_array($data['jobList'])) {
                        foreach ($data['jobList'] as &$item) {
                            $item['major'] = '管理科学与工程、计算机科学与技术、电子信息、智能科学与技术';
                        }
                    }
                    break;
                default:
                    break;
            }
        }

        return $data;
    }

    /**
     * 应用职位列表配置（支持优先级：职位配置 > 公告配置 > 单位配置）
     * @param array $data
     * @param int   $announcementId
     * @return array
     */
    protected function applyJobListConfigs($data, $announcementId)
    {
        // 检查新系统是否启用
        if (!BaseSpecialNeedConfig::isEnabled()) {
            return $data;
        }

        // 获取当前平台
        $platform = defined('PLATFORM') ? PLATFORM : 'PC';

        // 获取单位ID（从数据中提取或通过公告ID查询）
        $companyId = $this->extractCompanyId($data, $announcementId);

        // 获取各级别的配置
        $companyConfigs = [];
        if ($companyId) {
            $companyConfigs = BaseSpecialNeedConfig::getConfigs(BaseSpecialNeedConfig::TYPE_COMPANY, $companyId,
                $platform);
        }

        $announcementConfigs = BaseSpecialNeedConfig::getConfigs(BaseSpecialNeedConfig::TYPE_ANNOUNCEMENT,
            $announcementId, $platform);

        // 应用配置到职位列表
        if (isset($data['list']) && is_array($data['list'])) {
            foreach ($data['list'] as &$item) {
                // 1. 先应用单位级别配置（最低优先级）
                if (!empty($companyConfigs)) {
                    $item = BaseSpecialNeedConfig::applyConfigs($item, $companyConfigs,
                        BaseSpecialNeedConfig::TYPE_COMPANY);
                }

                // 2. 再应用公告级别配置（中等优先级）
                if (!empty($announcementConfigs)) {
                    $item = BaseSpecialNeedConfig::applyConfigs($item, $announcementConfigs,
                        BaseSpecialNeedConfig::TYPE_ANNOUNCEMENT);
                }

                // 3. 最后应用职位级别配置（最高优先级，会覆盖前面的配置）
                $jobId = $item['jobId'] ?? $item['id'] ?? null;
                if ($jobId) {
                    $jobConfigs = BaseSpecialNeedConfig::getConfigs(BaseSpecialNeedConfig::TYPE_JOB, $jobId, $platform);
                    if (!empty($jobConfigs)) {
                        $item = BaseSpecialNeedConfig::applyConfigs($item, $jobConfigs,
                            BaseSpecialNeedConfig::TYPE_JOB);
                    }
                }
            }
        }

        // 应用配置到data字段（小程序等使用）
        if (isset($data['data']) && is_array($data['data'])) {
            foreach ($data['data'] as &$item) {
                // 1. 先应用单位级别配置（最低优先级）
                if (!empty($companyConfigs)) {
                    $item = BaseSpecialNeedConfig::applyConfigs($item, $companyConfigs,
                        BaseSpecialNeedConfig::TYPE_COMPANY);
                }

                // 2. 再应用公告级别配置（中等优先级）
                if (!empty($announcementConfigs)) {
                    $item = BaseSpecialNeedConfig::applyConfigs($item, $announcementConfigs,
                        BaseSpecialNeedConfig::TYPE_ANNOUNCEMENT);
                }

                // 3. 最后应用职位级别配置（最高优先级，会覆盖前面的配置）
                $jobId = $item['id'] ?? $item['jobId'] ?? null;
                if ($jobId) {
                    $jobConfigs = BaseSpecialNeedConfig::getConfigs(BaseSpecialNeedConfig::TYPE_JOB, $jobId, $platform);
                    if (!empty($jobConfigs)) {
                        $item = BaseSpecialNeedConfig::applyConfigs($item, $jobConfigs,
                            BaseSpecialNeedConfig::TYPE_JOB);
                    }
                }
            }
        }

        // 应用配置到公告详情本身（按优先级顺序）
        // 1. 先应用单位级别配置（最低优先级）
        if (!empty($companyConfigs)) {
            $data = BaseSpecialNeedConfig::applyConfigs($data, $companyConfigs, BaseSpecialNeedConfig::TYPE_COMPANY);
        }

        // 2. 再应用公告级别配置（高优先级，会覆盖单位配置）
        if (!empty($announcementConfigs)) {
            $data = BaseSpecialNeedConfig::applyConfigs($data, $announcementConfigs,
                BaseSpecialNeedConfig::TYPE_ANNOUNCEMENT);
        }

        return $data;
    }

    /**
     * 应用详情信息配置（支持优先级：公告配置 > 单位配置）
     * @param array $data
     * @param int   $announcementId
     * @return array
     */
    protected function applyDetailInfoConfigs($data, $announcementId)
    {
        // 检查新系统是否启用
        if (!BaseSpecialNeedConfig::isEnabled()) {
            return $data;
        }

        // 获取当前平台
        $platform = defined('PLATFORM') ? PLATFORM : 'PC';

        // 获取单位ID
        $companyId = $this->extractCompanyId($data, $announcementId);

        // 获取各级别的配置
        $companyConfigs = [];
        if ($companyId) {
            $companyConfigs = BaseSpecialNeedConfig::getConfigs(BaseSpecialNeedConfig::TYPE_COMPANY, $companyId,
                $platform);
        }

        $announcementConfigs = BaseSpecialNeedConfig::getConfigs(BaseSpecialNeedConfig::TYPE_ANNOUNCEMENT,
            $announcementId, $platform);

        // 处理公告详情页中的职位列表（jobList）
        if (isset($data['jobList']) && is_array($data['jobList'])) {
            foreach ($data['jobList'] as &$item) {
                // 1. 先应用单位级别配置（最低优先级）
                if (!empty($companyConfigs)) {
                    $item = BaseSpecialNeedConfig::applyConfigs($item, $companyConfigs,
                        BaseSpecialNeedConfig::TYPE_COMPANY);
                }

                // 2. 再应用公告级别配置（中等优先级）
                if (!empty($announcementConfigs)) {
                    $item = BaseSpecialNeedConfig::applyConfigs($item, $announcementConfigs,
                        BaseSpecialNeedConfig::TYPE_ANNOUNCEMENT);
                }

                // 3. 最后应用职位级别配置（最高优先级，会覆盖前面的配置）
                $jobId = $item['id'] ?? $item['jobId'] ?? null;
                if ($jobId) {
                    $jobConfigs = BaseSpecialNeedConfig::getConfigs(BaseSpecialNeedConfig::TYPE_JOB, $jobId, $platform);
                    if (!empty($jobConfigs)) {
                        $item = BaseSpecialNeedConfig::applyConfigs($item, $jobConfigs,
                            BaseSpecialNeedConfig::TYPE_JOB);
                    }
                }
            }
        }

        // 1. 先应用单位级别配置到公告详情本身（低优先级）
        if (!empty($companyConfigs)) {
            $data = BaseSpecialNeedConfig::applyConfigs($data, $companyConfigs,
                BaseSpecialNeedConfig::TYPE_COMPANY);
        }

        // 2. 再应用公告级别配置到公告详情本身（高优先级，会覆盖单位配置）
        if (!empty($announcementConfigs)) {
            $data = BaseSpecialNeedConfig::applyConfigs($data, $announcementConfigs,
                BaseSpecialNeedConfig::TYPE_ANNOUNCEMENT);
        }

        return $data;
    }

    /**
     * 提取单位ID
     * @param array $data
     * @param int   $announcementId
     * @return int
     */
    protected function extractCompanyId($data, $announcementId)
    {
        // 1. 优先从数据中直接获取
        $companyId = $data['companyId'] ?? $data['company_id'] ?? 0;
        if ($companyId) {
            return $companyId;
        }

        // 2. 如果数据中没有，通过公告ID查询
        return BaseAnnouncement::findOneVal(['id' => $announcementId], 'company_id') ?: 0;
    }

}
