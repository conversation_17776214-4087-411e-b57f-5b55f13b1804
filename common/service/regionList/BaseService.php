<?php

namespace common\service\regionList;

use common\base\models\BaseArticleAttribute;
use common\libs\Cache;

class BaseService
{
    const TYPE_JOB          = 1;
    const TYPE_ANNOUNCEMENT = 2;
    const TYPE_COMPANY      = 3;

    // 持续的天数
    const LAST_DAY = 15;

    // 首选公告属性
    const ATT_ANNOUNCEMENT_LIST = [
        BaseArticleAttribute::ATTRIBUTE_HOME_TOP,
        BaseArticleAttribute::ATTRIBUTE_COLUMN_TOP,
    ];

    const BASE_CACHE_KEY = Cache::MINI_REGION_LIST_KEY;

    const LIST_MAX_NUM = 20;

    public $type;
    public $areaId;

    public function getCacheKey()
    {
        switch ($this->type) {
            case self::TYPE_JOB:
                $key = self::BASE_CACHE_KEY . ':JOB' . ':' . $this->areaId;
                break;
            case self::TYPE_ANNOUNCEMENT:
                $key = self::BASE_CACHE_KEY . ':ANNOUNCEMENT' . ':' . $this->areaId;
                break;
            case self::TYPE_COMPANY:
                $key = self::BASE_CACHE_KEY . ':COMPANY' . ':' . $this->areaId;
                break;
            default:
                throw new \Exception("类型错误");
        }

        return $key;
    }
}
