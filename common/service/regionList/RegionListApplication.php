<?php

namespace common\service\regionList;

/**
 * 地区榜单,分为职位\公告\单位
 */
class RegionListApplication
{
    static $instance;

    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    // 设置地区榜单(职位)
    public function setJob($areaId)
    {
        return (new SetService())->job($areaId);
    }

    // 设置地区榜单(公告)
    public function setAnnouncement($areaId)
    {
        return (new SetService())->announcement($areaId);
    }

    // 设置地区榜单(单位)
    public function setCompany($areaId)
    {
        return (new SetService())->company($areaId);
    }

    public function getHostList($areaId, $type)
    {
        return (new GetService())->getList($areaId, $type);
    }

}
