<?php

namespace common\service\qiniu;

use common\base\models\BaseFile;
use common\base\models\BaseQiniuImageAsset;
use common\base\models\BaseQiniuImageAssetTagRel;
use common\base\models\BaseQiniuImageTag;
use common\base\models\BaseQiniuImageUploadLog;
use common\base\models\BaseUploadForm;
use common\base\models\BaseUser;
use common\helpers\FileHelper;
use common\helpers\TimeHelper;
use common\helpers\ValidateHelper;
use common\service\CommonService;
use yii\base\Exception;

/**
 * 七牛图片管理业务服务层七牛图片管理
 * 负责上传、标签处理、日志记录等复合业务逻辑
 */
class QiniuImageService extends CommonService
{
    /** 单次上传允许的最大图片体积（18MB） */
    private const MAX_IMAGE_SIZE = 18 * 1024 * 1024;

    /** 单张图片允许的标签数量上限 */
    private const MAX_TAG_COUNT = 3;

    /** 七牛存储前缀 */
    private const UPLOAD_BASE_DIR = 'uploads/position_image';

    /**
     * 获取专题图片列表（带标签）
     * @param array $params 查询参数
     * @return array
     * @throws Exception
     */
    public function getList(array $params): array
    {
        // 统一约束分页参数，避免异常值造成查询压力
        $page     = max(1, (int)($params['page'] ?? 1));
        $pageSize = max(1, min(100, (int)($params['pageSize'] ?? 20)));

        $query = BaseQiniuImageAsset::find()
            ->where(['status' => BaseQiniuImageAsset::STATUS_ACTIVE]);

        // keyword 关键字搜索：同时匹配学校名称、图片编码和备注
        if (!empty($params['keyword'])) {
            $keyword = trim($params['keyword']);
            $query->andWhere([
                'or',
                [
                    'like',
                    'school_name',
                    $keyword,
                ],
                [
                    'like',
                    'asset_code',
                    $keyword,
                ],
                [
                    'like',
                    'remark',
                    $keyword,
                ],
            ]);
        }

        if (!empty($params['assetCode'])) {
            $query->andWhere([
                'like',
                'asset_code',
                trim($params['assetCode']),
            ]);
        }
        if (!empty($params['schoolName'])) {
            $query->andWhere([
                'like',
                'school_name',
                trim($params['schoolName']),
            ]);
        }
        if (!empty($params['adminName'])) {
            $query->andWhere([
                'like',
                'admin_name',
                trim($params['adminName']),
            ]);
        }
        if (!empty($params['remark'])) {
            $query->andWhere([
                'like',
                'remark',
                trim($params['remark']),
            ]);
        }

        // 按标签ID筛选：需要关联查询 qiniu_image_asset_tag_rel 表
        if (!empty($params['tagIds']) && is_array($params['tagIds'])) {
            $tagIds = array_filter(array_map('intval', $params['tagIds']));
            if ($tagIds) {
                $query->andWhere([
                    'id' => BaseQiniuImageAssetTagRel::find()
                        ->select('asset_id')
                        ->where([
                            'tag_id' => $tagIds,
                            'status' => BaseQiniuImageAssetTagRel::STATUS_ACTIVE,
                        ])
                        ->distinct(),
                ]);
            }
        }

        if (!empty($params['dateStart'])) {
            $query->andWhere([
                '>=',
                'add_time',
                $params['dateStart'] . ' 00:00:00',
            ]);
        }
        if (!empty($params['dateEnd'])) {
            $query->andWhere([
                '<=',
                'add_time',
                $params['dateEnd'] . ' 23:59:59',
            ]);
        }

        // 提前复制查询对象计算总数
        $total  = (clone $query)->count();
        $offset = ($page - 1) * $pageSize;

        // 排序处理（默认按ID倒序）
        $orderBy = ['id' => SORT_DESC];
        if (!empty($params['sortField']) && !empty($params['sortOrder'])) {
            $sortField = $params['sortField'];
            $sortOrder = strtoupper($params['sortOrder']) === 'ASC' ? SORT_ASC : SORT_DESC;

            // 允许的排序字段
            $allowedSortFields = [
                'id'       => 'id',
                'addTime'  => 'add_time',
                'fileSize' => 'file_size',
            ];

            if (isset($allowedSortFields[$sortField])) {
                $orderBy = [$allowedSortFields[$sortField] => $sortOrder];
            }
        }

        $rows = $query->orderBy($orderBy)
            ->offset($offset)
            ->limit($pageSize)
            ->asArray()
            ->all();

        // 收集本页所有资产 ID，一次性加载关联标签
        $assetIds = array_column($rows, 'id');
        $tagMap   = [];
        if ($assetIds) {
            $relations = BaseQiniuImageAssetTagRel::find()
                ->where([
                    'asset_id' => $assetIds,
                    'status'   => BaseQiniuImageAssetTagRel::STATUS_ACTIVE,
                ])
                ->asArray()
                ->all();

            $tagIds = array_unique(array_column($relations, 'tag_id'));
            $tags   = [];
            if ($tagIds) {
                $tags = BaseQiniuImageTag::find()
                    ->select([
                        'id',
                        'name',
                    ])
                    ->where([
                        'id'     => $tagIds,
                        'status' => BaseQiniuImageTag::STATUS_ACTIVE,
                    ])
                    ->asArray()
                    ->all();
            }
            // 建立 id => name 字典，便于回填
            $tagDict = [];
            foreach ($tags as $tag) {
                $tagDict[$tag['id']] = $tag['name'];
            }
            foreach ($relations as $rel) {
                $assetId = (int)$rel['asset_id'];
                $tagId   = (int)$rel['tag_id'];
                if (!isset($tagDict[$tagId])) {
                    continue;
                }
                $tagMap[$assetId][] = [
                    'id'   => $tagId,
                    'name' => $tagDict[$tagId],
                ];
            }
        }

        // 拼装列表数据结构
        $list = [];
        foreach ($rows as $row) {
            $assetId = (int)$row['id'];
            $list[]  = [
                'id'          => $assetId,
                'assetCode'   => $row['asset_code'],
                'schoolName'  => $row['school_name'],
                'fileExt'     => $row['file_ext'],
                'fileSize'    => (int)$row['file_size'],
                'imageWidth'  => (int)$row['image_width'],
                'imageHeight' => (int)$row['image_height'],
                'previewUrl'  => $row['preview_url'],
                'fullUrl'     => $row['full_url'],
                'remark'      => $row['remark'],
                'adminId'     => (int)$row['admin_id'],
                'adminName'   => $row['admin_name'],
                'addTime'     => $row['add_time'],
                'status'      => (int)$row['status'],
                'tags'        => $tagMap[$assetId] ?? [],
            ];
        }

        return [
            'list'  => $list,
            'pages' => [
                'page'       => $page,
                'pageSize'   => $pageSize,
                'total'      => $total ? (int)ceil($total / $pageSize) : 0,
                'totalCount' => (int)$total,
                'offset'     => $offset,
                'limit'      => $pageSize,
            ],
        ];
    }

    /**
     * 查询标签列表
     * @param array $params 查询参数
     * @return array
     * @throws Exception
     */
    public function getTagList(array $params): array
    {
        $query = BaseQiniuImageTag::find()
            ->select([
                'id',
                'name',
            ])
            ->where(['status' => BaseQiniuImageTag::STATUS_ACTIVE])
            ->orderBy(['id' => SORT_DESC]);

        if (!empty($params['keyword'])) {
            $query->andWhere([
                'like',
                'name',
                trim($params['keyword']),
            ]);
        }

        // 查询结果保持轻量结构，供前端下拉展示
        $list = $query->asArray()
            ->all();

        return [
            'list' => array_map(static function ($item) {
                return [
                    'id'   => (int)$item['id'],
                    'name' => $item['name'],
                ];
            }, $list),
        ];
    }

    /**
     * 创建图片记录并处理标签
     * @param array $params    上传参数
     * @param array $adminInfo 管理员信息（id、name）
     * @return array
     * @throws Exception
     */
    public function create(array $params, array $adminInfo): array
    {
        $assetCode   = trim((string)($params['assetCode'] ?? ''));
        $schoolName  = trim((string)($params['schoolName'] ?? ''));
        $remark      = isset($params['remark']) ? trim((string)$params['remark']) : '';
        $imageBase64 = $params['imageBase64'] ?? '';
        $tagNames    = $params['tags'] ?? [];

        if ($assetCode === '') {
            throw new Exception('图片编码不能为空');
        }
        if (!preg_match('/^[A-Za-z0-9_\-]+$/', $assetCode)) {
            throw new Exception('图片编码仅支持字母、数字、下划线或短横线');
        }
        if ($schoolName === '') {
            throw new Exception('学校名称不能为空');
        }
        if (mb_strlen($remark) > 500) {
            throw new Exception('备注长度不能超过500字');
        }
        if ($imageBase64 === '') {
            throw new Exception('请上传图片');
        }
        if (!is_array($tagNames)) {
            throw new Exception('标签参数格式错误');
        }

        $adminId   = (int)($adminInfo['id'] ?? 0);
        $adminName = (string)($adminInfo['name'] ?? '');
        if ($adminId <= 0) {
            throw new Exception('未获取到管理员信息');
        }

        $imageData = $this->parseBase64Image($imageBase64);

        // 生成原始文件名，既用于唯一性提示也用于日志记录
        $originFilename = $assetCode . '.' . $imageData['extension'];
        if (BaseQiniuImageAsset::find()
            ->where(['asset_code' => $assetCode])
            ->exists()) {
            throw new Exception("图片编码已存在：{$originFilename}");
        }

        // 记录上传耗时，方便日志统计
        $uploadStart  = microtime(true);
        $uploadResult = $this->uploadToQiniu($assetCode, $imageData, $adminInfo);
        $duration     = (int)round((microtime(true) - $uploadStart) * 1000);

        $asset               = new BaseQiniuImageAsset();
        $asset->asset_code   = $assetCode;
        $asset->school_name  = $schoolName;
        $asset->file_id      = $uploadResult['file_id'];
        $asset->file_ext     = $uploadResult['extension'];
        $asset->file_size    = $uploadResult['size'];
        $asset->image_width  = $uploadResult['width'];
        $asset->image_height = $uploadResult['height'];
        $asset->preview_url  = $uploadResult['preview_url'];
        $asset->full_url     = $uploadResult['full_url'];
        $asset->remark       = $remark ?: null;
        $asset->admin_id     = $adminId;
        $asset->admin_name   = $adminName;
        $asset->status       = BaseQiniuImageAsset::STATUS_ACTIVE;
        $asset->add_time     = CUR_DATETIME;
        $asset->update_time  = TimeHelper::ZERO_TIME;

        if (!$asset->save()) {
            throw new Exception($asset->getFirstErrorsMessage() ?: '图片信息保存失败');
        }

        $tagList = $this->handleTags($asset->id, $tagNames, $adminInfo);

        $this->writeUploadLog($asset->id, [
            'admin_id'        => $adminId,
            'admin_name'      => $adminName,
            'source_filename' => $uploadResult['filename'],
            'duration_ms'     => $duration,
            'result'          => BaseQiniuImageUploadLog::RESULT_SUCCESS,
        ]);

        return [
            'id'         => $asset->id,
            'assetCode'  => $asset->asset_code,
            'previewUrl' => $asset->preview_url,
            'fullUrl'    => $asset->full_url,
            'tags'       => $tagList,
        ];
    }

    /**
     * 更新图片信息
     * @param array $params    更新参数
     * @param array $adminInfo 管理员信息
     * @return bool
     * @throws Exception
     */
    public function update(array $params, array $adminInfo): bool
    {
        $id         = (int)($params['id'] ?? 0);
        $schoolName = isset($params['schoolName']) ? trim((string)$params['schoolName']) : '';
        $remark     = isset($params['remark']) ? trim((string)$params['remark']) : '';
        $tagNames   = $params['tags'] ?? [];

        if ($id <= 0) {
            throw new Exception('参数错误：ID');
        }
        if ($schoolName === '') {
            throw new Exception('学校名称不能为空');
        }
        if (mb_strlen($remark) > 500) {
            throw new Exception('备注长度不能超过500字');
        }
        if (!is_array($tagNames)) {
            throw new Exception('标签参数格式错误');
        }

        $asset = BaseQiniuImageAsset::findOne([
            'id'     => $id,
            'status' => BaseQiniuImageAsset::STATUS_ACTIVE,
        ]);

        if (!$asset) {
            throw new Exception('图片记录不存在或已删除');
        }

        $asset->school_name = $schoolName;
        $asset->remark      = $remark ?: null;
        $asset->update_time = CUR_DATETIME;

        if (!$asset->save()) {
            throw new Exception($asset->getFirstErrorsMessage() ?: '图片信息更新失败');
        }

        $this->handleTags($asset->id, $tagNames, $adminInfo, true);

        return true;
    }

    /**
     * 解析并校验 Base64 图片
     * @param string $imageBase64 Base64 字符串
     * @return array [content, extension, size, width, height]
     * @throws Exception
     */
    protected function parseBase64Image(string $imageBase64): array
    {
        $imageBase64 = trim($imageBase64);

        $match = ValidateHelper::isBase64Image($imageBase64);
        if (!$match || empty($match[2])) {
            throw new Exception('图片格式不正确');
        }

        $extension = strtolower($match[2]);
        if ($extension === 'jpeg') {
            $extension = 'jpg';
        }
        if (!in_array($extension, [
            'jpg',
            'png',
            'gif',
        ], true)) {
            throw new Exception('仅支持 jpg、png、gif 格式的图片');
        }

        $commaPos = strpos($imageBase64, ',');
        if ($commaPos === false) {
            throw new Exception('图片数据解析失败');
        }

        $binary = base64_decode(substr($imageBase64, $commaPos + 1), true);
        if ($binary === false) {
            throw new Exception('图片数据解析失败');
        }

        $size = strlen($binary);
        if ($size <= 0) {
            throw new Exception('图片内容为空');
        }
        if ($size > self::MAX_IMAGE_SIZE) {
            throw new Exception('图片过大，请压缩后重新上传');
        }

        $imageInfo = getimagesizefromstring($binary);
        if ($imageInfo === false) {
            throw new Exception('图片内容无法识别，请重新上传');
        }

        return [
            'base64'    => $imageBase64,
            'content'   => $binary,
            'extension' => $extension,
            'size'      => $size,
            'width'     => (int)$imageInfo[0],
            'height'    => (int)$imageInfo[1],
        ];
    }

    /**
     * 上传图片至七牛并写入 file 表
     * @param string $assetCode 图片编码
     * @param array  $imageData 图片数据
     * @param array  $adminInfo 管理员信息
     * @return array
     * @throws Exception
     */
    protected function uploadToQiniu(string $assetCode, array $imageData, array $adminInfo): array
    {
        $adminId = (int)($adminInfo['id'] ?? 0);

        $needWebp        = in_array($imageData['extension'], [
            'jpg',
            'png',
        ], true);
        $targetExtension = $needWebp ? 'webp' : $imageData['extension'];

        $currentYmd   = date('Ymd');
        $fileName     = strtolower($assetCode) . '.' . $targetExtension;
        $relativePath = sprintf('%s/%s/%s', self::UPLOAD_BASE_DIR, $currentYmd, $fileName);

        $uploadForm = new BaseUploadForm();
        $uploadInfo = $uploadForm->saveBase64WithWebpSupport($imageData['base64'], $relativePath, $needWebp);

        $file               = new BaseFile();
        $file->name         = basename($uploadInfo['path']);
        $file->path         = $relativePath;
        $file->suffix       = $uploadInfo['extension'];
        $file->size         = $uploadInfo['size'];
        $file->platform     = BaseFile::PLATFORM_TYPE_QINIU;
        $file->width        = $uploadInfo['width'];
        $file->height       = $uploadInfo['height'];
        $file->creator_id   = $adminId;
        $file->creator_type = BaseUser::TYPE_ADMIN;
        $file->status       = BaseFile::STATUS_ACTIVE;

        if (!$file->save()) {
            throw new Exception($file->getFirstErrorsMessage() ?: '文件信息保存失败');
        }

        $fullUrl = FileHelper::getFullUrl($relativePath, BaseFile::PLATFORM_TYPE_QINIU);

        return [
            'file_id'     => (int)$file->id,
            'extension'   => $uploadInfo['extension'],
            'size'        => $uploadInfo['size'],
            'width'       => $uploadInfo['width'],
            'height'      => $uploadInfo['height'],
            'path'        => $relativePath,
            'filename'    => basename($uploadInfo['path']),
            'preview_url' => $fullUrl,
            'full_url'    => $fullUrl,
        ];
    }

    /**
     * 处理标签：去重、创建、建立关联
     * @param int   $assetId   图片ID
     * @param array $tagNames  标签名称集合
     * @param array $adminInfo 管理员信息
     * @return array 标签数据
     * @throws Exception
     */
    protected function handleTags(int $assetId, array $tagNames, array $adminInfo, bool $sync = false): array
    {
        if (empty($tagNames)) {
            if ($sync) {
                BaseQiniuImageAssetTagRel::updateAll([
                    'status'      => BaseQiniuImageAssetTagRel::STATUS_DELETE,
                    'update_time' => CUR_DATETIME,
                ], [
                    'asset_id' => $assetId,
                    'status'   => BaseQiniuImageAssetTagRel::STATUS_ACTIVE,
                ]);
            }

            return [];
        }

        $adminId   = (int)($adminInfo['id'] ?? 0);
        $adminName = (string)($adminInfo['name'] ?? '');

        // 按前端输入清洗标签：去空格、校验长度
        $cleanTags = [];
        foreach ($tagNames as $tag) {
            $tag = trim((string)$tag);
            if ($tag === '') {
                continue;
            }
            if (mb_strlen($tag) > 32) {
                throw new Exception('标签长度不能超过32个字符');
            }
            $cleanTags[] = $tag;
        }

        $cleanTags = array_values(array_unique($cleanTags));
        if (count($cleanTags) > self::MAX_TAG_COUNT) {
            throw new Exception('单张图片最多只能关联3个标签');
        }

        $result = [];
        // 记录此次提交中的有效标签，供同步模式下清理历史数据使用
        $keepTagIds = [];
        foreach ($cleanTags as $tagName) {
            $tagModel = BaseQiniuImageTag::find()
                ->where(['name' => $tagName])
                ->one();
            if ($tagModel) {
                if ($tagModel->status !== BaseQiniuImageTag::STATUS_ACTIVE) {
                    $tagModel->status = BaseQiniuImageTag::STATUS_ACTIVE;
                    if (!$tagModel->save()) {
                        throw new Exception($tagModel->getFirstErrorsMessage() ?: '标签状态更新失败');
                    }
                }
            } else {
                $tagModel              = new BaseQiniuImageTag();
                $tagModel->name        = $tagName;
                $tagModel->admin_id    = $adminId;
                $tagModel->admin_name  = $adminName;
                $tagModel->status      = BaseQiniuImageTag::STATUS_ACTIVE;
                $tagModel->add_time    = CUR_DATETIME;
                $tagModel->update_time = TimeHelper::ZERO_TIME;
                if (!$tagModel->save()) {
                    throw new Exception($tagModel->getFirstErrorsMessage() ?: '标签创建失败');
                }
            }

            $relModel = BaseQiniuImageAssetTagRel::find()
                ->where([
                    'asset_id' => $assetId,
                    'tag_id'   => $tagModel->id,
                ])
                ->one();

            if ($relModel) {
                if ($relModel->status !== BaseQiniuImageAssetTagRel::STATUS_ACTIVE) {
                    $relModel->status = BaseQiniuImageAssetTagRel::STATUS_ACTIVE;
                }
                $relModel->admin_id   = $adminId;
                $relModel->admin_name = $adminName;
            } else {
                $relModel              = new BaseQiniuImageAssetTagRel();
                $relModel->asset_id    = $assetId;
                $relModel->tag_id      = $tagModel->id;
                $relModel->admin_id    = $adminId;
                $relModel->admin_name  = $adminName;
                $relModel->status      = BaseQiniuImageAssetTagRel::STATUS_ACTIVE;
                $relModel->add_time    = CUR_DATETIME;
                $relModel->update_time = TimeHelper::ZERO_TIME;
            }

            if (!$relModel->save()) {
                throw new Exception($relModel->getFirstErrorsMessage() ?: '标签关联失败');
            }

            $keepTagIds[] = (int)$tagModel->id;

            $result[] = [
                'id'   => (int)$tagModel->id,
                'name' => $tagModel->name,
            ];
        }

        if ($sync) {
            BaseQiniuImageAssetTagRel::updateAll([
                'status'      => BaseQiniuImageAssetTagRel::STATUS_DELETE,
                'update_time' => CUR_DATETIME,
            ], [
                'and',
                ['asset_id' => $assetId],
                [
                    'not in',
                    'tag_id',
                    $keepTagIds ?: [0],
                ],
                ['status' => BaseQiniuImageAssetTagRel::STATUS_ACTIVE],
            ]);
        }

        return $result;
    }

    /**
     * 写入上传日志
     * @param int   $assetId 图片ID
     * @param array $logData 日志内容
     * @return void
     * @throws Exception
     */
    protected function writeUploadLog(int $assetId, array $logData): void
    {
        $log                  = new BaseQiniuImageUploadLog();
        $log->asset_id        = $assetId;
        $log->admin_id        = (int)($logData['admin_id'] ?? 0);
        $log->admin_name      = (string)($logData['admin_name'] ?? '');
        $log->source_filename = (string)($logData['source_filename'] ?? '');
        $log->duration_ms     = (int)($logData['duration_ms'] ?? 0);
        $log->result          = (int)($logData['result'] ?? BaseQiniuImageUploadLog::RESULT_FAILURE);
        $log->error_message   = $logData['error_message'] ?? null;
        $log->upload_time     = $logData['upload_time'] ?? CUR_DATETIME;
        $log->add_time        = CUR_DATETIME;
        $log->update_time     = TimeHelper::ZERO_TIME;
        $log->status          = BaseQiniuImageUploadLog::STATUS_ACTIVE;

        if (!$log->save()) {
            throw new Exception($log->getFirstErrorsMessage() ?: '上传日志写入失败');
        }
    }
}
