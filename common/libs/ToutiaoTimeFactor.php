<?php

namespace common\libs;

use common\components\MessageException;
use common\helpers\TimeHelper;

/**
 * 需求来源
 * https://rw7zrjmygqi.feishu.cn/docx/QYUCdflDVoHjQJxhDu3cDKQCnxg
 *
 * <meta property="bytedance:published_time" content="2022-06-13T16:21:49+08:00" />
 * <meta property="bytedance:updated_time" content="2023-10-10T10:25:58+08:00" />
 */
class ToutiaoTimeFactor
{
    // 其实这里只接受信息生成js,然后方便前端使用
    public static function create($pubDate, $upDate = '')
    {
        if ($upDate == TimeHelper::ZERO_TIME) {
            $upDate = '';
        }

        // 这里拿一下路由,如果发现是单位详情页面,需要走不一样的逻辑
        // http://zentao.jugaocai.com/index.php?m=story&f=view&id=613
        $url = \Yii::$app->controller->action->uniqueId;
        if (!empty($pubDate)) {
            if ($url == 'company/detail' || $url == 'company/detail-announcement-list' || $url == 'company/detail-job-list') {
                $pubDate = '';
            } else {
                $pubDate = date('Y-m-d\TH:i:s', strtotime($pubDate));
            }
        }

        if ($upDate) {
            $upDate = date('Y-m-d\TH:i:s', strtotime($upDate));
            $meta   = '<meta property="bytedance:updated_time" content="' . $upDate . '+08:00" />';
        }
        if ($pubDate) {
            $meta .= '<meta property="bytedance:published_time" content="' . $pubDate . '+08:00" />';
        }

        // 塞到yii全局变量里面去
        \Yii::$app->params['toutiaoFactor'] = $meta;

        return $meta;
    }

    /**
     * 创建时间因子，如果在$hour前就取前一天，否则取当天
     * @param $hour 1-24
     * @return string
     * @throws MessageException
     */
    public static function createMetaTagWithHour($hour)
    {
        // 验证输入是否在 1 - 24 范围内
        if ($hour < 1 || $hour > 24) {
            throw new MessageException('输入的小时数必须在 1 到 24 之间。');
        }
        // 将传入的小时数转换为 HH:00:00 格式
        $timePoint = sprintf('%02d:00:00', $hour);
        $nowTime = date('H:i:s');

        if ($nowTime < $timePoint) {
            // 如果当前时间小于传入的小时数，则将发布时间设置为前一天
            $pubDate = date('Y-m-d\T' . $timePoint, strtotime('-1 day'));
        } else {
            // 否则，将发布时间设置为当天
            $pubDate = date('Y-m-d\T' . $timePoint);
        }

        // 创建meta标签
        $meta = '<meta property="bytedance:updated_time" content="' . $pubDate . '+08:00" />';
        // 塞到yii全局变量里面去
        \Yii::$app->params['toutiaoFactor'] = $meta;

        return $meta;
    }

    // 其实这里只接受信息生成js,然后方便前端使用
    public static function createRczhaopin()
    {
        return self::createMetaTagWithHour(8);
    }

    // 其实这里只接受信息生成js,然后方便前端使用
    public static function createHotword()
    {
        return self::createMetaTagWithHour(8);
    }



}
