<?php
namespace common\libs;

use Fastknife\Service\Service;

class ImgCaptcha
{
    protected $captcha;

    public function __construct()
    {
        if (!$this->captcha) {
            $config        = \Yii::$app->params['captchaConfig'];
            $this->captcha = new \Fastknife\Service\BlockPuzzleCaptchaService($config);
        }
    }

    /**
     * 获取验证码
     * @return array
     */
    public function getCaptcha()
    {
        $captchaInfo = $this->captcha->get();

        return $captchaInfo;
    }

    /**
     * 检查验证码
     * @param $token
     * @param $pointJson
     */
    public function checkCaptcha($token, $pointJson)
    {
        $this->captcha->check($token, $pointJson);
    }

}