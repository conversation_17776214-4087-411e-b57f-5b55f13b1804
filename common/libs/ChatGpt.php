<?php
namespace common\libs;

use common\helpers\DebugHelper;

class ChatGpt
{

    public static $message;
    public static $adminId;
    public static $return;

    /**
     * 检查验证码
     * @param $token
     * @param $pointJson
     */
    public static function send($message, $adminId)
    {
        self::$message = $message;
        self::$adminId = $adminId;

        if (!self::check()) {
            self::$return = '一分钟内不允许发送多次';
        } else {
            $return = '你之前的问题是: ' . $message . PHP_EOL . '我的回答是: ' . PHP_EOL . self::post();

            self::$return = $return;
        }

        self::toWx();
    }

    public static function post()
    {
        $url     = "https://api.openai.com/v1/completions";
        $api_key = "***************************************************";

        // Request data
        $data = [
            "prompt"     => self::$message,
            "max_tokens" => 2048,
            "model"      => "text-davinci-003",
        ];

        // cURL options
        $options = [
            CURLOPT_URL            => $url,
            CURLOPT_POST           => true,
            CURLOPT_POSTFIELDS     => json_encode($data),
            CURLOPT_HTTPHEADER     => [
                "Content-Type: application/json",
                "Authorization: Bearer " . $api_key,
            ],
            CURLOPT_RETURNTRANSFER => true,
        ];

        // Initiate cURL request
        $curl = curl_init();
        curl_setopt_array($curl, $options);
        $response = curl_exec($curl);

        // Check for cURL errors
        if (curl_errno($curl)) {
            return "cURL error: " . curl_error($curl);
        }

        // Close cURL session
        curl_close($curl);

        // Decode JSON response
        $response_data = json_decode($response, true);

        // var_dump($response_data);

        return $response_data["choices"][0]["text"] ?: '接口出错了';
    }

    public static function post1()
    {
        $url = 'http://chatgpt.ideaboat.cn';

        $data = [
            'message' => self::$message,
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        curl_close($ch);

        // 去掉开头的那些换行符
        $response = trim($response);

        return $response;
    }

    public static function check()
    {
        if (self::$adminId == 1) {
            return true;
        }

        $key = 'TMP:chatgpt_' . self::$adminId;

        $rs = Cache::get($key);
        if ($rs) {
            return false;
        }
        // 设置1分钟
        Cache::set($key, 1, 60);

        return true;
    }

    public static function toWx()
    {
        $app = WxWork::getInstance();

        $app->message(self::$adminId, self::$return);
    }

}