<?php

namespace common\libs;

use common\base\models\BaseShortLink;
use common\helpers\UrlHelper;
use common\helpers\ValidateHelper;

/**
 * 做一套我们的短链尝试
 */
class ShortLink
{
    public static function create($url)
    {
        // 首先检查地址是否是正常的url
        if (!ValidateHelper::isUrl($url)) {
            throw new \Exception('地址不是一个正常的url');
        }

        $code        = self::generateCode();
        $model       = new BaseShortLink();
        $model->code = $code;
        $model->url  = $url;
        $model->save();

        // 这里需要把code和url组合成一个短链
        return [
            'url' => UrlHelper::createShortLink($code),
            'id'  => $model->id,
        ];
    }

    public static function getByCode($code)
    {
        $model = BaseShortLink::findOne(['code' => $code]);
        if (!$model) {
            throw new \Exception('短链不存在');
        }

        return $model->url;
    }

    public static function getById($id)
    {
        $model = BaseShortLink::findOne($id);
        if (!$model) {
            throw new \Exception('短链不存在');
        }

        return $model->url;
    }

    // 随机生成[a-z,A-Z,0-9]的字符串
    public static function generateCode()
    {
        $code     = '';
        $chars    = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $charsLen = strlen($chars);
        for ($i = 0; $i < 6; $i++) {
            $code .= $chars[mt_rand(0, $charsLen - 1)];
        }

        // 找一下是否已经存在
        $id = BaseShortLink::findOneVal(['code' => $code], 'id');

        if ($id) {
            return self::generateCode();
        }

        return $code;
    }

}