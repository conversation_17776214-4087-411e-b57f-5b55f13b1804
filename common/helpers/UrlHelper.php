<?php

namespace common\helpers;

use common\base\models\BaseShowcase;
use Yii;
use yii\helpers\BaseUrl;

class UrlHelper extends BaseUrl
{

    public static function getQuery($url)
    {
        $query = parse_url($url);

        if (empty($query)) {
            return '';
        }

        $data = \GuzzleHttp\Psr7\parse_query($query['query'], false);

        return $data;
    }

    /**
     * 用于补全url
     *
     * @param $url
     * @param $prefix
     * @return string
     */
    public static function fix($url, $prefix = '')
    {
        // 首先判断是否有http
        if (strstr($url, 'http')) {
            // 已经是全路径了
            return $url;
        }

        if (strstr($url, 'www.')) {
            // 已经是全路径了
            return 'http://' . $url;
        }

        try {
            $prefix = $prefix ?: Yii::$app->request->hostInfo;
        } catch (\Exception $e) {
        }

        // 由于不知道之前的路径是否是全路径,所以首先处理一下,过滤掉
        $url = str_replace($prefix . '/', '', $url);

        // 最后拼接上路径返回
        return $prefix . '/' . $url;
    }

    /**
     * 判断当前路由属于哪个类型模块
     * @param $pathInfo
     * @return int
     */
    public static function getPathBelongType($pathInfo): int
    {
        //1是职位，2是公告，3是单位，4是资讯
        $pathInfoArr = explode('/', $pathInfo);
        $controller  = $pathInfoArr[0];
        if ($controller == 'column') {
            //如果是栏目，判断是资讯还是公告
            // $id = Yii::$app->request->get('id');
            // if (!empty($id)) {
            //     $column = HomeColumn::find()
            //         ->select('id,template_type')
            //         ->where(['id' => $id])
            //         ->one();
            //     if ($column['template_type'] == BaseHomeColumn::TEMPLATE_TYPE_NEWS) {
            //         $type = 4;//资讯
            //     } else {
            //         $type = 2;//公告
            //     }
            // }
            $type = 1;
        } elseif ($controller == 'search') {
            //如果是搜索的情况，判断是否有type
            $type = Yii::$app->request->get('type');
            if (!empty($type) && $type == 2) {
                $type = 4;//资讯
            } else {
                $type = 2;//公告
            }
        } elseif ($controller == 'announcement') {
            //公告
            $type = 2;
        } elseif ($controller == 'company') {
            //单位
            $type = 3;
        } elseif ($controller == 'news') {
            //资讯
            $type = 4;
        } elseif ($controller == 'daily.html' || $controller == 'daily') {
            //特殊情况，每日汇总的
            $type = 2;
        } else {
            //其余为职位，即默认情况
            $type = 1;
        }

        return $type;
    }

    public static function createRealUrlPath($id, $type)
    {
        $path = '';
        switch ($type) {
            case 1:
                $path = '/job/detail/' . $id;
                break;
            case 2:
                $path = '/announcement/detail/' . $id;
                break;
            case 3:
                $path = '/company/detail/' . $id;
                break;
            case 4:
                $path = '/news/detail/' . $id;
                break;
        }

        return $path;
    }

    public static function createAnnouncementDetailPath($id)
    {
        return '/announcement/detail/' . $id . '.html';
    }

    public static function createPcAnnouncementJobListPath($id)
    {
        return '/announcement/job-list?id=' . $id;
    }

    public static function createJobDetailPath($id)
    {
        return '/job/detail/' . $id . '.html';
    }

    public static function createNewsDetailPath($id)
    {
        return '/news/detail/' . $id . '.html';
    }

    public static function createCompanyDetailPath($id)
    {
        return '/company/detail/' . $id . '.html';
    }

    public static function createCompanyDetailTabJobPath($id)
    {
        return '/company/detail/' . $id . '_j.html';
    }

    public static function createDeilyDetailPath($id)
    {
        return '/daily/detail/' . $id . '.html';
    }

    // 获取PC首页的链接
    public static function createPcHomePath()
    {
        return 'https://' . Yii::$app->params['pcHost'];
    }

    public static function createH5HomePath()
    {
        return 'https://' . Yii::$app->params['h5Host'];
    }

    public static function createH5AnnouncementDetailPath($id)
    {
        return 'https://' . Yii::$app->params['h5Host'] . '/announcement/detail/' . $id . '.html';
    }

    public static function createH5JobDetailPath($id)
    {
        return 'https://' . Yii::$app->params['h5Host'] . '/job/detail/' . $id . '.html';
    }

    public static function createH5CompanyDetailPath($id)
    {
        return 'https://' . Yii::$app->params['h5Host'] . '/company/detail/' . $id . '.html';
    }

    // 获取PC单位登录的链接
    public static function createPcCompanyLoginPath()
    {
        return (Yii::$app->params['environment'] == 'prod' ? 'https://' . Yii::$app->params['pcHost'] : '//' . Yii::$app->params['pcHost']) . '/member/company/login';
    }

    // 获取PC求职者登录的链接
    public static function createPcPersonLoginPath()
    {
        return (Yii::$app->params['environment'] == 'prod' ? 'https://' . Yii::$app->params['pcHost'] : '//' . Yii::$app->params['pcHost']) . '/member/person/login';
    }

    public static function createPcAnnouncementDetailPath($id)
    {
        return 'https://' . Yii::$app->params['pcHost'] . '/announcement/detail/' . $id . '.html';
    }

    public static function createPcPcAnnouncementJobListPath($id)
    {
        return 'https://' . Yii::$app->params['pcHost'] . '/announcement/job-list?id=' . $id;
    }

    public static function createPcJobDetailPath($id)
    {
        return 'https://' . Yii::$app->params['pcHost'] . '/job/detail/' . $id . '.html';
    }

    public static function createPcNewsDetailPath($id)
    {
        return 'https://' . Yii::$app->params['pcHost'] . '/news/detail/' . $id . '.html';
    }

    public static function createPcCompanyDetailPath($id)
    {
        return 'https://' . Yii::$app->params['pcHost'] . '/company/detail/' . $id . '.html';
    }

    public static function createPcCompanyDetailTabJobPath($id)
    {
        return 'https://' . Yii::$app->params['pcHost'] . '/company/detail/' . $id . '_j.html';
    }

    public static function createPcDeilyDetailPath($id)
    {
        return 'https://' . Yii::$app->params['pcHost'] . '/daily/detail/' . $id . '.html';
    }

    public static function createUrlParamsPath($uri, $params)
    {
        $url = $uri . '?' . http_build_query($params);

        return trim($url);
    }

    public static function createPcNewsDetail($id)
    {
        return 'https://' . Yii::$app->params['pcHost'] . '/news/detail/' . $id . '.html';
    }

    public static function createBoshihouNewDeatil($id)
    {
        return self::getBoShiHouHome() . '/news/detail/' . $id . '.html';
    }

    public static function createShortLink($code)
    {
        $baseUrl = str_replace('http://', '', Yii::$app->params['pcHost']);
        $baseUrl = str_replace('https://', '', $baseUrl);

        // 如果是正式环境
        if (Yii::$app->params['environment'] == 'prod') {
            $baseUrl = 'https://' . $baseUrl;
        } else {
            $baseUrl = 'http://' . $baseUrl;
        }

        return $baseUrl . '/s/' . $code;
    }

    public static function createResumeShare($code)
    {
        $baseUrl = str_replace('http://', '', Yii::$app->params['pcHost']);
        $baseUrl = str_replace('https://', '', $baseUrl);
        // 如果是正式环境
        if (Yii::$app->params['environment'] == 'prod') {
            $baseUrl = 'https://' . $baseUrl;
        } else {
            $baseUrl = 'http://' . $baseUrl;
        }

        return $baseUrl . '/resume/share/' . $code;
    }

    public static function createPcResumeStepUrl($stepNumber)
    {
        if ($stepNumber > 3) {
            return self::createPcHomePath() . '/member/person/home';
        }
        $urlSet = [
            1 => '/member/person/required/basic',
            2 => '/member/person/required/education',
            3 => '/member/person/required/intention',
        ];

        return self::createPcHomePath() . $urlSet[$stepNumber];
    }

    /**
     * 判断当前url，底部导航栏选中类型
     * @return string|void
     */
    public static function getFooterActiveType()
    {
        $controllerId = \Yii::$app->controller->id;
        $actionId     = \Yii::$app->controller->action->id;
        $action       = $controllerId . '/' . $actionId;
        $searchData   = \Yii::$app->request->get();
        if ($action == 'home/index' || $action == 'home/column' || $action == 'home/area-column' || $action == 'home/major-column') {
            return 'home';
        } elseif ($action == 'job/index' || $action == 'engine/index' || $action == 'hot-word/index' || $action == 'seo-job-wiki/index') {
            if (array_key_exists('keyword', $searchData) || array_key_exists('jobCategoryId', $searchData)) {
                //如果存在查询信息，即为查询结果列表
                return '';
            } else {
                return 'job';
            }
        } elseif ($action == 'company/index') {
            if (array_key_exists('keyword', $searchData)) {
                return '';
            } else {
                return 'company';
            }
        } elseif ($action == 'home/person') {
            return 'person';
        } elseif ($action == 'news/detail') {
            return 'news';
        } elseif ($action == 'job/detail' || $action == 'company/detail' || $action == 'announcement/detail' || $action == 'home/code-login' || $action == 'home/login' || $action == 'home/reset-password' || $action == 'job/search' || $action == 'message/index' || $action == 'home/setting' || $action == 'person/on-site-apply-list' || $action == 'resume/preview' || $action == 'resume/index' || $action == 'home/daily-summary-detail') {
            //特殊页面，底部不需要展示导航
            return '';
        }
    }

    public static function getAlertShowStatus()
    {
        $controllerId = \Yii::$app->controller->id;
        $actionId     = \Yii::$app->controller->action->id;
        $action       = $controllerId . '/' . $actionId;
        $searchData   = \Yii::$app->request->get();
        if ($action == 'home/index' || $action == 'home/column' || $action == 'home/area-column' || $action == 'home/major-column') {
            return true;
        } elseif ($action == 'company/index') {
            if (array_key_exists('keyword', $searchData)) {
                return false;
            } else {
                return true;
            }
        } elseif ($action == 'job/index' || $action == 'engine/index' || $action == 'hot-word/index' || $action == 'seo-job-wiki/index') {
            if (array_key_exists('keyword', $searchData) || array_key_exists('jobCategoryId', $searchData)) {
                //如果存在查询信息，即为查询结果列表
                return false;
            } else {
                return true;
            }
        } else {
            return false;
        }
    }

    // 获取海外首页的链接
    public static function getHaiwaiHome()
    {
        $url = 'https://haiwai.' . Yii::$app->params['pcHost'];

        // 去掉www.
        $url = str_replace('www.', '', $url);

        return $url;
    }

    public static function getHaiwaiChuhai()
    {
        $url = 'https://haiwai.' . Yii::$app->params['pcHost'] . '/chuhai';

        // 去掉www.
        $url = str_replace('www.', '', $url);

        return $url;
    }

    // 归国
    public static function getHaiwaiGuiguo()
    {
        $url = 'https://haiwai.' . Yii::$app->params['pcHost'] . '/guiguo';

        // 去掉www.
        $url = str_replace('www.', '', $url);

        return $url;
    }

    // 求贤
    public static function getHaiwaiQiuxian()
    {
        $url = 'https://haiwai.' . Yii::$app->params['pcHost'] . '/gonggao';

        // 去掉www.
        $url = str_replace('www.', '', $url);

        return $url;
    }

    // 海外优青
    public static function getHaiwaiYouqing()
    {
        $url = 'https://haiwai.' . Yii::$app->params['pcHost'] . '/haiwaiyouqing';

        // 去掉www.
        $url = str_replace('www.', '', $url);

        return $url;
    }

    // 单位大厅
    public static function getHaiwaiCompany()
    {
        $url = 'https://haiwai.' . Yii::$app->params['pcHost'] . '/danwei';

        // 去掉www.
        $url = str_replace('www.', '', $url);

        return $url;
    }

    /**
     * 博士后首页
     * @return string|string[]
     */
    public static function getBoShiHouHome()
    {
        $baseUrl = 'boshihou.' . Yii::$app->params['pcHost'];
        if (Yii::$app->params['environment'] != 'local') {
            $baseUrl = 'https://' . $baseUrl;
        } else {
            $baseUrl = 'http://' . $baseUrl;
        }

        // 去掉www.
        $url = str_replace('www.', '', $baseUrl);

        return $url;
    }

    /**
     * 博士后公告
     * @return string|string[]
     */
    public static function getBoShiHouGongGao()
    {
        $baseUrl = 'boshihou.' . Yii::$app->params['pcHost'] . '/gonggao';
        if (Yii::$app->params['environment'] != 'local') {
            $baseUrl = 'https://' . $baseUrl;
        } else {
            $baseUrl = 'http://' . $baseUrl;
        }
        // 去掉www.
        $url = str_replace('www.', '', $baseUrl);

        return $url;
    }

    /**
     * 博士后职位
     * @return string|string[]
     */
    public static function getBoShiHouZhiWei()
    {
        $baseUrl = 'boshihou.' . Yii::$app->params['pcHost'] . '/zhiwei';
        if (Yii::$app->params['environment'] != 'local') {
            $baseUrl = 'https://' . $baseUrl;
        } else {
            $baseUrl = 'http://' . $baseUrl;
        }
        // 去掉www.
        $url = str_replace('www.', '', $baseUrl);

        return $url;
    }

    /**
     * 博士后单位
     * @return string|string[]
     */
    public static function getBoShiHouDanWei()
    {
        $baseUrl = 'boshihou.' . Yii::$app->params['pcHost'] . '/danwei';
        if (Yii::$app->params['environment'] != 'local') {
            $baseUrl = 'https://' . $baseUrl;
        } else {
            $baseUrl = 'http://' . $baseUrl;
        }
        // 去掉www.
        $url = str_replace('www.', '', $baseUrl);

        return $url;
    }

    /**
     * 博士后活动
     * @return string|string[]
     */
    public static function getBoShiHouHuoDong()
    {
        $baseUrl = 'boshihou.' . Yii::$app->params['pcHost'] . '/huodong';
        if (Yii::$app->params['environment'] != 'local') {
            $baseUrl = 'https://' . $baseUrl;
        } else {
            $baseUrl = 'http://' . $baseUrl;
        }
        // 去掉www.
        $url = str_replace('www.', '', $baseUrl);

        return $url;
    }

    /**
     * 博士后服务
     * @return string|string[]
     */
    public static function getBoShiHouFuWu()
    {
        $baseUrl = 'boshihou.' . Yii::$app->params['pcHost'] . '/fuwu';
        if (Yii::$app->params['environment'] != 'local') {
            $baseUrl = 'https://' . $baseUrl;
        } else {
            $baseUrl = 'http://' . $baseUrl;
        }
        // 去掉www.
        $url = str_replace('www.', '', $baseUrl);

        return $url;
    }

    /**
     * 获取域名或者IP后问号前的URI
     * @return mixed|string
     */
    public static function getRouteUri()
    {
        $url    = Yii::$app->request->url;
        $urlArr = explode('?', $url);

        return $urlArr[0];
    }

    public static function createMiniCompanyDetailPath($companyId)
    {
        return '/packages/company/detail/index?id=' . $companyId;
    }

    public static function createMiniAnnouncementDetailPath($announcementId)
    {
        return '/packages/announcement/detail/index?id=' . $announcementId;
    }

    // 判断用户是否在博士后站点下面
    public static function isBoshihou()
    {
        $host         = Yii::$app->request->hostName;
        $boshihouHost = 'boshihou.' . Yii::$app->params['pcHost'];

        return $host == $boshihouHost;
    }

    /**
     * 整理链接，返回符合可以跳转的链接，并且返回链接是否是本地连接
     * @param $link
     * @return array
     */
    public static function formatLinkInfo($link)
    {
        $data = MiniHelper::getUrlType($link);

        return [
            'newLink'  => (PLATFORM == 'MINI') ? BaseShowcase::urlToMiniRouter($data['pageType'], $data['url']) : $link,
            'link'     => $link,
            'pageType' => strval($data['pageType']),
            'linkType' => strval($data['targetType']),
        ];
    }
}
