<?php

namespace common\helpers;

use yii\helpers\BaseArrayHelper;

/** 公共助手类 */
class ArrayHelper extends BaseArrayHelper
{
    /**
     * 将一个有下标的数组转换为键值对数组（方便前端展示）
     * @param array $obj 输入的数组
     * @return array 转换后的键值对数组
     */
    public static function obj2Arr($obj): array
    {
        $arr = [];

        foreach ($obj as $k => $v) {
            $arr[] = [
                'k' => (string)$k,
                'v' => $v,
            ];
        }

        return $arr;
    }

    /**
     * 将数组转换为键值对数组（指定键和值）
     * @param array  $list 输入的数组
     * @param string $k    键的字段名
     * @param string $v    值的字段名
     * @return array 转换后的键值对数组
     */
    public static function arr2KV($list, $k, $v): array
    {
        $arr = [];

        foreach ($list as $value) {
            $arr[] = [
                'k' => (string)$value[$k],
                'v' => $value[$v],
            ];
        }

        return $arr;
    }

    /**
     * 将对象数组转换为标签值对数组（默认字段为 'name' 和 'id'）
     * @param array  $list 输入的对象数组
     * @param string $k    标签字段名（默认为 'name'）
     * @param string $v    值字段名（默认为 'id'）
     * @return array 转换后的标签值对数组
     */
    public static function object2LV($list, $k = 'name', $v = 'id'): array
    {
        $arr = [];

        foreach ($list as $value) {
            $arr[] = [
                'label' => (string)$value[$k],
                'value' => $value[$v],
            ];
        }

        return $arr;
    }

    /**
     * 将数组转换为标签值对数组（默认键为字符串）
     * @param array $list 输入的数组
     * @return array 转换后的标签值对数组
     */
    public static function array2LV($list): array
    {
        $arr = [];

        foreach ($list as $k => $v) {
            $arr[] = [
                'label' => $v,
                'value' => (string)$k,
            ];
        }

        return $arr;
    }

    /**
     * 将多维数组转换为父子结构
     * @param array $elements 输入的数组
     * @param int   $parentId 父节点ID
     * @return array 转换后的父子结构数组
     */
    public static function objMoreArr(array $elements, int $parentId = 0): array
    {
        $array = [];
        foreach ($elements as $k => $element) {
            if ($element['parentId'] == $parentId) {
                $children = self::objMoreArr($elements, $element['k'] ? $element['k'] : $element['id']);
                if ($children) {
                    $element['children'] = $children;
                }
                if (isset($element['k'])) {
                    $array[] = $element;
                } else {
                    $array[$k]['k'] = $element['id'];
                    $array[$k]['v'] = $element['name'];
                    if ($element['children']) {
                        $array[$k]['children'] = $element['children'];
                    }
                }
            } else {
                $element = null;
            }
        }

        return array_values($array);
    }

    /**
     * 将键值对数组转换为标签值对数组
     * @param array $array 输入的键值对数组
     * @return array 转换后的标签值对数组
     */
    public static function kV2LV($array): array
    {
        $arr = [];
        foreach ($array as $value) {
            $tmp = [
                'label' => $value['v'],
                'value' => $value['k'],
            ];
            if (isset($value['children'])) {
                $tmp['children'] = self::kV2LV($value['children']);
            }

            $arr[] = $tmp;
        }

        return $arr;
    }

    /**
     * 根据字段对二维数组进行排序
     * @param array  $array 输入的二维数组
     * @param string $keys  排序字段
     * @param string $sort  排序顺序（'asc' 或 'desc'）
     * @return array 排序后的二维数组
     */
    public static function arraySort($array, $keys, string $sort = 'asc'): array
    {
        $newArr = $valArr = [];

        foreach ($array as $key => $value) {
            $valArr[$key] = $value[$keys];
        }

        ($sort == 'asc') ? asort($valArr) : arsort($valArr);

        reset($valArr);
        foreach ($valArr as $key => $value) {
            $newArr[$key] = $array[$key];
        }

        return $newArr;
    }

    /**
     * 去除数组中重复的元素（根据指定字段）
     * @param array  $arr 输入的数组
     * @param string $key 指定字段
     * @return array 去重后的数组
     */
    public static function assocUnique($arr, $key): array
    {
        $tmp_arr = [];
        foreach ($arr as $k => $v) {
            if (in_array($v[$key], $tmp_arr)) {
                unset($arr[$k]);
            } else {
                $tmp_arr[] = $v[$key];
            }
        }
        sort($arr);

        return $arr;
    }

    /**
     * 根据一个或多个条件对二维数组进行排序
     * @param array  $array    输入的二维数组
     * @param mixed  $sortRule 排序条件（单个字段或多个字段数组）
     * @param string $order    排序顺序（'asc' 或 'desc'）
     * @return mixed 排序后的二维数组
     */
    public static function arraySorts($array, $sortRule, $order = "asc")
    {
        if (is_array($sortRule)) {
            usort($array, function ($a, $b) use ($sortRule) {
                foreach ($sortRule as $sortKey => $order) {
                    if ($a[$sortKey] == $b[$sortKey]) {
                        continue;
                    }

                    return (($order == 'desc') ? -1 : 1) * (($a[$sortKey] < $b[$sortKey]) ? -1 : 1);
                }

                return 0;
            });
        } else {
            if (is_string($sortRule)) {
                usort($array, function ($a, $b) use ($sortRule, $order) {
                    if ($a[$sortRule] == $b[$sortRule]) {
                        return 0;
                    }

                    return (($order == 'desc') ? -1 : 1) * (($a[$sortRule] < $b[$sortRule]) ? -1 : 1);
                });
            }
        }

        return $array;
    }

    /**
     * 将二维数组转换为索引数组
     * @param array $array 输入的二维数组
     * @return array 转换后的索引数组
     */
    public static function convertAssocArrayToIndexed($array): array
    {
        $newArray = [];
        foreach ($array as $key => $subArray) {
            $newArray[$key] = array_values($subArray);
        }

        return $newArray;
    }

    /**
     * 将多维数组添加一维数组
     * @param array $array    输入的多维数组
     * @param int   $parentId 父节点ID
     * @return array 添加一维数组后的数组
     */
    public static function arrayAddNneDimensional($array, int $parentId = 0): array
    {
        $lastArr = [];
        foreach ($array as $item) {
            $template = [
                [
                    'k'        => '-1',
                    'v'        => '分组数据',
                    'parentId' => (string)$parentId,
                    'level'    => '0',
                    'children' => $item,
                ],
            ];
            $lastArr  = array_merge($lastArr, $template);
        }

        return $lastArr;
    }

    /**
     * 将数组转换为字符串键
     * @param array $array 输入的数组
     * @return string 转换后的字符串键
     */
    public static function arrayToStringKey($array): string
    {
        return md5(serialize($array));
    }

    /**
     * 控制数据为空时的显隐
     * @param array $array 输入的数组
     * @param array $list  需要检查的字段列表
     * @return array 添加显隐控制后的数组
     */
    public static function arrayToDisplay($array, $list): array
    {
        foreach ($array as &$item) {
            foreach ($list as $v) {
                if (self::checkDateFormat($item[$v])) {
                    $item[$v . 'Show'] = strtotime($item[$v]) > 0 ? 'inline' : 'none';
                } else {
                    $item[$v . 'Show'] = $item[$v] ? 'inline' : 'none';
                }
            }
        }

        return $array;
    }

    /**
     * 校验日期格式
     * @param string $date 输入的日期字符串
     * @return bool 是否为有效日期
     */
    public static function checkDateFormat($date): bool
    {
        if (preg_match("/^([0-9]{4})-([0-9]{2})-([0-9]{2})$/", $date, $parts)) {
            if (checkdate($parts[2], $parts[3],
                    $parts[1]) || ($parts[1] == '0000' && $parts[2] == '00' && $parts[3] == '00')) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * 将一个有下标的数组转换为键值对数组（键为整数）
     * @param array $obj 输入的数组
     * @return array 转换后的键值对数组
     */
    public static function obj22Arr($obj): array
    {
        $arr = [];

        foreach ($obj as $k => $v) {
            $arr[] = [
                'k' => (int)$k,
                'v' => $v,
            ];
        }

        return $arr;
    }

    /**
     * 将数组中的所有整数转换为字符串
     * @param array $array 输入的数组
     * @return array 转换后的数组
     */
    public static function intToString($array, $zeroToString = false): array
    {
        foreach ($array as $k => $v) {
            if (is_array($v)) {
                $array[$k] = self::intToString($v);
            } else {
                if (is_int($v)) {
                    $array[$k] = (string)$v;
                }

                if (is_float($v)) {
                    $array[$k] = (string)$v;
                }

                if ($zeroToString && ($v === '0' || $v === 0 || $v === null)) {
                    $array[$k] = '';
                }
            }
        }

        return $array;
    }

    /**
     * 清除数组中的空值（null、空字符串、'undefined'）
     * @param array $params 输入的数组
     * @return array 清除空值后的数组
     */
    public static function clearNoValue($params): array
    {
        foreach ($params as $k => $v) {
            if (is_array($v)) {
                $params[$k] = self::clearNoValue($v);
            } else {
                if ($v === null || $v === '' || $v === 'undefined') {
                    unset($params[$k]);
                }
            }
        }

        return $params;
    }

    /**
     * 去除无用数据，并且验证数组是否全包含允许的key值
     * @param $allowedKeys
     * @param $array
     * @return bool
     */
    public static function filterAndValidateKeys($allowedKeys, $array): bool
    {
        $filteredArray = array_filter($array, function ($value) {
            return $value !== 0 && $value !== '';
        });

        $filteredArray = array_keys($filteredArray);
        sort($filteredArray);
        sort($allowedKeys);

        return $allowedKeys === $filteredArray;
    }

    /**
     * 整理redis有序集合的数据
     * @param $data
     * @return array
     */
    public static function organizeZRevRangeData($data)
    {
        // 确保数据长度是偶数
        if (count($data) % 2 !== 0) {
            return $data;
        }

        // 初始化结果数组
        $result = [];

        // 遍历数据并将其整理成关联数组
        for ($i = 0; $i < count($data); $i += 2) {
            $key          = $data[$i];
            $score        = $data[$i + 1];
            $result[$key] = (int)$score; // 将分数转换为整数
        }

        return $result;
    }

    /**
     * 将数组中的groupType字段拆分成多个参数，再重新组装
     * @param $params     array 所有数据，需要包含groupType
     * @param $configKey  array 获取groupType里所需的内容
     * @param $isTotalNum bool 是否返回查询数量
     * @return array
     */
    public static function groupParamsToRealParams($params, $configKey, $isTotalNum = false)
    {
        if (!$params['groupType']) {
            if ($isTotalNum) {
                return [
                    $params,
                    0,
                ];
            }

            return $params;
        }

        // 这里做一个兼容,如果首字符是[
        if (substr($params['groupType'], 0, 1) == '[') {
            $groupType = json_decode($params['groupType'], true);
            // ["experienceType_4","experienceType_1","natureType_3","natureType_2","titleType_3","titleType_1","titleType_2","releaseTimeType_1","releaseTimeType_3","companyType_5","companyType_3","companyType_2","educationType_1","educationType_2","educationType_5","isFast_1"] 是这种格式的,需要转成数组
        } else {
            $groupType = explode(',', $params['groupType']);
        }

        unset($params['groupType']);

        $newParamsArray = [];
        $paramsNum      = 0;

        foreach ($groupType as $k => $item) {
            $itemArray = explode('_', $item);
            $key       = $itemArray[0];
            if (!in_array($key, $configKey)) {
                continue;
            }
            $paramsNum++;
            $newParamsArray[$key][] = $itemArray[1];
        }

        // 最后把数组转成逗号隔开返回回去
        foreach ($newParamsArray as $k => $item) {
            $params[$k] = implode(',', $item);
        }

        if ($isTotalNum) {
            return [
                $params,
                $paramsNum,
            ];
        }

        return $params;
    }

    /**
     * 将数据字典数组按parent_id转换为父子结构
     * @param array $items    原始数据数组，包含id和parent_id字段
     * @param int   $parentId 父节点ID，默认为0（顶级节点）
     * @return array 转换后的父子结构数组
     */
    public static function buildTree(array $items, int $parentId = 0): array
    {
        $tree = [];

        foreach ($items as $item) {
            // 找到当前父节点的子节点
            if ($item['parent_id'] == $parentId) {
                // 递归查找当前节点的子节点
                $children = self::buildTree($items, $item['id']);

                // 如果有子节点，则添加到children字段
                if (!empty($children)) {
                    $item['children'] = $children;
                }

                // 将处理好的节点添加到树中
                $tree[] = $item;
            }
        }

        return $tree;
    }

    public static function buildDictTree(array $elements, int $parentId): array {
        $tree = [];

        foreach ($elements as $element) {
            // 找到当前父节点的子元素
            if ($element['parent_id'] == $parentId) {
                // 递归获取子节点
                $children = self::buildDictTree($elements, $element['id']);

                // 构建当前节点的精简结构，只包含k和v
                $node = [
                    'k' => $element['code'],  // 以code作为k
                    'v' => $element['name']   // 以name作为v
                ];

                // 如果有子节点，添加children键
                if (!empty($children)) {
                    $node['children'] = $children;
                }

                $tree[] = $node;
            }
        }

        return $tree;
    }
}

