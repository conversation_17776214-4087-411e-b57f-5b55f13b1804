<?php

namespace common\helpers;

/**
 * 数学计算类
 */
class MathHelper
{
    /**
     * 分数简化
     */
    public static function scoreSimplify($numerator, $denominator)
    {
        if ($numerator == 0 || $denominator == 0) {
            return $numerator . ":" . $denominator;
        }
        $a = $numerator;
        $b = $denominator;
        $a = abs($a);
        $b = abs($b);
        if ($a < $b) {
            [
                $b,
                $a,
            ] = [
                $a,
                $b,
            ];
        }
        if ($b == 0) {
            return $a;
        }
        $r = $a % $b;
        while ($r > 0) {
            $a = $b;
            $b = $r;
            $r = $a % $b;
        }

        return $numerator / $b . ":" . $denominator / $b;
    }

    /**
     * 获取2个数的最大公约数
     * @param $a
     * @param $b
     * @return int|mixed
     */
    public static function getMaxCommonDivisor($a,$b){
        while ($b != 0) {
            $temp = $a % $b; // 取余数
            $a = $b;
            $b = $temp; // 将较小数替换为余数
        }
        return $a;
    }
}