<style>
    .resume-wrapper {
        font-family: simhei;
        font-size: 14px;
        width: 827px;
        padding: 0px 60px 150px 60px;
        background-color: #fff;
    }

    .bold {
        font-weight: bold;
    }

    .common-details {
        margin-top: 7px;
    }

    .common-title {
        font-size: 18px;
        font-weight: bold;
        line-height: 1;
        padding-top: 16px;
        padding-bottom: 10px;
    }

    .common-title .underline {
        float: left;
        width: 39px;
        height: 2px;
        background: #FFA000;
        margin-top: 6px;
    }

    .classify-title {
        font-size: 15px;
        font-weight: bold;
        text-align: center;
        line-height: 30px;
        border-radius: 4px;
        color: #FFA000;
        background-color: #ffedd1;
        margin-bottom: 12px;
    }

    .between-content .list {
        margin: 10px 0 5px;
    }

    .between-content .col-1 {
        float: left;
        width: 59%;
    }

    .between-content .col-2 {
        float: left;
    }

    .resume-wrapper .clear::after {
        display: inline-block;
        content: '';
    }

    .row-content {
        border-top: 1px solid #EBEBEB;
    }

    .row-content .row {
        padding: 5px 0;
    }

    .resume-wrapper .spacing-1 {
        padding-bottom: 10px;
    }

    .resume-wrapper .spacing-2 {
        padding-bottom: 5px;
    }

    .has-line {
        line-height: 1;
        border-right: 2px solid #666;
    }

    /* .dot {
        font-size: 15px;
        line-height: 15px;
        font-weight: bold;
        color: #FFA000;
        font-family: gb;
    } */

    .tag-blue {
        font-size: 12px;
        line-height: 24px;
        color: #486CF5;
        background-color: #F6F8FF;
        border-radius: 4px;
    }

    .tag-practice {
        font-size: 12px;
        line-height: 24px;
        color: #FFA000;
        background-color: #FFF3E0;
        border-radius: 4px;
    }

    .tag-orange {
        font-size: 12px;
        line-height: 20px;
        color: #FFA000;
        background-color: #FFF3E0;
        border-radius: 4px;
        justify-content: space-between;
    }

    .jc-between-title .title {
        float: left;
        width: 79%;
    }

    .jc-between-title .name {
        font-size: 15px;
        font-weight: bold;
    }

    .jc-between-title .aside {
        float: right;
        text-align: right;
    }

    .second-list {
        padding-bottom: 15px;
    }

    .text-description .label {
        width: 75px;
        float: left;
        line-height: 2;
    }

    .text-description .text-content {
        float: left;
    }

    .text-content {
        line-height: 1.6;
        padding: 5px 0 0px;
        margin-top: -2px;
        font-family: simhei;
    }

    /* 顶部 start */
    .resume-wrapper .top {
        padding-bottom: 5px;
        padding-top: 30px;
    }

    .resume-wrapper .top .logo {
        width: 123px;
        height: 36px;
        background-image: url(https://img.gaoxiaojob.com/uploads/static/image/logo/logo_home.png);
        background-size: 100%;
        background-repeat: no-repeat;
        float: left;
        background-color: white;
    }

    .resume-wrapper .top .update {
        float: right;
        text-align: right;
        padding-top: 12px;
        font-size: 13px;
        color: #494949;
    }

    /* 顶部 end */

    /* 个人信息 start */
    .apply-job{
        padding-top: 16px;
    }

    .info-content {
        padding-top: 18px;
        margin-top: 30px;
    }

    .info-content .avatar {
        border: 2px solid #EBEBEB;
        border-radius: 8px;
        width: 120px;
        height: 130px;
        float: left;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }

    .info-content .info {
        float: left;
        padding-left: 40px;
    }

    .info-content .name {
        padding-bottom: 14px;
    }

    .info-content .name {
        float: left;
        font-size: 22px;
        font-weight: bold;
        line-height: 1;
    }

    .info-content .mobile {
        float: left;
        width: 200px;
        background-image: url(https://img.gaoxiaojob.com/uploads/static/image/resume/mobile.png);
        padding-left: 20px;
        background-repeat: no-repeat;
        background-size: 40px 40px;
        background-position: left;
    }

    .info-content .email {
        float: left;
        background-image: url(https://img.gaoxiaojob.com/uploads/static/image/resume/email.png);
        padding-left: 20px;
        background-repeat: no-repeat;
        background-size: 40px 40px;
        background-position: left;
    }

    .info-content .contact {
        color: #ffa000;
    }

    .advantage-content {
        padding-top: 6px;
    }

    /* 个人信息 end */

    /* 工作经历 start */
    .work-content .position {
        margin-top: 10px;
        margin-bottom: 6px;
    }

    .work-content .position .name {
        font-size: 15px;
        font-weight: bold;
    }

    /* 工作经历 end */

    /* 科研项目 start */
    .project-content .role {
        margin-top: 12px;
        margin-bottom: 8px;
    }

    /* 科研项目 end */

    /* 技能/语言 end */
    .skill-content .second-list {
        padding-bottom: 10px;
    }

    /* 技能/语言 end */

    /* 附加信息 start */
    .addition-content .name {
        font-size: 15px;
        font-weight: bold;
    }

    /* 附加信息 end */

    /* 底部 start */
    .statement-content {
        font-size: 13px;
        padding: 20px 45px;
        text-align: center;
        color: #8D8D90;
        line-height: 2;
    }
    .statement-content .footer-logo{
        text-align: center;
    }
    /* 底部 end */

</style>

<div class="resume-wrapper">
    <div class="top clear">
        <div class="logo"></div>
        <?php if ($operatorType!=3): ?>
        <div class="update">更新时间：<?php echo $lastUpdateResumeTime;?></div>
        <?php endif; ?>
    </div>

    <div class="row-content">
        <?php if($employment){?>
        <div class="apply-job">
            <b>应聘职位：</b><?php echo $employment;?>
        </div>
        <?php }?>
        <div class="info-content clear">
            <div class="avatar" style="background-image: url(<?php echo $userInfo['avatar'] ?>);"></div>

            <div class="info">
                <div class="row clear">
                    <span class="name"><?php echo $userInfo['name'] ?></span>&nbsp;
                    <?php if($userInfo['gender']==1){?>
                    <img style="margin-bottom:-3px;" src="https://img.gaoxiaojob.com/uploads/static/image/resume/male.png" width="20px" alt="" />
                    <?php }else if($userInfo['gender']==2){?>
                    <img style="margin-bottom:-3px;" src="https://img.gaoxiaojob.com/uploads/static/image/resume/female.png" width="20px" alt="" />
                    <?php }?>
                    <?php foreach ($userInfo['updateResumeInfo']['tag'] as $key => $vv): ?>
                    <span class="tag-orange">&nbsp;<?php echo $vv ?>&nbsp;</span>&nbsp;
                    <?php endforeach ?>
                </div>
                <!--   非已注销用户才需要这些信息 -->
                <?php if ($userInfo['memberStatus'] !=10): ?>
                    <div class="row clear">
                        <?php echo $userInfo['educationName'] ?> <?php if(strlen($userInfo['schoolName'])>0){?>·<?php }?>
                        <?php echo $userInfo['schoolName'] ?> <?php if(strlen($userInfo['majorTxt'])>0){?>·<?php }?>
                        <?php echo $userInfo['majorTxt'] ?> <?php if(strlen($userInfo['age'])>0){?>·<?php }?>
                        <?php echo $userInfo['age'] ?>岁 <?php if(strlen($userInfo['workExperience'])>0){?>·<?php }?>
                        <?php echo $userInfo['workExperience'] ?>
                    </div>
                    <div class="row clear">
                        <?php if(trim($userInfo['residenceTxt'])!='-'){?>
                        <span>现居住于<?php echo $userInfo['residenceTxt'] ?></span>
                        <?php if((trim($userInfo['householdRegisterText'])!='-')||(trim($userInfo['nativePlaceAreaTxt'])!='-')){?>·<?php }?>
                        <?php }?>
                        <?php if(trim($userInfo['householdRegisterText'])!='-'){?>
                        <span><?php echo $userInfo['householdRegisterText'] ?>户籍</span>
                        <?php if(trim($userInfo['nativePlaceAreaTxt'])!='-'){?>·<?php }?>
                        <?php }?>
                        <?php if(trim($userInfo['nativePlaceAreaTxt'])!='-'){?>
                        <span>籍贯<?php echo $userInfo['nativePlaceAreaTxt'] ?></span>
                        <?php }?>
                    </div>
                    <div class="row clear">
                        <?php if(trim($userInfo['politicalStatusName'])!='-'){?>
                        <span><?php echo $userInfo['politicalStatusName'] ?></span>
                        <?php if((trim($userInfo['nationTxt'])!='-')||(trim($userInfo['marriageTxt'])!='-')):?>&nbsp;&nbsp;|&nbsp;&nbsp;<?php endif?>
                        <?php }?>
                        <?php if(trim($userInfo['nationTxt'])!='-'){?>
                        <span><?php echo $userInfo['nationTxt'] ?></span>
                        <?php if(trim($userInfo['marriageTxt'])!='-'):?>&nbsp;&nbsp;|&nbsp;&nbsp;<?php endif?>
                        <?php }?>
                        <?php if(trim($userInfo['marriageTxt'])!='-'){?>
                        <span><?php echo $userInfo['marriageTxt'] ?> &nbsp;</span>
                        <?php }?>
                    </div>
                    <div class="row contact clear">
                        <div class="mobile">
                            <?php echo $userInfo['fullMobile'] ?>
                        </div>
                        <div class="email">
                            <?php echo $userInfo['email'] ?>
                        </div>
                    </div>
                <?php endif; ?>


            </div>
        </div>
        <?php if (strlen($userInfo['advantage'])>0){?>
        <div class="advantage-content">
            <div class="common-title">个人优势
                <div class="underline"></div>
            </div>

            <pre class="text-content" style="font-family: simhei"><?php echo $userInfo['advantage'] ?></pre>
        </div>
        <?php }?>
    </div>

    <?php if ($educationList): ?>
    <div class="row-content intention-content spacing-1">
        <div class="common-title">求职意向
            <div class="underline"></div>
        </div>

        <div class="row status">
            <img src="https://img.gaoxiaojob.com/uploads/static/image/resume/title.png" width="16px" alt="">
            求职状态：<?php echo $userInfo['workStatusName'] ?> 到岗时间：<?php echo $userInfo['arriveDateTypeName']?>
        </div>

        <?php foreach ($intentionList as $key => $value): ?>
        <div class="row">
            <img style="margin-bottom:3px;" width="4px" src="https://img.gaoxiaojob.com/uploads/static/image/resume/dot.png" alt="">
            <span class="name"><?php echo $value['jobCategoryName'] ?> </span>
            <span>&nbsp;<?php echo $value['areaName'] ?>&nbsp;|</span>
            <span>&nbsp;<?php echo $value['wageName'] ?> &nbsp;|</span>
            <span>&nbsp;<?php echo $value['natureName'] ?> </span>
        </div>
        <?php endforeach ?>
    </div>
    <?php endif; ?>

    <?php if ($educationList): ?>
    <div class="row-content education-content spacing-1">
        <div class="common-title">教育经历
            <div class="underline"></div>
        </div>

        <?php foreach ($educationList as $index => $item): ?>
        <div class="row">
            <div class="jc-between-title clear">
                <div class="title">
                    <img style="margin-bottom:3px;" width="4px" src="https://img.gaoxiaojob.com/uploads/static/image/resume/dot.png" alt="">
                    <span class="name"><?php echo $item['school'] ?></span>
                    <?php if ($item['isOverseasStudy']==1): ?><span
                        class="tag-blue">&nbsp;海外&nbsp;</span><?php endif; ?>
                    <?php if ($item['is_project_school']==1): ?><span
                        class="tag-orange ">&nbsp;985/211&nbsp;</span><?php endif; ?>
                </div>
                <div class="aside"><?php echo $item['studyBeginDate'] ?>
                    - <?php echo $item['studyEndDate'] ?></div>
            </div>
            <div class="common-details">
                <span><?php echo $item['educationName'] ?><?php if ($item['isRecruitment']==1): ?>（统招）<?php endif; ?>
                    <?php if($item['majorName']){?> |  <?php echo $item['majorName'] ?><?php }?><?php if($item['college']){?>  |  二级院系（机构）：<?php echo $item['college'];?><?php }?><?php if($item['mentor']):?>  |  导师：<?=$item['mentor']?><?php endif?></span>
            </div>
        </div>
        <?php endforeach ?>
    </div>
    <?php endif; ?>

    <?php if (strlen($researchDirection)>0): ?>
    <div class="row-content">
        <div class="common-title">研究方向
            <div class="underline"></div>
        </div>

        <pre class="text-content" style="font-family: simhei"><?php echo $researchDirection ?></pre>
    </div>
    <?php endif; ?>

    <?php if ($workList): ?>
    <div class="row-content work-content">
        <div class="common-title">工作经历
            <div class="underline"></div>
        </div>
        <?php foreach ($workList as $index => $item): ?>
        <div class="row">
            <div class="jc-between-title clear">
                <div class="title">
                    <img style="margin-bottom:3px;" width="4px" src="https://img.gaoxiaojob.com/uploads/static/image/resume/dot.png" alt="">
                    <span class="name"><?php echo $item['company'] ?></span>
                    <?php if ($item['isOverseas']==1): ?><span class="tag-blue">&nbsp;海外&nbsp;</span><?php endif ?>
                    <?php if ($item['isPostdoc']==1): ?><span class="tag-blue">&nbsp;博士后&nbsp;</span><?php endif ?>
                    <?php if ($item['isPractice']==1): ?><span class="tag-practice">&nbsp;实习&nbsp;</span><?php endif ?>
                </div>
                <div class="aside"><?php echo $item['jobBeginDate'] ?> - <?php echo $item['jobEndDate'] ?></div>
            </div>
            <div class="position">
                <span class="name"><?php echo $item['jobName'] ?></span>
                <?php if(strlen($item['department'])>0){?>&nbsp;|&nbsp;
                所在部门：<?php echo $item['department'] ?><?php }?>
            </div>
            <?php if(strlen($item['jobContent'])>0){?>
            <div class="text-description clear">
                <div class="label">工作内容：</div>
                <pre class="text-content" style="font-family: simhei"><?php echo $item['jobContent'] ?></pre>
            </div>
            <?php }?>
        </div>
        <?php endforeach ?>
    </div>
    <?php endif; ?>

    <?php if ($projectList): ?>
    <div class="row-content project-content">
        <div class="common-title">项目经历
            <div class="underline"></div>
        </div>
        <?php foreach ($projectList as $index => $item): ?>
        <div class="row">
            <div class="jc-between-title clear">
                <div class="title">
                    <img style="margin-bottom:3px;" width="4px" src="https://img.gaoxiaojob.com/uploads/static/image/resume/dot.png" alt="">
                    <span class="name"><?php echo $item['name'] ?></span>
                    <?php if($item['categoryName']){?><span class="tag-orange">&nbsp;<?php echo $item['categoryName'] ?>&nbsp;</span><?php }?>
                    <span class="tag-orange">&nbsp;<?php if ($item['isClose']==1): ?>
                        （已结项）<?php else: ?>（未结项）<?php endif ?>&nbsp;</span>
                </div>
                <div class="aside"><?php echo $item['beginDate'] ?> - <?php echo $item['endDate'] ?></div>
            </div>
            <div class="role">
                <?php if(strlen($item['role'])>0){?>担任角色：<?php echo $item['role'] ?>  ｜ <?php }?>  <?php if(strlen($item['company'])>0){?> 所属单位：<?php echo $item['company'] ?><?php }?>
            </div>
            <?php if(strlen($item['description'])>0){?>
            <div class="text-description clear">
                <div class="label">项目描述：</div>
                <pre class="text-content"><?php echo $item['description'] ?></pre>
            </div>
            <?php }?>
        </div>
        <?php endforeach ?>
    </div>
    <?php endif; ?>

    <?php if ($pageList || $patentList ||$bookList): ?>
    <div class="row-content">
        <div class="common-title">学术成果
            <div class="underline"></div>
        </div>

        <?php if ($pageList): ?>
        <div class="row">
            <div class="classify-title">学术论文</div>
            <?php foreach ($pageList as $index => $item): ?>
            <div class="list">
                <div class="jc-between-title clear">
                    <div class="title">
                        <img style="margin-bottom:3px;" width="4px" src="https://img.gaoxiaojob.com/uploads/static/image/resume/dot.png" alt="">
                        <span class="name"><?php echo $item['title'] ?></span>
                    </div>
                    <div class="aside"><?php echo $item['publishDate'] ?></div>
                </div>
                <div class="between-content">
                    <div class="list">
                        <div class="col-1">刊物名/卷（期号）：<?php echo $item['serialNumber'] ?></div>
                        <div class="col-2">收录情况：<?php echo $item['recordSituation'] ?></div>
                    </div>
                    <div class="list">
                        <?php if(strlen($item['position'])>0){?><div class="col-1">本人位次：<?php echo $item['positionText'] ?></div><?php }?>
                        <?php if(strlen($item['impactFactor'])>0){?><div class="col-2">影响因子：<?php echo $item['impactFactor'] ?></div><?php }?>
                    </div>
                </div>
                <?php if(strlen($item['description'])>0){?>
                <div class="text-description clear">
                    <div class="label">论文描述：</div>
                    <pre class="text-content"><?php echo $item['description'] ?></pre>
                </div>
                <?php }?>
            </div>
            <?php endforeach ?>
        </div>
        <?php endif; ?>

        <?php if ($patentList): ?>
        <div class="row">
            <div class="classify-title">学术专利</div>
            <?php foreach ($patentList as $index => $item): ?>
            <div class="list">
                <div class="jc-between-title clear">
                    <div class="title">
                        <img style="margin-bottom:3px;" width="4px" src="https://img.gaoxiaojob.com/uploads/static/image/resume/dot.png" alt="">
                        <span class="name"><?php echo $item['name'] ?></span>
                    </div>
                    <div class="aside"><?php echo $item['authorizationDate'] ?></div>
                </div>
                <div class="between-content">
                    <div class="list">
                        <?php if(strlen($item['positionText'])>0){?><div class="col-1">本人位次：<?php echo $item['positionText'] ?></div><?php }?>
                        <?php if(strlen($item['number'])>0){?><div class="col-2">专利编号：<?php echo $item['number'] ?></div><?php }?>
                    </div>
                    <?php if(strlen($item['finishStatus'])>0){?>
                    <div class="list">
                        <div class="col-1">完成状态：<?php echo $item['finishStatus'] ?></div>
                    </div>
                    <?php }?>
                </div>
                <?php if(strlen($item['description'])>0){?>
                <div class="text-description clear">
                    <div class="label">专利描述：</div>
                    <pre class="text-content"><?php echo $item['description'] ?></pre>
                </div>
                <?php }?>
            </div>
            <?php endforeach ?>
        </div>
        <?php endif; ?>

        <?php if ($bookList): ?>
        <div class="row book-content">
            <div class="classify-title">学术专著</div>
            <?php foreach ($bookList as $index => $item): ?>
            <div class="second-list">
                <div class="jc-between-title clear">
                    <div class="title">
                        <img style="margin-bottom:3px;" width="4px" src="https://img.gaoxiaojob.com/uploads/static/image/resume/dot.png" alt="">
                        <span class="name"><?php echo $item['name'] ?></span>
                    </div>
                    <div class="aside"><?php echo $item['publishDate'] ?></div>
                </div>
                <div class="common-details">字数（万字）：<?php echo $item['words'] ?><?php if(strlen($item['publishAmount'])>0){?>  |
                    发行数量：<?php echo $item['publishAmount'] ?><?php }?>
                </div>
            </div>
            <?php endforeach ?>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <?php if ($rewardList || $otherRewardList): ?>
    <div class="row-content">
        <div class="common-title">荣誉奖励
            <div class="underline"></div>
        </div>

        <?php if ($rewardList): ?>
        <div class="row">
            <div class="classify-title">学术奖励</div>
            <?php foreach ($rewardList as $index => $item): ?>
            <div class="second-list">
                <div class="jc-between-title clear">
                    <div class="title">
                        <img style="margin-bottom:3px;" width="4px" src="https://img.gaoxiaojob.com/uploads/static/image/resume/dot.png" alt="">
                        <span class="name"><?php echo $item['name'] ?></span>
                    </div>
                    <div class="aside"><?php echo $item['obtainDate'] ?></div>
                </div>
                <div class="common-details"><?php if(strlen($item['level'])>0){?>奖励级别：<?php echo $item['level']; }?><?php if(strlen($item['role'])>0&&strlen($item['level'])>0){?> | <?php }?><?php if(strlen($item['role'])>0){?> 获奖角色：<?php echo $item['role']; }?></div>
            </div>
            <?php endforeach ?>
        </div>
        <?php endif; ?>

        <?php if ($otherRewardList): ?>
        <div class="row">
            <div class="classify-title">其他荣誉</div>
            <?php foreach ($otherRewardList as $index => $item): ?>
            <div class="second-list">
                <div class="jc-between-title clear">
                    <div class="title">
                        <img style="margin-bottom:3px;" width="4px" src="https://img.gaoxiaojob.com/uploads/static/image/resume/dot.png" alt="">
                        <span class="name"><?php echo $item['name'] ?></span>
                    </div>
                    <div class="aside"><?php echo $item['obtainDate'] ?></div>
                </div>
                <div class="common-details"><?php if(strlen($item['level'])>0){?>奖励级别：<?php echo $item['level'];}?><?php if(strlen($item['role'])>0&&strlen($item['level'])>0){?> | <?php }?><?php if(strlen($item['role'])>0){?> 获奖角色：<?php echo $item['role'];}?></div>
            </div>
            <?php endforeach ?>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <?php if ($certificateList || $skillList || $otherSkillList): ?>

    <div class="row-content">
        <div class="common-title">技能/语言
            <div class="underline"></div>
        </div>
        <?php if ($certificateList): ?>
        <div class="row">
            <div class="classify-title">资质证书</div>
            <?php foreach ($certificateList as $index => $item): ?>
            <div class="second-list">
                <div class="jc-between-title clear">
                    <div class="title">
                        <img style="margin-bottom:3px;" width="4px" src="https://img.gaoxiaojob.com/uploads/static/image/resume/dot.png" alt="">
                        <span class="name"><?php echo $item['certificateName'] ?></span>
                    </div>
                    <div class="aside"><?php echo $item['obtainDate'] ?></div>
                </div>
                <?php if(strlen($item['score'])>0){?><div class="common-details">成绩：<?php echo $item['score'] ?></div><?php }?>
            </div>
            <?php endforeach ?>
        </div>
        <?php endif; ?>

        <?php if ($skillList): ?>
        <div class="row skill-content">
            <div class="classify-title">技能/语言</div>
            <?php foreach ($skillList as $index => $item): ?>
            <div class="second-list">
                <div class="common-details">
                    <img style="margin-bottom:3px;" width="4px" src="https://img.gaoxiaojob.com/uploads/static/image/resume/dot.png" alt="">
                    <span class="bold"><?php echo $item['skillName'] ?></span>
                    <?php if(strlen($item['degreeTypeName'])>0){?>&nbsp;|&nbsp;
                    掌握程度：<?php echo $item['degreeTypeName'] ?>
                    <?php }?>
                </div>
            </div>
            <?php endforeach ?>
        </div>
        <?php endif; ?>

        <?php if ($otherSkillList): ?>
        <div class="row">
            <div class="classify-title">其他技能</div>
            <?php foreach ($otherSkillList as $index => $item): ?>
            <div class="second-list">
                <div class="common-details">
                    <img style="margin-bottom:3px;" width="4px" src="https://img.gaoxiaojob.com/uploads/static/image/resume/dot.png" alt="">
                    <span class="bold"><?php echo $item['name'] ?></span>
                    <?php if($item['degreeTypeName']){?>&nbsp;|&nbsp;
                    掌握程度：<?php echo $item['degreeTypeName'] ?>
                    <?php }?>
                </div>
                <?php if(strlen($item['description'])>0){?>
                <div class="text-description clear">
                    <div class="label">技能说明:</div>
                    <pre class="text-content"><?php echo $item['description'] ?></pre>
                </div>
                <?php }?>
            </div>
            <?php endforeach ?>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <?php if ($addInfoList): ?>
    <div class="row-content addition-content">
        <div class="common-title">附加信息
            <div class="underline"></div>
        </div>
        <?php foreach ($addInfoList as $index => $item): ?>
        <div class="row">
            <div class="title">
                <img style="margin-bottom:3px;" width="4px" src="https://img.gaoxiaojob.com/uploads/static/image/resume/dot.png" alt="">
                <span class="name"><?php echo $item['themeName']?$item['themeName']:$item['themeIdName'] ?></span>
            </div>
            <?php if(strlen($item['content'])>0){?>
            <div class="text-description clear">
                <div class="label">主题描述：</div>
                <pre class="text-content"><?php echo $item['content'] ?></pre>
            </div>
            <?php }?>
        </div>
        <?php endforeach ?>
    </div>
    <?php endif; ?>

    <?php if ($operatorType==2): ?>
    <div class="row-content statement-content">
        <div class="footer-logo">
            <img width="80px" src="https://img.gaoxiaojob.com/uploads/static/image/resume/logo-gray.png" alt="">
        </div>
        声明：该简历信息仅供下载本简历的用人单位招聘使用，禁止用于招聘以外的任何用途，禁止使用简历信息或利用平台信息从事任何违法违规活动。否则，平台方有权单方决定采取一切必要措施，包括但不限于删除发布内容，限制、暂停使用，终止服务等。
    </div>
    <?php endif; ?>
</div>
