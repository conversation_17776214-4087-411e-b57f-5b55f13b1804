<style>
.resume-wrapper {
    font-family: simhei;
    font-size: 14px;
    width: 827px;
    /* padding: 0px 60px 150px 60px; */
    background-color: #fff;
}

.resume-wrapper .clear::after {
    display: inline-block;
    content: '';
}
.dislineBlock {
    display: inline-block;
}

.bold {
    font-weight: bold;
}

.common-details {
    margin-top: 7px;
}

.common-title {
    font-size: 18px;
    font-weight: bold;
    line-height: 1;
    padding-top: 16px;
    padding-bottom: 15px;
}

.common-title .job-intention {
    /* width: 120%; */
    
    background: url(/static/images/template/job-intention.png) no-repeat;
    background-size: contain;
    height: 25px;
}
.common-title .education-intention {
    /* width: 120%; */
    
    background: url(/static/images/template/education-intention.png) no-repeat;
    background-size: contain;
    height: 25px;
}
.common-title .research-intention {
    /* width: 120%; */
    
    background: url(/static/images/template/research-intention.png) no-repeat;
    background-size: contain;
    height: 25px;
}
.common-title .work-intention {
    /* width: 120%; */
    
    background: url(/static/images/template/work-intention.png) no-repeat;
    background-size: contain;
    height: 25px;
}
.common-title .project-intention {
    /* width: 120%; */
    
    background: url(/static/images/template/project-intention.png) no-repeat;
    background-size: contain;
    height: 25px;
}
.common-title .achievement-intention {
    /* width: 120%; */
    
    background: url(/static/images/template/achievement-intention.png) no-repeat;
    background-size: contain;
    height: 25px;
}
.common-title .rewards-intention {
    /* width: 120%; */
    
    background: url(/static/images/template/rewards-intention.png) no-repeat;
    background-size: contain;
    height: 25px;
}
.common-title .skill-intention {
    /* width: 120%; */
    
    background: url(/static/images/template/skill-intention.png) no-repeat;
    background-size: contain;
    height: 25px;
}
.common-title .append-intention {
    /* width: 120%; */
    
    background: url(/static/images/template/append-intention.png) no-repeat;
    background-size: contain;
    height: 25px;
}
.common-title .advantage-intention {
    /* width: 120%; */
    
    background: url(/static/images/template/advantage-intention.png) no-repeat;
    background-size: contain;
    height: 25px;
}


.flleft{
    float: left;
}
.flright{
    float: right;
    text-align: right;
}

.research div {
    /* display: block; */
    padding: 0px 6px;
    line-height: 1.8;
}

.cell {
    background: #F6F8FF;
    color: #243C6E;
    text-align: left;
    padding: 5px 10px;
    font-weight: bold;
}
.cell .first-title {
    width: 35%;

}
.cell .two-title {
    width: 35%;

}
.cell .three-title {
    width: 15%;

}
.cell .four-title {
    width: 13%;
    text-align: right;
    
}
.cell-color .first-title {
    width: 32%;
}
.cell-color .two-title {
    width: 32%;
}
.cell-color .three-title {
    width: 15%;
}
.cell-color .four-title {
    width: 10%;
    text-align: right;
}
.text-top {
    vertical-align: top;
}
.cell-color {
    border-bottom: 1px solid #f2f2f2;
    padding: 5px 10px;
    font-weight: 100;
}

.cell-last{
    /* padding: 5px 10px; */
    padding: 8px 10px;
}
.cell-last .first-title {
    width: 32%;
    float: left;
}
.cell-color div {
    padding: 8px 0;
}

.classify-title {
    font-size: 15px;
    font-weight: bold;
    text-align: center;
    line-height: 30px;
    border-radius: 4px;
    color: #ffa000;
    background-color: #ffedd1;
    margin-bottom: 12px;
}

.between-content .list {
    margin: 10px 0 5px;
}

.between-content .col-1 {
    float: left;
    width: 59%;
}

.between-content .col-2 {
    float: left;
}

.resume-wrapper .clear::after {
    display: inline-block;
    content: '';
}


.row-content .row {
    padding: 5px 0;
}

.resume-wrapper .spacing-1 {
    padding: 0 20px 10px 30px;
}

.resume-wrapper .spacing-2 {
    padding-bottom: 5px;
}

.has-line {
    line-height: 1;
    border-right: 2px solid #666;
}

/* .bluedot {
    font-size: 15px;
    line-height: 15px;
    font-weight: bold;
    color: #FFA000;
    font-family: gb;
} */

.tag-blue {
    font-size: 10px;
    color: #3075B6;
    background-color: #F6F8FF;
    border-radius: 4px;
    text-align: center;
}

.tag-practice {
    font-size: 12px;
    line-height: 24px;
    color: #FFA000;
    background-color: #FFF3E0;
    border-radius: 4px;
}

.tag-orange {
    font-size: 10px;
    color: #E97E32;
    background-color: #FFEEE4;
    /* border: 1px solid #FFEEE4; */
    border-radius: 4px;
    text-align: center;
}
.orange{
    background: url(/static/images/template/orange.png) no-repeat;
    background-size: 100% 100%;
    color: #ffa000;
    font-size: 10px;
    padding: 2px 8px;
    text-align: center;
    width: 60px;
    /* height: 25px; */
    float: left;
}
.tag-black {
    width: 58px;
    font-size: 14px;
    color: #333333;
    background-color: #fff;
    border: 1px solid #333333;
    border-radius: 15px;
    padding: 1px 5px;
    font-weight: bold;
    text-align: center;
    
}
.marg-special{
    margin: -15px 0 15px 0;
}


/* .jc-between-title .title {
    float: left;
    width: 79%;
}

.jc-between-title .name {
    font-size: 15px;
    font-weight: bold;
    margin-left: 5px;
}

.jc-between-title .aside {
    float: right;
    text-align: right;
} */

.second-list {
    padding-bottom: 15px;
}

.text-description .label {
    width: 75px;
    float: left;
    line-height: 2;
}

.text-description .text-content {
    float: left;
}

.text-content {
    line-height: 1.6;
    padding: 5px 0 0px;
    margin-top: -2px;
    font-family: simhei;
}

/* 顶部 start */
.resume-wrapper .top {
    padding-bottom: 5px;
}

.resume-wrapper .top .logo {
    width: 123px;
    height: 36px;
    background-image: url(https://img.gaoxiaojob.com/uploads/static/image/logo/logo_home.png);
    background-size: 100%;
    background-repeat: no-repeat;
    float: left;
    background-color: white;
}

.resume-wrapper .top .update {
    float: right;
    text-align: right;
    padding-top: 12px;
    font-size: 13px;
    color: #494949;
}

/* 顶部 end */

/* 个人信息 start */
.apply-job {
    padding-top: 16px;
}

.info-content {
    /* display:inline-block; */
    background: url(https://img.gaoxiaojob.com/uploads/static/image/resume/astral.jpg) no-repeat;
    background-size: contain;
    /* height: 325px;     */
    width: 100%;
    padding: 20px 0 0 30px;
}

.avatar-content{
    float: left;
    width: 90px;
    text-align: center;
    margin-right: 30px;
    margin-left: 5px;
    margin-top: 40px;
}
.avatar-content .name{
    /* width: 110px; */
    color: #243C6E;
    font-weight: bold;
    font-size: 18px;
    margin-top: 8px;

}

.info-content .avatar {
    border: 2px solid #fff;
    display: block;
    border-radius: 50%;
    width: 100%;
    height: 90px;
    background-size: 100% 100%;
    margin-bottom: 8px;
}

.info-content .info {
    /* padding-top: -10px; */
    padding: 15px 0 5px 30px;
    border-radius: 10px;
    /* padding-bottom: 20px; */
    float: left;
    background-color: #fff;
    /* width: calc(100% - 10px); */
    /* width: 450px; */
    margin: 0 30px;
    box-shadow: 0px 3px 7px 3px rgba(184,184,184,0.2);
    margin-top: -10px;
    
}


.info-content .name {
    font-size: 22px;
    font-weight: bold;
    line-height: 1;
    word-wrap: break-word;
    word-break: break-all;
}

.info-content .mobile {
    float: left;
    width: 160px;
    background-image: url(/static/images/template/blue-phone.png);
    padding-left: 20px;
    background-repeat: no-repeat;
    background-size: 40px 40px;
    background-position: left;
}

.info-content .email {
    float: left;
    background-image: url(/static/images/template/blue-mailbox.png);
    padding-left: 20px;
    background-repeat: no-repeat;
    background-size: 40px 40px;
    background-position: left;
}


.advantage-content {
    padding-top: 6px;
}

/* 个人信息 end */

/* 工作经历 start */
.work-content {
    padding: 0 0 10px 7px;
}
.work-details {
    padding:  6px 0 10px 7px;
}

.work-content .position .name {
    font-size: 15px;
    font-weight: bold;
}
.school-name {
    margin-right: 5px;
    font-weight: 700;
}
.ma-top {
    margin-top: 20px;
}
.ma-top15 {
    margin-top: 15px;
}
.ma-r40 {
    margin-right: 40px;
}
.job-date {
    border-bottom: 1px solid #f2f2f2;
}
.job-name{
    width: 100%;
}
.job-name .date {
    float: right;
    color: #6e6e6e;
}

.job-details {
    padding:  6px 0 15px 7px;
}
.work-date {
    border-bottom: 1px solid #f2f2f2;
}

.work-name .date {
    float: right;
    color: #6e6e6e;
}


/* 工作经历 end */

/* 科研项目 start */
.project-content {
    padding: 0 0 10px 7px;
}
.project-details {
    padding:  6px 0 10px 7px;
}

.project-date {
    border-bottom: 1px solid #f2f2f2;
}

.project-name .date {
    float: right;
    color: #6e6e6e;
}


/* 科研项目 end */

/* 学术成果 start */
.science-content {
    padding: 0 0 10px 7px;
    float: left;
    line-height: 1.8;
}
.science-details {
    padding:  6px 0 10px 7px;
}

.science-date {
    border-bottom: 1px solid #f2f2f2;
}
.science-name .date {
    float: right;
    color: #6e6e6e;
}

/* 学术成果 end */

/* 荣誉奖励 start */

.honor-date {
    border-bottom: 1px solid #f2f2f2;
}
.honor-details {
    padding:  6px 0 10px 7px;
}
.honor-all .name {
    font-weight: bold;
}

/* 荣誉奖励 end */

/* 技能/语言 end */


.skill-date {
    border-bottom: 1px solid #f2f2f2;
}

.skill-date .col-name{
    width: 53%!important;
    padding-left: 1%;
}
.skill-date .col-score{
    width: 30%!important;
    padding-left: 1%;
}
.skill-date .col-time{
    width: 13%!important;
    padding-left: 1%;
}

.skill-name .date {
    float: right;
    color: #6e6e6e;
}
.skill-details {
    padding: 15px 0;
}

/* 技能/语言 end */

/* 附加信息 start */
.append-title{
        float: left;
        width: 80%;
    }
.append-date {
    border-bottom: 1px solid #f2f2f2;
}
.append-content {
    padding: 0 0 12px 7px;
}
/* 附加信息 end */

/* 个人优势 start */


/* 个人优势 end */

/* 底部 start */
.statement-content {
    font-size: 13px;
    padding: 20px 45px;
    text-align: center;
    color: #8d8d90;
    line-height: 2;
}
.statement-content .footer-logo {
    text-align: center;
}
.education-title{
    float: left;
    width: 80%;
}
.skill-title{
    float: left;
    width: 250px;
}
.jc-between-title{
    float: left;
    width: 32%;
}

.jc-between-title .name {
    font-size: 14px;
    font-weight: 100;
}
.label {
    width: 70px;
    float: left;
    /* line-height: 2; */
}
.color6e{
    color: #6e6e6e;
}

.text-description{
    float: left;
    padding-left: 7px;
    line-height: 1.8;
}

.ma-top10 {
    margin-bottom: 10px;
}
.ma-bot20 {
    margin-bottom: 20px;
}
.ma-bot10 {
    margin-bottom: 10px;
}
.ma-bot15 {
    margin-bottom: 15px;
}
.tag-top{
    width: 60px;
    font-size: 10px;
    color: #243C6E;
    background-color: #fff;
    border: 1px solid #243C6E;
    border-radius: 3px;
    padding: 1px 5px;
    text-align: center;
    margin-left: 5px;
    float: left;
}
.font16{
    font-size: 16px;
}
.orangeline {
    float: left;
    width: 140px;
    background-image: url(/static/images/template/orange-line.png);
    padding-left: 10px;
    background-repeat: no-repeat;
    background-size: 8px 46px;
    background-position: left;
    font-weight: bold;
    font-size: 18px;
    color: #243C6E;
}
.male-0{
    margin-left: -1px;
}
.male-5{
    margin-left: -5px;
}
.male-15{
    margin-left: 22px;
}
.resume-wrapper .borderbot{
    border-bottom: none;
}
.cell-color .borderbot{ 
    border-bottom:none;
}

/* 底部 end */

</style>

<div class="resume-wrapper">
    <div class="row-content">
        <div class="info-content">
            <div class="avatar-content" >
                <div class="avatar" style="background: url(<?= $userInfo['avatar'] ?>) no-repeat center/contain"></div>
                <span class="name"><?= $userInfo['name'] ?></span>
            </div>
            <div class="info">
                <div class="row clear male-5">
                    <?php foreach ($userInfo['updateResumeInfo']['tag'] as $v): ?>
                       <span class="tag-top">&nbsp;<?= $v ?>&nbsp;</span>&nbsp;
                   <?php endforeach ?>
                </div>
                <div class="row clear">
                    <div class="row clear">
                        <?php
                            $info=[];
                            if($userInfo['genderTxt'] && trim($userInfo['genderTxt'])!='-'){ array_push($info,$userInfo['genderTxt']);}
                            if($userInfo['age']){ array_push($info,$userInfo['age'].'岁');}
                            if($userInfo['educationName'] && trim($userInfo['educationName'])!='-'){ array_push($info,$userInfo['educationName']);}
                            if($userInfo['politicalStatusName'] && trim($userInfo['politicalStatusName'])!='-'){ array_push($info,$userInfo['politicalStatusName']);}
                            if($userInfo['workExperience']){ array_push($info,$userInfo['workExperience']);}
                            echo implode(' · ',$info);
                        ?>
                    </div>
                    <div class="row clear">
                            <?php if($userInfo['nativePlaceAreaTxt'] && trim($userInfo['nativePlaceAreaTxt'])!='-'){ ?>
                                <span>籍贯：<?=$userInfo['nativePlaceAreaTxt']?></span>
                            <?php }?>
                            <?php if($userInfo['householdRegisterText'] && trim($userInfo['householdRegisterText'])!='-'){ ?>
                                <?php if($userInfo['nativePlaceAreaTxt'] && trim($userInfo['nativePlaceAreaTxt'])!='-'){ ?>
                                <span>&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                <?php } ?>
                                <span>户籍：<?=$userInfo['householdRegisterText'] ?></span>
                            <?php } ?>
                            <?php if($userInfo['residenceTxt'] && trim($userInfo['residenceTxt'])!='-'){ ?>
                                <?php if($userInfo['householdRegisterText'] && trim($userInfo['householdRegisterText'])!='-'){ ?>
                                <span>&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                <?php }?>
                                <span>现居住地：<?=$userInfo['residenceTxt']?></span>
                             <?php }?>
                             <?php if($userInfo['nationTxt'] && trim($userInfo['nationTxt'])!='-'){ ?>
                                <?php if($userInfo['residenceTxt'] && trim($userInfo['residenceTxt'])!='-'){ ?>
                                <span>&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                <?php }?>
                                <span>民族：<?=$userInfo['nationTxt']?></span>
                             <?php }?>

                    </div>
                    <div class="row contact clear">
                        <div class="mobile"><?php if($userInfo['fullMobile']){echo '电话：'.$userInfo['fullMobile'];} ?></div>
                        <div class="email"><?php if($userInfo['email']){echo '邮箱：'.$userInfo['email'];} ?></div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>

    <div class="intention-content spacing-1 ma-top15">
        <!--个人优势开始-->
        <?php if ($userInfo['advantage']){?>
        <div class="common-title">
            <div class="advantage-intention"></div>
        </div>
        <div class="text-description clear">
            <pre class="text-content"><?= $userInfo['advantage'] ?></pre>
        </div>
        <?php }?>
        <!--个人优势结束-->

        <!--求职意向开始-->
        <?php if ($intentionList): ?>
        <div class="common-title">
             <div class="job-intention"></div>
        </div>
        <div class="table">
            <div class="cell clear">
                <div class="first-title flleft">意向职能</div>
                <div class="two-title flleft">意向城市</div>
                <div class="three-title flleft">期望月薪</div>
                <div class="four-title flleft">工作性质</div>
            </div>
            <?php foreach ($intentionList as $value){ ?>
            <div class="cell-color">
                <div class="first-title text-top text flleft"><?= $value['jobCategoryName'] ?></div>
                <div class="two-title text-top male-15 flleft"><?= $value['areaName'] ?></div>
                <div class="three-title text-top male-15 flleft"><?= $value['wageName'] ?></div>
                <div class="four-title text-top male-15 flleft"><?= $value['natureName'] ?></div>
            </div>
            <?php } ?>
            <div class="cell-last borderbot clear" >
                <div class="first-title">
                    <span class="label">求职状态：</span>
                    <span><?= $userInfo['workStatusName'] ?></span>
                </div>
                <div class="two-title male-15 flleft">
                    <span class="label">到岗时间：</span>
                    <span><?= $userInfo['arriveDateTypeName']?></span>
                </div>
            </div>
        </div>
        <?php endif; ?>
        <!--求职意向结束-->

        <!--教育经历开始-->
        <?php if ($educationList){ ?>
        <div class="common-title">
            <div class="education-intention"></div>
        </div>
        <div class="job-all">
            <?php foreach ($educationList as $index => $item): ?>
            <div class="job-date clear <?= $index>0?' ma-top15 ':'' ?> <?= $index+1==count($educationList)?' borderbot ':'' ?>">
                <div class="clear">
                    <div class="education-title">
                        <img style="margin-bottom:2px;" width="4px" src="/static/images/template/bluedot.png" alt="">
                        <span class="name first-title bold font16"><?= $item['school'] ?></span>&nbsp;
                        <?php if ($item['isOverseasStudy']==1): ?>
                            <span  class="tag-blue">&nbsp;海外&nbsp;</span>
                        <?php endif; ?>
                        <?php if ($item['is_project_school']==1): ?>
                            <span class="tag-orange ">&nbsp;985/211&nbsp;</span>
                        <?php endif; ?>
                    </div>
                        
                    <div class="color6e flright"><?= $item['studyBeginDate'] ?> - <?= $item['studyEndDate'] ?></div>
                </div>
                <div class="job-details color6e">
                    <?php
                        $info=[];
                        if($item['educationName']){ array_push($info,$item['educationName'].($item['isRecruitment']==1?'（统招）':''));}
                        if($item['majorName']){ array_push($info,'专业：'.$item['majorName']);}
                        if($item['college']){ array_push($info,'二级院系（机构）：'.$item['college']);}
                        if($item['mentor']){ array_push($info,'导师：'.$item['mentor']);}
                        echo implode(' | ',$info);
                    ?>
                </div>
            </div>
            <?php endforeach ?>
        </div>
        <?php } ?>
        <!--教育经历结束-->

        <!--研究方向开始-->
        <?php if ($researchDirection){ ?>
        <div class="common-title ma-top">
            <div class="research-intention"></div>
        </div>
        <div class="research">
            <div><?= $researchDirection ?></div>
        </div>
        <?php } ?>
        <!--研究方向开结束-->

        <!-- 工作经历开始-->
        <?php if ($workList){ ?>
        <div class="common-title ma-top">
            <div class="work-intention"></div>
        </div>

        <div class="work-all">
            <?php foreach ($workList as $index => $item): ?>
            <div class="work-date clear <?= $index>0?' ma-top15 ':'' ?> <?= $index+1==count($workList)?' borderbot ':'' ?>">
                <div class="clear">
                        <div class="education-title">
                            <img style="margin-bottom:3px;" width="4px" src="/static/images/template/bluedot.png" alt="">
                            <span class="name first-title font16 bold"><?= $item['company'] ?></span>&nbsp;
                            <?php if ($item['isOverseas']==1): ?><span class="tag-blue">&nbsp;海外&nbsp;</span><?php endif ?>
                            <?php if ($item['isPostdoc']==1): ?><span class="tag-blue">&nbsp;博士后&nbsp;</span><?php endif ?>
                            <?php if ($item['isPractice']==1): ?><span class="tag-practice">&nbsp;实习&nbsp;</span><?php endif ?>
                        </div>
                    <div class="color6e flright"><?= $item['jobBeginDate'] ?> - <?= $item['jobEndDate'] ?></div>
                </div>
                <div class="work-details">
                    <?php
                        $info=[];
                        if($item['jobName']){ array_push($info,$item['jobName']);}
                        if($item['department']){ array_push($info,$item['department']);}
                        echo implode(' | ',$info);
                    ?>
                </div>
                <?php if($item['jobContent']){?>
                <div class="work-content color6e">
                    <div class="ma-bot10">工作内容：</div>
                    <div style="line-height: 1.8;"><?= $item['jobContent'] ?></div>
                </div>
                <?php }?>
            </div>
            <?php endforeach ?>
        </div>
        <?php } ?>
        <!-- 工作经历结束-->

        <!-- 科研项目开始-->
        <?php if ($projectList): ?>
        <div class="common-title ma-top10">
            <div class="project-intention"></div>
        </div>
        <div class="project-all">
            <?php foreach ($projectList as $index => $item): ?>
            <div class="project-date clear <?= $index>0?' ma-top15 ':'' ?> <?= $index+1==count($projectList)?' borderbot ':'' ?>">
                <div class="clear">
                        <div class="education-title">
                            <img style="margin-bottom:3px;" width="4px" src="/static/images/template/bluedot.png" alt="">
                            <span class="name first-title font16 bold"><?= $item['name'] ?></span>&nbsp;
                            <?php if($item['categoryName']){?><span class="tag-orange">&nbsp;<?= $item['categoryName'] ?>&nbsp;</span><?php }?>
                            <span class="tag-orange">&nbsp;<?php if ($item['isClose']==1){ echo '已结项'; }else{echo ' 未结项';} ?>&nbsp;</span>
                        </div>   
                        <div class="color6e flright"><?= $item['beginDate'] ?> - <?= $item['endDate'] ?></div>
                </div>
                <div class="project-details">
                <?php
                    $info=[];
                    if($item['role']){ array_push($info,$item['role']);}
                    if($item['company']){ array_push($info,$item['company']);}
                    echo implode(' | ',$info);
                ?>
                </div>
                <?php if($item['description']){?>
                <div class="project-content  color6e">
                    <div class="ma-bot10">项目描述：</div>
                    <div style="line-height: 1.8;"><?= $item['description'] ?></div>
                </div>
                <?php }?>
            </div>
            <?php endforeach ?>
        </div>
        <?php endif; ?>
        <!-- 科研项目结束-->

        <!-- 学术成果开始-->
        <?php if ($pageList || $patentList ||$bookList): ?>
        <div class="common-title ma-top">
            <div class="achievement-intention"></div>
        </div>
        <div class="science-all clear" >
            <!-- 学术论文开始-->
            <?php if ($pageList): ?>
            <div class="science-thesis">
                <div class="ma-bot15 clear">
                    <div class="orangeline">学术论文</div>
                </div>
                <?php foreach ($pageList as $index => $item){ ?>
                <div class="science-date clear ma-bot15  <?= $index+1==count($pageList)?' borderbot ':''; ?>">
                    <div class="clear">
                            <div class="education-title">
                                <img style="margin-bottom:3px;" width="4px" src="/static/images/template/bluedot.png" alt="">
                                <span class="name first-title font16 bold"><?= $item['title'] ?></span>
                            </div>
                        <div class="color6e flright"><?= $item['publishDate'] ?></div>
                    </div>
                    <div class="science-details">
                        <?php
                          $info=[];
                          if($item['serialNumber']){ array_push($info,'刊物名/卷（期号）：'.$item['serialNumber']);}
                          if($item['recordSituation']){ array_push($info,'收录情况：'.$item['recordSituation']);}
                          if($item['positionText']){ array_push($info,'本人位次：'.$item['positionText']);}
                          if($item['impactFactor']){ array_push($info,'影响因子：'.$item['impactFactor']);}
                          echo implode(' | ',$info);
                        ?>
                    </div>
                    <?php if($item['description']){ ?>
                    <div class="text-description color6e clear">
                        <div class="label">论文描述：</div>
                        <pre class="text-content"><?= $item['description'] ?></pre>
                    </div>
                    <?php } ?>
                </div>
                <?php } ?>
            </div>
            <?php endif; ?>
            <!-- 学术论文结束-->

            <!-- 学术专利开始-->
            <?php if ($patentList): ?>
            <div class="science-patent">
                <div class="ma-bot15 clear">
                    <div class="orangeline">学术专利</div>
                </div>
                <?php foreach ($patentList as $index => $item): ?>
                <div class="science-date clear ma-bot15  <?= $index+1==count($patentList)?' borderbot ':''; ?>">
                    <div class="clear">
                            <div class="education-title">
                                <img style="margin-bottom:3px;" width="4px" src="/static/images/template/bluedot.png" alt="">
                                <span class="name first-title font16 bold"><?= $item['authorizationDate'] ?></span>
                            </div>    
                        <div class="color6e flright"><?= $item['authorizationDate'] ?></div>
                    </div>
                    <div class="science-details">
                        <?php
                            $info=[];
                            if($item['positionText']){ array_push($info,'本人位次：'.$item['positionText']);}
                            if($item['number']){ array_push($info,'专利编号：'.$item['number']);}
                            if($item['finishStatus']){ array_push($info,'完成状态：'.$item['finishStatus']);}
                            echo implode(' | ',$info);
                        ?>
                    </div>
                    <?php if($item['description']){?>
                    <div class="text-description color6e clear">
                        <div class="label">专利描述：</div>
                        <pre class="text-content"><?=$item['description']?></pre>
                    </div>
                    <?php }?>
                </div>
                <?php endforeach ?>
            </div>
            <?php endif; ?>
            <!-- 学术专利结束-->

            <!-- 学术专著开始-->
            <?php if ($bookList): ?>
            <div class="science-patent">
                <div class="ma-bot15 clear">
                    <div class="orangeline">学术专著</div>
                </div>
                <div class="science-date clear ma-bot15 borderbot">
                    <div class="clear">
                        <div class="cell clear">
                            <div class="first-title flleft">著作名称/出版社名称</div>
                            <div class="two-title flleft">字数（万字）</div>
                            <div class="three-title flleft">发行数量</div>
                            <div class="four-title flleft">出版日期</div>
                        </div>
                        <?php foreach ($bookList as $index => $item): ?>
                        <div class="cell-color <?= $index+1==count($bookList)?' borderbot ':''; ?>">
                            <div class="first-title text-top text flleft"><?= $item['name'] ?></div>
                            <div class="two-title text-top male-15 flleft"><?= $item['words'] ?></div>
                            <div class="three-title text-top male-15 flleft"><?= $item['publishAmount'] ?></div>
                            <div class="four-title text-top male-15 flleft"><?= $item['publishDate'] ?></div>
                        </div>
                        <?php endforeach ?>
                    </div>
                </div>   
            </div>
            <?php endif; ?>
            <!-- 学术专著结束-->
        </div>
        <?php endif; ?>
        <!-- 学术成果结束-->

        <!-- 荣誉奖励开始-->
        <?php if ($rewardList || $otherRewardList): ?>
        <div class="common-title ma-top">
            <div class="rewards-intention"></div>
        </div>
        <div class="honor-all">
            <!-- 学术奖励开始-->
            <?php if ($rewardList): ?>
            <div class="honor-rewards">
                <div class="ma-bot15 clear">
                    <div class="orangeline">学术奖励</div>
                </div>
                <?php foreach ($rewardList as $index => $item): ?>
                <div class="honor-date clear ma-bot15  <?= $index+1==count($rewardList)?' borderbot ':''; ?>">
                    <div class="clear">
                            <div class="education-title">
                                <img style="margin-bottom:3px;" width="4px" src="/static/images/template/bluedot.png" alt="">
                                <span class="name first-title bold font16"><?= $item['name'] ?></span>
                            </div>    
                        <div class="color6e flright"><?= $item['obtainDate'] ?></div>
                    </div>
                    <div class="honor-details">
                        <?php
                           $info=[];
                           if($item['level']){ array_push($info,'奖励级别：'.$item['level']);}
                           if($item['role']){ array_push($info,'获奖角色：'.$item['role']);}
                           echo implode(' | ',$info);
                       ?>
                    </div>
                </div>
                <?php endforeach ?>
            </div>
            <?php endif; ?>
            <!-- 学术奖励结束-->

            <!-- 其他荣誉开始-->
            <?php if ($otherRewardList): ?>
            <div class="honor-other">
                <div class="ma-bot15 clear">
                    <div class="orangeline">其他荣誉</div>
                </div>
                <?php foreach ($otherRewardList as $index => $item): ?>
                <div class="honor-date clear ma-bot15 <?= $index+1==count($otherRewardList)?' borderbot ':''; ?>">
                    <div class="clear">
                            <div class="education-title">
                                <img style="margin-bottom:3px;" width="4px" src="/static/images/template/bluedot.png" alt="">
                                <span class="name first-title bold font16"><?= $item['name'] ?></span>
                            </div>    
                        <div class="color6e flright"><?= $item['obtainDate'] ?></div>
                    </div>
                    <div class="honor-details">
                         <?php
                            $info=[];
                            if($item['level']){ array_push($info,'奖励级别：'.$item['level']);}
                            if($item['role']){ array_push($info,'获奖角色：'.$item['role']);}
                            echo implode(' | ',$info);
                        ?>
                    </div>
                </div>
                <?php endforeach ?>
            </div>
            <?php endif; ?>
            <!-- 其他荣誉结束-->
        </div>
        <?php endif; ?>
        <!-- 荣誉奖励结束-->

        <!-- 技能特长开始-->
        <?php if ($certificateList || $skillList || $otherSkillList): ?>
        <div class="common-title ma-top">
            <div class="skill-intention"></div>
        </div>
        <div class="skill-all">
            <!-- 资质证书开始-->
            <?php if ($certificateList): ?>
            <div class="skill-certificate">
                <div class="ma-bot15 clear">
                    <div class="orangeline">资质证书</div>
                </div>
                <div class="skill-date clear ma-bot15 borderbot">
                    <div class="clear">
                        <div class="cell clear">
                            <div class="col-name flleft">证书名称</div>
                            <div class="col-score flleft">成绩</div>
                            <div class="col-time flleft">获得时间</div>
                        </div>
                        <?php foreach ($certificateList as $index => $item): ?>
                        <div class="cell-color <?= $index+1==count($certificateList)?' borderbot ':''; ?>">
                            <!-- <div class="first-title text-top text flleft"></div> -->
                            <div class="col-name jc-between-title clear">
                                <img style="margin-bottom:2px; margin-left: -10px;" width="4px" src="/static/images/template/bluedot.png" alt="">
                                <span class="name "><?= $item['certificateName'] ?></span>
                            </div>    
                            <div class="col-score text-top flleft"><?= $item['score'] ?></div>
                            <div class="col-time text-top flleft"><?= $item['obtainDate'] ?></div>
                        </div>
                        <?php endforeach ?>
                    </div>
                </div>  
            </div>
            <?php endif; ?>
            <!-- 资质证书结束-->

            <!-- 技能/语言开始-->
            <?php if ($skillList): ?>
            <div class="skill-lingo">
                <div class="ma-bot15 clear">
                    <div class="orangeline">技能/语言</div>
                </div>
                <div class="clear ma-bot15 borderbot">
                    <div class="clear">
                        <?php foreach ($skillList as $index => $item): ?>
                        <div class="clear  <?= $index==0?'':'ma-bot10' ?>">
                            <div class="skill-title ma-bot10" >
                                <img style="margin-bottom:2px;" width="4px" src="/static/images/template/bluedot.png" alt="">
                                <span class="name first-title"><?= $item['skillName'] ?></span>
                            </div>
                            <div style="margin-left: 10px;"><?= $item['degreeTypeName'] ?></div>
                        </div>
                        <?php endforeach ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            <!-- 技能/语言结束-->

            <!-- 其他技能开始-->
            <?php if ($otherSkillList): ?>
            <div class="skill-other">
                <div class="ma-bot15 clear">
                    <div class="orangeline">其他技能</div>
                </div>
                <?php foreach ($otherSkillList as $index => $item): ?>
                <div class="clear ma-bot15 <?= $index+1==count($otherSkillList)?' borderbot ':''; ?>">
                    <div class="clear">
                        <div class="clear ">
                            <div class="skill-title ma-bot10" >
                                <img style="margin-bottom:2px;" width="4px" src="/static/images/template/bluedot.png" alt="">
                                <span class="name first-title"><?= $item['name'] ?></span>
                            </div>    
                            <div style="margin-left: 10px;"><?= $item['degreeTypeName'] ?></div>
                        </div>
                    </div>
                    <div class="text-description color6e clear">
                        <div class="label">技能说明：</div>
                        <pre class="text-content"><?= $item['description'] ?></pre>
                    </div>
                </div>
                <?php endforeach ?>
            </div>
            <?php endif; ?>
            <!-- 其他技能结束-->
        </div>
        <?php endif; ?>
        <!-- 技能特长结束-->

        <!-- 附加信息开始-->
        <?php if ($addInfoList): ?>
        <div class="common-title ">
            <div class="append-intention"></div>
        </div>
        <div class="append-all">
            <?php foreach ($addInfoList as $index => $item): ?>
            <div class="append-date clear <?= $index==0?'':' ma-top15 ' ?> <?= $index+1==count($addInfoList)?'borderbot':'' ?>">
                <div class="clear">
                        <div class="append-title ma-bot10">
                            <img style="margin-bottom:2px;" width="4px" src="/static/images/template/bluedot.png" alt="">
                            <span class="name first-title bold"><?= $item['themeName']?$item['themeName']:$item['themeIdName'] ?></span>
                        </div>    
                </div>
                <?php if($item['content']){?>
                <div class="append-content color6e">
                    <div class="label">主题描述：</div>
                    <div style="line-height: 1.8;"><?= $item['content'] ?></div>
                </div>
                <?php }?>
            </div>
            <?php endforeach ?>
        </div>
        <?php endif; ?>
        <!-- 附加信息结束-->
    </div>
</div>
