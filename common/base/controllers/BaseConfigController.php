<?php

namespace common\base\controllers;

use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCertificate;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyCooperationApply;
use common\base\models\BaseCompanyDictionary;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobTopConfig;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResearchField;
use common\base\models\BaseSchoolDictionary;
use common\base\models\BaseShowcase;
use common\base\models\BaseSkill;
use common\base\models\BaseWelfareLabel;
use common\helpers\ArrayHelper;
use common\helpers\ConfigHelper;
use common\helpers\FormatConverter;
use common\helpers\IpHelper;
use frontendPc\models\Dictionary;
use frontendPc\models\JobApply;
use frontendPc\models\OffSiteJobApply;
use frontendPc\models\WelfareLabel;
use Yii;
use yii\console\Response;

trait BaseConfigController
{

    /**
     * 获取手机国家区号
     */
    public function actionLoadCountryMobileCode()
    {
        return $this->success(ConfigHelper::getCountryMobileCode());
    }

    public function actionGetNativePlace()
    {
        return $this->success(BaseArea::getNativePlaceAreaList());
    }

    /**
     * 获取学历水平列表
     * @return Response|\yii\web\Response
     */
    public function actionGetEducationList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getEducationList()));
    }

    /**
     * 获取学历水平筛选列表
     * @return Response|\yii\web\Response
     */
    public function actionGetEducationSearchList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getEducationSearchList()));
    }

    /**
     * 获取意向职能
     * @return Response|\yii\web\Response
     */
    public function actionGetJobCategoryList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getJobCategoryList()));
    }

    /**
     * 获取工作性质
     * @return Response|\yii\web\Response
     */
    public function actionGetNatureList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getNatureList()));
    }

    /**
     * 获取求职状态
     * @return Response|\yii\web\Response
     */
    public function actionGetJobStatusList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getJobStatusList()));
    }

    /**
     * 获取到岗时间
     * @return Response|\yii\web\Response
     */
    public function actionGetArriveDateList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getArriveDateList()));
    }

    /**
     * 获取期望月薪
     * @return Response|\yii\web\Response
     */
    public function actionGetWageRangeList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getWageRangeList()));
    }

    /**
     * 获取期望月薪
     * @return Response|\yii\web\Response
     */
    public function actionGetWageList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getWageRangeList()));
    }

    /**
     * 获取项目类别
     * @return Response|\yii\web\Response
     */
    public function actionGetProjectCateList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getProjectCateList()));
    }

    /**
     * 获取行业
     * @return Response|\yii\web\Response
     */
    public function actionGetTradeList()
    {
        return $this->success(Dictionary::getTradeList());
    }

    /**
     * 获取资质证书
     * @return Response|\yii\web\Response
     */
    public function actionGetCertificateList()
    {
        return $this->success(BaseCertificate::getDropDownList());
    }

    /**
     * 获取技能语言
     * @return Response|\yii\web\Response
     */
    public function actionGetSkillList()
    {
        return $this->success(BaseSkill::getList());
    }

    /**
     * 获取技能语言掌握程度
     * @return Response|\yii\web\Response
     */
    public function actionGetDegreeTypeList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getDegreeTypeList()));
    }

    /**
     * 获取福利标签
     * @return Response|\yii\web\Response
     */
    public function actionGetWelfareTagList()
    {
        return $this->success(ArrayHelper::obj2Arr(WelfareLabel::getList()));
    }

    /**
     * 获取附加信息主题列表
     * @return Response|\yii\web\Response
     */
    public function actionGetThemeList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getThemeList()));
    }

    /**
     * 获取职称列表
     * @return Response|\yii\web\Response
     */
    public function actionGetTitleList()
    {
        return $this->success(Dictionary::getTitleList());
    }

    /**
     * 获取职称列表
     * @return Response|\yii\web\Response
     */
    public function actionGetFirstTitleList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getFirstTitleList()));
    }

    /**
     * 获取政治面貌信息
     * @return Response|\yii\web\Response
     */
    public function actionGetPoliticalStatusList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getPoliticalStatusList()));
    }

    /**
     * 获取企业职位类型列表
     * @return Response|\yii\web\Response
     */
    public function actionGetCategoryJobList()
    {
        return $this->success(ArrayHelper::objMoreArr(BaseCategoryJob::getCompanyCategoryJobList()));
    }

    /**
     * 获取专业类型列表
     * @return Response|\yii\web\Response
     */
    public function actionGetMajorList()
    {
        return $this->success(ArrayHelper::objMoreArr(BaseMajor::getAllMajorList()));
    }

    /**
     * 获取求职者可选专业类型列表
     * @return Response|\yii\web\Response
     */
    public function actionGetPersonMajorList()
    {
        return $this->success(ArrayHelper::objMoreArr(BaseMajor::getPersonMajorList()));
    }

    /**
     * 获取专业类型列表
     * @return Response|\yii\web\Response
     */
    public function actionGetLevel2MajorList()
    {
        return $this->success(ArrayHelper::objMoreArr(BaseMajor::getLevel2MajorList()));
    }

    /**
     * 获取企业福利标签列表
     * @return Response|\yii\web\Response
     */
    public function actionGetWelfareLabelList()
    {
        $request = Yii::$app->request->get();

        return $this->success(BaseWelfareLabel::getCompanyWelfareLabelList(FormatConverter::convertHump($request)));
    }

    /**
     * 获取经验要求列表
     * @return Response|\yii\web\Response
     */
    public function actionGetExperienceList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getExperienceList()));
    }

    /**
     * 获取海外经历列表
     * @return Response|\yii\web\Response
     */
    public function actionGetAbroadList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getAbroadList()));
    }

    /**
     * 获取年龄要求列表
     * @return Response|\yii\web\Response
     */
    public function actionGetAgeList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getAgeList()));
    }

    /**
     * 获取地区列表
     * @return Response|\yii\web\Response
     */
    public function actionGetFullAreaList()
    {
        return $this->success(BaseArea::getAreaList());
    }

    /**
     * 获取地区列表
     * @return Response|\yii\web\Response
     */
    public function actionGetAreaList()
    {
        return $this->success(BaseArea::getAreaList());
    }

    /**
     * 获取是否985/211
     * @return Response|\yii\web\Response
     */
    public function actionGetSchoolTypeList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getSchoolTypeList()));
    }

    /**
     * 获取单位性质列表
     * @return Response|\yii\web\Response
     */
    public function actionGetCompanyNatureList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getCompanyNatureList()));
    }

    /**
     * 获取单位类型列表
     * @return Response|\yii\web\Response
     */
    public function actionGetCompanyTypeList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getCompanyTypeList()));
    }

    /**
     * 获取单位申请的合作类型
     * @return Response|\yii\web\Response
     */
    public function actionGetCompanyCooperationTypeList()
    {
        return $this->success(ArrayHelper::obj2Arr(BaseCompanyCooperationApply::TYPE_LIST));
    }

    /**
     * 获取工作年限
     * @return Response|\yii\web\Response
     */
    public function actionGetWorkExperienceList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getExperienceList()));
    }

    /**
     * 获取编制列表
     * @return Response|\yii\web\Response
     */
    public function actionGetSystematicList()
    {
        $data = Dictionary::getSystematicList();
        ksort($data);

        return $this->success(ArrayHelper::obj2Arr($data));
    }

    /**
     * 获取工作年限
     * @return Response|\yii\web\Response
     */
    public function actionGetMemberSourceTypeList()
    {
        return $this->success(ArrayHelper::obj2Arr(BaseMember::SOURCE_TYPE_LIST));
    }

    /**
     * 获取站内投递状态
     * @return Response|\yii\web\Response
     */
    public function actionGetOnSiteApplyStatus()
    {
        return $this->success(ArrayHelper::obj2Arr(JobApply::PERSON_STATUS_LIST));
    }

    /**
     * 获取站外投递状态
     * @return Response|\yii\web\Response
     */
    public function actionGetOffSiteApplyStatus()
    {
        return $this->success(ArrayHelper::obj2Arr(OffSiteJobApply::APPLY_STATUS_LIST));
    }

    /**
     * 获取论文位次列表
     * @return Response|\yii\web\Response
     */
    public function actionGetPaperRankList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getPaperRankList()));
    }

    /**
     * 获取民族列表
     * @return Response|\yii\web\Response
     */
    public function actionGetNationList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getNationList()));
    }

    /**
     * 获取专利位次列表
     * @return Response|\yii\web\Response
     */
    public function actionGetPatentRankList()
    {
        return $this->success(ArrayHelper::obj2Arr(Dictionary::getPatentRankList()));
    }

    /**
     * 获取职位类型列表
     * @return Response|\yii\web\Response
     */
    public function actionGetPersonCategoryJobList()
    {
        return $this->success(BaseCategoryJob::getPersonCategoryJobList());
    }

    /**
     * 获取职位投递限制列表
     * @return Response|\yii\web\Response
     */
    public function actionGetDeliveryLimitList()
    {
        return $this->success(ArrayHelper::obj2Arr(BaseJob::DELIVERY_LIMIT_LIST));
    }

    /**
     * 获取职位投递类型
     * @return Response|\yii\web\Response
     */
    public function actionGetDeliveryType()
    {
        return $this->success(ArrayHelper::obj2Arr(BaseJob::DELIVERY_TYPE_NAME));
    }

    /**
     * 获取职位投递方式
     * @return Response|\yii\web\Response
     */
    public function actionGetDeliveryWay()
    {
        return $this->success(ArrayHelper::obj2Arr(BaseJob::DELIVERY_WAY_NAME));
    }

    public function actionGetPrivate()
    {
        return $this->success([
            'private'        => Yii::$app->params['private'],
            'privateTitle'   => Yii::$app->params['privateTitle'],
            'privateMessage' => Yii::$app->params['privateMessage'],
        ]);
    }

    /**
     * 获取地区列表--含海外一二三
     * 二三五级数据混套
     * @return Response|\yii\web\Response
     */
    public function actionGetAllAreaList()
    {
        return $this->success(BaseArea::getNativeAreaList());
    }

    /**
     * 获取户籍国籍列表--含海外
     * 二三五级数据混套
     * @return Response|\yii\web\Response
     */
    public function actionGetHouseholdRegisterList()
    {
        return $this->success(BaseArea::getHouseholdRegisterList());
    }

    /**
     * 获取专业列表--二三级同层次
     * @return Response|\yii\web\Response
     */
    public function actionGetHierarchyMajorList()
    {
        $list = ArrayHelper::objMoreArr(BaseMajor::getAllMajorList());

        foreach ($list as $k => $v) {
            $children = $v['children'];
            foreach ($children as &$item) {
                foreach ($item['children'] as $kk => $vv) {
                    $item['children'][$kk]['topParentId'] = $item['parentId'];
                }
            }
            //这里做分组
            $childrenHierarchyList = array_chunk($children, 4);
            $list[$k]['children']  = ArrayHelper::arrayAddNneDimensional($childrenHierarchyList, $v['k']);
        }

        return $this->success($list);
    }

    /**
     * 意向城市列表
     * @return Response|\yii\web\Response
     */
    public function actionGetHierarchyCityList()
    {
        return $this->success(BaseArea::getHierarchyCityList());
    }

    /**
     * 获取地区列表--不含含海外
     * 获取户籍国籍（h5专用）--含热门城市，无海外
     * @return Response|\yii\web\Response
     */
    public function actionGetNativeCityAreaList()
    {
        return $this->success(BaseArea::getAllCityAreaList());
    }

    /**
     * 获取职位类型列表（层级）
     * @return Response|\yii\web\Response
     */
    public function actionGetAllCategoryJobList()
    {
        return $this->success(BaseCategoryJob::getAllCategoryJobList());
    }

    public function actionGetPositionToken()
    {
        return $this->success([
            'token' => Yii::$app->params['showcaseBrowse']['token'],
        ]);
    }

    /**
     * 获取置顶职位类型列表
     * @return Response|\yii\web\Response
     */
    public function actionGetJobTopTypeList()
    {
        return $this->success(ArrayHelper::obj2Arr(BaseJobTopConfig::TYPE_LIST));
    }

    /**
     * 获取置顶职位状态列表
     * @return Response|\yii\web\Response
     */
    public function actionGetJobTopStatusList()
    {
        return $this->success(ArrayHelper::obj2Arr(BaseJobTopConfig::STATUS_LIST));
    }

    public function actionGetAreaInfo()
    {
        // 找出ip所在地
        return $this->success(IpHelper::getAreaInfo());
    }

    public function actionGetShowcaseTargetLinkTypeList()
    {
        return $this->success(ArrayHelper::obj2Arr(BaseShowcase::TARGET_LINK_TYPE_LIST));
    }

    public function actionGetShowcasePageLinkTypeList()
    {
        $pageLinkType = ArrayHelper::obj2Arr(BaseShowcase::PAGE_LINK_TYPE_LIST);
        foreach ($pageLinkType as &$item) {
            if (in_array($item['k'], [
                BaseShowcase::PAGE_LINK_TYPE_CHANNEL_MAIN,
                BaseShowcase::PAGE_LINK_TYPE_CHANNEL_DETAIL,
            ])) {
                $item['type'] = BaseShowcase::TARGET_LINK_TYPE_VIDEO;
            } else {
                $item['type'] = BaseShowcase::TARGET_LINK_TYPE_STATION;
            }
        }

        return $this->success($pageLinkType);
    }

    // 获取投递端口
    public function actionGetJobApplyPlatformList()
    {
        return $this->success(ArrayHelper::obj2Arr(BaseJobApplyRecord::PLATFORM_LIST));
    }

    // 获取投递端口
    public function actionGetIsMiniappList()
    {
        return $this->success(ArrayHelper::obj2Arr(BaseJob::IS_MINIAPP_LIST));
    }

    // 获取是否海外
    public function actionGetIsAbroadList()
    {
        return $this->success(ArrayHelper::obj2Arr(BaseCompany::IS_ABROAD_LIST));
    }

    // 获取公共字典文案
    public function actionGetDictionaryText()
    {
        $type  = Yii::$app->request->get('type');
        $value = Yii::$app->request->get('value');

        return $this->success(['textList' => BaseDictionary::commonGetText($type, $value)]);
    }

    public function actionGetEstablishmentTypeList()
    {
        return $this->success(ArrayHelper::obj2Arr(BaseDictionary::getSystematicList()));
    }

    public function actionGetEmoteList()
    {
        return $this->success(['list' => BaseDictionary::getEmoteList()]);
    }

    public function actionGetBaseUrl()
    {
        $baseUrl = Yii::$app->params['baseUrl'];

        return $this->success($baseUrl);
    }

    public function actionGetCaptcha()
    {
        $config = Yii::$app->params['tencentCloud'];

        return $this->success(['captchaAppId' => $config['captcha']['CaptchaAppId']]);
    }

    public function actionGetHwActivityAreaList()
    {
        return $this->success(BaseArea::getHwAreaList());
    }

    public function actionGetDict()
    {
        $type = Yii::$app->request->get('type');
        if (empty($type)) {
            $list = [];
        } else {
            $list = BaseDictionary::find()
                ->where(['type' => $type])
                ->asArray()
                ->all();
        }

        return $this->success(ArrayHelper::buildDictTree($list, 0));
    }



    public function actionGetTopEducation()
    {
        return $this->success(ArrayHelper::obj2Arr(BaseResumeEducation::UN_COMPLETE_EDUCATION_TYPE_LIST));
    }

    /**
     * 获取校园列表字典
     */
    public function actionGetSchoolDict()
    {
        $name = Yii::$app->request->get('name');

        $where = [
            'and',
            [
                '=',
                'status',
                BaseSchoolDictionary::STATUS_ACTIVE,
            ],
        ];
        if (!empty($name)) {
            $where[] = [
                'or',
                [
                    'like',
                    'name_cn',
                    $name,
                ],
                [
                    'like',
                    'short_name_cn',
                    $name,
                ],
                [
                    'like',
                    'name_en',
                    $name,
                ],
                [
                    'like',
                    'short_name_en',
                    $name,
                ],
            ];
        }
        $list = BaseSchoolDictionary::find()
            ->where($where)
            ->limit(20)
            ->asArray()
            ->all();

        return $this->success(ArrayHelper::intToString($list));
    }

    /**
     * 获取单位列表字典
     */
    public function actionGetCompanyDict()
    {
        $name = Yii::$app->request->get('name');

        $where = [
            'and',
            [
                '=',
                'status',
                BaseCompanyDictionary::STATUS_ACTIVE,
            ],
        ];
        if (!empty($name)) {
            $where[] = [
                'or',
                [
                    'like',
                    'name_cn',
                    $name,
                ],
                [
                    'like',
                    'short_name_cn',
                    $name,
                ],
                [
                    'like',
                    'name_en',
                    $name,
                ],
                [
                    'like',
                    'short_name_en',
                    $name,
                ],
            ];
        }
        $list = BaseCompanyDictionary::find()
            ->where($where)
            ->limit(20)
            ->asArray()
            ->all();

        return $this->success(ArrayHelper::intToString($list));
    }

    public function actionGetCategoryLastLevel()
    {
        $name = Yii::$app->request->get('name');

        if (empty($name)) {
            $categoryList = [];
        } else {
            $lastCategoryList = BaseCategoryJob::find()
                ->select([
                    'parent_id',
                    'name',
                    'id'
                ])
                ->where([
                    'and',
                    [
                        'like',
                        'name',
                        $name,
                    ],
                    [
                        '=',
                        'level',
                        2,
                    ],
                ])
                ->asArray()
                ->all();
            $categoryList = [];
            foreach ($lastCategoryList as $item) {
                $categoryContent = BaseCategoryJob::findOneVal(['id' => $item['parent_id']], 'name');

                $categoryList[] = [
                    'name' => $categoryContent . '>' . $item['name'],
                    'id'   => $item['id'],
                ];
            }
        }

        return $this->success(ArrayHelper::object2LV($categoryList));
    }

    public function actionGetMajorLastLevel()
    {
        $name = Yii::$app->request->get('name');

        if (empty($name)) {
            $majorList = [];
        } else {
            $lastMajorList = BaseMajor::find()
                ->select(['parent_id', 'name', 'id', 'code'])
                ->where([
                    'and',
                    [
                        'like',
                        'name',
                        $name,
                    ],
                    [
                        '=',
                        'level',
                        3,
                    ],
                ])
                ->asArray()
                ->all();

            $majorList = [];
            foreach ($lastMajorList as $major) {
                $majorContent = BaseMajor::getParentContent($major, [$major['name']]);

                $majorList[] = [
                    'name' => implode('-', array_reverse($majorContent)),
                    'id'   => $major['id'],
                ];
            }
        }

        return $this->success(ArrayHelper::object2LV($majorList));
    }
}