<?php

namespace common\base\models;

use common\models\JobApplyTopEquityRecord;

class BaseJobApplyTopEquityRecord extends JobApplyTopEquityRecord
{
    //权益过期类型 1正在生效 2系统到期失效 3单位操作失效
    const EXPIRE_TYPE_EFFECT  = 1;
    const EXPIRE_TYPE_SYSTEM  = 2;
    const EXPIRE_TYPE_COMPANY = 3;
    //是否使用权益投递0失效 1生效中
    const EQUITY_STATUS_EXPIRE = 0;
    const EQUITY_STATUS_EFFECT = 1;

    /**
     * 30天对职位是否使用过权益
     */
    public static function isUseEquity($jobId, $resumeId, $day = 30)
    {
        $count = self::find()
            ->where([
                'job_id'    => $jobId,
                'resume_id' => $resumeId,
            ])
            ->andWhere([
                '>=',
                'add_time',
                date('Y-m-d H:i:s', strtotime('-' . $day . ' day')),
            ])
            ->count();

        return $count > 0;
    }

    /**
     * 获取职位投递权益最近一次使用记录(可以限定时间)
     */
    public static function getLastUseEquity($jobId, $resumeId, $day = 0)
    {
        $query = self::find()
            ->where([
                'job_id'    => $jobId,
                'resume_id' => $resumeId,
            ]);
        if ($day > 0) {
            $query->andWhere([
                '>=',
                'add_time',
                date('Y-m-d H:i:s', strtotime('-' . $day . ' day')),
            ]);
        }
        $info = $query->orderBy('add_time desc')
            ->one();

        return $info;
    }

    /**
     * 失效职位投递权益
     */
    public static function expireEquity($applyId, $expireType)
    {
        $model = self::findOne(['apply_id' => $applyId]);
        if ($model) {
            $model->equity_status = self::EQUITY_STATUS_EXPIRE;
            $model->expire_type   = $expireType;
            $model->expire_time   = date('Y-m-d H:i:s');
            $model->save();
        }
    }
}