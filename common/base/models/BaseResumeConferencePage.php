<?php
/**
 * create user：伍彦川
 * create time：2025/9/15 09:31
 */
namespace common\base\models;

use common\components\MessageException;
use common\helpers\TimeHelper;
use common\models\ResumeConferencePage;

class BaseResumeConferencePage extends ResumeConferencePage
{
    // 国际顶级
    const LEVEL_INTERNATIONAL_TOP = 135010000;
    // 国际权威
    const LEVEL_INTERNATIONAL_AUTHORITY = 135020000;
    // 国内顶级
    const LEVEL_DOMESTIC_TOP = 135030000;
    // 国内权威
    const LEVEL_DOMESTIC_AUTHORITY = 135040000;
    // 其他
    const LEVEL_OTHER = 135990000;

    public static function getCompleteInfo($resumeId, $resumeConferencePageId)
    {
        if (empty($resumeId) || empty($resumeConferencePageId)) {
            throw new MessageException('数据异常');
        }

        $academicPageInfo = self::find()
            ->select([
                'id',
                // 论文题目
                'title',
                // 会议名称
                'conference_name',
                // 举办时间
                'publish_time',
                // 举办地点
                'address_detail',
                // 卷期页码
                'page_number',
                // 论文集名称
                'collection_name',
                // 本人位次
                'author_position',
                // 会议级别
                'conference_level',
                // 论文录用率
                'acceptance_rate',
                // 近5年被引次数
                'citation_count_five_year',
                // 详细说明
                'description',
                // 所获奖项
                'awards',
                'status'
            ])
            ->where([
                'resume_id' => $resumeId,
                'id'        => $resumeConferencePageId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->asArray()
            ->one();

        if (empty($academicPageInfo)) {
            throw new MessageException('数据异常');
        }

        // 研究领域标签
        $academicPageInfo['researchFieldRelation'] = BaseResumeConferencePageResearchFieldRelation::find()
            ->alias('r')
            ->leftJoin(['rf' => BaseResearchField::tableName()], 'rf.id = r.research_field_id')
            ->where([
                'r.resume_conference_page_id' => $academicPageInfo['id'],
                'r.status'                    => self::STATUS_ACTIVE,
            ])
            ->select([
                'rf.name',
                'r.research_field_id',
                'r.type',
            ])
            ->asArray()
            ->all();

        // 论文关联阶段
        $academicPageInfo['stageRelation'] = BaseResumeConferencePageStageTypeRelation::find()
            ->where([
                'resume_conference_page_id' => $academicPageInfo['id'],
                'status'                    => self::STATUS_ACTIVE,
            ])
            ->select([
                'related_stage_id',
                'type',
            ])
            ->asArray()
            ->all();

        $academicPageInfo['publish_time'] = TimeHelper::formatToYearMonth($academicPageInfo['publish_time']);

        return $academicPageInfo;
    }
}