<?php

namespace common\base\models;

use common\models\ResumeTopConfig;

class BaseResumeTopConfig extends ResumeTopConfig
{
    // 待生效
    const STATUS_WAIT = 1;
    // 已生效
    const STATUS_EFFECT = 2;
    // 已过期
    const STATUS_EXPIRED = 9;
    // 权益异常失效
    const STATUS_EXCEPTION = 10;

    /**
     * 简历是否有在置顶生效中
     * @param $resumeId
     * @return bool
     */
    public static function isResumeTopEffect($resumeId): bool
    {
        return self::find()
            ->Where([
                'resume_id' => $resumeId,
                'status'    => self::STATUS_EFFECT,
                'set_date'  => date('Y-m-d'),
            ])
            ->exists();
    }

    /**
     * 获取一个求职者的简历置顶配置数据
     * @param $resumeId
     */
    public static function getResumeTopData($resumeId, $status = null)
    {
        return self::find()
            ->select([
                'set_date',
                'status',
            ])
            ->where([
                'resume_id' => $resumeId,
            ])
            ->andFilterWhere(['status' => $status])
            ->orderBy('set_date asc')
            ->asArray()
            ->all();
    }

    /**
     * 获取所有生效的简历ID
     */
    public static function getEffectResumeIds()
    {
        return self::find()
            ->select([
                'resume_id',
            ])
            ->Where([
                'status' => self::STATUS_EFFECT,
            ])
            ->distinct()
            ->asArray()
            ->column();
    }

    /**
     * 某一天是否置顶
     * @param $resumeId
     * @param $date
     */
    public static function isHasTop($resumeId, $date)
    {
        return self::find()
            ->Where([
                'resume_id' => $resumeId,
                'set_date'  => $date,
            ])
            ->exists();
    }
}