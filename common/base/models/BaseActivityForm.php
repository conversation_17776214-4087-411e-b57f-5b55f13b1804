<?php

namespace common\base\models;

use common\base\BaseActiveRecord;
use common\helpers\ArrayHelper;
use common\helpers\FileHelper;
use common\models\ActivityForm;
use yii\base\Exception;
use yii\db\conditions\AndCondition;

class BaseActivityForm extends ActivityForm
{
    const REQUIRED_PARAMETER_LIST = [
        'name',
        'intentionOption',
    ];

    const EDIT_PARAMETER_LIST = [
        'id',
        'name',
        'intentionOption',
    ];

    const STATUS_ACTIVE = 1;
    const STATUS_STOP   = 0;

    // * 您目前的学业/就业状态是（单选题）
    const TYPE_DOUBLE_MEETING_EMPLOYMENT_STATUS_LIST = [
        '1' => '在读博士',
        '2' => '待业中的毕业博士',
        '3' => '在读即将毕业或刚毕业正在找工作的博士',
        '4' => '正在从事科研、教学或产业的在职博士',
        '5' => '博士后研究人员',
        '6' => '博士后出站人员',
        '7' => '硕士',
        '8' => '其它',
    ];

    // * 您博士毕业后的海外连续工作时长（含海外博士后）（单选题）
    const TYPE_DOUBLE_MEETING_OVERSEAS_WORKING_TIME_LIST = [
        '1' => '24个月以内',
        '2' => '24-36个月',
        '3' => '36个月及以上',
        '4' => '暂无海外工作或科研经历',
    ];

    /**
     * @throws Exception
     */
    public static function checkParameter($keywords, $parameter)
    {
        foreach ($parameter as $value) {
            if (strlen($keywords[$value]) < 1) {
                throw new Exception('参数不全');
            }
        }
    }

    /**
     * 创建表单
     * @throws Exception
     */
    public static function createInfo($data): int
    {
        if ($data['id']) {
            $baseModel = BaseActivityForm::findOne(['id' => $data['id']]);
        } else {
            $baseModel              = new self();
            $baseModel->add_time    = $data['addTime'] ?: CUR_DATETIME;
            $baseModel->code        = $data['code'];
            $baseModel->activity_id = $data['activityId'];
            $baseModel->link        = $data['link'] ?: '';
        }

        $baseModel->status                                    = self::STATUS_ACTIVE;
        $baseModel->name                                      = $data['name'];
        $baseModel->background_url                            = $data['backgroundUrl'] ?: '';
        $baseModel->introduction                              = $data['introduction'];
        $baseModel->conclusion                                = $data['conclusion'];
        $baseModel->remark                                    = $data['remark'] ?: '';
        $baseModel->intent_explain                            = $data['intentExplain'] ?: '';
        $baseModel->option_ids                                = $data['optionIds'] ?: '';
        $baseModel->show_message                              = $data['showMessage'] ?: '';
        $baseModel->upload_instructions                       = $data['uploadInstructions'] ?: '';
        $baseModel->wechat_instructions                       = $data['wechatInstructions'] ?: '';
        $baseModel->reference_instructions                    = $data['referenceInstructions'] ?: '';
        $baseModel->postdoctor_institution_instructions       = $data['postdoctorInstitutionInstructions'] ?: '';
        $baseModel->postdoctor_overseas_duration_instructions = $data['postdoctorOverseasDurationInstructions'] ?: '';
        $baseModel->custom_question_instructions_title        = $data['customQuestionInstructionsTitle'] ?: '';
        $baseModel->custom_question_instructions_content      = $data['customQuestionInstructionsContent'] ?: '';
        $baseModel->success_tips                              = $data['successTips'] ?: '';
        $baseModel->community_file_ids                        = $data['communityFileIds'] ?: '';
        $baseModel->option_item_ids                           = $data['optionItemIds'] ?: '';
        $baseModel->max_count                                 = $data['maxCount'] ?: 0;

        if (!$baseModel->save()) {
            throw new Exception($baseModel->getFirstErrorsMessage());
        }

        return $baseModel->id;
    }

    /**
     * 双会活动表单详情（显示）
     * @throws Exception
     */
    public static function getActivityFormInfo($activityId, $memberId = 0, $type = 1): array
    {
        $info = BaseActivityForm::find()
            ->select([
                'id',
                'add_time',
                'status',
                'activity_id',
                'name',
                'code',
                'link',
                'background_url',
                'introduction',
                'conclusion',
                'remark',
                'intent_explain',
                'option_ids',
                'show_message',
                'upload_instructions',
                'wechat_instructions',
                'reference_instructions',
                'success_tips',
                'community_file_ids',
                'option_item_ids',
                'postdoctor_institution_instructions',
                'postdoctor_overseas_duration_instructions',
                'custom_question_instructions_title',
                'custom_question_instructions_content',
                'max_count',
            ])
            ->where(['activity_id' => $activityId])
            ->asArray()
            ->one();

        $optionIds = explode(',', $info['option_ids']);
        if ($type == 1) {
            $activityFormIntentionOption = BaseActivityFormIntentionOption::getActivityFormIntentionOption($optionIds);
        } else {
            $activityFormIntentionOption = BaseActivityFormIntentionOption::getBackActivityFormIntentionOption($optionIds);
            foreach ($activityFormIntentionOption['list'] as &$optionItem) {
                $checkOne                  = BaseActivityFormRegistrationForm::find()
                    ->where("find_in_set(" . $optionItem['id'] . ",option_ids)")
                    ->asArray()
                    ->one();
                $optionItem['isOldChoose'] = $checkOne ? 1 : 0;
            }
        }

        $info['intentionOption']        = $activityFormIntentionOption['list'];
        $info['intentionOptionIds']     = $info['option_ids'];
        $info['intentionOptionMessage'] = $activityFormIntentionOption['intentionOptionMessage'];
        $info['showMessageList']        = json_decode($info['show_message'], true);
        $info['showMessageList']        = self::fixShowMessageList($info['showMessageList']);
        $info['communityFileList']      = FileHelper::getAllNameListByIds($info['community_file_ids']);

        if ($memberId > 0) {
            $resumeId             = BaseResume::findOneVal(['member_id' => $memberId], 'id');
            $info['oldOptionIds'] = BaseActivityFormRegistrationForm::getMemberRegistrationForm($info['id'], $resumeId);

            foreach ($optionIds as $item) {
                if (!in_array($item, $info['oldOptionIds'])) {
                    $info['optionTips'] = '至少选择1项';
                }
            }
            $info['oldOptionIds'] = array_unique($info['oldOptionIds']);

            // https://zentao.jugaocai.com/index.php?m=story&f=view&id=939

            $allHideIds = BaseActivityFormIntentionOption::find()
                ->select('id')
                ->where([
                    'activity_form_id' => $info['id'],
                    'is_show'          => BaseActivityFormIntentionOption::IS_SHOW_NO,
                ])
                ->column();

            // 把报名的过滤掉隐藏的
            $myOldOptionIds = [];
            foreach ($info['oldOptionIds'] as $oldOption) {
                if (!in_array($oldOption, $allHideIds)) {
                    $myOldOptionIds[] = $oldOption;
                }
            }
            $oldOptionIdsCount = count($myOldOptionIds);
            $myIntentionOption = [];
            foreach ($info['intentionOption'] as $item) {
                if (!in_array($item['id'], $allHideIds)) {
                    $myIntentionOption[] = $item;
                }
            }
            $intentionOptionCount               = count($myIntentionOption);
            $info['allIntentionOptionDisabled'] = false;
            if ($oldOptionIdsCount == $intentionOptionCount) {
                $info['optionTips'] = '暂无可报名活动';
            } else {
                if ($info['max_count'] > 0) {
                    $info['myMaxCount'] = $info['max_count'] - $oldOptionIdsCount;
                    if ($info['myMaxCount'] > 0) {
                        $info['optionTips']             = "最多可报名{$info['max_count']}项，您还可以报名{$info['myMaxCount']}项";
                        $info['optionTipsErrorMessage'] = "最多可报名{$info['max_count']}项，您还可以报名{$info['myMaxCount']}项！（您已报名了{$oldOptionIdsCount}项）";
                    } else {
                        $info['optionTips']                 = "最多可报名{$info['max_count']}项，您已报名了{$oldOptionIdsCount}项！";
                        $info['allIntentionOptionDisabled'] = true;
                    }
                } else {
                    $info['optionTips'] = '至少选择1项';
                }
                $info['myMaxCount'] = $info['max_count'] - $oldOptionIdsCount;
            }

            // oldOptionIds参数下标重塑
            $info['oldOptionIds'] = array_values($myOldOptionIds);
        } else {
            $info['oldOptionIds'] = [];
        }

        $info['max_count']         = $info['max_count'] * 1;
        $info['oldOptionIdsCount'] = $oldOptionIdsCount;

        return $info;
    }

    // 这里是为了兼容旧版的数据，旧版的数据里面没有sort字段，所以要塞一个sort=0进去
    public static function fixShowMessageList($list)
    {
        foreach ($list as &$item) {
            if (!isset($item['sort'])) {
                $item['sort'] = '0';
            }
        }

        return $list;
    }

    // 先排序sort再排序id
    public static function sortShowMessageList($list)
    {
        $sort = array_column($list, 'sort');
        array_multisort($sort, SORT_DESC, $list);

        return $list;
    }

    /**
     * 获取表单列表，不分页
     * @param array  $params
     * @param array  $selectField
     * @param string $order
     * @param int    $limit
     * @return array
     */
    public static function getFormList(
        array  $params = [],
        array  $selectField = [
            'id',
            'name',
        ],
        string $order = 'id desc',
        int    $limit = 10
    ): array {
        $baseWhere = [
            [
                '=',
                'status',
                BaseActivity::STATUS_ACTIVE,
            ],
        ];
        $where     = BaseActiveRecord::getSearchCondition($params, [
            [
                'keyword',
                'like',
                'name',
            ],
            [
                'formId',
                '=',
                'id',
            ],
        ]);

        return self::find()
            ->select($selectField)
            ->andWhere(new AndCondition($baseWhere))
            ->andwhere($where)
            ->orderBy($order)
            ->limit($limit)
            ->asArray()
            ->all();
    }

}