<?php

namespace common\base\models;

use common\models\ResumeEquityPackageCategorySetting;

class BaseResumeEquityPackageCategorySetting extends ResumeEquityPackageCategorySetting
{
    // vip---Gold Vip  黄金会员
    const ID_GOLD_VIP      = 1;
    const ID_GOLD_VIP_TEXT = '黄金VIP';
    // 洞察
    const ID_INSIGHT      = 2;
    const ID_INSIGHT_TEXT = '竞争力洞察';
    //Diamond Vip 钻石会员
    const ID_DIAMOND_VIP      = 3;
    const ID_DIAMOND_VIP_TEXT = '钻石VIP';
    //求职快Job fast
    const ID_JOB_FAST        = 4;
    const ID_JOB_FAST_TEXT   = '求职快';
    const VIP_TYPE_TEXT_LIST = [
        self::ID_GOLD_VIP    => '黄金VIP',
        self::ID_DIAMOND_VIP => '钻石VIP',
        self::ID_INSIGHT     => '竞争力洞察',
        self::ID_JOB_FAST    => '求职快',
    ];
    const VIP_TYPE_BUY_URL   = [
        self::ID_GOLD_VIP    => '/vip.html',
        self::ID_DIAMOND_VIP => '/vip.html',
        self::ID_INSIGHT     => '/competitive-power.html',
        self::ID_JOB_FAST    => '/job-fast.html',
    ];

    // 30天黄金的vip配置
    const ID_SETTING_30_DAY_GOLD_VIP = 1;

    // 硬编码（绑定包类型跟权益操作类型的关系）
    // 包类型id => 权益操作类型type
    const PACKAGE_EQUITY_CATEGORY_ACTION_TYPE_RELATION = [
        // vip
        self::ID_GOLD_VIP    => BaseResumeEquityActionRecord::ACTION_ADD_VIP,
        // 竞争力洞察
        self::ID_INSIGHT     => BaseResumeEquityActionRecord::ACTION_ADD_INSIGHT,
        // 钻石会员
        self::ID_DIAMOND_VIP => BaseResumeEquityActionRecord::ACTION_ADD_VIP,
        // 求职快
        self::ID_JOB_FAST    => BaseResumeEquityActionRecord::ACTION_USED_JOB_FAST,
    ];

    //权益包的权益明细
    const PACKAGE_EQUITY_CATEGORY_EQUITY_DETAIL = [
        // vip
        self::ID_GOLD_VIP    => BaseResumeEquitySetting::ID_GOLD_VIP_SERVICES,
        // 竞争力洞察
        self::ID_INSIGHT     => BaseResumeEquitySetting::ID_VALUE_ADDED_SERVICES,
        // 钻石会员
        self::ID_DIAMOND_VIP => BaseResumeEquitySetting::ID_DIAMOND_VIP_SERVICES,
        // 求职快
        self::ID_JOB_FAST    => BaseResumeEquitySetting::ID_JOB_FAST_SERVICES,
    ];

    // 硬编码（指定特定类型修改会员标识）
    // 目前只有vip类型的会修改vip标识（简历主表）
    // 包类型id
    const PACKAGE_EQUITY_CATEGORY_VIP_IDS = [
        self::ID_GOLD_VIP,
        self::ID_DIAMOND_VIP,
    ];

    // 支付弹窗的配置
    const PAY_POPOVER_CONFIG = [
        self::ID_GOLD_VIP    => [
            'name'                  => '开通' . BaseResumeEquityPackageCategorySetting::ID_GOLD_VIP_TEXT . '服务',
            // 服务协议url
            'serviceAgreementUrl'   => '/agreement/value-added-services?type=1',
            // 服务协议标题
            'serviceAgreementTitle' => '《高校人才网增值服务协议》',
        ],
        self::ID_INSIGHT     => [
            'name'                  => '开通' . BaseResumeEquityPackageCategorySetting::ID_INSIGHT_TEXT . '服务',
            // 服务协议url
            'serviceAgreementUrl'   => '/agreement/value-added-services?type=1',
            // 服务协议标题
            'serviceAgreementTitle' => '《高校人才网增值服务协议》',
        ],
        self::ID_DIAMOND_VIP => [
            'name'                  => '开通' . BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP_TEXT . '服务',
            'upgradeName'           => '升级' . BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP_TEXT . '服务',
            // 服务协议url
            'serviceAgreementUrl'   => '/agreement/value-added-services?type=1',
            // 服务协议标题
            'serviceAgreementTitle' => '《高校人才网增值服务协议》',
        ],
        self::ID_JOB_FAST    => [
            'name'                  => '开通' . BaseResumeEquityPackageCategorySetting::ID_JOB_FAST_TEXT . '服务',
            // 服务协议url
            'serviceAgreementUrl'   => '/agreement/value-added-services?type=2',
            // 服务协议标题
            'serviceAgreementTitle' => '《高校人才网求职快服务协议》',
        ],
    ];

    /**
     * 获取权益配置列表
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getList()
    {
        return self::find()
            ->asArray()
            ->all();
    }
}
