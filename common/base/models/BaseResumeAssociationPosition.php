<?php
/**
 * create user：伍彦川
 * create time：2025/9/15 09:31
 */
namespace common\base\models;


use common\components\MessageException;
use common\helpers\TimeHelper;
use common\models\ResumeAssociationPosition;

class BaseResumeAssociationPosition extends ResumeAssociationPosition
{
    public static function getCompleteInfo($resumeId, $resumeAssociationPositionId)
    {
        if (empty($resumeId) || empty($resumeAssociationPositionId)) {
            throw new MessageException('数据异常');
        }
        $associationPositionInfo = self::find()
            ->select([
                'id',
                // 机构名称
                'name',
                // 任职时间
                'begin_date',
                'end_date',
                'is_now',
                // 机构类别
                'type_code',
                // 担任角色
                'role',
                'role_code',
                // 详细说明
                'description',
                'status'
            ])
            ->where([
                'resume_id' => $resumeId,
                'id'        => $resumeAssociationPositionId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->asArray()
            ->one();

        if (empty($associationPositionInfo)) {
            throw new MessageException('数据异常');
        }

        $associationPositionInfo['begin_date'] = TimeHelper::formatToYearMonth($associationPositionInfo['begin_date']);
        $associationPositionInfo['end_date']   = TimeHelper::formatToYearMonth($associationPositionInfo['end_date']);


        return $associationPositionInfo;
    }
}