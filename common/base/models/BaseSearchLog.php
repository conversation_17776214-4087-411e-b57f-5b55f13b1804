<?php

namespace common\base\models;

use common\models\SearchLog;
use yii\base\Exception;

class BaseSearchLog extends SearchLog
{
    const TYPE_JOB          = 1; //检索类型，职位
    const TYPE_ANNOUNCEMENT = 2; //检索类型，公告
    const TYPE_COMPANY      = 3; //检索类型，单位

    //检索类型列表
    const TYPE_LIST = [
        self::TYPE_JOB          => '职位',
        self::TYPE_ANNOUNCEMENT => '公告',
        self::TYPE_COMPANY      => '单位',
    ];

    const PLATFORM_TYPE_OPERATE  = 1; //平台类型，运营系统
    const PLATFORM_TYPE_UNIT     = 2; //平台类型，单位端
    const PLATFORM_TYPE_JOB_HUNT = 3; //平台类型，求职端
    const PLATFORM_TYPE_H5       = 4; //平台类型，H5
    const PLATFORM_TYPE_MINI_APP = 5; //平台类型，小程序

    //平台类型列表
    const PLATFORM_TYPE_LIST = [
        self::PLATFORM_TYPE_OPERATE  => '运营系统',
        self::PLATFORM_TYPE_UNIT     => '单位端',
        self::PLATFORM_TYPE_JOB_HUNT => '求职端',
        self::PLATFORM_TYPE_H5       => 'H5',
        self::PLATFORM_TYPE_MINI_APP => '小程序',
    ];

    const STATUS_ACTIVE = 1; //状态，在线
    const STATUS_DELETE = 2; //状态，删除

    /**
     * 新增检索记录
     * @throws Exception
     */
    public static function createSearchLog($data): int
    {
        $model                = new self();
        $model->status        = $data['status'];
        $model->title         = $data['title'] ?: '';
        $model->resume_id     = $data['resume_id'] ?: '';
        $model->platform_type = $data['platform_type'] ?: '';
        $model->type          = $data['type'] ?: '';

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }

        return $model->id;
    }

    /**
     * 查询用户（人才）检索记录
     * 2023.04.26 毛萍确定不用去重
     */
    public static function getResumeSearchMess($resumeId, $type, $limit = 6): array
    {
        $select = [
            'id',
            'add_time',
            'status',
            'title',
            'resume_id',
            'platform_type',
            'type',
        ];

        $list = self::find()
            ->select($select)
            ->where([
                'status'    => 1,
                'resume_id' => $resumeId,
                'type'      => $type,
            ])
            ->andWhere([
                '!=',
                'title',
                '',
            ])
            ->limit($limit)
            ->orderBy('add_time desc,id desc')
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['typeTxt']         = BaseSearchLog::TYPE_LIST[$item['type']];
            $item['platformTypeTxt'] = BaseSearchLog::PLATFORM_TYPE_LIST['platform_type'];
            $item['addTime']         = strtotime($item['add_time']);
        }

        return $list;
    }

    /**
     * 删除检索记录
     * @throws Exception
     */
    public static function editInfo($searchIds)
    {
        $baseJob = new self();
        if (!self::updateAll(['status' => self::STATUS_DELETE], ['id' => $searchIds])) {
            throw new Exception($baseJob->getFirstErrorsMessage());
        }
    }
}