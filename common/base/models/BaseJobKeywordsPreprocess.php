<?php

namespace common\base\models;

use common\components\MessageException;
use common\models\JobKeywordsPreprocess;
use yii\base\Exception;
use yii\db\conditions\AndCondition;

class BaseJobKeywordsPreprocess extends JobKeywordsPreprocess
{

    /**
     * 插入数据
     * @param $data
     * @return int
     * @throws MessageException
     */
    public static function insertOne($data)
    {
        $model           = new self();
        $model->keywords = $data['keywords'] ?? '';
        $model->job_ids  = $data['jobIds'] ?? '';
        if (!$model->save()) {
            throw new MessageException('插入数据失败' . http_build_query($data));
        }

        return $model->id;
    }

    /**
     * 返回单一数据
     * @param $where
     * @param $field
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function getOneByWhere($where, $field = ['id'])
    {
        return self::find()
            ->where(new AndCondition($where))
            ->select($field)
            ->asArray()
            ->one();
    }
    
    /**
     * 根据条件获取列表
     * @param $where
     * @param $field
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getListByWhere($where = [], $field = ['id'])
    {
        return self::find()
            ->where(new AndCondition($where))
            ->select($field)
            ->asArray()
            ->all();
    }
}