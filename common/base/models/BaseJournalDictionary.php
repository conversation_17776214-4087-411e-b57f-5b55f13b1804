<?php
/**
 * create user：shannon
 * create time：2025/10/13 10:04
 */
namespace common\base\models;

use common\helpers\ArrayHelper;
use common\libs\Cache;
use common\models\JournalDictionary;

class BaseJournalDictionary extends JournalDictionary
{
    const IS_TOP_YES = 1;
    const IS_TOP_NO  = 0;

    public static function setCache()
    {
        $list = self::find()
            ->where(['status' => self::STATUS_ACTIVE])
            ->select(['id', 'name_cn'])
            ->asArray()
            ->all();

        Cache::hMSet(Cache::ALL_TABLE_JOURNAL_DICTIONARY_KEY, array_column($list, null, 'id'));

        return true;
    }

    /**
     * 快速获取字典名称
     * @param $id
     * @return mixed
     * @throws \Exception
     */
    public static function getName($id)
    {
        $info = Cache::hGet(Cache::ALL_TABLE_JOURNAL_DICTIONARY_KEY, $id);
        if (!$info) {
            self::setCache();
            $info = Cache::hGet(Cache::ALL_TABLE_JOURNAL_DICTIONARY_KEY, $id);
        }
        $info = json_decode($info, true);

        return ArrayHelper::getValue($info, 'name_cn');
    }
}