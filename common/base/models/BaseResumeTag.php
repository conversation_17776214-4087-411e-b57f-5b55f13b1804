<?php

namespace common\base\models;

use common\helpers\ArrayHelper;
use common\models\ResumeTag;

class BaseResumeTag extends ResumeTag
{
    public static function getTagList($resumeId)
    {
        return self::find()
            ->select('a.id')
            ->alias('a')
            ->innerJoin(['b' => BaseResumeTagRelation::tableName()], 'a.id=b.resume_tag_id')
            ->where(['b.resume_id' => $resumeId])
            ->column();
    }

    public static function getTabNameList($resumeId)
    {
        return self::find()
            ->select('a.tag')
            ->alias('a')
            ->innerJoin(['b' => BaseResumeTagRelation::tableName()], 'a.id=b.resume_tag_id')
            ->where(['b.resume_id' => $resumeId])
            ->column();
    }

    public static function getAllTagList()
    {
        $field = [
            'id as k',
            'tag as v',
        ];

        $list = self::find()
            ->select($field)
            ->orderBy('id desc')
            ->asArray()
            ->all();

        return ArrayHelper::objMoreArr($list);
    }
}