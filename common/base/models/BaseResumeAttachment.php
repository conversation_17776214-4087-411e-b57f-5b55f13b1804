<?php

namespace common\base\models;

use common\helpers\ArrayHelper;
use common\helpers\FileHelper;
use common\libs\Aliyun\Oss;
use common\models\ResumeAttachment;
use yii\base\Exception;

class BaseResumeAttachment extends ResumeAttachment
{

    const ENCRYPTION_KEY = 'resume';

    const STATUS_DISABLE = 3;

    const IS_DEFAULT_YES      = 1;
    const IS_DEFAULT_NO       = 2;
    const LIMIT_RECORD_AMOUNT = 8;

    //根据简历下载次数排序
    const SORT_RESUME_DOWNLOAD_AMOUNT_ASC  = 1;
    const SORT_RESUME_DOWNLOAD_AMOUNT_DESC = 2;
    //根据站内投递数量排序
    const SORT_ON_SITE_APPLY_AMOUNT_ASC  = 1;
    const SORT_ON_SITE_APPLY_AMOUNT_DESC = 2;
    //根据站外投递数量排序
    const SORT_OFF_SITE_APPLY_AMOUNT_ASC  = 1;
    const SORT_OFF_SITE_APPLY_AMOUNT_DESC = 2;
    //上传时间排序
    const SORT_UPLOAD_TIME_ASC  = 1;
    const SORT_UPLOAD_TIME_DESC = 2;
    //最近使用时间
    const SORT_LAST_APPLY_TIME_ASC  = 1;
    const SORT_LAST_APPLY_TIME_DESC = 2;
    //是否拥有附件简历
    const HAS_FILE_YES = 1;
    const HAS_FILE_NO  = 2;

    // 是否已经上传到oss
    const IS_UPLOAD_OSS_YES = 1;
    const IS_UPLOAD_OSS_NO  = 2;

    public static function selectInfo($where, $select = [], $orderBy = '')
    {
        return self::find()
            ->where($where)
            ->select($select)
            ->orderBy($orderBy)
            ->asArray()
            ->one();
    }

    /**
     * 根据id寻找名称
     * @param $id
     * @return mixed
     * @throws \Exception
     */
    public static function getNameById($id)
    {
        $info = self::find()
            ->where(['id' => $id])
            ->select(['file_name'])
            ->asArray()
            ->one();

        return ArrayHelper::getValue($info, 'file_name');
    }

    /**
     * 获取用户的附件简历列表
     * @param $memberId
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getList($memberId)
    {
        $list = self::find()
            ->where(['member_id' => $memberId])
            ->andWhere([
                'in',
                'status',
                [
                    self::STATUS_ACTIVE,
                    self::STATUS_DISABLE,
                ],
            ])
            ->select([
                'file_name as fileName',
                'id',
                'file_url as fileUrl',
                'token',
                'is_default',
                'add_time as addTime',
            ])
            ->orderBy('add_time desc')
            ->asArray()
            ->limit(self::LIMIT_RECORD_AMOUNT)
            ->all();
        foreach ($list as &$item) {
            $item['addTime']  = date('Y-m-d H:i', strtotime($item['addTime']));
            $item['trueName'] = FileHelper::getFileName($item['fileName']);
        }

        return $list;
    }

    /**
     * 修改附件简历状态
     * @param $id
     */
    public static function changeStatus($id)
    {
        $model = self::findOne(['id' => $id]);
        if ($model->status == self::STATUS_DISABLE) {
            $model->status = self::STATUS_ACTIVE;
        } elseif ($model->status == self::STATUS_ACTIVE) {
            $model->status = self::STATUS_DISABLE;
        }
        $model->save();
    }

    /**
     * @param $memberId
     * @param $id
     */
    public static function encryption($memberId, $id)
    {
        return md5($memberId . '_' . $id);
    }

    /**
     * 获取默认简历token
     * @param $memberId
     * @return mixed
     * @throws \Exception
     */
    public static function getDefaultResumeToken($memberId)
    {
        $info  = self::find()
            ->where(['member_id' => $memberId])
            ->andWhere(['is_default' => self::IS_DEFAULT_YES])
            ->andWhere(['status' => self::STATUS_ACTIVE])
            ->select('token')
            ->asArray()
            ->one();
        $token = ArrayHelper::getValue($info, 'token');
        if (!empty($token)) {
            return $token;
        } else {
            return '';
        }
    }

    /**
     * 获取简历token
     * @param $resumeId
     * @return mixed
     * @throws \Exception
     */
    public static function getResumeToken($resumeId)
    {
        $info = self::find()
            ->where(['resume_id' => $resumeId])
            ->select('token')
            ->one();
        if ($info) {
            return $info['token'];
        } else {
            return '';
        }
    }

    /**
     * 设置默认简历
     * @param $resumeAttachmentId
     * @param $memberId
     */
    public static function setDefaultResume($resumeAttachmentId, $memberId)
    {
        //先清除用户的默认简历状态
        self::updateAll(['is_default' => self::IS_DEFAULT_NO], ['member_id' => $memberId]);
        //再把当前简历设置为默认
        $currentModel = self::findOne(['id' => $resumeAttachmentId]);
        if (!$currentModel) {
            return;
        }
        $currentModel->is_default = self::IS_DEFAULT_YES;
        $currentModel->save();
    }

    /**
     * 获取真实的路径
     * @param $path
     * @return bool|string
     */
    public static function getRealPath($path)
    {
        return \Yii::getAlias('@resume') . '/' . $path;
    }

    public static function getFileAmount($resumeId)
    {
        return self::find()
            ->where([
                'resume_id' => $resumeId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->count();
    }

    /**
     * 保存附件简历
     * @param $data
     * @return array
     * @throws Exception
     */
    public static function saveAttachment($data)
    {
        if (empty($data['path']) || empty($data['name'])) {
            throw new Exception('文件信息获取失败！');
        }
        $memberId             = \Yii::$app->user->id ?: 0;
        $resumeId             = BaseMember::getMainId() ?: 0;
        $model                = new self();
        $model->file_id       = $data['id'] ?: 0;
        $model->file_url      = $data['path'];
        $model->file_name     = $data['name'];
        $model->member_id     = $data['memberId'] ?: $memberId;
        $model->is_upload_oss = self::IS_UPLOAD_OSS_NO;
        $model->resume_id     = $data['resumeId'] ?: $resumeId;
        $model->note          = $data['note'] ?: '';

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        $id = $model->id;

        $token        = self::encryption($memberId, $id);
        $model->token = $token;

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }

        //        $env = \Yii::$app->params['environment'];
        //        if ($env == 'prod') {
        // 判断是正式环境就上传到阿里云备份一份
        self::uploadToOss($id, $data['isPdf'], $data['pathPdf']);

        //        }

        return [
            'id'    => $id,
            'token' => $token,
        ];
    }

    // 上传到oss
    public static function uploadToOss($resumeAttachmentId, $isPdf = false, $pdfPath = '')
    {
        $model = self::findOne(['id' => $resumeAttachmentId]);
        if (!$model) {
            return;
        }

        $oss = new Oss();

        // 处理文件路径及上传逻辑
        $fileUrl = self::buildOssFilePath($model->file_url);
        self::processUpload($oss, $model->file_url, $fileUrl, '上传失败');

        // 如果需要上传PDF
        if ($isPdf) {
            $pdfPathDir = self::buildOssFilePath($pdfPath);
            self::processUpload($oss, $pdfPath, $pdfPathDir, 'PDF上传失败');
        }

        $model->is_upload_oss = self::IS_UPLOAD_OSS_YES;
        $model->save();
    }

    private static function buildOssFilePath($filePath)
    {
        $env = \Yii::$app->params['environment'] === 'prod' ? '' : \Yii::$app->params['environment'];

        return $env ? str_replace('original/', $env . '/original/', $filePath) : $filePath;
    }

    private static function processUpload($oss, $localPath, $ossPath, $errorMessage)
    {
        if (!$oss->uploadAttachment(self::getRealPath($localPath), $ossPath)) {
            throw new Exception($errorMessage);
        }
    }

    /**
     * 检查上传限制
     * @param $memberId
     * @return void
     * @throws Exception
     */
    public static function checkLimit($memberId)
    {
        $count = self::find()
            ->where(['member_id' => $memberId])
            ->andWhere([
                'in',
                'status',
                [
                    self::STATUS_ACTIVE,
                ],
            ])
            ->count();

        if ($count >= 8) {
            throw new Exception('最多只能有8份附件简历，请删除一份后再上传！');
        }
    }

    /**
     * 修改附件简历名称
     * @param $token
     * @param $name
     * @return bool
     * @throws Exception
     */
    public static function changeFileName($token, $name, $memberId, $hasLimit = false)
    {
        $model = self::findOne([
            'token'     => $token,
            'member_id' => $memberId,
        ]);
        if (empty($model)) {
            throw new Exception('简历不存在');
        }
        if ($hasLimit && mb_strlen($name) > 20) {
            throw new Exception('附件简历名称字数限制20字');
        }

        //获取文件原本的后缀
        //通过点号划分文件名成数组
        $nameArr = explode('.', $model->file_name);
        //获取拓展部分
        $extension = $nameArr[count($nameArr) - 1];

        $model->file_name = $name . '.' . $extension;
        if (!$model->save()) {
            throw new Exception('简历名称修改失败！');
        }

        return true;
    }

    public static function getContent($id)
    {
        // 以文件流的方式返回
        $file = self::findOne($id);

        if ($file->is_upload_oss == self::IS_UPLOAD_OSS_YES) {
            $oss = new Oss();

            return $oss->getAttachmentContent($file->file_url);
        }
        $env  = \Yii::$app->params['environment'];
        $path = $file->file_url;
        if ($env != 'prod' && strpos($file->file_url, $env) === false) {
            $path = $env . '/' . $file->file_url;
        }

        return file_get_contents($path);
    }
}