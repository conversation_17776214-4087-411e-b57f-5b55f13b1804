<?php

namespace common\base\models;

use common\models\AdminJobInviteConfig;

class BaseAdminJobInviteConfig extends AdminJobInviteConfig
{
    //执行状态
    const STATUS_CALL_EXEC_WAITING = 1;//待执行
    const STATUS_CALL_EXEC_SUCCESS = 2;//执行完成
    const STATUS_CALL_EXEC_LOCK    = 9;//执行中

    const INVITE_WAY_MANUAL    = 1;//手动邀约
    const INVITE_WAY_AUTO      = 2;//智能邀约
    const INVITE_WAY_TYPE      = [
        self::INVITE_WAY_MANUAL,
        self::INVITE_WAY_AUTO,
    ];
    const INVITE_WAY_TYPE_TEXT = [
        self::INVITE_WAY_MANUAL => '手动邀约',
        self::INVITE_WAY_AUTO   => '智能邀约',
    ];

    //1学历 2专业 3意向职位  4意向城市 5工作经验 6职称 7政治面貌 8海外经验
    const MATCH_TYPE_EDUCATION  = 1;
    const MATCH_TYPE_MAJOR      = 2;
    const MATCH_TYPE_CATEGORY   = 3;
    const MATCH_TYPE_CITY       = 4;
    const MATCH_TYPE_EXPERIENCE = 5;
    const MATCH_TYPE_TITLE      = 6;
    const MATCH_TYPE_POLITICAL  = 7;
    const MATCH_TYPE_ABROAD     = 8;

    //    const MATCH_PARAM_CONFIG    = [
    //        self::MATCH_TYPE_EDUCATION  => 'educationType',
    //        self::MATCH_TYPE_MAJOR      => 'majorId',
    //        self::MATCH_TYPE_CATEGORY   => 'jobCategoryId',
    //        self::MATCH_TYPE_CITY       => [
    //            'provinceId',
    //            'cityId',
    //        ],
    //        self::MATCH_TYPE_EXPERIENCE => 'experienceType',
    //        self::MATCH_TYPE_TITLE      => 'titleType',
    //        self::MATCH_TYPE_POLITICAL  => 'politicalType',
    //        self::MATCH_TYPE_ABROAD     => 'abroadType',
    //    ];

    const PERSON_ACTIVE_DAY_NUMBER_MAX = 90;//活跃时间最大值（单位：天）
    const INVITE_NUMBER_MAX            = 200;//邀请人数最大值（单位：人）

    ///1、先做所有大前提校验（3个条件）
    /// ---- 人才是否存在
    /// ---- 简历未开放
    /// ---- 屏蔽该单位
    const SEARCH_VERIFY_TYPE_RESUME_ISSET   = 20;
    const SEARCH_VERIFY_TYPE_RESUME_IS_OPEN = 21;
    const SEARCH_VERIFY_TYPE_RESUME_IS_HIDE = 22;

    ///2、可选条件校验（四个，即人才过滤项）
    /// ---- 30天内已邀约过该职位
    /// ---- 30天内已投递过该职位
    /// ---- 简历完善度不足65%
    /// ---- 30天内已被邀约过
    const RESUME_COMPLETE_VERIFY_65               = 65;
    const SEARCH_VERIFY_TYPE_RESUME_INVITE_JOB_30 = 1;
    const SEARCH_VERIFY_TYPE_RESUME_DELIVERY_30   = 2;
    const SEARCH_VERIFY_TYPE_RESUME_COMPLETE_65   = 3;
    const SEARCH_VERIFY_TYPE_RESUME_INVITE_30     = 4;
    const SEARCH_VERIFY_TYPE_RESUME               = [
        self::SEARCH_VERIFY_TYPE_RESUME_ISSET         => '人才不存在',
        self::SEARCH_VERIFY_TYPE_RESUME_IS_OPEN       => '简历未开放',
        self::SEARCH_VERIFY_TYPE_RESUME_IS_HIDE       => '屏蔽了该单位',
        self::SEARCH_VERIFY_TYPE_RESUME_INVITE_JOB_30 => '30天内已邀约过该职位',
        self::SEARCH_VERIFY_TYPE_RESUME_DELIVERY_30   => '30天内已投递过该职位',
        self::SEARCH_VERIFY_TYPE_RESUME_COMPLETE_65   => '简历完善度不足65%',
        self::SEARCH_VERIFY_TYPE_RESUME_INVITE_30     => '30天内已被邀约过',
    ];
    //固定文本
    const SEARCH_VERIFY_TYPE_RESUME_FIXED_TEXT_CONFIG = [
        self::SEARCH_VERIFY_TYPE_RESUME_INVITE_JOB_30 => '邀请人：{a}，邀请时间：{t}',
        self::SEARCH_VERIFY_TYPE_RESUME_DELIVERY_30   => '',
        self::SEARCH_VERIFY_TYPE_RESUME_COMPLETE_65   => '',
        self::SEARCH_VERIFY_TYPE_RESUME_INVITE_30     => '邀请人：{a}，邀请时间：{t}',
    ];
    //可选晒筛选项
    const SEARCH_VERIFY_TYPE_RESUME_OPTIONS = [
        self::SEARCH_VERIFY_TYPE_RESUME_INVITE_JOB_30,
        self::SEARCH_VERIFY_TYPE_RESUME_DELIVERY_30,
        self::SEARCH_VERIFY_TYPE_RESUME_COMPLETE_65,
        self::SEARCH_VERIFY_TYPE_RESUME_INVITE_30,
    ];

    //已邀约职位的类型
    const INVITE_JOB_TYPE_7  = 1;
    const INVITE_JOB_TYPE_15 = 2;
    const INVITE_JOB_TYPE_30 = 3;

    //已投递过该职位类型
    const APPLY_TYPE_7  = 1;
    const APPLY_TYPE_15 = 2;
    const APPLY_TYPE_30 = 3;

    const RESUME_COMPLETE_TYPE_NO  = 0;
    const RESUME_COMPLETE_TYPE_YES = 1;

    //已邀约职位的类型
    const INVITE_TYPE_7  = 1;
    const INVITE_TYPE_15 = 2;
    const INVITE_TYPE_30 = 3;

    /**
     * 邀约文案配置
     * @return array
     */
    public static function getTextConfig()
    {
        return [
            'select_list'      => [
                [
                    "id"   => 1,
                    "name" => "通用版",
                ],
                [
                    "id"   => 2,
                    "name" => "版本2",
                ],
                [
                    "id"   => 3,
                    "name" => "版本3",
                ],
                [
                    "id"   => 4,
                    "name" => "版本4",
                ],
                [
                    "id"   => 5,
                    "name" => "版本5",
                ],
                [
                    "id"   => 66,
                    "name" => "自定义",
                ],
            ],
            //定义替换标签内容
            //{c}  单位名称
            //{u}  人才姓名
            //{j}  职位名称
            'select_text_list' => [
                1  => "{c}对您的简历很感兴趣，邀请您投递{j}职位，期待您的回复。",
                2  => "hi,{u}~你关注的{j}有新职位发布啦~",
                3  => "hi,{u}~我们觉得这个职位非常适合您，诚邀您投递",
                4  => "{c}正在急招{j}，待遇丰厚，欢迎了解",
                5  => "hi，{j}正在热招中，您感兴趣吗？期待进一步的沟通",
                66 => "",
                //邀约文案文本框可编辑限60字以内；
            ],
        ];
    }

    /**
     * 验证信息文本替换
     * @param $type
     * @param $invite_admin_name
     * @param $time
     * @return string|string[]
     */
    public static function verifyTextReplace($type, $invite_admin_name = '', $time = '')
    {
        $text_config = BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_FIXED_TEXT_CONFIG[$type];
        if (empty($text_config)) {
            return '';
        }

        return str_replace([
            '{a}',
            '{t}',
        ], [
            $invite_admin_name,
            date('Y年m月d日', strtotime($time)),
        ], $text_config);
    }

    /**
     * 邀约文本替换
     * @param $type
     * @param $invite_admin_name
     * @param $time
     * @return string|string[]
     */
    public static function inviteTextReplace($text, $resumeName = '', $jobName = '', $companyName = '')
    {
        return str_replace([
            '{u}',
            '{j}',
            '{c}',
        ], [
            $resumeName,
            $jobName,
            $companyName,
        ], $text);
    }
}