<?php

namespace common\base\models;

use common\models\CompanyResumeLibrary;
use yii\base\Exception;

class BaseCompanyResumeLibrary extends CompanyResumeLibrary
{

    const SOURCE_TYPE_APPLY    = 1; // 投递来源
    const SOURCE_TYPE_DOWNLOAD = 2; // 下载来源
    const SOURCE_TYPE_BOTH     = 3; // 二者都有

    //存在记录就是1
    const HAS_RECORD_YES = 1;
    const HAS_RECORD_NO  = 2;

    public static function saveApplyRecord($data)
    {
        //这里要判断是否已经有简历库记录了，如果有的话，就更新下source_type和apply_time，否则再创建新的
        $model = self::find()
            ->where([
                'company_id'        => $data['companyId'],
                'resume_id'         => $data['resumeId'],
            ])
            ->one();
        if (!empty($model)) {
            //只有下载了的情况，才需要改动
            if ($model->source_type == self::SOURCE_TYPE_DOWNLOAD) {
                $model->source_type = self::SOURCE_TYPE_BOTH;
                $model->apply_time  = CUR_DATETIME;
                if (!$model->save()) {
                    throw new Exception('保存简历库失败');
                }
            }
        } else {
            $model                    = new self();
            $model->company_id        = $data['companyId'];
            $model->resume_id         = $data['resumeId'];
            $model->source_type       = self::SOURCE_TYPE_APPLY;
            $model->apply_time        = CUR_DATETIME;
            $model->company_member_id = $data['companyMemberId'];
            if (!$model->save()) {
                throw new Exception('保存简历库失败');
            }
        }
    }

    /**
     * 判断是否存在记录
     * @param $resumeId
     * @param $companyId
     * @return int
     */
    public static function checkDownLoadStatus($resumeId, $companyId)
    {
        $hasDownloadRecord = self::findOne([
            'resume_id'  => $resumeId,
            'company_id' => $companyId,
        ]);
        if (!empty($hasDownloadRecord)) {
            return self::HAS_RECORD_YES;
        } else {
            return self::HAS_RECORD_NO;
        }
    }
}