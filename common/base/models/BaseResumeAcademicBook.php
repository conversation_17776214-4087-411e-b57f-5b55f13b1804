<?php

namespace common\base\models;

use common\components\MessageException;
use common\helpers\TimeHelper;
use common\models\ResumeAcademicBook;

class BaseResumeAcademicBook extends ResumeAcademicBook
{
    const LIMIT_NUM = 5;

    public function rules()
    {
        return [
            [
                [
                    'add_time',
                    'update_time',
                    'publish_date',
                    'status',
                    'resume_id',
                    'member_id',
                ],
                'safe',
            ],
            [
                [
                    'name',
                    'words',
                ],
                'required',
            ],
            [
                ['name'],
                'string',
                'max' => 64,
            ],
            [
                [
                    'words',
                    'publish_amount',
                ],
                'string',
                'max' => 32,
            ],
        ];
    }

    /**
     * 获取正常数据的条数
     * @param $memberId
     * @return bool|int|string|null
     */
    public static function getRecordAmount($memberId)
    {
        return self::find()
            ->where([
                'member_id' => $memberId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->count();
    }

    public static function getCompleteInfo($resumeId, $resumeAcademicBookId)
    {
        $resumeAcademicBookInfo = self::find()
            ->select([
                'id',
                // 著作名称
                'name',
                // 著作类别
                'type_code',
                // 出版社
                'publisher',
                // 出版时间
                'publish_date',
                // 本人角色
                'role_code',
                // 本人撰写占比
                'writing_ratio',
                // 所获奖项
                'awards',
                // 著作描述
                'description',
                // 所获奖项
                'awards',
                'status',
            ])
            ->where([
                'resume_id' => $resumeId,
                'id'        => $resumeAcademicBookId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->asArray()
            ->one();

        if (!$resumeAcademicBookInfo) {
            throw new MessageException('数据异常');
        }

        $resumeAcademicBookInfo['publish_date']  = TimeHelper::formatToYearMonth($resumeAcademicBookInfo['publish_date']);
        $resumeAcademicBookInfo['stageRelation'] = BaseResumeAcademicBookStageTypeRelation::find()
            ->where([
                'resume_academic_book_id' => $resumeAcademicBookInfo['id'],
                'status'                  => BaseResumeAcademicBookStageTypeRelation::STATUS_ACTIVE,
            ])
            ->select([
                'related_stage_id',
                'type',
            ])
            ->asArray()
            ->all();

        return $resumeAcademicBookInfo;
    }
}