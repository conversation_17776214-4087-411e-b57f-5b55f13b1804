<?php

namespace common\base\models;

use common\helpers\TimeHelper;
use common\models\AnnouncementClickTotalDaily;

class BaseAnnouncementClickTotalDaily extends AnnouncementClickTotalDaily
{
    public static function updateDate($date)
    {
        // 如果没有传时间，就获取昨天的时间
        if (!$date) {
            $date = TimeHelper::getYesterday();
        }

        // 获取日期的全部公告点击数据，按照公告维度来分组
        $list = BaseArticleClickLog::find()
            ->alias('a')
            ->innerJoin(['b' => BaseAnnouncement::tableName()], 'a.article_id=b.article_id')
            ->select([
                new \yii\db\Expression("'" . $date . "' as date"),
                'b.id as announcement_id',
                'count(a.id) as total',
            ])
            // 日期$date 直接传入
            // between
            ->where([
                'between',
                'a.add_time',
                TimeHelper::dayToBeginTime($date),
                TimeHelper::dayToEndTime($date),
            ])
            ->groupBy('b.id')
            ->asArray()
            ->all();

        if (!$list) {
            return false;
        }

        // 首先删除掉这个日期里面的数据
        self::deleteAll(['add_date' => $date]);

        // 然后开始插入数据
        self::getDb()
            ->createCommand()
            ->batchInsert(self::tableName(), [
                'add_date',
                'announcement_id',
                'total',
            ], $list)
            ->execute();
    }
}