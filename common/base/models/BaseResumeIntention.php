<?php

namespace common\base\models;

use common\base\BaseActiveRecord;
use common\components\MessageException;
use common\libs\Cache;
use common\models\ResumeIntention;
use yii\base\Exception;

class BaseResumeIntention extends ResumeIntention
{
    // 完整状态
    const COMPLETE_STATUS_COMPLETE    = 1;
    const COMPLETE_STATUS_UN_COMPLETE = 2;

    const STATUS_FREEZE = 9; // 冻结

    const CAN_DELETE_RECORD_YES = 1;
    const CAN_DELETE_RECORD_NO  = 2;

    public function rules()
    {
        return [
            [
                [
                    'add_time',
                    'update_time',
                    'min_wage',
                    'max_wage',
                ],
                'safe',
            ],
            [
                [
                    'status',
                    'resume_id',
                    'member_id',
                    'nature_type',
                    'job_category_id',
                    'wage_type',
                ],
                'integer',
            ],
            [
                ['area_id'],
                'string',
                'max' => 256,
            ],
        ];
    }

    const LIMIT_NUM = 5;

    /**
     * 保存求职意向
     * @param $data
     * @throws Exception
     */
    public static function saveInfo($data)
    {
        //判断参数
        if (empty($data['jobCategoryId']) || empty($data['natureType']) || empty($data['areaId']) || empty($data['wageType'])) {
            throw new Exception('求职意向缺失必填参数');
        }
        $memberId = $data['memberId'];

        if (!empty($data['id'])) {
            //说明是从编辑页面进来
            $model = self::findOne($data['id']);
            if (empty($model) || $model->status != self::STATUS_ACTIVE) {
                throw new Exception('求职意向记录不存在!');
            }
        } else {
            $resumeId = Cache::get(Cache::PC_ALL_RESUME_ID_KEY . ':' . $memberId);

            $model = new self();

            $model->member_id = $memberId;
            $model->resume_id = $resumeId;
        }
        $model->job_category_id = $data['jobCategoryId'];
        $model->area_id         = $data['areaId'];
        $model->nature_type     = $data['natureType'];
        //        $model->wage_type       = $data['wageType'];
        //        $wage_info = Dictionary::getMinAndMaxWage($data['wageType']);
        $model->min_wage      = $data['minWage'];
        $model->max_wage      = $data['maxWage'];
        $model->preference    = $data['preference'] ? implode(',', $data['preference']) : '';
        $model->template_type = $data['educationId'] == BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE ? 1 : 2;

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }

        //新增操作日志
        $log_data = [
            'content' => '保存简历求职意向信息，memberId：' . $memberId,
        ];
        // 写日志
        BaseMemberActionLog::log($log_data);
    }

    public static function getAllCityName($resumeId)
    {
        $cityName = self::find()
            ->select('a.name')
            ->innerJoin(['a' => BaseArea::tableName()], 'a.id = area_id')
            ->where(['resume_id' => $resumeId])
            ->asArray()
            ->all();
        $cityName = array_column($cityName, 'name');
        $cityName = implode(',', $cityName);

        return $cityName;
    }

    /**
     * 获取人才简历一个求职意向（人才库，简历库显示用）
     * @param $resumeId
     * @return mixed|string
     * @throws \Exception
     */
    public static function getResumeLibraryJobCategoryText($resumeId)
    {
        $jobCategoryIdArr = self::find()
            ->select('job_category_id')
            ->where(['resume_id' => $resumeId])
            ->asArray()
            ->column();
        if (!empty($jobCategoryIdArr)) {
            $jobCategoryCount = count($jobCategoryIdArr);
            if ($jobCategoryCount > 2) {
                $jobCategoryTextArr = [];
                foreach ($jobCategoryIdArr as $k => $id) {
                    if ($k > 2) {
                        break;
                    }
                    $jobCategoryTextArr[] = BaseCategoryJob::getName($id);
                }

                return implode('、', $jobCategoryTextArr) . '等';
            } else {
                return BaseCategoryJob::getName($jobCategoryIdArr[0]);
            }
        } else {
            return '';
        }
    }

    /**
     * 获取所有求职意向，拼接起来
     * @param $resumeId
     * @return false|string
     * @throws \Exception
     */
    public static function getAllJobCategoryText($resumeId, $separator = ',')
    {
        $jobCategoryList = self::find()
            ->select('job_category_id')
            ->where([
                'resume_id' => $resumeId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->asArray()
            ->all();

        if (empty($jobCategoryList)) {
            return '';
        } else {
            $jobCategoryNameArr = [];
            foreach ($jobCategoryList as $item) {
                $jobCategoryNameArr[] = BaseCategoryJob::getName($item['job_category_id']);
            }

            return implode($separator, $jobCategoryNameArr);
        }
    }

    public static function getFullInfo($resumeId)
    {
        $info = self::find()
            ->select('nature_type,job_category_id,area_id,min_wage,max_wage,wage_type')
            ->where([
                'resume_id' => $resumeId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->asArray()
            ->all();

        foreach ($info as &$item) {
            $areaIds = explode(',', $item['area_id']);

            $areaList = BaseArea::find()
                ->select('id,name,level')
                ->where(['id' => $areaIds])
                ->asArray()
                ->all();

            // 等级1的是省，等级2的是市，等级3的是区
            $provinceList = [];
            $cityList     = [];

            foreach ($areaList as $areaItem) {
                if ($areaItem['level'] == 1) {
                    $provinceList[] = $areaItem['name'];
                } elseif ($areaItem['level'] == 2) {
                    $cityList[] = $areaItem['name'];
                }
            }

            $item['provinceList'] = $provinceList;
            $item['cityList']     = $cityList;
            // $item['jobCategory']  = BaseCategoryJob::getName($item['job_category_id']);

            $categoryLevel2 = BaseCategoryJob::find()
                ->where([
                    'id' => $item['job_category_id'],
                ])
                ->select('id,name,parent_id')
                ->asArray()
                ->one();

            $categoryLevel1 = BaseCategoryJob::find()
                ->where([
                    'id' => $categoryLevel2['parent_id'],
                ])
                ->select('id,name,parent_id')
                ->asArray()
                ->one();

            $item['jobCategoryLevel2'] = $categoryLevel2['name'];
            $item['jobCategoryLevel1'] = $categoryLevel1['name'];
        }

        return $info;

        // // 获取所有的城市
        // $areaIds = array_column($info, 'area_id');
        // $areaIds = explode(',', implode(',', $areaIds));
        // $areaIds = array_unique($areaIds);
        //
        // $areaList = BaseArea::find()
        //     ->select('id,name,level')
        //     ->where(['id' => $areaIds])
        //     ->asArray()
        //     ->all();
        //
        // // 等级1的是省，等级2的是市，等级3的是区
        // $provinceList = [];
        // $cityList     = [];
        //
        // foreach ($areaList as $item) {
        //     if ($item['level'] == 1) {
        //         $provinceList[$item['id']] = $item['name'];
        //     } elseif ($item['level'] == 2) {
        //         $cityList[$item['id']] = $item['name'];
        //     }
        // }
        //
        // // 获取所有的职位类型
        // $jobCategoryIds  = array_column($info, 'job_category_id');
        // $jobCategoryIds  = explode(',', implode(',', $jobCategoryIds));
        // $jobCategoryList = BaseCategoryJob::find()
        //     ->select('id,name')
        //     ->where(['id' => $jobCategoryIds])
        //     ->asArray()
        //     ->all();
        //
        // return [
        //     'provinceList'    => $provinceList,
        //     'cityList'        => $cityList,
        //     'jobCategoryList' => $jobCategoryList,
        // ];
    }

    public static function getIntentionInfo($id): array
    {
        $list = BaseResumeIntention::find()
            ->where([
                'id' => $id,
            ])
            ->select([
                'id',
                'nature_type',
                'job_category_id',
                'area_id',
                'wage_type',
            ])
            ->asArray()
            ->one();

        $list['jobCategoryName'] = BaseCategoryJob::getName($list['job_category_id']);
        $list['wageName']        = BaseDictionary::getDataName(BaseDictionary::TYPE_24, $list['wage_type']);
        $list['natureName']      = BaseDictionary::getDataName(BaseDictionary::TYPE_3, $list['nature_type']);
        $areaId                  = explode(',', $list['area_id']);
        $areaNum                 = count($areaId);
        $list['areaName']        = '';
        foreach ($areaId as $key => $val) {
            if ($key < $areaNum - 1) {
                $list['areaName'] = $list['areaName'] . BaseArea::getAreaName($val) . ',';
            } else {
                $list['areaName'] = $list['areaName'] . BaseArea::getAreaName($val);
            }
        }

        return $list;
    }

    /**
     * 获取正常数据的条数
     * @param $memberId
     * @return bool|int|string|null
     */
    public static function getRecordAmount($memberId)
    {
        return self::find()
            ->where([
                'member_id' => $memberId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->count();
    }

    /**
     * 获取求职意向列表
     * @param $resumeId
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getIntentionList($resumeId)
    {
        return self::find()
            ->select([
                'id',
                'resume_id',
                'nature_type',
                'job_category_id',
                'area_id',
                'min_wage',
                'max_wage',
                'wage_type',
            ])
            ->where([
                'resume_id' => $resumeId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->orderBy('add_time desc')
            ->asArray()
            ->all();
    }

    public static function getLastIntentionInfo($resumeId): array
    {
        return BaseResumeIntention::find()
            ->where([
                'resume_id' => $resumeId,
                //                'status'    => BaseActiveRecord::STATUS_ACTIVE,
            ])
            ->select([
                'id',
                'status as work_status',
                'nature_type',
                'job_category_id',
                'area_id',
                'wage_type',
                'min_wage',
                'max_type',
                'preference',
            ])
            ->orderBy('add_time desc')
            ->asArray()
            ->one();
    }

    /**
     * 获取单条求职意向
     * @param $resumeId
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function getOneInfo($resumeId)
    {
        $info = self::find()
            ->where([
                'resume_id' => $resumeId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->select([
                //                'id',
                'nature_type as natureType',
                'job_category_id as jobCategoryId',
                'area_id as areaId',
                'wage_type as wageType',
            ])
            ->asArray()
            ->one();
        if ($info) {
            $info['areaId']   = array_filter(explode(',', $info['areaId']));
            $info['areaText'] = [];
            foreach ($info['areaId'] as $item) {
                array_push($info['areaText'], BaseArea::getAreaName($item));
            }
            $info['natureText']      = BaseDictionary::getNatureName($info['natureType']);
            $info['jobCategoryText'] = BaseCategoryJob::getName($info['jobCategoryId']);
            $info['wageText']        = BaseDictionary::getWageRangeName($info['wageType']);
        } else {
            //给定默认值，为前端服务
            $info = [
                'natureType'      => '',
                'natureText'      => '',
                'jobCategoryId'   => '',
                'jobCategoryText' => '',
                'areaId'          => [],
                'areaText'        => [],
                'wageType'        => '',
                'wageText'        => '',
            ];
        }

        return $info;
    }

    public static function getIntentionJobCateList($memberId): array
    {
        $list = BaseResumeIntention::find()
            ->where([
                'member_id' => $memberId,
                'status'    => BaseActiveRecord::STATUS_ACTIVE,
            ])
            ->select([
                'id',
                'job_category_id',
            ])
            ->asArray()
            ->all();
        foreach ($list as &$item) {
            $item['name'] = BaseCategoryJob::findOneVal(['id' => $item['job_category_id']], 'name');
            unset($item['job_category_id']);
        }

        return $list;
    }

    /**
     * 获取未完善简历时的用户求职意向
     * @param $resumeId
     * @param $templateType
     */
    public static function getUnCompleteInfo($resumeId, $templateType)
    {
        $resumeIntention = BaseResumeIntention::find()
            ->where([
                'resume_id'     => $resumeId,
                'template_type' => $templateType,
            ])
            ->andWhere([
                '<>',
                'status',
                self::STATUS_DELETE,
            ])
            ->select([
                'id',
                'template_type',
                'nature_type',
                'job_category_id',
                'min_wage',
                'max_wage',
                'preference',
                'status',
            ])
            ->orderBy('add_time desc')
            ->asArray()
            ->one() ?: [
            'id'              => '',
            'nature_type'     => '',
            'job_category_id' => '',
            'min_wage'        => '20',
            'max_wage'        => '30',
            'preference'      => '',
            'areaRelation'    => [],
        ];

        if (!$resumeIntention['id']) {
            $otherIntentionInfo = self::find()
                ->where([
                    'resume_id'     => $resumeId,
                    'template_type' => $templateType == BaseResume::TEMPLATE_TYPE_ACADEMIC ? BaseResume::TEMPLATE_TYPE_COMMON : BaseResume::TEMPLATE_TYPE_ACADEMIC,
                ])
                ->asArray()
                ->one();

            if ($otherIntentionInfo) {
                $resumeIntention       = self::getIntentionRelation($otherIntentionInfo);
                $resumeIntention['id'] = '';
            }
        } else {
            $resumeIntention = self::getIntentionRelation($resumeIntention);
        }

        return $resumeIntention;
    }

    private static function getIntentionRelation($resumeIntention)
    {
        $resumeIntention['areaRelation'] = BaseResumeIntentionAreaRelation::find()
            ->select([
                'area_id',
                'sort',
            ])
            ->where([
                'intention_id' => $resumeIntention['id'],
            ])
            ->orderBy('sort asc, id desc')
            ->asArray()
            ->all();

        if (PLATFORM == 'H5') {
            $resumeIntention['areaList'] = array_column($resumeIntention['areaList'], 'area_id');
        }

        return $resumeIntention;
    }

    /**
     * 简历编辑-求职意向
     * @param $resumeId
     * @param $templateType
     */
    public static function getCompleteInfo($resumeId, $intentionId)
    {
        if (empty($resumeId) || empty($intentionId)) {
            throw new MessageException('数据异常');
        }

        $intentionInfo = BaseResumeIntention::find()
            ->where([
                'resume_id' => $resumeId,
                'status'    => BaseActiveRecord::STATUS_ACTIVE,
                'id'        => $intentionId,
            ])
            ->select([
                'id',
                'template_type',
                'nature_type',
                'job_category_id',
                'min_wage',
                'max_wage',
            ])
            ->orderBy('add_time desc')
            ->asArray()
            ->one() ?: $defaultInfo;

        if ($intentionId['id']) {
            $areaRelation                  = BaseResumeIntentionAreaRelation::find()
                ->where([
                    'resume_id' => $resumeId,
                    'status'    => BaseActiveRecord::STATUS_ACTIVE,
                ])
                ->select([
                    'area_id',
                    'sort',
                ])
                ->orderBy('sort asc, id desc')
                ->asArray()
                ->all();
            $intentionInfo['areaRelation'] = $areaRelation;
        }

        return $intentionInfo;
    }

}