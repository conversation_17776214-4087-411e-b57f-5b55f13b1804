<?php

namespace common\base\models;

use common\libs\Cache;
use common\models\ResumeSetting;
use common\models\ShieldCompany;
use common\service\resume\ResumeLibraryService;

class BaseResumeSetting extends ResumeSetting
{
    //是否隐藏简历
    const IS_HIDE_RESUME_YES = 1;
    const IS_HIDE_RESUME_NO  = 2;

    //是否匿名显示
    const IS_ANONYMOUS_YES = 1;
    const IS_ANONYMOUS_NO  = 2;

    //是否代理投递
    const IS_PROXY_DELIVER_YES = 1;
    const IS_PROXY_DELIVER_NO  = 2;

    //是否开启求职反馈
    const IS_JOB_FEEDBACK_YES = 1;
    const IS_JOB_FEEDBACK_NO  = 2;

    //是否开启系统消息推送
    const IS_SYSTEM_MESSAGE_YES = 1;
    const IS_SYSTEM_MESSAGE_NO  = 2;

    //是否开启待办通知
    const IS_TODO_NOTICE_YES = 1;
    const IS_TODO_NOTICE_NO  = 2;

    // 是否开启职位邀请通知
    const IS_JOB_INVITE_YES = 1;
    const IS_JOB_INVITE_NO  = 2;

    // 是否开启谁看过我通知
    const IS_COMPANY_VIEW_ME_YES = 1;
    const IS_COMPANY_VIEW_ME_NO  = 2;

    public function create($resume_id)
    {
        $model                     = new self();
        $model->resume_id          = $resume_id;
        $model->is_hide_resume     = self::IS_HIDE_RESUME_NO;
        $model->is_anonymous       = self::IS_ANONYMOUS_YES;
        $model->is_proxy_deliver   = self::IS_PROXY_DELIVER_YES;
        $model->is_job_feedback    = self::IS_JOB_FEEDBACK_YES;
        $model->is_system_message  = self::IS_SYSTEM_MESSAGE_YES;
        $model->is_todo_notice     = self::IS_TODO_NOTICE_YES;
        $model->is_job_invite      = self::IS_JOB_INVITE_YES;
        $model->is_company_view_me = self::IS_COMPANY_VIEW_ME_YES;
        if (!$model->save()) {
            throw new \Exception($model->getFirstErrorsMessage());
        }
    }

    /**
     * 修改用户隐藏简历状态
     * @param $resumeId
     */
    public static function changeHideStatus($resumeId)
    {
        $model = self::findOne(['resume_id' => $resumeId]);
        $msg   = '';
        if ($model->is_hide_resume == self::IS_HIDE_RESUME_YES) {
            $model->is_hide_resume = self::IS_HIDE_RESUME_NO;
            $msg                   = '简历公开成功';
        } elseif ($model->is_hide_resume == self::IS_HIDE_RESUME_NO) {
            $model->is_hide_resume = self::IS_HIDE_RESUME_YES;
            $msg                   = '简历隐藏成功';
        }
        $res = $model->save();
        if ($res) {
            //更新简历人才库字段
            (new ResumeLibraryService())->setResumeId($resumeId)
                ->setOparetion(ResumeLibraryService::OPERATION_UPDATE_FILTER_RESUME_LIBRARY)
                ->run();
        }

        return [
            'msg' => $msg,
        ];
    }

    /**
     * 修改用户隐藏简历状态
     * @param $resumeId
     */
    public static function hideStatus($resumeId)
    {
        $model = self::findOne(['resume_id' => $resumeId]);
        $msg   = '';
        if ($model->is_hide_resume == self::IS_HIDE_RESUME_YES) {
            return true;
        } elseif ($model->is_hide_resume == self::IS_HIDE_RESUME_NO) {
            $model->is_hide_resume = self::IS_HIDE_RESUME_YES;
            $msg                   = '简历隐藏成功';
        }
        $res = $model->save();
        if ($res) {
            //更新简历人才库字段
            (new ResumeLibraryService())->setResumeId($resumeId)
                ->setOparetion(ResumeLibraryService::OPERATION_UPDATE_FILTER_RESUME_LIBRARY)
                ->run();
        }

        return [
            'msg' => $msg,
        ];
    }

    /**
     * 修改匿名显示状态
     * @param $memberId
     */
    public static function changeAnonymousStatus($memberId)
    {
        $resumeId = BaseResume::findOneVal(['member_id' => $memberId], 'id');
        $model    = self::findOne(['resume_id' => $resumeId]);
        if ($model->is_anonymous == self::IS_ANONYMOUS_YES) {
            $model->is_anonymous = self::IS_ANONYMOUS_NO;
        } elseif ($model->is_anonymous == self::IS_ANONYMOUS_NO) {
            $model->is_anonymous = self::IS_ANONYMOUS_YES;
        }
        $model->save();
    }

    /**
     * 修改简历代投状态
     * @param $memberId
     */
    public static function changeProxyDeliverStatus($memberId)
    {
        $resumeId = BaseResume::findOneVal(['member_id' => $memberId], 'id');
        $model    = self::findOne(['resume_id' => $resumeId]);
        if ($model->is_proxy_deliver == self::IS_PROXY_DELIVER_YES) {
            $model->is_proxy_deliver = self::IS_PROXY_DELIVER_NO;
        } elseif ($model->is_proxy_deliver == self::IS_PROXY_DELIVER_NO) {
            $model->is_proxy_deliver = self::IS_PROXY_DELIVER_YES;
        }
        $model->save();
    }

    /**
     * 修改求职反馈状态
     * @param $memberId
     */
    public static function changeJobFeedbackStatus($memberId)
    {
        $resumeId = BaseResume::findOneVal(['member_id' => $memberId], 'id');
        $model    = self::findOne(['resume_id' => $resumeId]);
        if ($model->is_job_feedback == self::IS_JOB_FEEDBACK_YES) {
            $model->is_job_feedback = self::IS_JOB_FEEDBACK_NO;
        } elseif ($model->is_job_feedback == self::IS_JOB_FEEDBACK_NO) {
            $model->is_job_feedback = self::IS_JOB_FEEDBACK_YES;
        }
        $model->save();
    }

    /**
     * 修改系统通知状态
     * @param $memberId
     */
    public static function changeSystemMessageStatus($memberId)
    {
        $resumeId = BaseResume::findOneVal(['member_id' => $memberId], 'id');
        $model    = self::findOne(['resume_id' => $resumeId]);
        if ($model->is_system_message == self::IS_SYSTEM_MESSAGE_YES) {
            $model->is_system_message = self::IS_SYSTEM_MESSAGE_NO;
        } elseif ($model->is_system_message == self::IS_SYSTEM_MESSAGE_NO) {
            $model->is_system_message = self::IS_SYSTEM_MESSAGE_YES;
        }
        $model->save();
    }

    /**
     * 修改待办通知状态
     * @param $memberId
     */
    public static function changeTodoNoticeStatus($memberId)
    {
        $resumeId = BaseResume::findOneVal(['member_id' => $memberId], 'id');
        $model    = self::findOne(['resume_id' => $resumeId]);
        if ($model->is_todo_notice == self::IS_TODO_NOTICE_YES) {
            $model->is_todo_notice = self::IS_TODO_NOTICE_NO;
        } elseif ($model->is_todo_notice == self::IS_TODO_NOTICE_NO) {
            $model->is_todo_notice = self::IS_TODO_NOTICE_YES;
        }
        $model->save();
    }

    /**
     * 修改求职邀请状态
     * @param $memberId
     */
    public static function changeJobInviteStatus($memberId)
    {
        $resumeId = BaseResume::findOneVal(['member_id' => $memberId], 'id');
        $model    = self::findOne(['resume_id' => $resumeId]);
        if ($model->is_job_invite == self::IS_JOB_INVITE_YES) {
            $model->is_job_invite = self::IS_JOB_INVITE_NO;
        } elseif ($model->is_job_invite == self::IS_JOB_INVITE_NO) {
            $model->is_job_invite = self::IS_JOB_INVITE_YES;
        }
        $model->save();
    }

    /**
     * 修改单位查看我的简历状态
     * @param $memberId
     */
    public static function changeCompanyViewMeStatus($memberId)
    {
        $resumeId = BaseResume::findOneVal(['member_id' => $memberId], 'id');
        $model    = self::findOne(['resume_id' => $resumeId]);
        if ($model->is_company_view_me == self::IS_COMPANY_VIEW_ME_YES) {
            $model->is_company_view_me = self::IS_COMPANY_VIEW_ME_NO;
        } elseif ($model->is_company_view_me == self::IS_COMPANY_VIEW_ME_NO) {
            $model->is_company_view_me = self::IS_COMPANY_VIEW_ME_YES;
        }
        $model->save();
    }

    public static function getMemberSettingInfo($resumeId)
    {
        $settingInfo = self::find()
            ->where(['resume_id' => $resumeId])
            ->select([
                'is_hide_resume as isHideResume',
                'is_anonymous as isAnonymous',
                'is_proxy_deliver as isProxyDeliver',
                'is_job_invite as isJobInvite',
                'is_company_view_me as isCompanyViewMe',
            ])
            ->asArray()
            ->one();
        //获取屏蔽单位列表数量
        $settingInfo['shieldCompanyAmount'] = ShieldCompany::find()
            ->where(['resume_id' => $resumeId])
            ->count();

        return $settingInfo;
    }
}