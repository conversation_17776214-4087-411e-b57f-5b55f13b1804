<?php

namespace common\base\models;

use common\helpers\UUIDHelper;
use common\libs\Cache;
use common\models\OffSiteJobApply;
use common\service\match\MatchCompleteService;
use Faker\Provider\Base;
use yii\base\Exception;
use yii\helpers\Url;
use Yii;

class BaseOffSiteJobApply extends OffSiteJobApply
{
    //职位来源
    const SOURCE_PERSONAL = 1;
    const SOURCE_WEBSITE  = 2;

    const SOURCE_TYPE_TEXT = [
        self::SOURCE_PERSONAL => '自主录入',
        self::SOURCE_WEBSITE  => '站内职位',
    ];

    const STATUS_HANDLE_WAIT     = 1;//已投递
    const STATUS_THROUGH_FIRST   = 2;//通过初筛
    const STATUS_SEND_INVITATION = 3;//邀请面试
    const STATUS_INAPPROPRIATE   = 4;//不合适
    const STATUS_EMPLOYED        = 5;//已录用

    //求职者列表状态

    const PERSON_STATUS_LIST = [
        self::STATUS_HANDLE_WAIT     => '已投递',
        self::STATUS_THROUGH_FIRST   => '通过初筛',
        self::STATUS_SEND_INVITATION => '邀请面试',
        self::STATUS_INAPPROPRIATE   => '不合适',
        self::STATUS_EMPLOYED        => '已录用',
    ];

    //限制多少天不能再次投递
    // const LIMIT_APPLY_DAYS = BaseJobApply::LIMIT_APPLY_DAYS_ONE;

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'add_time',
                    'update_time',
                    'apply_date',
                    'source',
                    'job_id',
                ],
                'safe',
            ],
            [
                [
                    'status',
                    'apply_status',
                    'resume_id',
                    'member_id',
                    'source',
                ],
                'integer',
            ],
            [
                ['title'],
                'string',
                'max' => 256,
            ],
            [
                ['link'],
                'string',
                'max' => 512,
            ],
            [
                [
                    'job_name',
                    'email',
                ],
                'string',
                'max' => 128,
            ],
            [
                ['salary'],
                'string',
                'max' => 64,
            ],
            [
                ['content'],
                'string',
                'max' => 1024,
            ],
        ];
    }

    //获取列表是否需要分页信息
    const NEED_PAGE_INFO_YES = true;
    const NEED_PAGE_INFO_NO  = false;

    // 这里大部分是从baseJobApply给拿过来的
    //产品图有问题，这里出现了两套列表，去除多余的值
    //    const APPLY_STATUS_HANDLE_WAIT     = 1;
    const APPLY_STATUS_THROUGH_FIRST   = 2;
    const APPLY_STATUS_SEND_INVITATION = 3;
    const APPLY_STATUS_INVITATION      = 4;
    const APPLY_STATUS_INAPPROPRIATE   = 5;
    const APPLY_STATUS_EMPLOYED_WAIT   = 6;
    const APPLY_STATUS_DELIVERY        = 7;

    const APPLY_STATUS_LIST = [
        self::APPLY_STATUS_DELIVERY        => '已投递',
        self::APPLY_STATUS_THROUGH_FIRST   => '通过初筛',
        self::APPLY_STATUS_SEND_INVITATION => '邀请面试',
        self::APPLY_STATUS_INVITATION      => '已面试',
        self::APPLY_STATUS_INAPPROPRIATE   => '不合适',
        self::APPLY_STATUS_EMPLOYED_WAIT   => '待应聘',
        //        self::APPLY_STATUS_HANDLE_WAIT     => '被查看',
    ];

    /**
     * 获取站外投递列表
     * @param $searchData
     * @return array
     * @throws \Exception
     */
    public static function getOffSiteApplyList($searchData, $needPageInfo = false, $needIdInfo = false)
    {
        $query = self::find()
            ->alias('o')
            ->where(['o.status' => self::STATUS_ACTIVE])
            ->leftJoin(['j' => BaseJob::tableName()], 'j.id = o.job_id')
            ->leftJoin(['an' => BaseAnnouncement::tableName()], 'j.announcement_id = an.id')
            ->leftJoin(['jar' => BaseJobApplyRecord::tableName()], 'o.id = jar.apply_site_id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = j.company_id')
            ->andWhere(['jar.delivery_type' => BaseJobApplyRecord::DELIVERY_TYPE_OUTSIDE]);

        $keyWordType = $searchData['keywordType'];
        //搜索内容
        if ($keyWordType == 1) {
            //职位id
            $query->andFilterWhere([
                'like',
                'o.job_id',
                $searchData['keyword'],
            ]);
        } elseif ($keyWordType == 2) {
            //职位名称
            $query->andFilterWhere([
                'like',
                'o.job_name',
                $searchData['keyword'],
            ]);
        } elseif ($keyWordType == 3) {
            //公告id
            $query->andFilterWhere([
                'like',
                'o.announcement_id',
                $searchData['keyword'],
            ]);
        } elseif ($keyWordType == 4) {
            //公告名称
            $query->andFilterWhere([
                'like',
                'o.title',
                $searchData['keyword'],
            ]);
        } elseif ($keyWordType == 5) {
            //单位id
            $query->andFilterWhere([
                'like',
                'c.id',
                $searchData['keyword'],
            ]);
        } elseif ($keyWordType == 6) {
            //单位名称
            $query->andFilterWhere([
                'like',
                'c.full_name',
                $searchData['keyword'],
            ]);
        }
        //投递方式
        $query->andFilterWhere(['jar.delivery_way' => $searchData['deliveryWay']]);
        //用户id
        $query->andFilterWhere(['o.member_id' => $searchData['memberId']]);
        //附件简历id
        $query->andFilterWhere(['o.resume_attachment_id' => $searchData['resumeAttachmentId']]);
        //状态查询
        $query->andFilterWhere(['o.apply_status' => $searchData['applyStatus']]);
        //投递方式查询
        $query->andFilterWhere(['o.source' => $searchData['source']]);
        // 投递端口
        $query->andFilterWhere(['jar.platform' => $searchData['platform']]);

        $count    = $query->count();
        $pageSize = $searchData['pageSize'] ?: \Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $searchData['page'], $pageSize);

        $query->select([
            'o.id',
            'o.title as announcementName',
            'o.link',
            'o.job_name as siteJobName',
            'o.apply_status as applyStatus',
            'o.apply_date as applyDate',
            'o.salary',
            'o.email',
            'o.content',
            'o.source',
            'o.job_id as jobId',
            'o.announcement_id as announcementId',
            'j.company_id as companyId',
            'c.full_name as companyName',
            'j.experience_type as experienceType',
            'j.education_type as educationType',
            'j.province_id as provinceId',
            'j.city_id as cityId',
            'j.name as jobName',
            'j.apply_type',
            'j.apply_address',
            'j.city_id as cityId',
            'c.nature as companyNature',
            'c.type as companyType',
            'o.resume_attachment_id as resumeAttachmentId',
            'jar.delivery_way',
            'an.apply_type as an_apply_type',
            'an.apply_address as an_apply_address',
            'jar.platform',
            'j.status as jobStatus',
            'j.amount',
        ]);

        $list = $query->offset($pages['offset'])
            ->orderBy('apply_date desc')
            ->limit($pages['limit'])
            ->asArray()
            ->all();

        foreach ($list as &$record) {
            //获取申请状态名称
            $record['applyStatusTxt']       = self::APPLY_STATUS_LIST[$record['applyStatus']];
            $record['educationText']        = BaseDictionary::getEducationName($record['educationType']);          //获取学历要求名称
            $record['experienceText']       = BaseDictionary::getExperienceName($record['experienceType']);        //获取经验要求名称
            $record['provinceText']         = BaseArea::getAreaName($record['provinceId']);                        //获取省
            $record['cityText']             = BaseArea::getAreaName($record['cityId']);                            //获取市
            $record['companyNatureText']    = BaseDictionary::getCompanyNatureName($record['companyNature']);      //获取单位性质
            $record['companyTypeText']      = BaseDictionary::getCompanyTypeName($record['companyType']);          //获取单位类型
            $record['resumeAttachmentName'] = BaseResumeAttachment::getNameById($record['resumeAttachmentId']);    //获取附件简历名称
            $record['sourceText']           = self::SOURCE_TYPE_TEXT[$record['source']];                           //获取投递端口名称
            $record['platformText']         = BaseJobApplyRecord::PLATFORM_LIST[$record['platform']];                           //获取投递来源名称
            $record['delivery_way_txt']     = BaseJobApplyRecord::DELIVERY_WAY_NAME[$record['delivery_way']];
            if (!empty($record['provinceText']) && !empty($record['cityText'])) {
                $record['area'] = $record['provinceText'] . '-' . $record['cityText'];
            } else {
                $record['area'] = '';
            }

            // 做一个兼容小程序的
            $record['areaName'] = $record['area'];

            if (!empty($record['jobId'])) {
                //如果存在职位id，说明是站内发布的职位
                if (empty($record['email'])) {
                    $record['applyTypeText'] = '网址';
                } else {
                    $record['applyTypeText'] = '邮件';
                }
                $record['url'] = Url::toRoute([
                    'job/detail',
                    'id' => $record['jobId'],
                ]);
                if ($needIdInfo) {
                    //名称后面拼接uid
                    $jobUid            = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $record['jobId']);
                    $record['jobName'] = $record['jobName'] . '(' . $jobUid . ')';
                }
                if ($record['apply_type'] && $record['apply_address']) {
                    $record['apply_type_name']    = baseJob::getApplyTypeName($record['apply_type']);
                    $record['apply_address_name'] = $record['apply_address'];
                } else {
                    if ($record['an_apply_type'] && $record['an_apply_address']) {
                        $record['apply_type_name']    = baseJob::getApplyTypeName($record['an_apply_type']);
                        $record['apply_address_name'] = $record['an_apply_address'];
                    }
                }
            } else {
                $record['applyTypeText']      = '自主投递';
                $record['url']                = $record['link'];
                $record['apply_type_name']    = '自主投递';
                $record['apply_address_name'] = $record['link'];
                $record['jobName']            = $record['siteJobName'];
            }

            if (!empty($record['announcementId'])) {
                $record['announcementUrl'] = Url::toRoute([
                    'announcement/detail',
                    'id' => $record['announcementId'],
                ]);
                if ($needIdInfo) {
                    $announcementUid            = UUIDHelper::encrypt(UUIDHelper::TYPE_ANNOUNCEMENT, $record['announcementId']);
                    $record['announcementName'] = $record['announcementName'] . '(' . $announcementUid . ')';
                }
            }

            if (!empty($record['companyId'])) {
                $record['companyUrl'] = Url::toRoute([
                    'company/detail',
                    'id' => $record['companyId'],
                ]);
                if ($needIdInfo) {
                    $companyUid            = UUIDHelper::encrypt(UUIDHelper::TYPE_COMPANY, $record['companyId']);
                    $record['companyName'] = $record['companyName'] . '(' . $companyUid . ')';
                }
            }
            unset($record['an_apply_type'], $record['an_apply_address'], $record['apply_type'], $record['apply_address']);

            /**
             * 已下线的职位，显示“停止招聘”，卡片置灰；
             *
             * 已删除的职位，显示“已删除”，卡片置灰；
             */
            if ($record['jobStatus'] == BaseJob::STATUS_OFFLINE) {
                $record['jobNotice'] = '停止招聘';
            }

            if ($record['jobStatus'] == BaseJob::STATUS_DELETE) {
                $record['jobNotice'] = '已删除';
            }
        }

        if ($needPageInfo) {
            $data = [
                'list' => $list,
                'page' => [
                    'limit' => (int)$pages['limit'],
                    'count' => (int)$count,
                    'page'  => (int)$pages['page'],
                ],
            ];

            return $data;
        } else {
            return $list;
        }
    }

    /**
     * 判断是否有记录
     * @param $memberId
     * @param $jobId
     * @return bool|int|string|null
     */
    // public static function checkJobApplyStatus($memberId, $jobId)
    // {
    //     //获取限制天数
    //     $limitTime = date('Y-m-d', strtotime('-' . self::LIMIT_APPLY_DAYS . 'day'));
    //
    //     $count = self::find()
    //         ->where([
    //             'member_id' => $memberId,
    //             'job_id'    => $jobId,
    //         ])
    //         ->andWhere([
    //             '>',
    //             'add_time',
    //             $limitTime,
    //         ])
    //         ->select('id')
    //         ->count();
    //     if (!empty($count)) {
    //         return BaseJob::JOB_APPLY_STATUS_YES;
    //     } else {
    //         return BaseJob::JOB_APPLY_STATUS_NO;
    //     }
    // }

    /**
     * 新增/编辑站外投递
     * @param $data
     * @param $memberId
     * @throws \Exception
     */
    public static function saveInfo($data, $memberId)
    {
        $resumeId = BaseMember::getMainId($memberId);

        if (!empty($data['id'])) {
            $model = self::findOne($data['id']);
            //如果存在数据的判断历史数据的状态是什么

            $add_bool = false;
        } else {
            $model            = new self();
            $model->member_id = $memberId;
            $model->resume_id = $resumeId;
            $add_bool         = true;
        }
        $model->title                = $data['title'] ?: '';
        $model->link                 = $data['link'] ?: '';
        $model->job_name             = $data['jobName'] ?: '';
        $model->apply_status         = $data['applyStatus'];
        $model->apply_date           = $data['applyDate'] ?: '';
        $model->salary               = $data['salary'] ?: '';
        $model->email                = $data['email'] ?: '';
        $model->content              = $data['content'] ?: '';
        $model->source               = $data['source'] ?: self::SOURCE_PERSONAL;
        $model->job_id               = $data['jobId'] ?: 0;
        $model->announcement_id      = $data['announcementId'] ?: 0;
        $model->resume_attachment_id = $data['resumeAttachmentId'] ?: 0;
        $model->stuff_file_id        = $data['stuffFileId'] ?: 0;

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        //更新面试邀请统计
        BaseResume::updateInterviewAmount($resumeId);

        $off_id = $model->attributes['id'];
        if ($add_bool) {
            $rule_id = 0;
            if ($data['jobId'] > 0) {
                $service        = new MatchCompleteService();
                $init           = [
                    'job_id'    => $data['jobId'],
                    'resume_id' => $resumeId,
                ];
                $service_result = $service->setRuleKey()
                    ->setProject(MatchCompleteService::PROJECT_TYPE_2)
                    ->setOparetion($data['operation_platform'])
                    ->init($init)
                    ->run();
                $rule_id        = $service_result['rule_id'] ?: 0;
            }
            //写投递记录总表
            $apply_model                  = new BaseJobApplyRecord();
            $apply_model->delivery_type   = BaseJobApplyRecord::DELIVERY_TYPE_OUTSIDE;
            $apply_model->delivery_way    = $data['delivery_way'];
            $apply_model->add_time        = CUR_DATETIME;
            $apply_model->update_time     = CUR_DATETIME;
            $apply_model->apply_site_id   = $off_id;
            $apply_model->company_id      = $data['company_id'] ?: 0;
            $apply_model->resume_id       = $resumeId;
            $apply_model->job_id          = $data['jobId'] ?: 0;
            $apply_model->announcement_id = $data['announcementId'] ?: 0;
            $apply_model->source          = $data['source'] == self::SOURCE_WEBSITE ? BaseJobApplyRecord::SOURCE_AUTO : BaseJobApplyRecord::SOURCE_WRITE;
            $apply_model->platform        = $data['platform'] ?: 0;
            $apply_model->match_complete  = $rule_id;
            if (!$apply_model->save()) {
                throw new Exception($apply_model->getFirstErrorsMessage());
            }
            // 更新邀约投递相关记录
            BaseJobApplyRecord::updateInvite($apply_model->id);
        }
        if (empty($data['id'])) {
            //如果是新增，增加统计数据
            BaseResume::offSiteApplyAdd($resumeId, $data['resumeAttachmentId']);
        }

        return $model->id;
    }

}