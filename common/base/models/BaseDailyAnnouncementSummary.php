<?php

namespace common\base\models;

use common\helpers\TimeHelper;
use common\libs\DailyContent;
use common\models\DailyAnnouncementSummary;

class BaseDailyAnnouncementSummary extends DailyAnnouncementSummary
{
    const STATUS_ONLINE       = 1;
    const STATUS_DELETE       = -1;
    const STATUS_WAIT_RELEASE = 9;

    const STATUS_LIST = [
        self::STATUS_DELETE       => '删除',
        self::STATUS_ONLINE       => '已发布',
        self::STATUS_WAIT_RELEASE => '未发布',
    ];

    public static function create($date = CUR_DATE)
    {
        // 首先是文章
        $article = new BaseArticle();

        $chineseDate               = TimeHelper::getChinese($date);
        $article->original         = '高校人才网';
        $article->author           = '小才';
        $article->title            = "高校人才网招聘信息日报（{$chineseDate}）";
        $article->content          = (new DailyContent($date))->create();
        $article->refresh_time     = CUR_DATETIME;
        $article->refresh_date     = CUR_DATE;
        $article->status       = BaseArticle::STATUS_STAGING;
        $article->type         = BaseArticle::TYPE_DAILY_ANNOUNCEMENT_SUMMARY;
        if (!$article->save()) {
            throw new \Exception($article->getFirstErrorsMessage());
        }

        $model              = new self();
        $model->article_id  = $article->id;
        $model->status      = self::STATUS_WAIT_RELEASE;
        $model->belong_date = $date;
        if (!$model->save()) {
            throw new \Exception($model->getFirstErrorsMessage());
        }
    }

}