<?php

namespace common\base\models;

use common\models\CompanyPackageSystemConfig;
use yii\base\Exception;

class BaseCompanyPackageSystemConfig extends CompanyPackageSystemConfig
{
    const STATUS_ACTIVE = 1; //企业套餐配置，状态在用
    const STATUS_DELETE = 2; //企业套餐配置，状态禁用

    const TYPE_SENIOR = 1; // 高级
    const TYPE_FREE   = 2; // 免费
    const TYPE_TRIAL  = 3; // 试用

    const PACKAGE_AMOUNT_SYSTEM              = 6; //免费单位默认权益备至包数--有效期/月
    const JOB_AMOUNT_SYSTEM                  = 0; //免费单位默认纯发布职位个数
    const ANNOUNCEMENT_AMOUNT_SYSTEM         = 0; //免费单位公告&简章默认发布条数
    const JOB_REFRESH_AMOUNT_SYSTEM          = 0; //免费单位纯职位刷新默认次数
    const ANNOUNCEMENT_REFRESH_AMOUNT_SYSTEM = 0; //免费单位公告&简章默认刷新次数
    const DOWNLOAD_AMOUNT_AMOUNT_SYSTEM      = 0; //免费单位简历下载点数

    //A套餐基础包
    const A_JOB_AMOUNT_SYSTEM                  = 20;
    const A_ANNOUNCEMENT_AMOUNT_SYSTEM         = 10;
    const A_JOB_REFRESH_AMOUNT_SYSTEM          = 50;
    const A_ANNOUNCEMENT_REFRESH_AMOUNT_SYSTEM = 0;
    const A_RESUME_DOWNLOAD_AMOUNT_SYSTEM      = 100;

    //B套餐基础包
    const B_JOB_AMOUNT_SYSTEM                  = 20;
    const B_ANNOUNCEMENT_AMOUNT_SYSTEM         = 40;
    const B_JOB_REFRESH_AMOUNT_SYSTEM          = 100;
    const B_ANNOUNCEMENT_REFRESH_AMOUNT_SYSTEM = 0;
    const B_RESUME_DOWNLOAD_AMOUNT_SYSTEM      = 100;

    const PACKAGE_ID_TEST = 3; //试用会员

    const PACKAGE_CONFIG_NAME = [
        'job_amount'                       => '职位发布个数（条）',
        'announcement_amount'              => '公告&简章发布数（条）',
        'job_refresh_amount'               => '职位刷新次数（次）',
        'announcement_refresh_amount'      => '公告&简章刷新次数（次）',
        'resume_download_amount'           => '简历下载（点）',
        'chat_amount'                      => '直聊沟通（点）',
        'sms_amount'                       => '短信条数（条）',
        'base_job_amount'                  => '基础包职位发布个数（条）',
        'base_announcement_amount'         => '基础包公告&简章发布数（条）',
        'base_job_refresh_amount'          => '基础包职位刷新次数（次）',
        'base_announcement_refresh_amount' => '基础包公告&简章刷新次数（次）',
        'base_resume_download_amount'      => '基础简历下载（点）',
        'base_chat_amount'                 => '基础直聊沟通（点）',
        'base_sms_amount'                  => '短信基础条数（条）',
    ];

    public static function getPackageType($packageId)
    {
        $config = BaseCompanyPackageSystemConfig::findOne(['id' => $packageId]);
        if (!$config) {
            $configType = 0;
        } else {
            $configType = $config->type;
        }
        switch ($configType) {
            case BaseCompanyPackageSystemConfig::TYPE_SENIOR:
                $packageType = BaseCompany::PACKAGE_TYPE_SENIOR;
                $handleType   = BaseCompanyPackageChangeLog::HANDLE_TYPE_CONFIGURE_SENIOR;
                break;
            case BaseCompanyPackageSystemConfig::TYPE_TRIAL:
                $handleType  = BaseCompanyPackageChangeLog::HANDLE_TYPE_CONFIGURE_TEST;
                $packageType = BaseCompany::PACKAGE_TYPE_TRIAL;
                break;
            default:
                $packageType = BaseCompany::PACKAGE_TYPE_FREE;
                $handleType  = BaseCompanyPackageChangeLog::HANDLE_TYPE_CONFIGURE_FREE;
        }

        return [
            'packageType' => $packageType,
            'handleType'  => $handleType,
        ];
    }
}
