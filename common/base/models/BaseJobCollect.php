<?php

namespace common\base\models;

use common\helpers\ArrayHelper;
use common\models\JobCollect;
use common\models\Major;
use yii\base\Exception;

class BaseJobCollect extends JobCollect
{
    //获取列表是否需要分页信息
    const NEED_PAGE_INFO_YES = true;
    const NEED_PAGE_INFO_NO  = false;

    //个人中心列表限制条数
    const PERSONAL_SHOW_LIST = 4;

    /**
     * 获取用户对该职位的收藏状态
     * @param $memberId
     * @param $jobId
     * @return int|mixed
     * @throws \Exception
     */
    public static function checkIsCollect($memberId, $jobId)
    {
        $info = self::find()
            ->where([
                'member_id' => $memberId,
                'job_id'    => $jobId,
            ])
            ->select('status')
            ->asArray()
            ->one();

        if ($info['status'] == self::STATUS_ACTIVE) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取职位收藏状态
     * @param $jobId
     * @param $memberId
     * @return int
     * @throws \Exception
     */
    public static function getCollectStatus($jobId, $memberId)
    {
        $isCollectStatus = BaseJob::JOB_COLLECT_STATUS_NO;
        if (!empty($memberId)) {
            $isCollect = self::checkIsCollect($memberId, $jobId);
            if ($isCollect) {
                $isCollectStatus = BaseJob::JOB_COLLECT_STATUS_YES;
            }
        }
        return $isCollectStatus;
    }

    /**
     * 新增/取消收藏记录
     * @param $jobId
     * @param $memberId
     */
    public static function saveInfo($jobArr, $memberId)
    {
        foreach ($jobArr as $k => $jobId) {
            $model = self::findOne([
                'job_id'    => $jobId,
                'member_id' => $memberId,
            ]);
            if (!empty($model)) {
                if ($model['status'] == self::STATUS_ACTIVE) {
                    $model->status = self::STATUS_DELETE;
                } elseif ($model['status'] == self::STATUS_DELETE) {
                    $model->status = self::STATUS_ACTIVE;
                }
            } else {
                $model = new self();

                $model->job_id      = $jobId;
                $model->member_id   = $memberId;
                $model->status      = self::STATUS_ACTIVE;
                $model->update_time = CUR_DATETIME;
            }

            if (!$model->save()) {
                throw new Exception($model->getFirstErrorsMessage());
            }
            //新增操作日志
            $data = [
                'content' => '新增职位收藏，jobCollectId：' . $model->id,
            ];
            // 写日志
            BaseMemberActionLog::log($data);
        }
    }

    /**
     * 获取指定职位ID的用户ID
     * @param $jobId
     * @return array
     */
    public static function collectMemberIds($jobId)
    {
        return self::find()
            ->select('member_id')
            ->where(['job_id' => $jobId])
            ->column();
    }

    /**
     * 获取收藏的数量
     * @param $memberId
     * @return bool|int|string|null
     */
    public static function getCollectAmount($memberId)
    {
        $amount = self::find()
            ->where([
                'status'    => self::STATUS_ACTIVE,
                'member_id' => $memberId,
            ])
            ->count();
        if ($amount > 99) {
            $amount = '99+';
        }

        return $amount;
    }

}