<?php

namespace common\base\models;

use common\models\ChatMessageCard;
use yii\db\Exception;

class BaseChatMessageCard extends ChatMessageCard
{
    //卡片类型
    const CARD_TYPE_JOB             = 1;//职位卡片
    const CARD_TYPE_RESUME          = 2;//在线简历卡片
    const CARD_TYPE_ATTACHMENT      = 3;//附件发送
    const CARD_TYPE_APPLY_INVITE    = 4;//投递邀约
    const CARD_TYPE_RESUME_COMPLETE = 5;//简历完善提醒

    const CARD_TYPE_LIST = [
        self::CARD_TYPE_JOB,
        self::CARD_TYPE_RESUME,
        self::CARD_TYPE_ATTACHMENT,
        self::CARD_TYPE_APPLY_INVITE,
        self::CARD_TYPE_RESUME_COMPLETE,
    ];

    //邀请投递卡片状态
    const INVITE_STATUS_IGNORE   = 1;
    const INVITE_STATUS_INTEREST = 2;

    //邀请投递卡片状态
    const INVITE_OPERATION_STATUS_IGNORE   = 1;
    const INVITE_OPERATION_STATUS_INTEREST = 2;
    const INVITE_OPERATION_STATUS_NO = 0;

}