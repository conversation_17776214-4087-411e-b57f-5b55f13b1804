<?php

namespace common\base\models;

use common\helpers\ArrayHelper;
use common\libs\Cache;
use common\models\Certificate;

class BaseCertificate extends Certificate
{
    const ADD_UNLIMITED_NO  = 0;
    const ADD_UNLIMITED_YES = 1;

    public static function setCache()
    {
        $list = self::find()
            ->where(['status' => self::STATUS_ACTIVE])
            ->select('id,name,parent_id,level')
            ->asArray()
            ->all();

        Cache::hMSet(Cache::ALL_TABLE_CERTIFICATE_KEY, array_column($list, null, 'id'));

        return true;
    }

    public static function getList($addUnlimited = self::ADD_UNLIMITED_NO)
    {
        $list = Cache::hvals(Cache::ALL_TABLE_CERTIFICATE_KEY);
        if (!$list) {
            self::setCache();
            $list = Cache::hvals(Cache::ALL_TABLE_CERTIFICATE_KEY);
        }
        $list = array_column($list, 'name', 'id');
        //        $list = self::find()
        //            ->where([
        //                'status' => self::STATUS_ACTIVE,
        //            ])
        //            ->select('name')
        //            ->asArray()
        //            ->orderBy('id asc')
        //            ->indexBy('id')
        //            ->column();

        if ($addUnlimited == self::ADD_UNLIMITED_YES) {
            array_unshift($list, '不限');
        }

        return $list;
    }

    public static function getDropDownList()
    {
        // 先拿出第一级
        $parentList = self::find()
            ->select([
                'id as k',
                'name as v',
            ])
            ->where([
                'parent_id' => 0,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->orderBy('sort')
            ->asArray()
            ->all();

        foreach ($parentList as &$item) {
            $item['children'] = self::find()
                ->select([
                    'k'           => 'id',
                    'v'           => 'name',
                    'topParentId' => 'parent_id',
                ])
                ->where([
                    'parent_id' => $item['k'],
                    'status'    => self::STATUS_ACTIVE,
                ])
                ->orderBy('sort')
                ->asArray()
                ->all();
        }

        return $parentList;
    }

    public static function getName($id)
    {
        $info = Cache::hGet(Cache::ALL_TABLE_CERTIFICATE_KEY, $id);
        if (!$info) {
            self::setCache();
            $info = Cache::hGet(Cache::ALL_TABLE_CERTIFICATE_KEY, $id);
        }
        $info = json_decode($info, true);

        return ArrayHelper::getValue($info, 'name');
    }
}