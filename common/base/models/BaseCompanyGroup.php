<?php
/**
 * create user：shannon
 * create time：2024/7/1 16:33
 */
namespace common\base\models;

use common\models\CompanyGroup;
use yii\base\Exception;
use Yii;

class BaseCompanyGroup extends CompanyGroup
{
    /** 是否删除 */
    const IS_DELETE_YES = 1;
    const IS_DELETE_NO  = 2;

    /** 写死合作与非合作默认的群组ID组 */
    const COOPERATIVE_GROUP_ID     = 15;//合作群组ID
    const COOPERATIVE_GROUP_NON_ID = 18;//非合作群组ID

    const OPERATION_TYPE_INCREASE = 1;//加
    const OPERATION_TYPE_REDUCE   = 2;//减

    /**
     * 分值计算
     */
    public static function scoreCalculate()
    {
        BaseCompanyGroup::updateAll(['weight' => 0], [
            'and',
            ['is_delete' => BaseCompanyGroup::IS_DELETE_YES],
            [
                '>',
                'weight',
                0,
            ],
        ]);
        //获取表中生效的所有数据
        $data           = self::find()
            ->select([
                'id',
                'sort_number',
            ])
            ->where(['is_delete' => self::IS_DELETE_NO])
            ->asArray()
            ->all();
        $sortNumberData = array_column($data, 'sort_number', 'id');
        //$sortNumberData正序排序
        asort($sortNumberData);
        //计算分值
        $sum      = array_sum(array_values($sortNumberData));
        $scores   = [];
        $iniScore = 1000000000000;
        foreach ($sortNumberData as $id => $sortNumber) {
            $weight = round($iniScore * (($sum - $sortNumber) / $sum), 2);
            if ($weight < 0.01) {
                //直接报错
                throw new Exception('已超出计算范围，请重新调整排序或者群组数量');
            }
            $scores[$id] = $weight;
            $iniScore    = $weight / 2;
        }
        //更新分值
        $transaction = Yii::$app->db->beginTransaction();
        try {
            foreach ($scores as $id => $score) {
                $model         = self::findOne($id);
                $model->weight = $score;
                if (!$model->save()) {
                    throw new Exception('权重分值更新失败,请联系系统管理员！');
                }
            }

            $transaction->commit();

            return true;
        } catch (Exception $e) {
            $transaction->rollBack();
            throw new Exception('权重分值更新失败,请联系系统管理员！');
        }
    }

    /**
     * 单位数量加+、减-1操作
     * @param int $type 1加 2减
     */
    public static function companyGroupNumber($groupIds, $type)
    {
        if (!in_array($type, [
            1,
            2,
        ])) {
            throw new Exception('该操作不被允许！');
        }
        if (is_string($groupIds)) {
            $groupIds = explode(',', $groupIds);
        }

        if ($type == 1) {
            $number = 1;
        } else {
            $number = -1;
        }
        foreach ($groupIds as $groupId) {
            $model = self::findOne($groupId);
            $model->updateCounters(['company_number' => $number]);
        }

        return true;
    }

    /**
     * 获取单位的群组信息
     */
    public static function getGroupName($groupIds)
    {
        if (is_string($groupIds)) {
            $groupIds = explode(',', $groupIds);
        }
        $data = self::find()
            ->select('name')
            ->where(['id' => $groupIds])
            ->asArray()
            ->column();

        return $data ? implode(',', $data) : '';
    }
}