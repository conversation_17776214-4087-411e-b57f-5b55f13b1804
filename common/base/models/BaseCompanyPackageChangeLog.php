<?php

namespace common\base\models;

use common\models\CompanyPackageChangeLog;
use yii\base\Exception;

class BaseCompanyPackageChangeLog extends CompanyPackageChangeLog
{
    const TYPE_JOB_RELEASE          = 1;  //权益变更类型，职位发布
    const TYPE_JOB_REFRESH          = 2;  //权益变更类型，职位刷新
    const TYPE_ANNOUNCEMENT_RELEASE = 3;  //权益变更类型，公告发布
    const TYPE_ANNOUNCEMENT_REFRESH = 4;  //权益变更类型，公告刷新
    const TYPE_SYSTEM_GIFT          = 5;  //权益变更类型，开通服务(系统赠送)
    const TYPE_SYSTEM_CONFIG        = 6;  //权益变更类型，系统配置
    const TYPE_ANNOUNCEMENT_REJECT  = 7;  //权益变更类型，公告审核拒绝
    const TYPE_JOB_REJECT           = 8;  //权益变更类型，职位审核拒绝
    const TYPE_RESUME_DOWN          = 9;  //权益变更类型，人才库下载
    const TYPE_SUB_ACCOUNT          = 10; //权益变更类型，子账号总量
    const TYPE_VIP_AUTH             = 11; //权益变更类型，VIP权限授权总量
    const TYPE_CHAT_ACCOUNT         = 12; //权益变更类型，直聊沟通
    const TYPE_SMS_SEND             = 13; //权益变更类型，短信发送

    const IDENTIFY_ADD    = 1;  //权益标识，增加
    const IDENTIFY_REDUCE = 2;  //权益标识，减少

    const HANDLER_TYPE_PLATFORM = 1; //操作人类型，平台
    const HANDLER_TYPE_PERSON   = 2; //操作人类型，个人

    const JOB_REFRESH_INTERVAL_DAY          = 7; //职位刷新间隔天数
    const ANNOUNCEMENT_REFRESH_INTERVAL_DAY = 7; //公告刷新间隔天数
    const ANNOUNCEMENT_RELEASE_INTERVAL_DAY = 30; //公告发布时间间隔
    const JOB_RELEASE_INTERVAL_DAY          = 7; //职位发布时间间隔

    const PACKAGE_UNIT_DAY = 31;

    const CONTENT_CONFIGURE_TITLE = "注册会员系统配置：高级会员";
    const CONTENT_FREE_TITLE      = "注册会员系统赠送：免费会员";
    const CONTENT_TRIAL_TITLE     = "注册会员系统赠送：试用会员";

    const PACKAGE_TYPE_CONTEMT_TITLE_MAP = [
        self::HANDLE_TYPE_CONFIGURE_SENIOR => self::CONTENT_CONFIGURE_TITLE,
        self::HANDLE_TYPE_CONFIGURE_FREE  => self::CONTENT_FREE_TITLE,
        self::HANDLE_TYPE_CONFIGURE_TEST  => self::CONTENT_TRIAL_TITLE,
    ];

    const TYPE_NAME = [
        self::TYPE_JOB_RELEASE          => '纯职位发布',
        self::TYPE_JOB_REFRESH          => '纯职位刷新',
        self::TYPE_ANNOUNCEMENT_RELEASE => '公告&简章发布',
        self::TYPE_ANNOUNCEMENT_REFRESH => '公告&简章刷新',
        self::TYPE_SYSTEM_GIFT          => '开通服务(系统赠送)',
        self::TYPE_SYSTEM_CONFIG        => '系统配置',
        self::TYPE_ANNOUNCEMENT_REJECT  => '公告&简章审核拒绝',
        self::TYPE_JOB_REJECT           => '纯职位审核拒绝',
        self::TYPE_RESUME_DOWN          => '简历下载',
        self::TYPE_SUB_ACCOUNT          => '子账号总量',
        self::TYPE_VIP_AUTH             => 'VIP权限授权总量',
        self::TYPE_CHAT_ACCOUNT         => '直聊沟通',
        self::TYPE_SMS_SEND             => '短信条数',

    ];

    //权益类型--服务项目
    const SEARCH_TYPE_NAME = [
        self::TYPE_JOB_RELEASE          => '纯职位发布',
        self::TYPE_JOB_REFRESH          => '纯职位刷新',
        self::TYPE_ANNOUNCEMENT_RELEASE => '公告&简章发布',
        self::TYPE_ANNOUNCEMENT_REFRESH => '公告&简章刷新',
        self::TYPE_RESUME_DOWN          => '简历下载',
        self::TYPE_SUB_ACCOUNT          => '子账号总量',
        self::TYPE_VIP_AUTH             => 'VIP权限授权总量',
        self::TYPE_CHAT_ACCOUNT         => '直聊沟通',
        self::TYPE_SMS_SEND             => '短信条数',

    ];

    const HANDLE_TYPE_JOB_RELEASE          = 1; //纯职位发布
    const HANDLE_TYPE_ANNOUNCEMENT_RELEASE = 2; //公告&简章发布
    const HANDLE_TYPE_JOB_REFUSE           = 3; //纯职位审核拒绝
    const HANDLE_TYPE_ANNOUNCEMENT_REFUSE  = 4; //公告&简章审核拒绝
    const HANDLE_TYPE_JOB_REFRESH          = 5; //刷新职位
    const HANDLE_TYPE_ANNOUNCEMENT_REFRESH = 6; //刷新公告
    const HANDLE_TYPE_RESUME_DOWNLOAD      = 7; //下载简历
    const HANDLE_TYPE_SMS_SEND             = 8; //发送短信
    const HANDLE_TYPE_SMS_SEND_FAIL        = 9; //发送短信失败
    const HANDLE_TYPE_EXPIRATION           = 10; //服务过期
    const HANDLE_TYPE_CONFIGURE_SENIOR     = 11; //注册会员系统配置：高级会员
    const HANDLE_TYPE_CONFIGURE_TEST       = 12; //注册会员系统配置：试用会员
    const HANDLE_TYPE_CONFIGURE_FREE       = 13; //注册会员系统赠送：免费会员
    const HANDLE_TYPE_CONFIGURE_SUB        = 14; //子账号配置

    const HANDLE_TYPE_SYSTEM_ADD_RESUME_DOWNLOAD    = 101; // 系统增加简历下载次数
    const HANDLE_TYPE_SYSTEM_REDUCE_RESUME_DOWNLOAD = 102; // 系统减少简历下载次数

    const HANDLE_TYPE_INITIATE_CHAT             = 103; // 发起直聊
    const HANDLE_TYPE_SYSTEM_CHANGE_RESUME_CHAT = 104; // 系统配置直聊点数
    const HANDLE_TYPE_SYSTEM_SET_SMS_AMOUNT     = 105; // 系统配置短信

    const HANDLER_TYPE_NAME = [
        self::HANDLE_TYPE_JOB_RELEASE                   => '纯职位发布',
        self::HANDLE_TYPE_ANNOUNCEMENT_RELEASE          => '公告&简章发布',
        self::HANDLE_TYPE_JOB_REFUSE                    => '纯职位审核拒绝',
        self::HANDLE_TYPE_ANNOUNCEMENT_REFUSE           => '公告&简章审核拒绝',
        self::HANDLE_TYPE_JOB_REFRESH                   => '刷新职位',
        self::HANDLE_TYPE_ANNOUNCEMENT_REFRESH          => '刷新公告',
        self::HANDLE_TYPE_RESUME_DOWNLOAD               => '下载简历',
        self::HANDLE_TYPE_EXPIRATION                    => '服务过期',
        self::HANDLE_TYPE_CONFIGURE_SENIOR              => '注册会员系统配置：高级会员',
        self::HANDLE_TYPE_CONFIGURE_TEST                => '注册会员系统配置：试用会员',
        self::HANDLE_TYPE_CONFIGURE_FREE                => '注册会员系统赠送：免费会员',
        self::HANDLE_TYPE_SYSTEM_ADD_RESUME_DOWNLOAD    => '系统增加简历下载次数',
        self::HANDLE_TYPE_SYSTEM_REDUCE_RESUME_DOWNLOAD => '系统减少简历下载次数',
        self::HANDLE_TYPE_CONFIGURE_SUB                 => '子账号配置',
        self::HANDLE_TYPE_INITIATE_CHAT                 => '发起直聊',
        self::HANDLE_TYPE_SYSTEM_CHANGE_RESUME_CHAT     => '系统配置直聊点数',
        self::HANDLE_TYPE_SYSTEM_SET_SMS_AMOUNT         => '系统配置短信',
        self::HANDLE_TYPE_SMS_SEND                      => '发送短信',
    ];

    //权益类型--服务项目字段呼应
    const RELEVANT_TYPE_LIST = [
        [
            'name'  => '纯职位发布（条）',
            'field' => 'job_amount',
            'type'  => self::TYPE_JOB_RELEASE,
        ],
        [
            'name'  => '纯职位刷新（条）',
            'field' => 'job_refresh_amount',
            'type'  => self::TYPE_JOB_REFRESH,
        ],
        [
            'name'  => '公告&简章发布（条）',
            'field' => 'announcement_amount',
            'type'  => self::TYPE_ANNOUNCEMENT_RELEASE,
        ],
        [
            'name'  => '公告&简章刷新（条）',
            'field' => 'announcement_refresh_amount',
            'type'  => self::TYPE_ANNOUNCEMENT_REFRESH,
        ],
        [
            'name'  => '简历下载（点）',
            'field' => 'resume_download_amount',
            'type'  => self::TYPE_RESUME_DOWN,
        ],
        [
            'name'  => '直聊沟通（点）',
            'field' => 'chat_amount',
            'type'  => self::TYPE_CHAT_ACCOUNT,
        ],
        [
            'name'  => '短信数量（条）',
            'field' => 'sms_amount',
            'type'  => self::TYPE_SMS_SEND,
        ],
    ];

    //系统操作层
    const PLATFORM_HANDLE_TYPE = [
        self::HANDLE_TYPE_EXPIRATION,
        self::HANDLE_TYPE_CONFIGURE_SENIOR,
        self::HANDLE_TYPE_CONFIGURE_TEST,
        self::HANDLE_TYPE_CONFIGURE_FREE,
        self::HANDLE_TYPE_SYSTEM_ADD_RESUME_DOWNLOAD,
        self::HANDLE_TYPE_SYSTEM_REDUCE_RESUME_DOWNLOAD,
        self::HANDLE_TYPE_CONFIGURE_SUB,
        self::HANDLE_TYPE_JOB_REFUSE,
        self::HANDLE_TYPE_ANNOUNCEMENT_REFUSE,
        self::HANDLE_TYPE_SYSTEM_CHANGE_RESUME_CHAT,
        self::HANDLE_TYPE_SYSTEM_SET_SMS_AMOUNT,
    ];

    // 不需要在单位端显示的权益类型备注
    const HIDE_IN_COMPANY_REMARK_TYPE = [
        self::TYPE_CHAT_ACCOUNT,
    ];

    /**
     * 创建一条套餐变更记录
     * @param $data
     * @throws Exception
     */
    public static function createCompanyPackageChangeLog($data)
    {
        $model                  = new self();
        $model->type            = $data['type'] ?: '';
        $model->identify        = $data['identify'] ?: '';
        $model->change_amount   = $data['change_amount'] ?: '';
        $model->surplus         = $data['surplus'] ?: '';
        $model->package_surplus = $data['package_surplus'] ?: '';
        $model->member_id       = $data['member_id'] ?: '';
        $model->member_name     = $data['member_name'] ?: '';
        $model->company_id      = $data['company_id'] ?: '';
        $model->company_name    = $data['company_name'] ?: '';
        $model->handle_before   = $data['handle_before'];
        $model->handle_after    = $data['handle_after'];
        $model->handler_type    = $data['handler_type'] ?: '';
        $model->handler         = $data['handler'] ?: '';
        $model->handler_id      = $data['handler_id'] ?: '';
        $model->content         = $data['content'] ?: '';
        $model->remark          = $data['remark'] ?: '';
        $model->handle_type     = $data['handle_type'] ?: 0;

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }

        return $model->id;
    }
}