<?php

namespace common\base\models;

use common\models\Topic;

class BaseTopic extends Topic
{
    const STATUS_WAIT         = 0; //待处理
    const STATUS_ONLINE       = 1; //审核通过
    const STATUS_DRAFT        = 3; //编辑中
    const STATUS_WAIT_AUDIT   = 7; //待审核
    const STATUS_REFUSE_AUDIT = -1; //审核拒绝
    const STATUS_DELETE       = 9; //已删除

    // 审核状态列表
    const STATUS_NAME = [
        self::STATUS_WAIT         => '待处理',
        self::STATUS_ONLINE       => '审核通过',
        self::STATUS_DRAFT        => '编辑中',
        self::STATUS_WAIT_AUDIT   => '等待审核',
        self::STATUS_REFUSE_AUDIT => '审核拒绝',
    ];

    /**
     * 获取话题详情
     */
    public static function getTopicDetail($Id)
    {
        $select = [
            'id',
            'add_time',
            'status',
            'title',
            'sub_title',
            'target_url',
            'cover_url',
            'news_list_json',
        ];

        $where = ['id' => $Id];

        return BaseTopic::find()
            ->select($select)
            ->where($where)
            ->asArray()
            ->one();
    }
}