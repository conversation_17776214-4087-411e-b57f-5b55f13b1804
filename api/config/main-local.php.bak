<?php

$config = [
    'as cors'    => [
        'class' => \yii\filters\Cors::className(),
        'cors'  => [
            // restrict access to
            'Origin'                           => [
                'http://127.0.0.1:5501',
                'http://127.0.0.1:5500',
            ],
            'Access-Control-Request-Method'    => [
                'GET',
                'POST',
                'PUT',
                'PATCH',
                'DELETE',
                'HEAD',
                'OPTIONS',
            ],
            'Access-Control-Request-Headers'   => ['*'],
            'Access-Control-Allow-Credentials' => true,
            'Access-Control-Max-Age'           => 86400,
            'Access-Control-Expose-Headers'    => [],

        ],
    ],
    'aliases'    => [
        '@bower' => '@vendor/bower-asset',
        '@npm'   => '@vendor/npm-asset',
    ],
    'components' => [

        'view'    => [
            'class'            => 'yii\web\View',
            'defaultExtension' => "html",

        ],
        'request' => [
            // !!! insert a secret key in the following (if it is empty) - this is required by cookie validation
            'cookieValidationKey' => '4tCyb81HdgF12CczncTIzrxNfT0ecglI2m',
        ],
    ],
];

if (!YII_ENV_TEST) {
    // configuration adjustments for 'dev' environment
    $config['bootstrap'][]      = 'debug';
    $config['modules']['debug'] = [
        'class' => 'yii\debug\Module',
    ];

    $config['bootstrap'][]    = 'gii';
    $config['modules']['gii'] = [
        'class' => 'yii\gii\Module',
    ];
}

return $config;
