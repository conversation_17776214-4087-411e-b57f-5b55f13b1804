<?php

namespace h5\controllers;

use common\base\models\BaseAnnouncementCollect;
use common\base\models\BaseCompanyCollect;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobCollect;
use common\base\models\BaseNewsCollect;
use common\base\models\BaseOffSiteJobApply;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEquityPackage;
use common\base\models\BaseResumeEquityPackageCategorySetting;
use common\helpers\ArrayHelper;
use Yii;

class PersonController extends BaseH5Controller
{
    /**
     * 站内投递列表页面
     * @return string
     */
    public function actionOnSiteApplyList()
    {
        $searchData             = [];
        $searchData['memberId'] = Yii::$app->user->id;
        $resumeId               = $this->getResumeId();
        $searchData['status']   = Yii::$app->request->get('status');
        $data                   = BaseJobApply::getApplyList($searchData, BaseJobApply::NEED_PAGE_INFO_YES);
        //获取站内投递状态列表
        $onSiteStatusList = BaseJobApply::PERSON_STATUS_LIST;
        //获取站外投递状态列表
        $offSiteStatusList = BaseOffSiteJobApply::APPLY_STATUS_LIST;
        $isEquityPackage   = BaseResumeEquityPackage::isPackageEffect(BaseResumeEquityPackageCategorySetting::ID_DIAMOND_VIP,
                $resumeId) || BaseResumeEquityPackage::isPackageEffect(BaseResumeEquityPackageCategorySetting::ID_JOB_FAST,
                $resumeId);

        return $this->render('job-apply.html', [
            'list'              => $data['list'],
            'onSiteStatusList'  => $onSiteStatusList,
            'offSiteStatusList' => $offSiteStatusList,
            'status'            => $searchData['status'],
            'isEquityPackage'   => $isEquityPackage,
        ]);
    }

    /**
     * 获取站内投递列表数据
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetOnSiteApplyList()
    {
        $searchData             = Yii::$app->request->get();
        $searchData['memberId'] = Yii::$app->user->id;

        $list = BaseJobApply::getApplyList($searchData, BaseJobApply::NEED_PAGE_INFO_YES);

        return $this->success($list['list']);
    }

    /**
     * 获取站外投递列表
     * @return string
     * @throws \Exception
     */
    public function actionOffSiteApplyList()
    {
        $memberId = Yii::$app->user->id;

        $list = BaseOffSiteJobApply::getOffSiteApplyList(['memberId' => $memberId], BaseJobApply::NEED_PAGE_INFO_YES);

        //获取站外投递状态列表
        $offSiteStatusList = BaseOffSiteJobApply::APPLY_STATUS_LIST;

        return $this->render('job-apply.html', [
            'list'              => $list,
            'offSiteStatusList' => $offSiteStatusList,
        ]);
    }

    /**
     * 站外投递数据获取接口
     * @throws \Exception
     */
    public function actionGetOffSiteApplyList()
    {
        $searchData             = Yii::$app->request->get();
        $searchData['memberId'] = Yii::$app->user->id;

        $data = BaseOffSiteJobApply::getOffSiteApplyList($searchData, BaseJobApply::NEED_PAGE_INFO_YES);

        return $this->success($data['list']);
    }

    /**
     * 我的收藏页面
     * @return string
     */
    public function actionCollection()
    {
        $memberId = Yii::$app->user->id;
        //获取默认首页数据

        $server = new \common\service\person\CollectService();
        $list   = $server->getH5JobList([
            'memberId' => $memberId,
        ])['list'];
        //获取收藏的数量列表
        $collectList = [
            'jobAmount'          => BaseJobCollect::getCollectAmount($memberId),
            'announcementAmount' => BaseAnnouncementCollect::getCollectAmount($memberId),
            'companyAmount'      => BaseCompanyCollect::getCollectAmount($memberId),
            'newsAmount'         => BaseNewsCollect::getCollectAmount($memberId),
        ];

        return $this->render('collection.html', [
            'list'        => $list,
            'collectList' => $collectList,
            'isVip'       => BaseResume::checkVip($memberId),
        ]);
    }

    /**
     * 获取收藏数量列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetCollectAmountList()
    {
        $memberId    = Yii::$app->user->id;
        $collectList = [
            'jobAmount'          => BaseJobCollect::getCollectAmount($memberId),
            'announcementAmount' => BaseAnnouncementCollect::getCollectAmount($memberId),
            'companyAmount'      => BaseCompanyCollect::getCollectAmount($memberId),
            'newsAmount'         => BaseNewsCollect::getCollectAmount($memberId),
        ];

        return $this->success($collectList);
    }
}