var service = axios.create({
    withCredentials: true, // 跨域支持发送cookie
    timeout: 8000 // 请求超时时间
})

function handleRedirect() {
    const url = window.location.href
    window.location.href = '/home/<USER>' + encodeURIComponent(url)
}

function httpGet(url, params) {
    return new Promise(function (resolve, reject) {
        service
            .get(url, {
                params: params
            })
            .then(function (response) {
                const res = response.data;

                if (res.result == 1) {
                    // 正常
                    if (res.msg != '') {
                        $.toast(res.msg, 'text');
                    }
                    resolve(res.data)
                } else {
                    if (res.code == 403) {
                        Session.clear(); // 清除浏览器全部临时缓存
                        handleRedirect()
                        return;
                    }
                    // 异常
                    if (res.msg != '') {
                        $.toast(res.msg, 'text');
                    }

                    if (res.data) {
                        reject(res.data);
                    } else {

                        reject(res.msg);
                    }
                }

            })
            .catch(function (error) {
                if (error.message.indexOf('timeout') != -1) {
                    $.toast('网络超时', 'text');
                } else if (error.message == 'Network Error') {
                    $.toast('网络连接错误', 'text');
                } else {
                    if (error.response.data) $.toast(error.response.statusText, 'text');
                    else $.toast('接口路径找不到', 'text');
                }
                reject(error);
            })
    })
}

function httpPost(url, params) {
    return new Promise(function (resolve, reject) {
        $.showLoading();
        service({
            headers: {},
            transformRequest: [
                function (data) {
                    if (data instanceof FormData) {
                        return data
                    }
                    // 在请求之前对data传参进行格式转换
                    var formData
                    if (data) {
                        formData = Qs.stringify(data);
                    }
                    return formData
                }
            ],
            url: url,
            method: 'post',
            data: params
        })
            .then(function (response) {
                const res = response.data;
                $.hideLoading();
                if (res.result == 1) {
                    // 正常
                    if (res.msg != '') {
                        $.toast(res.msg, 'text');
                    }
                    resolve(res.data)

                } else {
                    if (res.code == 403) {
                        handleRedirect()
                        return;
                    }
                    // 异常
                    if (res.msg != '') {
                        $.toast(res.msg, 'text');
                    }

                    if (res.data) {
                        reject(res.data);
                    } else {

                        reject(res.msg);
                    }
                }

            })
            .catch(function (error) {
                $.hideLoading();
                if (error.message.indexOf('timeout') != -1) {
                    $.toast('网络超时', 'text');
                } else if (error.message == 'Network Error') {
                    $.toast('网络连接错误', 'text');
                } else {
                    if (error.response.data) {
                        $.toast(error.response.statusText, 'text');
                    } else {
                        $.toast('接口路径找不到', 'text')
                    }
                }
                reject(error);
            })
    })
}
