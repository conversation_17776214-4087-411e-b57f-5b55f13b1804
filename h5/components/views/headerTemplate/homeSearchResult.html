<a href="<?= yii\helpers\Url::toRoute('/home/<USER>');?>" class="to-back"></a>

<form id="searchForm" class="primary-search header-search" action="<?= yii\helpers\Url::toRoute('/home/<USER>');?>">
    <input type="search" name="keyword" placeholder="请输入关键词" value="<?= Yii::$app->request->get('keyword')?>">
</form>

<div class="header-search-tab">
    <input id="searchFormType1" form="searchForm" type="radio" name="type" value="1"
           onchange="this.form.submit()" <?php if(Yii::$app->request->get('type') != 2):?>checked<?php endif?>>
    <label for="searchFormType1">搜公告</label>

    <input id="searchFormType2" form="searchForm" type="radio" name="type" value="2"
           onchange="this.form.submit()" <?php if(Yii::$app->request->get('type') == 2):?>checked<?php endif?>>
    <label for="searchFormType2">搜资讯</label>
</div>

