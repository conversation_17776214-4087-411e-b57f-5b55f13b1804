<?php
/**
 * create user：shannon
 * create time：2024/12/18 上午9:04
 */
namespace h5\components;

use common\base\models\BaseResume;
use common\base\models\BaseResumeComplete;
use yii\base\Widget;
use Yii;

class HomeNavWidget extends Widget
{
    public function run()
    {
        //是否登录
        $isLogin = Yii::$app->user->isGuest ? 0 : 1;

        //登录获取简历完善步骤
        $stepNum = 0;
        if ($isLogin) {
            $stepNum = BaseResumeComplete::getResumeStep(BaseResume::findOne(['member_id' => Yii::$app->user->id])->id);
        }

        return $this->render('homeNav.html', [
            'isLogin' => $isLogin,
            'stepNum' => $stepNum,
        ]);
    }
}