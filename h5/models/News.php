<?php

namespace h5\models;

use admin\models\ArticleColumn;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseArticleColumn;
use common\base\models\BaseJob;
use common\base\models\BaseNews;
use common\helpers\FileHelper;
use common\helpers\TimeHelper;
use Yii;
use yii\helpers\Url;

class News extends BaseNews
{

    public static function getSearchList($searchData)
    {
        $query = Article::find()
            ->alias('a')
            ->innerJoin(['c' => News::tableName()], 'a.id=c.article_id')
            ->where([
                'a.is_delete' => BaseArticle::IS_DELETE_NO,
                'a.status'    => BaseArticle::STATUS_ONLINE,
                'a.is_show'   => BaseArticle::IS_SHOW_YES,
            ])
            ->andWhere([
                'a.type' => [
                    BaseArticle::TYPE_NEWS,
                ],
            ]);

        //关键词查询
        $query->andFilterWhere([
            'like',
            'a.title',
            $searchData['keyword'],
        ]);
        $count = $query->count();

        $query->select([
            'a.title',
            'c.id',
            //            'ar.release_time as releaseTime',
            'a.click as clickAmount',
            'a.refresh_time',
            'a.status',
        ]);
        $pageSize = $searchData['pageSize'] ?: \Yii::$app->params['defaultPageSize'];

        $pages = self::setPage($count, $searchData['page'], $pageSize);

        $sort = 'a.refresh_time desc';

        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($sort)
            ->asArray()
            ->all();

        foreach ($list as $k => &$news) {
            $news['url']         = self::getDetailUrl($news['id']);
            $news['refreshTime'] = TimeHelper::formatDateByYear($news['refresh_time']);
        }

        return [
            'list'        => $list,
            'pageSize'    => $pageSize,
            'currentPage' => $pages['page'],
            'totalNum'    => $count,

        ];
    }

    /**
     * 获取页面的栏目列表
     * @param $page
     * @return array|mixed
     */
    public static function getAllColumnContent($page)
    {
        $config = \Yii::$app->params['homePosition']["$page"]['columnContent'];
        $list   = [];
        foreach ($config as $k => $item) {
            // 首先找到相对于的positionId
            $search['columnId'] = $item['id'];
            if ($item['type'] == 'announcement') {
                $search['limit'] = $item['count'];
                $data            = Announcement::getColumnAnnouncementList($search);
                $list[$k]        = $data;
            } elseif ($item['type'] == 'news') {
                $search['pageSize'] = $item['count'];
                $data               = News::getList($search);
                $list[$k]           = $data['list'];
            }
        }

        return $list;
    }

    //深度观察，调用系列、热图、热文属性的资讯，按时间倒序排，再按系列、热图、热文顺序排，取出27条数据（暂时展示9条，然后跳走）
    public static function getDeepHomeList($page)
    {
        $defaultPage = Yii::$app->params['defaultPageSize'];
        $offset      = ($page - 1) * $defaultPage;

        $list = Article::find()
            ->alias('a')
            ->select('title,a.type,c.id,refresh_time')
            ->innerJoin(['b' => ArticleAttribute::tableName()], 'a.id=b.article_id')
            ->innerJoin(['c' => News::tableName()], 'a.id=c.article_id')
            ->where([
                'a.is_delete' => Article::IS_DELETE_NO,
                'a.status'    => Article::STATUS_ONLINE,
                'a.is_show'   => Article::IS_SHOW_YES,
            ])
            ->andWhere([
                'a.type' => [
                    Article::TYPE_NEWS,
                ],
            ])
            ->andWhere([
                'b.type' => [
                    BaseArticleAttribute::ATTRIBUTE_SERIES,
                    BaseArticleAttribute::ATTRIBUTE_HOT_IMAGE,
                    BaseArticleAttribute::ATTRIBUTE_HOT_ARTICLE,
                ],
            ])
            ->orderBy('b.sort_time desc')
            ->limit($defaultPage)
            ->offset($offset)
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['url']         = News::getDetailUrl($item['id']);
            $item['refreshTime'] = TimeHelper::short($item['refresh_time']);
        }

        return $list;
    }

    public static function getStrategyList($page, $columId)
    {
        //求职攻略，取出求职攻略栏目的资讯，按各栏目资讯的发布时间倒序，展示最新发布的27条资讯信息（暂时展示9条，然后跳走）
        $defaultPage = Yii::$app->params['defaultPageSize'];
        $offset      = ($page - 1) * $defaultPage;

        $list = Article::find()
            ->alias('a')
            ->select('title,a.type,c.id,refresh_time')
            ->innerJoin(['b' => ArticleAttribute::tableName()], 'a.id=b.article_id')
            ->innerJoin(['c' => News::tableName()], 'a.id=c.article_id')
            ->innerJoin(['d' => ArticleColumn::tableName()], 'a.id=d.article_id')
            ->where([
                'a.is_delete' => Article::IS_DELETE_NO,
                'a.status'    => Article::STATUS_ONLINE,
                'a.is_show'   => Article::IS_SHOW_YES,
            ])
            ->andWhere([
                'a.type' => [
                    Article::TYPE_NEWS,
                ],
            ])
            ->andWhere([
                'd.column_id' => $columId,
            ])
            ->orderBy('b.sort_time desc')
            ->limit($defaultPage)
            ->offset($offset)
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['url']         = News::getDetailUrl($item['id']);
            $item['refreshTime'] = TimeHelper::short($item['refresh_time']);
        }

        return $list;
    }

    /**
     * 获取栏目/首页下面的最新公告 (逻辑是差不多的,每一页9个,然后按照公告的刷新时间来排序),有一个200条的限制
     * @param $page
     * @param $columnId
     */
    public static function getNewestList($page, $columnId = 0)
    {
        if ($page > 22) {
            return [];
        }
        $defaultSize = 9;
        $offset      = ($page - 1) * $defaultSize;
        // 这里其实只需要公告的ID和标题
        $query = Article::find()
            ->alias('a')
            ->innerJoin(['b' => self::tableName()], 'b.article_id = a.id')
            ->where([
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.status'    => Article::STATUS_ONLINE,
                'a.is_delete' => Article::IS_DELETE_NO,
                'a.type'      => Article::TYPE_NEWS,
            ]);

        if ($columnId) {
            $query->innerJoin(['c' => BaseArticleColumn::tableName()], 'c.article_id = a.id')
                ->andWhere(['c.column_id' => $columnId]);
        }
        $list = $query->select('b.id,a.title,a.refresh_time,a.click as clickAmount,cover_thumb as coverThumb')
            ->orderBy('a.refresh_time desc')
            ->limit($defaultSize)
            ->offset($offset)
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['url']          = self::getDetailUrl($item['id']);
            $item['refresh_time'] = substr($item['refresh_time'], 0, 10);
        }

        return $list;
    }

    /**
     * 拼接资讯热门搜索数据
     * @return array
     */
    public static function getHotKeywordList()
    {
        $hotList = \Yii::$app->params['hotSearchList']['news'];
        $list    = [];
        foreach ($hotList as $k => $item) {
            $list[$k]['name'] = $item;
            $list[$k]['url']  = Url::toRoute([
                'home/search-result',
                'keyword' => $item,
                'type'    => 2,
            ]);
        }

        return $list;
    }
}