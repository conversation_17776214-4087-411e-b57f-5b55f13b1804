<?php

namespace h5\models;

use common\base\models\BaseArticle;
use common\base\models\BaseDailyAnnouncementSummary;
use common\helpers\FileHelper;
use common\helpers\TimeHelper;
use yii\helpers\Url;

class DailyAnnouncementSummary extends BaseDailyAnnouncementSummary
{

    public static function getHomeList()
    {
        $query = self::find()
            ->alias('d')
            ->innerJoin(['a' => BaseArticle::tableName()], 'a.id=d.article_id')
            ->select([
                'd.id',
                'd.add_time as releaseTime',
                'd.belong_date as belongDate',
                'a.title',
                'a.click as clickAmount',
            ])
            ->where([
                'd.status' => self::STATUS_ACTIVE,
            ]);

        $list = $query->limit(9)
            ->orderBy('d.belong_date desc')
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['url'] = Url::toRoute([
                'home/daily-summary-detail',
                'id' => $item['id'],
            ]);
        }

        return $list;
    }

    /**
     * 获取每日汇总列表
     * @param $params
     * @return array
     */
    public static function getList($params)
    {
        $query = self::find()
            ->alias('d')
            ->innerJoin(['a' => BaseArticle::tableName()], 'a.id=d.article_id')
            ->select([
                'd.id',
                'd.add_time as releaseTime',
                'd.belong_date as belongDate',
                'a.title',
                'a.click as clickAmount',
            ])
            ->where([
                'd.status' => self::STATUS_ACTIVE,
            ]);

        $query->andFilterWhere([
            'd.status' => $params['status'],
        ]);

        $pageSize = $params['pageSize'] ?: \Yii::$app->params['defaultPageSize'];
        $count    = $query->count();
        $pages    = self::setPage($count, $params['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('d.add_time desc')
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['url'] = Url::toRoute([
                'home/daily-summary-detail',
                'id' => $item['id'],
            ]);
        }

        return [
            'list'  => $list,
            'pages' => [
                'limit' => $pages['limit'],
                'count' => (int)$count,
                'page'  => $params['page'],
            ],
        ];
    }

    /**
     * 获取每日汇总内容
     * @param $id
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function getDetail($id)
    {
        return self::find()
            ->alias('d')
            ->leftJoin(['a' => Article::tableName()], 'a.id=d.article_id')
            ->where(['d.id' => $id])
            ->select([
                'd.belong_date',
                'd.article_id',
                'd.add_time',
                'a.click',
                'a.refresh_time',
                'a.title',
                'a.content',
            ])
            ->asArray()
            ->one();
    }

    // 前一篇
    public static function getPrev($id)
    {
        $date = self::find()
            ->select('belong_date')
            ->where(['id' => $id])
            ->scalar();

        $query = self::find()
            ->alias('d')
            ->leftJoin(['a' => Article::tableName()], 'a.id=d.article_id')
            ->where([
                'd.status' => self::STATUS_ACTIVE,
            ])
            ->andWhere([
                '<',
                'd.belong_date',
                $date,
            ])
            ->select([
                'd.id',
                'd.belong_date',
                'a.title',
            ])
            ->orderBy('d.id desc')
            ->limit(1);

        $data = $query->asArray()
            ->one();

        if (!$data) {
            return [];
        }

        $data['url'] = Url::toRoute([
            'home/daily-summary-detail',
            'id' => $data['id'],
        ]);

        return $data;
    }

    public static function getNext($id)
    {
        $date = self::find()
            ->select('belong_date')
            ->where(['id' => $id])
            ->scalar();

        $query = self::find()
            ->alias('d')
            ->leftJoin(['a' => Article::tableName()], 'a.id=d.article_id')
            ->where([
                'd.status' => self::STATUS_ACTIVE,
            ])
            ->andWhere([
                '>',
                'd.belong_date',
                $date,
            ])
            ->select([
                'd.id',
                'd.belong_date',
                'a.title',
            ])
            ->orderBy('d.id asc')
            ->limit(1);

        $data = $query->asArray()
            ->one();

        if (!$data) {
            return [];
        }

        $data['url'] = Url::toRoute([
            'home/daily-summary-detail',
            'id' => $data['id'],
        ]);

        return $data;
    }

    /**
     * http://zentao.jugaocai.com/index.php?m=story&f=view&storyID=45&version=0&param=0&storyType=story&t=html&m=story&f=view&storyID=45&version=0&param=0&storyType=story&tid=cu5kqkrw
     * 推荐规则：调用最新发布的、属性勾选“推荐”的3条资讯信息；按发布时间倒序排列；
     */
    public static function getRecommend()
    {
        $list = Article::find()
            ->alias('a')
            ->select('title,a.type,a.id,cover_thumb,click,refresh_time')
            ->innerJoin(['b' => ArticleAttribute::tableName()], 'a.id=b.article_id')
            ->where([
                'a.is_delete' => Article::IS_DELETE_NO,
                'b.type'      => ArticleAttribute::ATTRIBUTE_RECOMMEND,
                'status'      => Article::STATUS_ONLINE,
                'a.is_show'   => Article::IS_SHOW_YES,
            ])
            ->andWhere([
                'a.type' => [
                    Article::TYPE_NEWS,
                ],
            ])
            ->orderBy('a.refresh_time desc')
            ->limit(3)
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['url']        = Article::getDetailUrl($item['id'], $item['type']);
            $item['time']       = date('m-d', strtotime($item['refresh_time']));
            $item['coverThumb'] = FileHelper::getFullUrl($item['cover_thumb']) ?: '/static/images/news/news.png';
        }

        return $list;
    }

}