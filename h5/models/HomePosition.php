<?php

namespace h5\models;

use common\base\models\BaseArticle;
use common\helpers\TimeHelper;
use Yii;

use common\base\models\BaseHomePosition;

class HomePosition extends BaseHomePosition
{
    /**
     * 这里比较大,暂时用一个方法来处理,未来需要迁移
     * @return array
     *
     */
    public static function getAllShowcase($page)
    {
        $config = Yii::$app->params['homePosition']["$page"]['showcase'];
        $list   = [];
        foreach ($config as $k => $item) {
            $limit = $item['count'];

            if ($item['needPaging']) {
                //如果是需要分页处理的，获取第一页的数据
                $list[$k] = self::getShowCaseList($k, $page);
            } else {
                //如果是不需要分页处理的，获取全部数据
                // 首先找到相对于的positionId
                $positionId = self::findOneVal([
                    'number'        => $k,
                    'status'        => self::STATUS_ACTIVE,
                    'platform_type' => self::PLATFORM_H5,
                ], 'id');

                $data     = ShowCase::getByPositionConfig($positionId, $k, $limit);
                $list[$k] = $data;
            }
        }

        return $list;
    }

    /**
     * 获取某个广告位的列表数据
     * @param $type     模块名称
     * @param $page     页码
     * @param $viewPage 显示页面
     */
    public static function getShowCaseList($type, $viewPage, $page = '')
    {
        $positionId = self::findOneVal([
            'number'        => $type,
            'status'        => self::STATUS_ACTIVE,
            'platform_type' => self::PLATFORM_H5,
        ], 'id');
        $searchData = [
            'page'       => $page ?: 1,
            'positionId' => $positionId,
            'key'        => $type,
            'viewPage'   => $viewPage,
        ];

        return ShowCase::getListByPosition($searchData);
    }

    public static function getHeadAnnouncement()
    {
        // 首头1
        $list[0] = Article::find()
            ->alias('a')
            ->innerJoin(['b' => ArticleAttribute::tableName()], 'b.article_id=a.id')
            ->where([
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.status'    => Article::STATUS_ONLINE,
                'a.is_delete' => Article::IS_DELETE_NO,
            ])
            ->andWhere([
                'b.type' => ArticleAttribute::ATTRIBUTE_HOME_TOP_ONE,
            ])
            ->select('a.id,a.type,content,title,refresh_time')
            ->orderBy('b.sort_time desc')
            ->limit(1)
            ->asArray()
            ->one();

        $list[1] = Article::find()
            ->alias('a')
            ->innerJoin(['b' => ArticleAttribute::tableName()], 'b.article_id=a.id')
            ->where([
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.status'    => Article::STATUS_ONLINE,
                'a.is_delete' => Article::IS_DELETE_NO,
            ])
            ->andWhere([
                'b.type' => ArticleAttribute::ATTRIBUTE_HOME_TOP_TWO,
            ])
            ->select('a.id,a.type,content,title,refresh_time')
            ->orderBy('b.sort_time desc')
            ->limit(1)
            ->asArray()
            ->one();

        $list[2] = Article::find()
            ->alias('a')
            ->innerJoin(['b' => ArticleAttribute::tableName()], 'b.article_id=a.id')
            ->where([
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.status'    => Article::STATUS_ONLINE,
                'a.is_delete' => Article::IS_DELETE_NO,
            ])
            ->andWhere([
                'b.type' => ArticleAttribute::ATTRIBUTE_HOME_TOP_THREE,
            ])
            ->select('a.id,a.type,content,title,refresh_time')
            ->orderBy('b.sort_time desc')
            ->limit(1)
            ->asArray()
            ->one();

        $list[3] = Article::find()
            ->alias('a')
            ->innerJoin(['b' => ArticleAttribute::tableName()], 'b.article_id=a.id')
            ->where([
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.status'    => Article::STATUS_ONLINE,
                'a.is_delete' => Article::IS_DELETE_NO,
            ])
            ->andWhere([
                'b.type' => ArticleAttribute::ATTRIBUTE_HOME_TOP_FOUR,
            ])
            ->select('a.id,a.type,content,title,refresh_time')
            ->orderBy('b.sort_time desc')
            ->limit(1)
            ->asArray()
            ->one();

        $list[4] = Article::find()
            ->alias('a')
            ->innerJoin(['b' => ArticleAttribute::tableName()], 'b.article_id=a.id')
            ->where([
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.status'    => Article::STATUS_ONLINE,
                'a.is_delete' => Article::IS_DELETE_NO,
            ])
            ->andWhere([
                'b.type' => ArticleAttribute::ATTRIBUTE_HOME_TOP_FIVE,
            ])
            ->select('a.id,a.type,content,title,refresh_time')
            ->orderBy('b.sort_time desc')
            ->limit(1)
            ->asArray()
            ->one();

        $ids = array_column($list, 'id');

        // 往期头条
        $data = Article::find()
            ->alias('a')
            ->innerJoin(['b' => ArticleAttribute::tableName()], 'b.article_id=a.id')
            ->where([
                'a.is_show'   => Article::IS_SHOW_YES,
                'a.status'    => Article::STATUS_ONLINE,
                'a.is_delete' => Article::IS_DELETE_NO,
            ])
            ->andWhere([
                'not in',
                'a.id',
                $ids,
            ])
            ->select('a.id,a.type,title,refresh_time')
            ->andWhere([
                'b.type' => [
                    ArticleAttribute::ATTRIBUTE_HOME_TOP_ONE,
                    ArticleAttribute::ATTRIBUTE_HOME_TOP_TWO,
                    ArticleAttribute::ATTRIBUTE_HOME_TOP_THREE,
                    ArticleAttribute::ATTRIBUTE_HOME_TOP_FOUR,
                    ArticleAttribute::ATTRIBUTE_HOME_TOP_FIVE,
                ],
            ])
            ->orderBy('b.sort_time desc')
            ->limit(27)
            ->asArray()
            ->all();
        $list = array_merge($list, $data);

        foreach ($list as &$item) {
            $item['content'] = strip_tags($item['content']);
            $item['url']     = BaseArticle::getDetailUrl($item['id'], $item['type']);
            $item['date']    = TimeHelper::short($item['refresh_time']);
        }

        return $list;
    }

    /**
     * 根据类型获取url
     * @param $type
     * @param $id
     */
    public static function getDetailUrl($type, $id)
    {
        switch ($type) {
            case Article::TYPE_ANNOUNCEMENT:
                $realId = Announcement::findOneVal(['article_id' => $id], 'id');

                return Announcement::getDetailUrl($realId);
            case Article::TYPE_NEWS:
                $realId = News::findOneVal(['article_id' => $id], 'id');

                return News::getDetailUrl($realId);
            case Article::TYPE_DAILY_ANNOUNCEMENT_SUMMARY:
                $realId = DailyAnnouncementSummary::findOneVal(['article_id' => $id], 'id');

                return DailyAnnouncementSummary::getDetailUrl($realId);
        }
    }
}