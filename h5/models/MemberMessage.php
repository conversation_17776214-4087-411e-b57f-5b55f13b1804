<?php

namespace h5\models;

use common\base\models\BaseMemberMessage;

class MemberMessage extends BaseMemberMessage
{

    public static function getContent($id, $memberId)
    {
        $info = self::find()
            ->select([
                'id',
                'add_time',
                'member_id',
                'type',
                'title',
                'content',
                'inner_link',
                'inner_link_params',
                'is_read',
            ])
            ->where([
                'member_id' => $memberId,
                'id'        => $id,
            ])
            ->asArray()
            ->one();

        //获取消息标题
        $typeName      = self::TYPE_NAME;
        $info['title'] = $typeName[$info['type']] . $info['title'];
        //获取消息参数
        $params = json_decode($info['inner_link_params'], true);
        //获取消息链接
        $info['link'] = self::getPath($info['inner_link'], $params);
        unset($params['id']);
        foreach ($params as $k => $item) {
            $info[$k] = $item;
        }

        $typeInfo = self::getPathTypeInfo($info['inner_link']);
        //获取点击查看文本
        $linkTypeText     = $typeInfo['text'];
        $info['linkType'] = $typeInfo['type'];
        if (!empty($linkTypeText)) {
            $info['linkTypeText'] = '点击查看' . $linkTypeText;
        } else {
            $info['linkTypeText'] = '';
        }
        unset($info['inner_link_params']);

        return $info;
    }
}