<script>
    // 页面加载完毕
    window.onload = f;

    function f() {
        const type = '<?=$type?>'
        const url = '<?=$url?>'

        if (type === 'navigateTo') {

        } else if (type === 'redirectTo') {
            redirectTo()
        } else if (type === 'switchTab') {
            wx.miniProgram.switchTab({
                url: url,
                success(r) {
                    // alert('success')
                },
                fail(e) {
                    // alert('fail')
                }
            })
        }

    }
</script>
