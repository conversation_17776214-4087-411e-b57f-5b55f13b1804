<?php
    use h5\components;
    use yii\helpers\Url;
?>
<link rel="stylesheet" href="/static/css/home.min.css?t=20240411">


<?= components\NavWidget::widget() ?>


<?=$HF_m?>


<?= components\CurrentLocationWidget::widget(['columnId'=>$columnId]) ?>

<div class="headline">
    <a class="headline-item" href="<?= $headAnnouncement[0]['url']?>">
        <h3 class="headline-title"><?= $headAnnouncement[0]['title']?></h3>
        <p class="headline-info"><?= $headAnnouncement[0]['content']?></p>
    </a>

    <a class="headline-item" href="<?= $headAnnouncement[1]['url']?>">
        <h3 class="headline-title"><?= $headAnnouncement[1]['title']?></h3>
        <p class="headline-info"><?= $headAnnouncement[1]['content']?></p>
    </a>

    <div class="headline-item recommend">
        <a href="<?= $headAnnouncement[2]['url']?>">
            <h3 class="headline-title"><?= $headAnnouncement[2]['title']?></h3>
        </a>
        <a href="<?= $headAnnouncement[3]['url']?>" class="headline-list"><?= $headAnnouncement[3]['title']?></a>
        <a href="<?= $headAnnouncement[4]['url']?>" class="headline-list"><?= $headAnnouncement[4]['title']?></a>
    </div>

</div>


<?=$B1_m?>
<?=$B2_m?>
<?=$A3_m?>
<?=$C1_m?>


<div class="switch-bar hot-recruitment">
    <h3 class="title">热招公告&简章</h3>

    <div class="switch-nav">
        <div class="switch-item is-active">热招单位</div>
        <div class="switch-item">推荐单位</div>
        <div class="switch-item">热门公告</div>
        <div class="switch-item">往期头条</div>
    </div>

    <div class="switch-message">
        <div class="switch-item is-active">
            <div class="switch-links">
                <?=$C4_m?>
            </div>

            <div class="switch-fetch" data-api="/home/<USER>" data-page="1">加载更多</div>
        </div>

        <div class="switch-item">
            <div class="switch-links">
                <?=$C5_m?>
            </div>

            <div class="switch-fetch" data-api="/home/<USER>" data-page="1">加载更多</div>
        </div>

        <div class="switch-item">
            <div class="time-nav">
                <span class="is-active">三日热门</span>
                <span>一周热门</span>
                <span>一月热门</span>
            </div>

            <div class="hot-content is-active">
                <?php foreach($threeDayHotAnnouncementList as $hotAnnouncement):?>
                <a href="<?=$hotAnnouncement['url']?>"><?=$hotAnnouncement['title']?></a>
                <?php endforeach;?>
            </div>

            <div class="hot-content">
                <?php foreach($weekHotAnnouncementList as $hotAnnouncement):?>
                <a href="<?=$hotAnnouncement['url']?>"><?=$hotAnnouncement['title']?></a>
                <?php endforeach;?>
            </div>

            <div class="hot-content">
                <?php foreach($monthHotAnnouncementList as $hotAnnouncement):?>
                <a href="<?=$hotAnnouncement['url']?>"><?=$hotAnnouncement['title']?></a>
                <?php endforeach;?>
            </div>
        </div>
        <div class="switch-item has-date">
            <?php foreach($c4Head as $item):?>
            <a href="<?=$item['url']?>">
                <span class="label-content"><?=$item['title']?></span>
                <span class="label-date"><?=$item['date']?></span>
            </a>
            <?php endforeach;?>
        </div>
    </div>
</div>

<div class="switch-bar latest-new">
    <h3 class="title">最新招聘&资讯</h3>

    <div class="switch-nav">
        <div class="switch-item is-active">最新更新</div>
        <div class="switch-item">每日汇总</div>
        <div class="switch-item">深度观察</div>
        <div class="switch-item">求职攻略</div>
    </div>

    <div class="switch-message">
        <!-- 最新更新 -->
        <!-- <div class="switch-item has-date is-active">
            <?php foreach($newestAnnouncementList as $item):?>
            <a class="<?php if(isset($item['is_top']) && $item['is_top']==1){echo 'top-mark';} ?>" href="<?=$item['url']?>">
                <span class="label-content">
                    <?=$item['title']?>
                </span>
                <span class="label-date">
                    <?=$item['refreshTime']?>
                </span>
            </a>
            <?php endforeach?>
        </div> -->
        <div class="switch-item has-date is-active">
            <?php foreach($newestAnnouncementList as $item):?>
            <a class="<?php if(isset($item['is_top']) && $item['is_top']==1){echo 'top-mark';} ?>" href="<?=$item['url']?>">
                <span class="label-content"> <?=$item['title']?></span>
                <span class="label-date"><?=$item['refreshTime']?></span>
            </a>
            <?php endforeach?>

        </div>

        <!-- 每日汇总 -->
        <!-- <div class="switch-item">
            <?php foreach($dailyList as $item):?>
            <a href="<?=$item['url']?>">
                <?=$item['title']?>
            </a>
            <?php endforeach?>
            <a class="view-more" href="<?= Url::toRoute('/home/<USER>')?>"><span>查看更多</span></a>
        </div> -->
        <div class="switch-item">
            <?php foreach($dailyList as $item):?>
            <a href="<?=$item['url']?>"> <?=$item['title']?></a>
            <?php endforeach?>

            <a class="view-more" href="<?= Url::toRoute('/home/<USER>')?>"><span>查看更多</span></a>
        </div>

        <!-- 深度观察 -->
        <!-- <div class="switch-item has-date">
            <?php foreach($depthInvestigationList as $item):?>
            <a href="<?=$item['url']?>">
                <span class="label-content">
                    <?=$item['title']?>
                </span>
                <span class="label-date">
                    <?=$item['refreshTime']?>
                </span>
            </a>
            <?php endforeach;?>

            <a class="view-more" href="<?= $depthInvestigationUrl?>"><span>查看更多</span></a>
        </div> -->
        <div class="switch-item has-date">
            <?php foreach($depthInvestigationList as $item):?>
            <a href="<?=$item['url']?>">
                <span class="label-content"><?=$item['title']?></span>
                <span class="label-date"><?=$item['refreshTime']?></span>
            </a>
            <?php endforeach;?>

            <a class="view-more" href="<?= $depthInvestigationUrl?>"><span>查看更多</span></a>
        </div>

        <!-- 求职攻略 -->
        <!-- <div class="switch-item has-date">
            <?php foreach($strategyList as $item):?>
            <a href="<?=$item['url']?>">
                <span class="label-content">
                    <?=$item['title']?>
                </span>
                <span class="label-date">
                    <?=$item['refreshTime']?>
                </span>
            </a>
            <?php endforeach?>
            <a class="view-more" href="<?= $strategyUrl ?>"><span>查看更多</span></a>
        </div> -->
        <div class="switch-item has-date">
            <?php foreach($strategyList as $item):?>
            <a href="<?=$item['url']?>">
                <span class="label-content"><?=$item['title']?></span>
                <span class="label-date"><?=$item['refreshTime']?></span>
            </a>
            <?php endforeach?>

            <a class="view-more" href="<?= $strategyUrl ?>"><span>查看更多</span></a>
        </div>
    </div>
</div>

<script>
    $(function () {
        var showcaseApiData = {
            src: "<?=$listShowcaseInfo['img']?>",
            url: "<?=$listShowcaseInfo['url']?>",
            index: <?=$listShowcaseInfo['position']?:0?>
        }
        
        var $hotBtn = $('.hot-recruitment .switch-nav .switch-item')
        var $hotItem = $('.hot-recruitment .switch-message .switch-item')
        var $hotTimeBtn = $('.hot-recruitment .time-nav span')
        var $hotContent = $('.hot-recruitment .hot-content')

        var $latestBtn = $('.latest-new .switch-nav .switch-item')
        var $latestItem = $('.latest-new .switch-message .switch-item')

        var $container = $('.main-container')
        var $switchFetch = $('.switch-fetch')

        var current = 0
        var page = 1
        var loading = false


        var $zhaopinhui = $('#zhaopinhui')

        $zhaopinhui.on('click', function (e) {
            e.preventDefault()
            $('#openMiniAppDialog1').fadeIn(100)
        })


        function renderList(data) {
            return data.reduce((previous, current) => {
                return previous += `
                        <a href="${current.url}">
                            <span class="label-content">${current.title}</span>
                            <span class="label-date">${current.refreshTime}</span>
                        </a>
                    `
            }, '')
        }

        function fetchData() {
            loading = true
            httpGet('/home/<USER>', {page: page + 1}).then(data => {
                if (data.length) {
                    $latestItem.eq(0)['append'](renderList(data))
                    page += 1
                }
                loading = data.length === 0
            })
        }

        function handleShowcaseCard() {
            if (showcaseApiData.src) {
                // 最新更新 tab
                $latestItem
                    .eq(0)
                    .find('a')
                    .eq(showcaseApiData.index)
                    .after($(`<a class="showcase-link" href="${showcaseApiData.url}"><img src="${showcaseApiData.src}" /></a>`))
            }
        }

        handleShowcaseCard()

        $container.infinite().on('infinite', function () {
            if (loading) return
            if (current === 0) {
                fetchData()
            }
        })

        $switchFetch.on('click', function () {
            var $lists = $(this).siblings('.switch-links')
            var query = $(this).data()
            var api = query.api
            var page = query.page + 1

            httpGet(api, {page}).then(data => {
                if (data.html.length) {
                    $(this).data({...query, page})
                    $lists.append(data.html)
                } else {
                    $(this).html('暂无更多数据')
                }
            })
        })

        // switchBar
        // 热招公告
        $hotBtn.on('click', function () {
            var index = $(this).index()
            $(this).siblings().removeClass('is-active')
            $(this).addClass('is-active')
            $hotItem.removeClass('is-active').eq(index).addClass('is-active')
        })

        $hotTimeBtn.on('click', function () {
            var index = $(this).index()
            $(this).siblings().removeClass('is-active')
            $(this).addClass('is-active')
            $hotContent.removeClass('is-active').eq(index).addClass('is-active')
        })

        // 最新资讯
        $latestBtn.on('click', function () {
            var index = $(this).index()

            current = index

            $(this).siblings().removeClass('is-active')
            $(this).addClass('is-active')
            $latestItem.removeClass('is-active').eq(index).addClass('is-active')
        })
    });
</script>
