<?php
use h5\components;
?>
<link rel="stylesheet" href="/static/css/summary.min.css?v=1">

<?= components\NavWidget::widget() ?>
<?=$HF_m?>

<?= components\CurrentLocationWidget::widget(['name'=>'每日汇总']) ?>


<ul class="result-list">
    <?php foreach($list as $item):?>
    <li class="result-item">
        <a class="result-link" href="<?=$item['url']?>">
        <h5 class="title"><?=$item['title']?></h5>
        <div class="tips">
            <span class="datetime">发布时间：<?=$item['releaseTime']?></span>
            <?php if($item['clickAmount']):?><span class="view"><?=$item['clickAmount']?></span><?php endif?>
        </div>
        </a>
    </li>


    <?php endforeach?>
</ul>

<div class="weui-loadmore" style="display: none;">
    <i class="weui-loading"></i>
    <span class="weui-loadmore__tips">正在加载</span>
</div>

<div class="weui-loadmore weui-loadmore_line" style="display: none;">
    <span class="weui-loadmore__tips">暂无数据</span>
</div>

<script>
    $(function () {
        var page = 1
        var loading = false

        function renderList(data) {
            return data.reduce((prevoius, current) => {
                return prevoius += `
                            <li class="result-item">
                                <a class="result-link" href="${current.url}">
                                    <h5 class="title">${current.title}（${current.belongDate}）</h5>
                                    <div class="tips">
                                        <div class="tag-view">
                                            <span class="datetime">发布时间：${current.releaseTime}</span>
                                            <span class="view">${current.clickAmount}</span>
                                        </div>
                                    </div>
                                </a>
                            </li>
                            `
            }, '')
        }

        function fetchData(data) {
            loading = true
            $loading.hide()
            $loading.eq(0).show()
            httpGet('/home/<USER>', { page: page + 1 }).then(data => {
                $loading.hide()
                if (data.length) {
                    page += 1
                    $resultList.append(renderList(data))
                } else {
                    $loading.eq(1).show()
                }

                loading = data.length === 0
            })
        }


        var $container = $('.main-container')
        var $resultList = $('.result-list')
        var $loading = $('.weui-loadmore')

        $container.infinite().on('infinite', function () {
            if (loading) return

            fetchData()
        })
    });
</script>