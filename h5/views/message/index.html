<link rel="stylesheet" href="/static/css/message.min.css">
<div class="top">
    <?php if(!$isEquityPackage){ ?>
    <div class="job-fast-box">
            <div class="job-fast-item">
                <img class="job-fast-item-img" src="/static/assets/resume/equity-notice.png" alt="">
                <span class="job-fast-item-text">想加快求职进度吗？</span>
            </div>
            <a class="job-fast-btn" href="/job-fast.html">去置顶</a>
    </div>
    <?php } ?>
    <div class="box">
        <ul>
            <li class="top-nav is-active <?php if($unReadInfo['all']):?>is-read<?php endif?>">全部</li>
            <li class="top-nav <?php if($unReadInfo['apply']):?>is-read<?php endif?>">投递反馈</li>
            <li class="top-nav <?php if($unReadInfo['jobApplyInvite']):?>is-read<?php endif?>">投递邀请</li>
            <li class="top-nav <?php if($unReadInfo['resumeCheck']):?>is-read<?php endif?>">谁看过我</li>
            <li class="top-nav <?php if($unReadInfo['waitHandle']):?>is-read<?php endif?>">待办事项</li>
            <li class="top-nav <?php if($unReadInfo['system']):?>is-read<?php endif?>">系统消息</li>
        </ul>
    </div>
    <div class="filter-box">
        <span class="filter-span is-check" data-id="">全部</span>
        <span class="filter-span" data-id="2">未读</span>
        <span class="filter-span" data-id="1">已读</span>
        <span class="all">全部已读</span>
    </div>
</div>

<div class="content <?=!$isEquityPackage?'job-fast-content':''?>">
    <ul id="messageList" class="tab-content is-active">
        <?php foreach($list as $message):?>
            <?php if($message['isRead'] == 2):?>
            <li class="new" data-id="<?= $message['id']?>">
            <?php else:?>
            <li data-id="<?= $message['id']?>">
            <?php endif;?>
            <div class="time"><?= $message['addTime']?></div>
            <div class="message">
                <h4><?= $message['title']?></h4>
                <p><?= $message['content']?></p>
            </div>
        </li>
        <?php endforeach?>


        <!-- <li>
        <div class="time">2021年11月16日 10:00</div>
        <div class="message interview">
            <h4>您的投递被查看</h4>
            <p>您投递给<a href="#">北京大学·大学讲师</a>职位的简历已经已经通过初筛</p>
            <span>
                面试职位：高校教师<br>
                面试时间：2021-07-06 16：30<br>
                联系人：李老师<br>
                联系电话：13548792663<br>
                联系地址：广州市天河区科韵路
            </span>
        </div>
    </li> -->
    </ul>

    <div class="weui-loadmore" style="display: none;">
        <i class="weui-loading"></i>
        <span class="weui-loadmore__tips">正在加载</span>
    </div>

    <div class="none-data" style="display: none;">
        <img src="/static/assets/message/none-data.png" alt="">
        <div class="weui-loadmore__tips">暂无数据</div>
        <div class="weui-loadmore__tips job-tips">快去投递心仪的职位吧～</div>
        <a href="/job" class="weui-loadmore__tips">找职位</a>
    </div>
</div>

<div id="messagePopup" class="weui-popup__container popup-bottom">
    <div class="weui-popup__overlay"></div>
    <div class="weui-popup__modal">
        <div class="apply-record">
        </div>
    </div>
</div>

<script>
    $(function () {
        var currentNav = 0
        var currentPage = 2
        var loading = false
        var options = ['', '4', '6', '7', '5', '3']
        var readStatus = [0, 0, 0, 0, 0, 0]

        var $container = $('.main-container')
        var $content = $('.content')
        var $topNav = $('.top-nav')
        var $filterBtn = $('.filter-span')
        var $messageList = $('#messageList')
        var $messagePopup = $('#messagePopup')
        var $loading = $('.weui-loadmore')
        var $readAll = $('.all')
        var $noneData = $('.none-data')

        $messageList.find('li').length === 0 && showDataNone()

       
        function dataNone(status) {
            var statusOptions = {
                '1': { text: '简历越完整，被查看的几率越高', herf: '/resume/index',buttonText: '完善简历' },
                '2': { text: '快去投递心仪的职位吧～', herf: '/job', buttonText: '找职位' },
            }
            if (currentNav === 0 || currentNav === 4 || currentNav === 5) {
                $('.job-tips').hide()
                $noneData.find('a').hide()
            }else{
                $('.job-tips').show()
                $noneData.find('a').show()
            }
            $noneData.find('a').attr('href', statusOptions[status]['herf'])
            $noneData.find('a').text(statusOptions[status]['buttonText'])
            $noneData.find('.job-tips').text(statusOptions[status]['text'])
        }

        function renderList(data) {
            return data.reduce((previous, current) => {
                return previous += `
                                <li class="${current.isRead === '2' ? 'new' : ''}" data-id="${current.id}">
                                    <div class="time">${current.addTime}</div>
                                    <div class="message">
                                        <h4>${current.title}</h4>
                                        <p>${current.content}</p>
                                    </div>
                                </li>
                            `
            }, '')
        }

        function fetchData(status, callback, noneDataCallback) {
            var isReplace = status === true
            var operate = isReplace ? 'html' : 'append'

            var api = '/message/get-list'

            var params = { page:currentPage }
            var readIndex = readStatus[currentNav]
            var isRead = $filterBtn.eq(readIndex).attr('data-id')
            params.type = options[currentNav]
            params.isRead = isRead

            loading = true
            $loading.show()

           httpGet(api, params).then(data => {
                $loading.hide()
                if (data.list.length) {
                    $messageList[operate](renderList(data.list))
                    callback && callback()
                } else {
                    $messageList[operate]('')
                    noneDataCallback && noneDataCallback()
                    dataNone(data.resumeStatus)
                }
                loading = data.list.length === 0
                currentPage += 1
            })
        }

        function refetchData() {
            loading = false
            currentPage = 1
            hideDataNone()
            fetchData(true, null, showDataNone)
        }

        $container.infinite().on('infinite', function () {
            if (loading) return
            fetchData(false, hideDataNone)
        })

        $topNav.on('click', function () {
            var index = $(this).index()

            currentNav = index
            $(this).siblings().removeClass('is-active')
            $(this).addClass('is-active')
            $filterBtn.removeClass('is-check').eq(readStatus[currentNav]).addClass('is-check')
            refetchData()
        })

        function showDataNone() {
            if (currentNav === 0) {
                $noneData.find('a').hide()
            }
            $container.addClass('data-none')
            $noneData.show()
        }

        function hideDataNone() {
            $container.removeClass('data-none')
            $noneData.hide()
        }

        $filterBtn.on('click', function () {
            $(this).siblings().removeClass('is-check')
            $(this).addClass('is-check')
            readStatus[currentNav] = $(this).index()
            currentPage = 1
            hideDataNone()
            fetchData(true, null, showDataNone)
        })

        $readAll.on('click', function () {
            var readParams = { type: options[currentNav] }
            httpPost('/message/update-status-all-read', readParams).then(() => { }).catch(err => { })
            if (currentNav === 0) {
                $topNav.removeClass('is-read')
            } else {
                $topNav.eq(currentNav).removeClass('is-read')
            }
            currentPage = 1
            $messageList['html']('')
            fetchData(true, null, showDataNone)
            if ($('.box').find('.is-read').length === 1) {
                $topNav.removeClass('is-read')
            }
        })


        $messageList.on('click', 'li', function () {
            var $target = $(this)
            var messageId = $target.data('id')

            if ($target.hasClass('new')) {
                httpPost('/message/update-read-status', { id: messageId }).then(function () {
                    $target.removeClass('new')
                })
            }

            httpPost('/message/get-content', { id: messageId }).then(function (data) {
                var linkStr = data.linkTypeText && data.link ? `<a href="${data.link}" target="_blank">${data.linkTypeText}</a>` : ''
                var htmlStr = `
                                    <h2 class="msg-title">${data.title}</h2>

                                    <p class="msg-datetime">${data.addTime}</p>

                                    <div class="msg-content line-height">
                                        ${data.content}
                                        ${linkStr}
                                    </div>
                                `

                if (data.remark || data.tips) {
                    htmlStr += `
                                    <div class="msg-remark line-height">${data.remark}</div>

                                    <div class="msg-tips line-height">
                                        <span>${data.tips}</span>
                                    </div>
                                `
                }

                if (data.additionalInfo) {
                    var appendStr = '<ul class="msg-append line-height">'

                    $.each(data.additionalInfo, function (index, item) {
                        appendStr += `<li>${item}</li>`
                    })

                    htmlStr += appendStr + '</ul>'
                }

                $messagePopup.find('.apply-record').html(htmlStr)
                $messagePopup.popup()
            })
        })
    })
</script>




