<link rel="stylesheet" href="/static/css/companyDetail.min.css?v=1.0.0">

<header>
    <div class="company-logo">
        <div class="logo">
            <img src="<?=$info['logo']?>" alt="">
        </div>

        <?php if($info['isCollect'] == 1):?>
         <div class="follow is-follow">已关注</div>
        <?php else:?>
        <div class="follow">关注</div>
        <?php endif;?>

    </div>

    <div class="company-information">
        <h1><?=$info['companyName']?></h1>
        <?php if($info['companyType']):?><span><?=$info['companyType']?></span><?php endif?>
        <?php if($info['nature']):?><span><?=$info['nature']?></span><?php endif?>
        <?php if($info['scale']):?><span><?=$info['scale']?></span><?php endif?>
    </div>
</header>

<div class="content">
    <nav>
        <button class="nav-btn <?php if($info['isCooperation'] == 1):?>is-active<?php endif?>">单位介绍</button>

        <button class="nav-btn notice-btn <?php if($info['isCooperation'] == 2):?>is-active<?php endif?>">公告(<span id="notice"><?=$info['announcementAmount']?></span>)</button>
        <button class="nav-btn position-btn">职位(<span id="position"><?=$info['jobAmount']?></span>)</button>
        <button class="nav-btn">联系方式</button>
    </nav>

    <div class="tab-content introduce <?php if($info['isCooperation'] == 1):?>is-active<?php endif?> "><?=$info['introduce']?></div>

    <div class="tab-content search-container notice <?php if($info['isCooperation'] == 2):?>is-active<?php endif?>">
        <div class="select-nav">
            <div class="item">
                <span id="noticeArea">地区</span>
                <span id="noticeJob">职位类型</span>
                <span id="wageId">薪资</span>
            </div>
            <span class="clear-btn">清除筛选</span>
        </div>

        <ul class="position-style">
            <?php foreach($info['announcementList'] as $announcement):?>
                <li class="element <?php if($announcement['status'] == 2):?>position-invalid<?php endif?>">
                    <a href="<?=$announcement['url']?>">
                        <div class="position">
                            <span class="job-name"><?=$announcement['title']?></span>
                        </div>
                        <div class="required">
                            <span>共<?=$announcement['jobAmount']?>个职位</span>
                            <span>招<?=$announcement['recruitAmount']?:0?>人</span>
                        </div>
                        <div class="time"><?= $announcement['refreshTime']?>发布</div>
                    </a>
                </li>
            <?php endforeach?>
        </ul>
    </div>

    <div class="tab-content search-container job">
        <div class="select-nav">
            <div class="item"><span id="positionArea">地区</span>
                <span id="positionJob">职位类型</span>
                <span id="majorId">专业</span>
            </div>
            <span class="clear-btn">清除筛选</span>
        </div>

        <ul class="position-style">
            <?php foreach($info['jobList'] as $job):?>
            <li class="element <?php if($job['status'] == 0):?>position-invalid<?php endif?>">
                <a href="<?=$job['url']?>">
                    <div class="position">
                        <span class="job-name"><?=$job['jobName']?></span>
                        <span class="pay"><?=$job['wage']?></span>
                    </div>
                    <div class="article"><?=$job['announcementName']?></div>
                    <div class="required">
                        <?php if($job['education']):?><span><?=$job['education']?></span><?php endif;?>
                        <?php if($job['amount']):?><span><?=$job['amount']?>人</span><?php endif;?>
                        <?php if($job['experience']):?><span><?=$job['experience']?></span><?php endif;?>
                        <span><?=$job['city']?></span>
                    </div>
                    <div class="time"><?=$job['releaseTime']?>发布</div>
                </a>
            </li>
            <?php endforeach?>

        </ul>
    </div>

    <div class="tab-content contact">
        <h4 class="title">联系我们</h4>

        <ul>
            <li>联系人：<?=$info['contact']?:''?></li>
            <li>联系方式：<?=$info['telephone']?:''?></li>
            <li>传真：<?=$info['fax']?:''?></li>
            <li>官方网址：<?=$info['website']?:''?></li>
        </ul>


        <?php if(!empty($info['childUnitList'])):?>
        <h4 class="title">二级院校</h4>
        <?php endif;?>

        <?php foreach($info['childUnitList'] as $k=>$childUnit):?>
        <p class="second-level"><?= $childUnit['name']?></p>
        <ul>
            <li>联系人：<?= $childUnit['contact']?></li>
            <li>联系方式：<?= $childUnit['telephone']?></li>
            <li>传真：<?= $childUnit['fax']?></li>
        </ul>
        <?php endforeach;?>
    </div>

    <div class="weui-loadmore" style="display: none;">
        <i class="weui-loading"></i>
        <span class="weui-loadmore__tips">正在加载</span>
    </div>

    <div class="weui-loadmore weui-loadmore_line" style="display: none;">
        <span class="weui-loadmore__tips">暂无数据</span>
    </div>
</div>

<script>
    $(function () {
        /* userStatus: 0 -> 未登录; 1 -> 未完成简历前三步; 2 -> 可以投递 */
        var userStatus = <?=$info['userStatus'] ?>;
        var companyId = <?=$info['companyId']?>;

        function redirect() {
            window.location.href = `/code-login?redirect=${encodeURIComponent(window.location.href)}`
        }

        var $followBtn = $('.follow')

        var $nav = $('nav')
        var $content = $('.content')

        var $navBtn = $('.nav-btn')
        var $selectBtn = $('.select-nav span')
        var $tabContent = $('.tab-content')
        var $clearBtn = $('.clear-btn')

        var $container = $('.main-container')
        var $loading = $('.weui-loadmore')
        var loading = false
        var currentNav = 0
        var $positionList = $('.position-style')
        var $noticeBtn = $('.notice-btn')
        var $positionBtn = $('.position-btn')

        var noticeAreaSelector = new MobileSelect({
            trigger: '#noticeArea',
            title: '地区',
            wheels: [
                {
                    data: [
                        {
                            id: '',
                            value: '全部'
                        },
                        <?php foreach($info['announceCityList'] as $k=>$city):?>
                        {
                            id: '<?= $city["k"]?>',
                                value: '<?= $city["v"]?>',
                        },
                        <?php endforeach;?>
                    ]
                }
            ],
            callback: function (index, data) {
                noticeQuery.areaId = data.pop().id
                refetchData()
            }
        })

        var noticeJobSelector = new MobileSelect({
            trigger: '#noticeJob',
            title: '职业类型',
            wheels: [
                {
                    data: [
                        {
                            id: '',
                            value: '全部'
                        },
                        <?php foreach($info['announceJobCategoryList'] as $jobCategory):?>
                        {
                            id: '<?= $jobCategory["k"]?>',
                                value: '<?= $jobCategory["v"]?>',
                        },
                        <?php endforeach;?>
                    ]
                }
            ],
            callback: function (index, data) {
                noticeQuery.jobCategoryId = data.pop().id
                refetchData()
            }
        })

        var wageIdSelector = new MobileSelect({
            trigger: '#wageId',
            title: '薪资',
            wheels: [
                {
                    data: [
                        {
                            id: '',
                            value: '全部'
                        },
                        <?php foreach($info['wageList'] as $wage):?>
                        {
                            id: '<?= $wage["k"]?>',
                                value: '<?= $wage["v"]?>',
                        },
                        <?php endforeach;?>
                    ]
                }
            ],
            callback: function (index, data) {
                noticeQuery.wageId = data.pop().id
                refetchData()
            }
        })

        var positionAreaSelector = new MobileSelect({
            trigger: '#positionArea',
            title: '地区',
            wheels: [
                {
                    data: [
                        {
                            id: '',
                            value: '全部'
                        },
                        <?php foreach($info['jobCityList'] as $k=>$city):?>
                        {
                            id: '<?= $city["k"]?>',
                                value: '<?= $city["v"]?>',
                        },
                        <?php endforeach;?>
                    ]
                }
            ],
            callback: function (index, data) {
                jobQuery.areaId = data.pop().id
                refetchData()
            }
        })

        var positionJobSelector = new MobileSelect({
            trigger: '#positionJob',
            title: '职业类型',
            wheels: [
                {
                    data: [
                        {
                            id: '',
                            value: '全部'
                        },
                        <?php foreach($info['jobCategoryList'] as $jobCategory):?>
                        {
                            id: '<?= $jobCategory["k"]?>',
                                value: '<?= $jobCategory["v"]?>',
                        },
                        <?php endforeach;?>
                    ]
                }
            ],
            callback: function (index, data) {
                jobQuery.jobCategoryId = data.pop().id
                refetchData()
            }
        })

        var majorIdSelector = new MobileSelect({
            trigger: '#majorId',
            title: '专业',
            wheels: [
                {
                    data: [
                        {
                            id: '',
                            value: '全部'
                        },
                        <?php foreach($info['majorList'] as $major):?>
                        {
                            id: '<?= $major["k"]?>',
                                value: '<?= $major["v"]?>',
                        },
                        <?php endforeach;?>
                    ]
                }
            ],
            callback: function (index, data) {
                jobQuery.majorId = data.pop().id
                refetchData()
            }
        })

        // 公告
        var noticeQuery = {
            areaId: '',
            jobCategoryId: '',
            wageId: '',
            page: 1
        }

        // 职位
        var jobQuery = {
            areaId: '',
            jobCategoryId: '',
            majorId: '',
            page: 1
        }

        function resetText(txt) {
            var indexLength = this.curIndexArr.length
            this.trigger.innerText = txt

            if (indexLength > 1) {
                this.checkRange(0, [])
            } else {
                this.setCurDistance(this.resetPosition(0, []))
            }
        }

        var defaultText = [
            {
                noticeArea: function () {
                    resetText.call(noticeAreaSelector, '地区')
                },
                noticeJob: function () {
                    resetText.call(noticeJobSelector, '职位类型')
                },
                wageId: function () {
                    resetText.call(wageIdSelector, '薪资')
                }
            }, {
                positionArea: function () {
                    resetText.call(positionAreaSelector, '地区')
                },
                positionJob: function () {
                    resetText.call(positionJobSelector, '职位类型')
                },
                majorId: function () {
                    resetText.call(majorIdSelector, '专业')
                }
            }
        ]

        // tab栏吸顶 start
        $container.on('scroll', function () {
            var isFixed = $content.offset().top - $nav.height() <= 60

            if (isFixed) {
                $nav.addClass('nav-fixed')
            } else {
                $nav.removeClass('nav-fixed')
            }
        })

        // tab栏吸顶 end

        function isFetchTab() {
            return /^[12]$/.test(currentNav)
        }

        function getFetchTabIndex() {
            return currentNav - 1
        }

        function getFetchQuery() {
            if (getFetchTabIndex() === 0) {
                return noticeQuery
            } else {
                return jobQuery
            }
        }

        function renderList(data) {
            /* isNotice: ture -> 公告模板; false -> 职位模板 */
            var isNotice = getFetchTabIndex() === 0

            if (isNotice) {
                return data.reduce((previous, current) => {
                    const {title, refreshTime, jobAmount, recruitAmount, url, status} = current
                    const noticeStatus = status == 2 ? ' position-invalid' : ''
                    return previous += `
                                    <li class="element${noticeStatus}">
                                        <a href="${url}">
                                            <div class="position">
                                                <span class="job-name">${title}</span>
                                            </div>
                                            <div class="required">
                                                <span>共${jobAmount}个职位</span>
                                                <span>招${recruitAmount}人</span>
                                            </div>
                                            <div class="time">${refreshTime}发布</div>
                                        </a>
                                    </li>
                                `
                }, '')
            }

            return data.reduce((previous, current) => {
                const {
                    jobName,
                    experience,
                    education,
                    amount,
                    city,
                    wage,
                    url,
                    announcementName,
                    status,
                    releaseTime
                } = current

                const jobStatus = status == 0 ? ' position-invalid' : ''
                const announcementText = announcementName ? `<div class="article">${announcementName}</div>` : ''
                const educationText = education ? `<span>${education}</span>` : ''
                const amountText = amount ? `<span>${amount}人</span>` : ''
                const experienceText = experience ? `<span>${experience}</span>` : ''
                const cityText = city ? `<span>${city}</span>` : ''

                return previous += `
                                <li class="element${jobStatus}">
                                    <a href="${url}">
                                        <div class="position">
                                            <span class="job-name">${jobName}</span>
                                            <span class="pay">${wage}</span>
                                        </div>
                                        ${announcementText}
                                        <div class="required">
                                            ${educationText}
                                            ${amountText}
                                            ${experienceText}
                                            ${cityText}
                                        </div>
                                        <div class="time">${releaseTime}发布</div>
                                    </a>
                                </li>
                            `
            }, '')
        }

        function fetchData(status) {
            var isReplace = status === true
            var operate = isReplace ? 'html' : 'append'
            var isNotice = getFetchTabIndex() === 0
            var query = getFetchQuery()
            var api = isNotice ? '/company/get-announcement-list' : '/company/get-job-list'
            var params = {companyId, ...query, page: isReplace ? query.page : query.page + 1}

            loading = true
            $loading.hide()
            $loading.eq(0).show()
            httpGet(api, params).then(data => {
                $loading.hide()
                if (data.length) {
                    $positionList.eq(getFetchTabIndex())[operate](renderList(data))
                    getFetchQuery().page += 1
                } else {
                    $positionList.eq(getFetchTabIndex())[operate]('')
                    $loading.eq(1).show()
                }
                loading = data.length === 0
            })
        }

        function refetchData() {
            loading = false
            getFetchQuery().page = 1
            fetchData(true)
        }

        /* 触底加载 */
        $container.infinite().on('infinite', function () {
            if (loading) return

            if (isFetchTab()) {
                fetchData()
            }
        })

        /* tab栏切换 */
        $navBtn.on('click', function () {
            var index = $(this).index()

            currentNav = index

            $loading.hide()
            loading = !isFetchTab()

            $(this).siblings().removeClass('is-active')
            $(this).addClass('is-active')
            $tabContent.hide()
            $tabContent.eq(index).show()
        })

        /* 点击关注按钮 */
        $followBtn.on('click', function () {
            if (userStatus === 0) {
                redirect()
                return
            }

            var isActive = $(this).hasClass('is-follow')
            var text = isActive ? '关注' : '已关注'

            httpPost('/company/collect', { companyId }).then(() => {
                $(this).text(text).toggleClass('is-follow')
            })
        })

        // 清除筛选项 start
        $clearBtn.on('click', function () {
            var trigger = defaultText[getFetchTabIndex()]

            Object.keys(trigger).forEach(function (item) {
                trigger[item]()
            })

            for (var item in getFetchQuery()) {
                getFetchQuery()[item] = ''
            }

            refetchData()
        })
        // 清除筛选项 end
    })
</script>
