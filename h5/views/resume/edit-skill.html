<link rel="stylesheet" href="/static/css/resumeCommon.min.css">

<div class="main-title">
    <div class="tltle-left">技能/语言</div>
    <img src="/static/assets/resume/header.png" alt="">
</div>

<div class="main">

    <form id="form" class="form">
        <div class="weui-cell weui-cell_select">
            <div class="weui-cell__hd">
                <label class="weui-label is-require">技能/语言</label>
            </div>
            <div class="weui-cell__bd">
                <input class="weui-input weui-input__select open-popup" id="skillId" name="skillId"
                        data-target="#skillPopup" readonly value="<?=$info['skill']?>" data-values="<?=$info['skillId']?>" placeholder="请选择" required>
            </div>
        </div>

        <div class="weui-cell weui-cell_select">
            <div class="weui-cell__hd">
                <label class="weui-label is-require">掌握程度</label>
            </div>
            <div class="weui-cell__bd">
                <input class="weui-input weui-input__select" id="degreeType" name="degreeType" type="text"
                        required value="<?=$info['degreeType']?>" data-values="<?=$info['degreeTypeId']?>" readonly placeholder="请选择">
            </div>
        </div>


        <div class="resume-container">
            <?php if(!empty($info['id'])):?>
            <button class="delete-button" id="delete">删除</button>
            <?php endif?>
            <button class="save-button" id="confirm">保存</button>
        </div>
    </form>


    <!-- 证书弹窗 start -->
    <div id="skillPopup" class="weui-popup__container filter-container">
        <div class="weui-popup__overlay"></div>
        <div class="weui-popup__modal">
            <form action="" class="filter-form" is-radio>
                <div class="filter-header">
                    <span>选择证书</span>
                    <i class="close-button close-popup"></i>
                </div>

                <div class="filter-main is-primary is-major-third navigation-main">

                    <ul class="navigation-nav">
                        <?php foreach($skillList as $k=>$item):?>
                        <li><span><?=$item['v']?></span></li>
                        <?php endforeach?>
                    </ul>
                    <div class="navigation-box">
                        <?php foreach($skillList as $k=>$item):?>
                            <ul class="navigation-panel">
                                <li class="panel-title"><span><?=$item['v']?></span></li>
                                <li class="panel-links filter-value">
                                    <?php foreach($item['children'] as $k1=>$item1):?>
                                    <label>
                                        <input type="checkbox" name="skill" value="<?=$item1['k']?>">
                                        <span><?=$item1['v']?></span>
                                    </label>
                                    <?php endforeach?>
                                </li>
                            </ul>
                        <?php endforeach?>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- 证书弹窗 end -->

</div>

<script>
    $(function () {
        var id = "<?= $info['id']?>"
        var addApi = '/resume/add-skill'
        var editApi = '/resume/edit-skill'
        let postApi

        var $degreeType = $('#degreeType')

        var degreeTypeSelector = $degreeType.select({
            closeText: '取消',
            items: [
                <?php foreach($degreeTypeList as $k=>$v):?>
                {title: "<?=$v?>", value: "<?=$k?>"},
                <?php endforeach?>
            ]
        })

        var skillEl = $('input[name="skillId"]')
        var skillId = skillEl.attr('data-values')
        filterCascaderPopup(skillId ? [skillId] : [], '#skillPopup', '#skillPopup', 'skill', function (data) {
            var {value, label} = data
            skillEl.val(label)
            skillEl.attr('data-values', value)
        })

        resumeOptimization(function (formEl, formData) {
            if (id) {
                postApi = editApi
                formData.id = id
            } else {
                postApi = addApi
            }

            httpPost(postApi, formData).then(function () {
                backResumeEdit()
            })
        }, function () {
            var api = '/resume/del-skill'
            httpPost(api, {id}).then(function () {
                backResumeEdit()
            })
        }, '.form', '.save-button', '.delete-button')


    })
</script>
