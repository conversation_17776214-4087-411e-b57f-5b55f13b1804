<link rel="stylesheet" href="/static/css/templateDownload.min.css?css=1.1" />

<div class="analyse-content">
    <div class="back">选择模板</div>
    <div class="choose-template">
        <?php foreach($data as $index => $item){ ?>
        <div class="list">
            <?php if($item['is_vip']==1){ ?>
            <div class="vip-template"></div>
            <?php } ?>
            <div class="template-list <?= $index==0?'active':'' ?>"  data-id='<?=$item["code"]?>'>
                <img src='<?=$item["template_image"]?>' alt="">
            </div>
            <span><?=$item["name"]?></span>
        </div>
        <?php } ?>
    </div>
</div>


<div class="main-bottom">
    <?php if($info['is_vip']==2){ ?>
    <div class="open-vip">
        <div class="vip-text">升级VIP会员免费使用所有模版</div>
        <a href="/vip.html" class="open-btn">去开通</a>
    </div>
    <?php } ?>

    <div class="down-container">
        <div class="down-content">
            使用此模板导出PDF
        </div>
    </div>
</div>


<script>
    $(function () {
        var $package = $('.template-list')
        var $time = $('.buy-time')
        var $price = $('.allprice')
        var $moneyday = $('.text-bot')
        var $content = $('.down-content')
        var $list = $('.list')
        var $viptips = $('.open-vip')
        var $back = $('.back')
        var isLogin = true



        // 简历模板切换
        $package.on('click', function () {
            $(this).addClass('active')
            $(this).parent().siblings().children().removeClass('active')
        })

        // 简历模板切换
        $back.on('click', function () {
           // 返回上一页
            window.history.back()
        })



        function redirect() {
            var path = '/code-login'
            window.location.href = `${path}?redirect=${encodeURIComponent(window.location.href)}`
        }

        //模版下载
        $content.on('click',function(){
                if(!isLogin){
                    redirect()
                    return
            }
            var id = $('.template-list.active').attr('data-id')
             httpGet('/resume/resume-template-equity',{ code: id }).then((res) => {
                const {isVip } = res
                if(isVip===true){
                    $.modal({
                        text: '升级VIP会员可解锁所有简历模版使用权限！',
                        title: '',
                        buttons: [
                            {
                                text: '取消',
                                className: 'default'
                            },
                            {
                                text: '开通VIP',
                                className: 'primary',
                                onClick: function () {
                                    $(`<a href="/vip.html"></a>`).click()
                                }
                            }
                        ]
                    })
                }else{
                    const link = document.createElement('a')
                    link.href = `/resume/resume-template-pdf-download?code=${id}`
                    link.click()
                    link.remove()
                    // httpGet('/resume/resume-template-pdf-download',{ code: id }).then((data) => {
                    // })
                }
             })
        })

    })
</script>
</section>
</div>
