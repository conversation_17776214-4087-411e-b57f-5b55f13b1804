<link rel="stylesheet" href="/static/css/jobFast.min.css" />
<script src="https://img.gaoxiaojob.com/uploads/static/lib/crypto/index.js"></script>

<div class="fast-content">
    <div class="wrapper-banner">
        <img src="//img.gaoxiaojob.com/uploads/image/purchase/h5/fast/banner.png" alt="" />
    </div>

    <div class="main-content">
        <img class="compare" src="//img.gaoxiaojob.com/uploads/image/purchase/h5/fast/compare.png" alt="" />
        <?php if($data['isMiniShow']):?>
        <div class="wrapper-package">
            <div class="package-content">
                <?php foreach($data['equity_package_info']['list'] as $k=>$v):?>
                <div class="list <?php if($k == 0):?>active<?php endif;?>" data-id="<?=$v['equity_package_id']?>">
                    <?php if($v['buy_type_txt']){ ?>
                    <span class="tag"><?=$v['buy_type_txt']?></span>
                    <?php } ?>
                    <div class="validity"><?=$v['days']?>天</div>
                    <div class="price"><?=$v['real_amount']?></div>
                    <div class="original-price">￥<?=$v['original_amount']?></div>
                    <div class="average-price"><?=$v['daily_amount']?>元/天</div>
                </div>
                <?php endforeach;?>
            </div>
            <div class="package-amount">
                <?php foreach($data['equity_package_info']['list'] as $k=>$v):?>
                <div class="list">
                    <div class="name">包含如下权益</div>
                    <div class="amount">
                        <?php foreach($v['equity_list'] as $vi):?>
                        <div class="item"><?=$vi['name']?>*<span class="num"><?=$vi['description']?></span></div>
                        <?php endforeach;?>
                    </div>
                </div>
                <?php endforeach;?>
            </div>
        </div>
        <?php endif;?>
        <div class="wrapper-privilege" id="privilege">
            <div class="desc">高效提升求职效果</div>
            <div class="list resume-top">
                <div class="introduce">简历置顶展示在高校人才网硕博人才库，让您从50w+人才中脱颖而出，加速提升简历曝光及求职效率。</div>
                <img src="//img.gaoxiaojob.com/uploads/image/purchase/h5/fast/resume-top.png" alt="" class="cover" />
                <div class="instruction resume-top-instruction">特权说明</div>
            </div>
            <div class="list deliver-top">
                <div class="introduce">您的投递信息将通过多渠道实时通知至单位端，并置顶展示在单位端投递列表，优先获取单位关注，便于单位第一时间查看及处理您的简历！</div>
                <img src="//img.gaoxiaojob.com/uploads/image/purchase/h5/fast/deliver-top.png" alt="" class="cover" />
                <div class="instruction deliver-top-instruction">特权说明</div>
            </div>
            <div class="list resume-refresh">
                <div class="introduce">
                    系统每天自动为您刷新简历，同时您也可以无限次主动刷新简历。<br />
                    刷新简历可提高简历在高校人才网硕博人才库中的排名，靠前的排名更有助于获得单位关注！
                </div>
                <img src="//img.gaoxiaojob.com/uploads/image/purchase/h5/fast/resume-refresh.png" alt="" class="cover" />
            </div>
        </div>

        <div class="wrapper-evaluate">
            <div class="evaluate-content">
                <div class="list"></div>
                <div class="list"></div>
                <div class="list"></div>
                <div class="list"></div>
                <div class="list"></div>
                <div class="list"></div>
                <div class="list"></div>
            </div>
        </div>
        <?php if($data['isMiniShow']):?>
        <div class="wrapper-server">
            <div class="title">服务说明</div>
            <p>1、请在本页面点击“立即开通”按钮，通过微信支付向唯一官方收款方“高校人才网”完成服务费用的支付。请勿尝试任何私下转账方式。</p>
            <p>2、服务将于付款成功后自动开通，服务过期则所有特权失效。购买后请合理安排时间并尽快使用。</p>
            <p>3、请务必确认您未“隐藏简历”，且在线简历完整度≥65%（以平台显示的完整度为准），否则无法提升简历曝光效果。</p>
            <p>4、本产品为虚拟服务，不支持退款，敬请谅解。</p>
            <p>5、购买即代表您同意 <a href="/agreement/value-added-services?type=2">《高校人才网求职快服务协议》</a></p>
        </div>
        <?php endif;?>
    </div>

    <div class="buy-container">
        <?php if($data['isMiniShow']){ ?>
        <div class="buy-content">
            <div class="buy-price">
                <div class="tag">￥</div>
                <div class="allprice">10</div>
                <span class="time">/30天</span>
                <span class="original">¥156</span>
            </div>
            <button class="open-serve">
                <div class="text-top">立即开通</div>
                <span class="text-bot">低至3.3元/天</span>
            </button>
        </div>
        <?php }else{ ?>
        <!-- 微信小程序 ios系统展示按钮 -->
        <a href="javascript:;" class="ios-open-btn" data-target="serviceQRcode">
            <span>iOS用户在小程序内暂不可用</span>
            <span class="solve">点此解决</span>
        </a>
        <?php } ?>
    </div>

    <div id="privilegePopup" class="weui-popup__container popup-bottom">
        <div class="weui-popup__overlay"></div>
        <div class="weui-popup__modal">
            <div class="resume-top">
                <div class="header">
                    <span>简历置顶</span>
                    <i class="close-button close-popup"></i>
                </div>

                <div class="popup-main">
                    <div class="desc">特权说明：支持您自主设置简历置顶时间，置顶当天0:00-24:00均生效。</div>
                    <img class="cover" src="//img.gaoxiaojob.com/uploads/image/purchase/h5/fast/resume-top-instruction.png" alt="" />
                    <div class="tips">
                        提示：<br />
                        1、设置入口：【个人主页】-【求职工具】<br />
                        2、置顶期间，请保证您的在线简历完整度≥65%，且为开放状态。<br />
                        3、您屏蔽的单位无法搜索到您的简历，请放心使用。
                    </div>
                </div>
            </div>

            <div class="deliver-top">
                <div class="header">
                    <span>投递置顶</span>
                    <i class="close-button close-popup"></i>
                </div>

                <div class="popup-main">
                    <div class="desc">特权说明：您可在发起投递时选择使用「投递置顶」特权，投递后，您的简历将置顶展示在单位端应聘列表。</div>
                    <img class="cover" src="//img.gaoxiaojob.com/uploads/image/purchase/h5/fast/deliver-top-instruction.png" alt="" />
                    <div class="tips">提示：该特权限报名方式为“站内投递”的职位使用；非合作单位职位/网申职位暂不支持使用。同一职位，30天内只能使用一次。</div>
                </div>
            </div>
        </div>
    </div>

    <div id="serviceQRcode" class="weui-popup__container popup-center">
        <div class="weui-popup__overlay"></div>
        <div class="weui-popup__modal">
            <img class="qrcode" src="<?=$data['buyGuideQrcode']?>" alt="" />
            <div class="guide">长按识别二维码</div>
            <div class="cancel">取消</div>
        </div>
    </div>

    <div id="paySuccessPopup" class="weui-popup__container popup-center">
        <div class="weui-popup__overlay"></div>
        <div class="weui-popup__modal">
            <!-- <div class="pay-success-title">下单成功</div>
            <div class="pay-success-sub-title">已为您开通“求职快30天”</div>
            <div class="pay-success-wrapper-title">推荐搭配以下服务，获得更多职场机会</div>
            <div class="list-content">
                <a href="" class="package-list">
                    <div class="icon">
                        <img src="//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/resume-top-icon.png" alt="" />
                    </div>
                    <div>
                        <div class="package-name">高才VIP</div>
                        <div class="package-desc">11+求职特权，<span>立即解锁</span></div>
                    </div>
                </a>
                <a href="" class="package-list">
                    <div class="icon">
                        <img src="//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/resume-top-icon.png" alt="" />
                    </div>
                    <div>
                        <div class="package-name">高才VIP</div>
                        <div class="package-desc">11+求职特权，<span>立即解锁</span></div>
                    </div>
                </a>
            </div>
            <div class="footer-btn">我知道了（5S）</div> -->
        </div>
    </div>
</div>

<script>
    $(function () {
        //var timeStart = new Date().getTime();
        var uuid = "<?=$data['uuid']?>"
        var $buyServe = $('.open-serve')
        var $effectiveTime = $('.buy-container .time')
        var $allprice = $('.buy-container .allprice')
        var $originalPrice = $('.buy-container .original')
        var $averagePrice = $('.buy-container .text-bot')
        var $privilegePopup = $('#privilegePopup')
        var $resumeTopInstruction = $('.resume-top-instruction')
        var $deliverTopInstruction = $('.deliver-top-instruction')

        var $packageList = $('.package-content .list')
        var $packageInfo = $('.package-amount .list')

        var $iosOpenBtn = $('.ios-open-btn')
        var $iosOpenPopup = $('#serviceQRcode')
        var $iosOpenClose = $('#serviceQRcode .cancel')

        var $paySuccessPopup = $('#paySuccessPopup')
        var turnOffTime = 5
        var turnOffTimer = null

        function scrollIntoPrivilege() {
            var hashStr = window.location.hash
            var hashArray = hashStr.split('?')
            var hash = hashArray[0]
            hash && document.querySelector(hash).scrollIntoView()
        }
        scrollIntoPrivilege()

        // 1vip页、2洞察页、4求职快
        var pageType = "<?=$data['page_type']?>"
        var equityPackageId = "<?=current($data['equity_package_info'])['equity_package_id']?>"
        var orderIdName = `orderId_${pageType}`
        var orderId = localStorage.getItem(orderIdName)
        var isLogin = "<?=$data['is_login']?>"

        function openSignupPopup() {
            window.signupPopup.show()
        }

        function privilegePopup(type) {
            $(type === 1 ? '#privilegePopup .resume-top' : '#privilegePopup .deliver-top')
                .show()
                .siblings()
                .hide()
            $privilegePopup.popup()
        }

        $resumeTopInstruction.on('click', function () {
            privilegePopup(1)
        })

        $deliverTopInstruction.on('click', function () {
            privilegePopup(2)
        })

        //查询是否购买成功
        function paymentData() {
            if (isLogin && orderId) {
                httpGet('/payment/query', {
                    orderId: orderId
                })
                .then((data) => {
                    const { tips, noticeCard, status } = data                                       
                    if (status == 1) {
                        showPaySuccessPopup(noticeCard)
                    } else {
                        $.toast(tips, 'text')
                    }
                    // 删除缓存
                    localStorage.removeItem(orderIdName)
                })
                .catch((r) => {
                    localStorage.removeItem(orderIdName)
                })
            }
        }
        paymentData()

        function showPaySuccessPopup(data = {}) {
            const { title, subTitle, lineTxt, contentList = [] } = data
            var html = `<div class="pay-success-title">${title}</div>
                        <div class="pay-success-sub-title">${subTitle}</div>
                        <div class="pay-success-wrapper-title">${lineTxt}</div>
                        <div class="list-content">`

            contentList.forEach((item) => {
                html += `<a href="${item.url}" class="package-list">
                        <div class="icon">
                            <img src="${item.icon}" alt="" />
                        </div>
                        <div>
                            <div class="package-name">${item.title}</div>
                            <div class="package-desc">${item.subTitle}<span>${item.linkLabel}</span></div>
                        </div>
                    </a>
                `
            })

            html += `</div>
                        <div class="footer-btn">我知道了（5S）</div>
                    `
            $paySuccessPopup.find('.weui-popup__modal').html(html)
            $paySuccessPopup.popup()
            countDown()
        }

        function countDown() {
            turnOffTime = 5
            clearTurnOffTimer()
            turnOffTimer = setInterval(() => {
                turnOffTime = turnOffTime - 1
                $paySuccessPopup.find('.footer-btn').html(`我知道了（${turnOffTime}S）`)
                if (turnOffTime === 0) {
                    $.closePopup()
                    clearTurnOffTimer()
                }
            }, 1000)
        }
        
        function clearTurnOffTimer() {
            clearInterval(turnOffTimer)
        }

        $paySuccessPopup.on('click', '.footer-btn', function () {
            $.closePopup()
            clearTurnOffTimer()
        })

        //跳转到支付中间页
        $buyServe.on('click', function () {
            //-------------用户付费转化数据埋点-开始---------------
            payBuriedPoint('/showcase-browse-log/job-fast-open-button-point-log',{equityPackageId: equityPackageId,uuid: uuid})
            //-------------用户付费转化数据埋点-结束---------------

            if (!isLogin) {
                openSignupPopup()
                return
            }

            if (isMiniapp()) {
                wx.miniProgram.redirectTo({url: '/packages/order/confirm?equityPackageId='+equityPackageId})
                return
            }

            //记录到缓存
            localStorage.setItem('pageType', pageType)
            localStorage.setItem('equityPackageId', equityPackageId)
            url = 'payment/middle-pay?uuid='+uuid
            window.location.href = url
        })

        // 切换套餐类型
        $packageList.on('click', function () {
            var index = $(this).index()
            $packageInfo.eq(index).show().siblings().hide()

            $(this).addClass('active')
            $(this).siblings().removeClass('active')

            equityPackageId = $(this).attr('data-id')

            var time = $(this).find('.validity').text()
            var price = $(this).find('.price').text()
            var originalPrice = $(this).find('.original-price').text()
            var averagePrice = $(this).find('.average-price').text()

            $allprice.text(price)
            $originalPrice.text(originalPrice)
            $effectiveTime.text('/' + time)
            $averagePrice.text('低至' + averagePrice)

            //-------------用户付费转化数据埋点-开始---------------
            payBuriedPoint('/showcase-browse-log/job-fast-click-package-point-log',{equityPackageId: equityPackageId,uuid: uuid})
            //-------------用户付费转化数据埋点-结束---------------
        })
        $packageList.eq(1).click()

        //-------------用户付费转化数据埋点-开始---------------
        function payBuriedPoint(api, params) {
            httpGet(api, params)
        }

        window.onbeforeunload = function (){
            payBuriedPoint('/showcase-browse-log/update-job-fast-view-point-log',{uuid: uuid})
        }
        //-------------用户付费转化数据埋点-结束---------------

        $iosOpenBtn.on('click', function () {
            $iosOpenPopup.popup()
        })

        $iosOpenClose.on('click', function () {
            $.closePopup()
        })
    })
</script>