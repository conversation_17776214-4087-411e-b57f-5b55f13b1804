<link rel="stylesheet" href="/static/css/baikeWords.min.css?t=20240518">

<div class="top-module">
    <div class="top-module-hd">
        <h1><?=$detail['keyword']?></h1>
    </div>
    <?php if($detail['jobIntroduction'] != ''):?>
    <div class="top-module-bd">
        <?=$detail['jobIntroduction']?>
    </div>
    <?php endif;?>
    <div class="top-module-ft"></div>
</div>

<?php if($detail['jobContent'] != '' || count($relatedJobList) > 0):?>
<div class="card-container">

    <?php if($detail['jobContent'] != ''):?>
    <div class="card">
        <div class="card-title">
            <a title="<?=$detail['keyword']?>" href="<?=$detail['jumpLink']?>"><?=$detail['keyword']?></a></div>
        <div class="card-content" data-type="first">
            <div class="card-content-box">
                <div class="content-bd">
                    <p>
                        <?=$detail['jobContent']?>
                    </p>
                </div>
            </div>
            <div class="more-btn">展开</div>
        </div>
    </div>
    <?php endif;?>

    <?php foreach ($relatedJobList as $jobInfo) {?>
    <!-- 模板 -->
    <div class="card">
        <div class="card-title">
            <a href="<?=$jobInfo['url']?>" title="<?=$jobInfo['jobName']?>"><?=$jobInfo['jobName']?></a>
        </div>
        <div class="card-content">
            <div class="card-content-box">
                <?php if($jobInfo['isShowDuty'] == 1):?>
                <div class="content-hd">岗位职责：</div>
                <div class="content-bd">
                    <p>
                        <?=$jobInfo['duty']?>
                    </p>
                </div>
                <?php endif;?>

                <?php if($jobInfo['isShowRequirement'] == 1 && $jobInfo['isShowDuty'] == 1):?>
                <div style="margin-top: 12px"></div>
                <?php endif;?>

                <?php if($jobInfo['isShowRequirement'] == 1):?>
                <div class="content-hd">任职要求：</div>
                <div class="content-bd">
                    <p>
                        <?=$jobInfo['requirement']?>
                    </p>
                </div>
                <?php endif;?>
            </div>

            <div class="more-btn">展开</div>
        </div>
        <div class="card-ft">
            <a href="<?=$jobInfo['companyUrl']?>">
                <span class="company"><?=$jobInfo['companyName']?></span>
                <span class="gather">
                    <?=$jobInfo['companyTypeAndNatureText']?>
                </span>
            </a>
        </div>
    </div>

    <?php } ?>

</div>
<?php endif;?>

<?php if(count($recommendJobList) > 0):?>
<div class="job-container">
    <div class="job-tit">相关职位推荐</div>

    <div class="job-list">
        <ul class="position-style">
            <?php foreach ($recommendJobList as $jobInfo) {?>
            <li class="element">
                <a href="<?=$jobInfo['url']?>" title="<?=$jobInfo['jobName']?>">
                    <div class="position">
                        <span class="job-name"><?=$jobInfo['jobName']?></span>
                        <span class="pay"><?=$jobInfo['wage']?></span>
                    </div>
                    <div class="article"><?=$jobInfo['announcementName']?></div>
                    <div class="required">
                        <?php if($jobInfo['education']):?>
                        <span><?=$jobInfo['education']?></span>
                        <?php endif;?>
                        <?php if($jobInfo['amount']):?>
                        <span><?=$jobInfo['amount']?></span>
                        <?php endif;?>
                        <?php if($jobInfo['cityName']):?>
                        <span><?=$jobInfo['cityName']?></span>
                        <?php endif;?>
                    </div>
                    <div class="introduce">
                        <span class="company"><?=$jobInfo['companyName']?></span>
                        <span class="gather">
                            <?=$jobInfo['companyTypeAndNatureText']?>
                        </span>

                    </div>
                </a>
            </li>
            <?php } ?>

        </ul>
    </div>

    <div class="more-text">
        <a href="<?=$detail['h5MoreLink']?>">查看更多<span> <?=$detail['keyword']?> </span>招聘职位</a>
    </div>
</div>
<?php endif;?>

<script>
    $(function () {
        $('.card').on('click', '.more-btn', function (e) {
            $(this).parents('.card').toggleClass('active')
            var flag = $(this).parents('.card').hasClass('active')
            $(this).text(!flag ? '展开' : '收起')
        })

        function showText() {
            var limitLine = 5

            // 遍历每个 .card 元素
            // $('.card').each(function () {
            //     var $card = $(this)
            //     var $content = $card.find('.content-bd p')

            //     var height = $content.height()
            //     var lineHeight = parseInt($content.css('line-height'), 10)
            //     var isOverflow = height > lineHeight * limitLine

            //     // console.log(isOverflow, height, lineHeight, lineHeight * limitLine)

            //     // 根据是否超出行数来显示/隐藏 .more-btn
            //     $card.find('.more-btn').toggleClass('is-show', isOverflow)
            // })

            $('.card').each(function () {
                var $card = $(this)
                var $contentBox = $card.find('.card-content-box')
                var $content = $card.find('.card-content')
                var $contentP = $contentBox.find('p')
                var lineHeight = parseInt($contentP.css('line-height'), 10)
                var height = $contentBox.height()
                var pTotal = 0
                $contentP.each(function (index, element) {
                    // console.log(this)
                    var h = parseInt($(this).css('height'), 10)
                    // console.log(h)
                    pTotal += h
                    var isOverflow = pTotal > lineHeight * limitLine
                    // console.log(pTotal)
                    // 根据是否超出行数来显示/隐藏 .more-btn
                    $card.find('.more-btn').toggleClass('is-show', isOverflow)
                    // console.log(index)
                    // console.log(lineHeight * limitLine)
                    if (h > lineHeight * limitLine) {
                        // console.log('进入')
                        var type = $content.attr('data-type')
                        if (type) {
                            $content.css('maxHeight', `132px`)
                        } else {
                            $content.css('maxHeight', `138px`)
                        }
                        return false
                    }

                    if (isOverflow) {
                        $content.css('maxHeight', `${32 * 2 + 115}px`)
                    }
                })
            })
        }

        showText()
    })
</script>
