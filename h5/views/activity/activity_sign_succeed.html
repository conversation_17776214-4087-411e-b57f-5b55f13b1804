<link rel="stylesheet" href="/static/css/dbMeetingSignSucceed.min.css">

<div class="empty-main"></div>

<div class="succeed-main">
    <div class="banner">
        <img src="<?=$backgroundUrl?>"
             alt=""/>
    </div>

    <div class="succeed-content">
        <?php if (!$message): ?>
        <div class="title">恭喜您签到成功！</div>
        <?php endif; ?>

        <!-- <div class="goto">您的信息已同步在线简历，请前往查看<a target="_blank" href="/person">简历中心 ></a></div> -->

        <!-- <div class="rich-content">
            感谢您的填写，预先了解相关信息，您也可以添加小博老师微信：123456（电话同号），备注“姓名+学历+专业+毕业院校+云聘会”，我们将邀请您加入活动交流群！或扫描下方二维码加入本场活动交流群，随时了解招聘会最新动态。
        </div> -->
        <div class="sign-info">
            <div class="sign-number">您的签到序列号为：<span><?=$signInfo['serial_number']?></span></div>
            <div class="sign-session">
                <div class="left">场次信息：</div>
                <div class="rigth"><?=$signInfo['title']?></div>
            </div>
            <?php if ($signInfo['sign_desc']): ?>
            <div class="sign-session">
                <div class="left">签到说明：</div>
                <div class="rigth"><?=$signInfo['sign_desc']?></div>
            </div>
            <?php endif; ?>
        </div>

        <!-- <div class="qr-code">
            <img src="https://img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/17.png" alt="" />
            <img src="https://img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/17.png" alt="" />
            <img src="https://img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/17.png" alt="" />
            <img src="https://img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/17.png" alt="" />
        </div> -->

        <div class="recommend">
            <a target="_blank" href="/resume/edit">简历中心</a>
            <a target="_blank" href="/job">找职位</a>
            <a target="_blank" href="/vip.html" class="hot">上岸直通车</a>
        </div>

        <!-- <div class="back-wrapper">
            <button class="go-back">我知道了</button>
        </div> -->
    </div>
</div>

<script>
    $(function () {
        var isLogin = '<?=$isLogin?>';
        var isSign = '<?=$isSign?>';
        var message = '<?=$message?>';
        var toUrl = '<?=$toUrl?>';

        if (!isLogin) {
            // 通过报名表单链接访问
            $('.empty-main').show()
            toastText(message, 3000, function () {
                // redirect
                toUrl = `${toUrl}?redirect=${encodeURIComponent(window.location.href)}`
                window.location.replace(toUrl)

            })
            return
        }

        if (!isSign) {
            // 未报名
            $('.empty-main').show()
            toastText('您尚未报名本场次活动，请报名后再进行签到！', 3000, function () {
                window.location.replace(toUrl)
            })
            return
        }


    })
</script>
