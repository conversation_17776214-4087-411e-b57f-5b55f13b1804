<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
</head>
<style>
    html,
    body {
        height: 100%;
        color: #333;
    }

    .checkedUrl {
        width: 0;
        height: 0;
    }

    .email-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100%;
    }

    .email-wrapper .email-container {
        width: 680px;
        flex-shrink: 0;
        border: 1px solid #ebebeb;
        border-radius: 16px;
        margin-top: 50px;
        margin-bottom: 50px;
    }

    .email-wrapper .email-container a {
        text-decoration: none !important;
    }

    .email-wrapper .email-container .common-title {
        font-size: 16px;
        font-weight: bold;
        background: url('https://img.gaoxiaojob.com/uploads/static/image/resume/triangle.png') no-repeat left center/22px auto;
        line-height: 1;
        padding: 30px 0 30px 30px;
    }

    .email-wrapper .email-container .header {
        height: 60px;
        background-color: #ffa000;
        position: relative;
        padding-left: 50px;
        border-top-left-radius: 16px;
        border-top-right-radius: 16px;
        display: flex;
        align-items: center;
    }

    .email-wrapper .email-container .header .link {
        height: 33px;
        display: flex;
        align-items: center;
        color: #fff;
        text-decoration: none;
        font-size: 14px;
        padding-left: 137px;
        background: url('//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png') no-repeat left center/122px auto;
    }

    .email-wrapper .email-container .header .notes {
        position: absolute;
        right: 20px;
        width: 116px;
        height: 116px;
        display: block;
        bottom: -8px;
    }

    .email-wrapper .email-container .hideSystemUrl {
        color: #333;
    }

    .email-wrapper .email-container .container {
        margin: 21px 21px 0;
        border: 1px dashed #ffa000;
        border-bottom: none;
        padding-left: 29px;
        padding-right: 29px;
        padding-bottom: 30px;
        border-radius: 8px 8px 0px 0px;
    }

    .email-wrapper .email-container .footer {
        color: #333 !important;
        background-color: #f8f8f8;
        padding: 15px 50px;
        border-bottom-left-radius: 16px;
        border-bottom-right-radius: 16px;
    }

    .email-wrapper .email-container .footer .qr-code {
        height: 74px;
        background: url('//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/11.png') no-repeat right 140px center/66px,
        url('https://img.gaoxiaojob.com/uploads/static/image/resume/showcase.png') no-repeat left center/100% auto;
    }

    .email-wrapper .email-container .footer p {
        margin: 0;
        opacity: 0.8;
        font-size: 12px;
    }

    .email-wrapper .email-container .footer p + p {
        margin-top: 8px;
    }

    .email-wrapper .email-container .footer p .orange {
        color: #fa635c !important;
    }

    .email-wrapper .email-container .footer p .blue {
        color: #486cf5 !important;
    }

    .email-wrapper .email-container .footer p a {
        text-decoration: none !important;
    }

    .recommend-content {
        background: #f3f8fd;
        border-radius: 8px;
    }

    .recommend-content .content {
        padding: 20px;
    }

    .recommend-content .recommend-title {
        padding-left: 30px;
        background: url('https://img.gaoxiaojob.com/uploads/static/image/resume/triangle.png') no-repeat left center/22px;
    }

    .recommend-content .big {
        font-weight: bold;
        font-size: 16px;
    }

    .recommend-content .recommend-tips {
        font-size: 13px;
        color: inherit;
        opacity: 0.8;
    }

    .recommend-content .recommend-content {
        margin-top: 15px;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
    }

    .recommend-content .recommend-content .recommend-card {
        box-sizing: border-box;
        margin-bottom: 12px;
        padding: 12px;
        background: #fff;
        border-radius: 8px;
        width: calc((100% - 12px) / 2);
        display: flex;
        color: inherit;
        text-decoration: none !important;
        line-height: 1.15;
    }

    .recommend-content .recommend-content .recommend-card .avatar {
        width: 50px;
        height: 64px;
        flex-shrink: 0;
    }

    .recommend-content .recommend-content .recommend-card .avatar img {
        width: 100%;
        border-radius: 8px;
        object-fit: contain;
    }

    .recommend-content .recommend-content .recommend-card .education {
        margin-top: 10px;
    }

    .recommend-content .recommend-content .recommend-card .want {
        display: block;
        margin-top: 10px;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .recommend-content .recommend-content .recommend-card .detail {
        flex-grow: 1;
        display: block;
        margin-left: 12px;
        font-size: 12px;
        overflow: hidden;
    }

    .recommend-content .recommend-content .recommend-card .detail .top {
        display: flex;
        align-items: center;
        font-size: 14px;
    }

    .recommend-content .recommend-content .recommend-card .detail .gender {
        width: 12px;
        height: 12px;
        margin: 0 6px;
    }

    .recommend-content .recommend-content .recommend-card .detail .tag {
        background: #f6f8ff;
        line-height: 18px;
        border-radius: 4px;
        padding: 0 8px;
        margin-left: 2px;
        font-size: 12px;
        color: #486cf5;
    }

    .recommend-content .recommend-content .recommend-card div {
        display: flex;
    }

    .recommend-content .recommend-content .recommend-card div b {
        background-color: #fff;
        height: 14px;
        font-size: 14px;
        font-weight: bold;
        line-height: 14px;
    }

    .recommend-content .more {
        display: flex;
        justify-content: center;
        margin-top: 8px;
    }

    .recommend-content .more a {
        display: flex;
        position: relative;
        align-items: center;
        font-size: 14px;
        color: #ffa000;
        text-decoration: none !important;
        font-weight: bold;
    }

    .recommend-content .more a::after {
        content: '';
        width: 8px;
        height: 8px;
        border-right: 2px solid #ffa000;
        border-bottom: 2px solid #ffa000;
        transform: rotate(-45deg) scale(0.8);
    }

    .email-wrapper {
        flex-direction: column;
        justify-content: flex-start;
        padding-top: 60px;
        box-sizing: border-box;
    }

    .email-wrapper .notification-container {
        color: #f8651b;
        width: 100%;
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fbf1e8;
        margin-bottom: 16px;
    }

    .email-wrapper .notification-container .notification-content {
        padding-left: 30px;
        background: url('https://img.gaoxiaojob.com/uploads/static/image/resume/notification.png') no-repeat left center/24px;
    }

    .email-body {
        padding-top: 28px;
        font-size: 14px;
        padding-bottom: 18px;
        border-bottom: solid 1px #ebebeb;
    }

    .email-body .hi {
        font-size: 15px;
        font-weight: bold;
    }

    .email-body .position {
        color: #ffa000;
        font-weight: bold;
    }

    .email-body .email-content {
        margin-top: 10px;
        text-indent: 2em;
    }

    .email-body .name {
        font-weight: bold;
    }

    .date {
        margin-top: 18px;
        font-size: 12px;
        display: flex;
        justify-content: space-between;
    }

    .date .recently {
        opacity: 0.6;
    }

    .date .login {
        color: #486cf5;
        font-weight: bold;
    }

    .info {
        padding-left: 20px;
        padding-top: 3px;
    }

    .userinfo {
        display: flex;
        margin: 12px 0 20px;
    }

    .userinfo .tag-orange {
        font-size: 12px;
        line-height: 18px;
        display: inline-block;
        color: #ffa000;
        background-color: #fff3e0;
        border-radius: 4px;
        padding: 0 10px;
        margin-right: 5px;
    }

    .userinfo .userphoto {
        width: 96px;
        height: 120px;
        object-fit: contain;
        border: 1px solid #ebebeb;
        border-radius: 8px;
    }

    .userinfo .user-basic {
        display: flex;
        flex-basis: auto;
        align-items: center;
        min-height: 38px;
        padding: 0;
    }

    .userinfo .user-basic .name {
        float: left;
        font-size: 22px;
        font-weight: bold;
        line-height: 1;
        flex-shrink: 0;
    }

    .userinfo .user-basic .gender {
        margin-bottom: -3px;
        margin-left: 4px;
        margin-right: 4px;
        width: 20px;
        object-fit: contain;
        flex-shrink: 0;
    }

    .userinfo .tag-content {
        display: flex;
        flex-grow: 1;
        flex-wrap: wrap;
    }

    .userinfo .tag-content span {
        margin-bottom: 3px;
        margin-top: 3px;
    }

    .education,
    .contact {
        font-size: 14px;
        line-height: 15px;
        margin-top: 9px;
        margin-bottom: 10px;
    }

    .education .contact-email,
    .education .contact-email a,
    .contact .contact-email,
    .contact .contact-email a {
        color: #486cf5 !important;
    }

    .button {
        width: 152px;
        height: 28px;
        background: #ffa000;
        border-radius: 4px;
        display: flex;
    }

    .button a {
        flex-grow: 1;
        font-size: 14px;
        color: #fff;
        text-align: center;
        line-height: 28px;
    }

    .operate {
        font-size: 14px;
    }

    .operate a {
        display: inline-block;
        text-align: center;
        color: inherit;
        line-height: 26px;
        border: 1px solid #ebebeb;
        height: 28px;
        border-radius: 4px;
        margin-right: 4px;
        transition: all ease 0.1s;
    }

    .operate a.more {
        color: #fff;
        background: #ffa000;
        border-color: #ffa000;
        width: 152px;
        height: 28px;
    }

    .operate a:not(.primary):hover {
        color: #ffa000;
        background-color: #ffeccc;
        border-color: #ffd999;
    }

    .operate a.chat {
        width: 96px;
        background: #fff;
        border: 1px solid #ffa000;
        border-radius: 4px;
        color: #ffa000;
        padding-left: 2px;
        background: url(https://img.gaoxiaojob.com/uploads/static/image/resume/chat-primary.png) no-repeat left 8px center/20px;
    }

    .operate a.chat span {
        margin-left: 20px;
    }


</style>

<body>
<img class="checkedUrl" src="<?= $checkedUrl ?>">
<div class="email-wrapper">
    <!--    <div class="notification-container">-->
    <!--        <div class="notification-content">--><?php //= $headNotice ?><!--</div>-->
    <!--    </div>-->
    <div class="email-container">
        <header class="header">
            <a class="link" target="_blank" href="www.gaoxiaojob.com">高层次人才求职招聘综合服务平台</a>
            <img src="<?= $notes ?>" alt="" class="notes">
        </header>

        <div class="container">
            <?= $html ?>
        </div>

        <footer class="footer">
            <p>此邮件为<a href="<?= $hideSystemUrl ?>" target="_blank" class="hideSystemUrl">系统</a>邮件，<span
                        class="orange">请勿直接回复。</span></p>
            <p>如有疑问或建议，欢迎随时联系我们</p>
            <p>Email：<a href="mailto:<?= $replyEmail ?>" class="blue"><?= $replyEmail ?></a>，服务热线：<a
                        href="tel:<?= $replyPhone ?>" class="orange"><?= $replyPhone ?></a></p>
        </footer>
    </div>
</div>
</body>

</html>
