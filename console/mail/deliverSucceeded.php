<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>

<style>
    html,
    body {
        height: 100%;
        color: #333;
    }
    .email-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100%;
    }
    .email-wrapper .email-container {
        width: 680px;
        flex-shrink: 0;
        border: 1px solid #ebebeb;
        border-radius: 16px;
        margin-top: 50px;
        margin-bottom: 50px;
    }
    .email-wrapper .email-container a {
        text-decoration: none !important;
    }
    .email-wrapper .email-container .common-title {
        font-size: 16px;
        font-weight: bold;
        background-image: url("<?=$triangle ?>");
        background-repeat: no-repeat;
        background-position: left;
        line-height: 1;
        padding: 30px 0 30px 30px;
    }
    .email-wrapper .email-container .header {
        height: 60px;
        background-color: #ffa000;
        position: relative;
        padding-left: 50px;
        border-top-left-radius: 16px;
        border-top-right-radius: 16px;
        display: flex;
        align-items: center;
    }
    .email-wrapper .email-container .header .link {
        height: 33px;
        display: flex;
        align-items: center;
        color: #fff;
        text-decoration: none;
        font-size: 14px;
        padding-left: 137px;
        background: url("//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png")
            no-repeat left center/122px auto;
    }
    .email-wrapper .email-container .header .notes {
        position: absolute;
        right: 20px;
        width: 116px;
        height: 116px;
        display: block;
        bottom: -8px;
    }
    .email-wrapper .email-container .container {
        margin: 21px 21px 0;
        border: 1px dashed #ffa000;
        border-bottom: none;
        padding-left: 29px;
        padding-right: 29px;
        padding-bottom: 30px;
        border-radius: 8px 8px 0px 0px;
    }
    .email-wrapper .email-container .footer {
        color: #333 !important;
        background-color: #f8f8f8;
        padding: 15px 50px;
        border-bottom-left-radius: 16px;
        border-bottom-right-radius: 16px;
    }
    .email-wrapper .email-container .footer .qr-code {
        height: 74px;
        background: url("//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/11.png")
                no-repeat right 140px center/66px,
            url("../assets/showcase.png") no-repeat left center/100% auto;
    }
    .email-wrapper .email-container .footer p {
        margin: 0;
        opacity: 0.8;
        font-size: 12px;
    }
    .email-wrapper .email-container .footer p + p {
        margin-top: 8px;
    }
    .email-wrapper .email-container .footer p .orange {
        color: #fa635c !important;
    }
    .email-wrapper .email-container .footer p .blue {
        color: #486cf5 !important;
    }
    .email-wrapper .email-container .footer p a {
        text-decoration: none !important;
    }
    .email-title {
        margin-top: 30px;
        display: flex;
        align-items: center;
    }
    .email-title img {
        width: 22px;
        object-fit: contain;
    }
    .title {
        font-size: 16px;
        margin-left: 8px;
    }
    .content {
        line-height: 28px;
    }
    .content .email-text {
        margin: 10px 0 0;
        font-size: 14px;
    }
    .content .email-text a {
        color: #ffa000 !important;
        font-weight: bold;
    }
    .content .email-text p {
        margin: 0;
        text-indent: 2em;
    }
</style>

<body>
<div class="email-wrapper">
    <div class="email-container">
        <header class="header">
                <a class="link" target="_blank" href="www.gaoxiaojob.com">高层次人才求职招聘综合服务平台</a>
            <img src="<?= $notes ?>" alt="" class="notes">
        </header>
        <div class="container">
            <div class="email-title"><img src="<?=$triangle ?>"> <b class="title"> 简历投递成功通知</b></div>
            <?= $html ?>
        </div>
        <footer class="footer">
            <p>此邮件为系统邮件，<span class="orange">请勿直接回复。</span></p>
            <p>如有疑问或建议，欢迎随时联系我们</p>
            <p>Email：<a href="mailto:<?= $replyEmail ?>" class="blue"><?= $replyEmail ?></a>，服务热线：<a
                        href="tel:<?= $replyPhone ?>" class="orange"><?= $replyPhone ?></a></p>
        </footer>
    </div>
</div>
</body>

</html>