<style type="text/css">
html,
body {
    height: 100%;
    color: #333;
}
.email-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100%;
}
.email-wrapper .email-container {
    width: 680px;
    flex-shrink: 0;
    border: 1px solid #ebebeb;
    border-radius: 16px;
    margin-top: 50px;
    margin-bottom: 50px;
}
.email-wrapper .email-container a {
    text-decoration: none !important;
}
.email-wrapper .email-container .common-title {
    font-size: 16px;
    font-weight: bold;
    background-image: url("<?=$triangle ?>");
    background-repeat: no-repeat;
    background-position: left;
    line-height: 1;
    padding: 30px 0 30px 30px;
}
.email-wrapper .email-container .header {
    height: 60px;
    background-color: #ffa000;
    position: relative;
    padding-left: 50px;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    display: flex;
    align-items: center;
}
.email-wrapper .email-container .header .link {
    height: 33px;
    display: flex;
    align-items: center;
    color: #fff;
    text-decoration: none;
    font-size: 14px;
    padding-left: 137px;
    background: url("//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png")
        no-repeat left center/122px auto;
}
.email-wrapper .email-container .header .notes {
    position: absolute;
    right: 20px;
    width: 116px;
    height: 116px;
    display: block;
    bottom: -8px;
}
.email-wrapper .email-container .container {
    margin: 21px 21px 0;
    border: 1px dashed #ffa000;
    border-bottom: none;
    padding-left: 29px;
    padding-right: 29px;
    padding-bottom: 30px;
    border-radius: 8px 8px 0px 0px;
}
.email-wrapper .email-container .footer {
    color: #333 !important;
    background-color: #f8f8f8;
    padding: 15px 50px;
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px;
}
.email-wrapper .email-container .footer .qr-code {
    height: 74px;
    background: url("//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/11.png")
            no-repeat right 140px center/66px,
        url("<?= $bottom ?>") no-repeat left center/100% auto;
}
.email-wrapper .email-container .footer p {
    margin: 0;
    opacity: 0.8;
    font-size: 12px;
}
.email-wrapper .email-container .footer p + p {
    margin-top: 8px;
}
.email-wrapper .email-container .footer p .orange {
    color: #fa635c !important;
}
.email-wrapper .email-container .footer p .blue {
    color: #486cf5 !important;
}
.email-wrapper .email-container .footer p a {
    text-decoration: none !important;
}
.email-container {
    font-size: 14px;
}
.email-container a {
    text-decoration: none !important;
}
.email-container .email-body {
    line-height: 28px;
    padding-bottom: 18px;
}
.email-container .email-body .hi {
    padding-top: 28px;
    font-size: 15px;
    font-weight: bold;
}
.email-container .email-body .email-content {
    text-indent: 2em;
}
.email-container .email-body a {
    font-weight: bold;
    text-decoration: none !important;
    color: #ffa000 !important;
}
.email-container .email-body .resume-tips {
    margin-top: 8px;
    line-height: 24px;
}
.email-container .email-body .resume-tips .warning {
    color: #fa635c;
}
.email-container .email-body .resume-tips .wait-perfect {
    color: #ffa000;
}
.email-container .email-body .resume-tips a {
    position: relative;
    display: inline-block;
    align-items: center;
    border-bottom: 1px solid #ffa000;
}
.email-container .email-body .resume-tips a::after {
    position: absolute;
    top: 6px;
    content: "";
    display: inline-block;
    width: 10px;
    height: 10px;
    border-top: 2px solid #ffa000;
    border-right: 2px solid #ffa000;
    transform: translateX(-2px) rotate(45deg) scale(0.7);
}
.email-container .recommend-wrapper {
    background: #f3f8fd;
    border-radius: 8px;
}
.email-container .recommend-wrapper .content {
    padding: 20px;
}
.email-container .recommend-wrapper .recommend-title {
    padding-left: 30px;
    background: url("<?=$triangle ?>") no-repeat left center/22px;
}
.email-container .recommend-wrapper .big {
    font-weight: bold;
    font-size: 16px;
}
.email-container .recommend-wrapper .recommend-tips {
    font-size: 13px;
    line-height: 10px;
    color: inherit;
    opacity: 0.8;
}
.email-container .recommend-wrapper .recommend-content {
    margin-top: 15px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}
.email-container .recommend-wrapper .recommend-content .recommend-card {
    box-sizing: border-box;
    margin-bottom: 12px;
    padding: 12px;
    background: #fff;
    border-radius: 8px;
    width: calc((100% - 12px) / 2);
    color: inherit;
    text-decoration: none !important;
    line-height: 1.15;
}
.email-container
    .recommend-wrapper
    .recommend-content
    .recommend-card
    .job-top {
    display: flex;
}
.email-container
    .recommend-wrapper
    .recommend-content
    .recommend-card
    .job-top
    .job-name {
    font-size: 13px;
    font-weight: bold;
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.email-container
    .recommend-wrapper
    .recommend-content
    .recommend-card
    .job-top
    .release-date {
    font-size: 12px;
    color: #ffa000;
    flex-shrink: 0;
}
.email-container
    .recommend-wrapper
    .recommend-content
    .recommend-card
    .tag-content {
    display: flex;
    flex-wrap: wrap;
    padding: 11px 0;
}
.email-container
    .recommend-wrapper
    .recommend-content
    .recommend-card
    .tag-content
    .tag {
    font-size: 12px;
    line-height: 22px;
    padding: 0 9px;
    color: rgba(51, 51, 51, 0.8);
    background-color: #f4f9ff;
    border-radius: 4px;
}
.email-container
    .recommend-wrapper
    .recommend-content
    .recommend-card
    .tag-content
    .tag
    + .tag {
    margin-left: 6px;
}
.email-container
    .recommend-wrapper
    .recommend-content
    .recommend-card
    .job-bottom {
    display: flex;
    justify-content: space-between;
    font-size: 13px;
    color: rgba(51, 51, 51, 0.8);
}
.email-container
    .recommend-wrapper
    .recommend-content
    .recommend-card
    .job-bottom
    .organizational {
    width: 158px;
    flex-shrink: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.email-container
    .recommend-wrapper
    .recommend-content
    .recommend-card
    .job-bottom
    .address {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.email-container
    .recommend-wrapper
    .recommend-content
    .recommend-card
    .job-bottom
    .address
    img {
    width: 12px;
    margin-bottom: -1px;
    margin-right: 4px;
}
.email-container .recommend-wrapper .more {
    display: flex;
    justify-content: center;
    margin-top: 8px;
}
.email-container .recommend-wrapper .more a {
    width: 152px;
    line-height: 32px;
    text-align: center;
    background-color: #ffa000;
    border-radius: 4px;
    color: #fff;
    text-decoration: none !important;
}
.email-container .footer {
    padding: 0 !important;
    background-color: #fff !important;
}
.email-container .footer .cantact-info {
    opacity: 1 !important;
    line-height: 44px;
    text-align: center;
}
.email-container .footer .cantact-info a {
    color: inherit;
    opacity: 0.6;
}

</style>

<body>
<div class="email-wrapper">
    <div class="email-container">
        <header class="header">
            <a class="link" target="_blank" href="www.gaoxiaojob.com">高层次人才求职招聘综合服务平台</a>
            <img src="<?= $notes ?>" alt="" class="notes">
        </header>

        <?= $html ?>

        <footer class="footer">
            <div class="qr-code"></div>
            <p class="cantact-info">
                此邮件为系统邮件，<span class="orange">请勿直接回复。</span>
                <a href="mailto:<?= $replyEmail ?>">Email：<?= $replyEmail ?>；</a>
                <a href="tel:<?= $replyPhone ?>">服务热线：<?= $replyPhone ?></a>
            </p>
        </footer>
    </div>
</div>
</body>

