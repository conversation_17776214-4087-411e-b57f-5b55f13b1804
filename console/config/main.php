<?php
$params = array_merge(require __DIR__ . '/../../common/config/params.php',
    require __DIR__ . '/../../common/config/params-local.php', require __DIR__ . '/params.php'
//    require __DIR__ . '/params-local.php'
);

return [
    'id'                  => 'app-console',
    'basePath'            => dirname(__DIR__),
    'bootstrap'           => ['log'],
    'controllerNamespace' => 'console\controllers',
    'aliases'             => [
        '@bower' => '@vendor/bower-asset',
        '@npm'   => '@vendor/npm-asset',
    ],
    'modules' => [
        'chat' => [
            'class' => 'console\modules\chat\Module',
        ],
    ],
//    'controllerMap'       => [
//        'fixture' => [
//            'class'     => 'yii\console\controllers\FixtureController',
//            'namespace' => 'common\fixtures',
//        ],
//    ],
    'components'          => [
        'log'   => [
            'targets' => [
                [
                    'class'  => 'yii\log\FileTarget',
                    'levels' => [
                        'error',
                        'warning',
                    ],
                ],
            ],
        ],
        'user'  => [
            'class'           => 'yii\web\User',
            'identityClass'   => 'common\base\models\BaseMember',
            'enableSession'   => false,
            'enableAutoLogin' => false,
        ],
        'request' => [
            'class' => 'console\components\ConsoleRequest',
        ],
    ],
    'params'              => $params,
];
