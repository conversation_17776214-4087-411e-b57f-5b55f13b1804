<?php

namespace queue;

use common\libs\Cache;
use common\service\meilisearch\job\AddService;
use common\service\meilisearch\resume\ResumeLibraryDocumentService;
use yii\base\BaseObject;

class MeilisearchJob extends BaseObject implements \yii\queue\JobInterface
{

    const TYPE_JOB            = 1;
    const TYPE_ANNOUNCEMENT   = 2;
    const TYPE_COMPANY        = 3;
    const TYPE_RESUME_LIBRARY = 4;//人才库

    public $mainId;
    public $type;

    public function execute($queue)
    {
        // 开启内存到1g
        ini_set('memory_limit', '1024M');

        switch ($this->type) {
            case self::TYPE_JOB:
                $this->job();
                break;
            case self::TYPE_ANNOUNCEMENT:
                $this->announcement();
                break;
            case self::TYPE_COMPANY:
                $this->company();
                break;
            case self::TYPE_RESUME_LIBRARY:
                $this->resumeLibrary();
                break;
        }

        return true;
    }

    private function job()
    {
        $id      = $this->mainId;
        $service = new AddService();
        $service->saveById($id);
    }

    private function announcement()
    {
        $id      = $this->mainId;
        $service = new \common\service\meilisearch\announcement\SimpleAddService();
        $service->saveById($id);
    }

    private function company()
    {
        $id      = $this->mainId;
        $service = new \common\service\meilisearch\company\AddService();
        $service->saveById($id);
    }

    private function resumeLibrary()
    {
        // 改为写入Redis Set，避免重复写入meilisearch
        // 使用Set数据结构自动去重，相同的resumeId只会保留一个
        Cache::sAdd(Cache::MEILISEARCH_RESUME_PENDING_QUEUE, $this->mainId);

        // 原来的直接写入逻辑，暂时注释
        // $service = new ResumeLibraryDocumentService();
        // $service->addOrUpdateDocument($this->mainId);
    }

}