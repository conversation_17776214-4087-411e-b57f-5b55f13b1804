<?php

namespace admin\models;

use common\base\models\BaseAdminDownloadTask;
use common\base\models\BaseResumeOrderSnapshot;
use common\helpers\ArrayHelper;
use common\libs\Excel;
use common\service\downloadTask\DownLoadTaskApplication;
use Yii;
use common\base\BaseActiveRecord;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEquityPackageCategorySetting;
use common\base\models\BaseResumeEquityPackageSetting;
use common\base\models\BaseResumeOrder;
use common\helpers\FormatConverter;
use common\helpers\TimeHelper;
use common\helpers\UUIDHelper;
use yii\base\Exception;

/**
 * 订单业务model
 */
class Order
{
    /**
     * 根据参数条件构建订单列表数据query
     * @param $params
     * @return \yii\db\ActiveQuery
     * @throws \yii\base\Exception
     */
    public static function getBuilderReusmeOrderQuery($params)
    {
        //构建数据DB句柄
        $query = BaseResumeOrder::find()
            ->alias('ro')
            ->innerJoin(['reps' => BaseResumeEquityPackageSetting::tableName()], 'ro.equity_package_id=reps.id')
            ->innerJoin(['repcs' => BaseResumeEquityPackageCategorySetting::tableName()],
                'reps.equity_package_category_id=repcs.id')
            ->leftJoin(['s' => BaseResumeOrderSnapshot::tableName()], 's.order_id = ro.id')
            ->leftJoin(['r' => BaseResume::tableName()], 'ro.resume_id=r.id');
        //构建查询条件--根据params变量
        //订单号
        if (isset($params['order_no']) && $params['order_no']) {
            $query->andWhere(['ro.order_no' => $params['order_no']]);
        }
        //流水号
        if (isset($params['trade_no']) && $params['trade_no']) {
            $query->andWhere(['ro.trade_no' => $params['trade_no']]);
        }
        //产品ID
        if (isset($params['equity_package_id']) && $params['equity_package_id']) {
            $query->andWhere(['ro.equity_package_id' => $params['equity_package_id']]);
        }
        //产品名称-模糊匹配
        if (isset($params['equity_package_name']) && $params['equity_package_name']) {
            $query->andWhere([
                'like',
                's.equity_package_name',
                $params['equity_package_name'],
            ]);
        }
        //手机号
        if (isset($params['mobile']) && $params['mobile']) {
            //链接member表
            $query->innerJoin(['m' => BaseMember::tableName()], 'r.member_id=m.id');
            $query->andWhere(['m.mobile' => $params['mobile']]);
        }
        //简历ID
        if (isset($params['resume_id']) && $params['resume_id']) {
            //简历ID解密
            $resume_id = UUIDHelper::decryption($params['resume_id']);
            if ($resume_id) {
                $query->andWhere(['ro.resume_id' => $resume_id]);
            }
        }
        //简历名称-模糊匹配
        if (isset($params['resume_name']) && $params['resume_name']) {
            $query->andWhere([
                'like',
                'r.name',
                $params['resume_name'],
            ]);
        }
        //产品类型
        if (isset($params['equity_package_category_id']) && $params['equity_package_category_id']) {
            $query->andWhere(['reps.equity_package_category_id' => $params['equity_package_category_id']]);
        }
        //支付状态
        if (isset($params['status']) && strlen($params['status']) > 0) {
            $query->andWhere(['ro.status' => $params['status']]);
        }
        //支付方式
        if (isset($params['payway']) && $params['payway']) {
            $query->andWhere(['ro.payway' => $params['payway']]);
        }
        //支付时间-开始时间与结束时间between
        if (isset($params['pay_time_start']) && $params['pay_time_start'] && isset($params['pay_time_end']) && $params['pay_time_end']) {
            $query->andWhere([
                'between',
                'ro.pay_time',
                TimeHelper::dayToBeginTime($params['pay_time_start']),
                TimeHelper::dayToEndTime($params['pay_time_end']),
            ]);
        }
        //下单平台
        if (isset($params['platform']) && $params['platform']) {
            $query->andWhere(['ro.platform' => $params['platform']]);
        }
        //下单时间-开始时间与结束时间between
        if (isset($params['add_time_start']) && $params['add_time_start'] && isset($params['add_time_end']) && $params['add_time_end']) {
            $query->andWhere([
                'between',
                'ro.add_time',
                TimeHelper::dayToBeginTime($params['add_time_start']),
                TimeHelper::dayToEndTime($params['add_time_end']),
            ]);
        }

        return $query;
    }

    /**
     * 获取求职者订单列表数据
     * @param $params
     * @return array
     * @throws Exception
     */
    public static function getResumeOrderList($params)
    {
        //$params--驼峰转成下划线格式
        $params = FormatConverter::convertHump($params);
        //构建数据DB句柄
        $query = self::getBuilderReusmeOrderQuery($params);
        //统计数据句柄
        $total_query = clone $query;
        $total       = $total_query->select([
            //已支付的数量
            'SUM(IF(ro.status=1,1,0)) AS payed_count',
            //已支付的真实金额
            'SUM(IF(ro.status=1,ro.real_amount,0)) AS payed_amount',
        ])
            ->asArray()
            ->one();
        //释放句柄
        unset($total_query);
        //总数句柄
        $count_query = clone $query;
        $count       = $count_query->count();
        //释放句柄
        unset($count_query);
        //获取分页列表数据
        ////排序-下单时间倒叙
        $query->orderBy('ro.add_time DESC');
        ////分页参数
        $page_size = $params['page_size'] ?: Yii::$app->params['defaultPageSize'];
        $pages     = BaseActiveRecord::setPage($count, $params['page'], $page_size);
        $list      = $query->select([
            //订单ID
            'ro.id',
            //订单号
            'ro.order_no',
            //流水号
            'ro.trade_no',
            //产品ID
            'ro.equity_package_id',
            //产品名称
            's.equity_package_name',
            //产品类型
            'reps.equity_package_category_id',
            //产品类型名称
            'repcs.name AS equity_package_category_name',
            //简历ID
            'ro.resume_id',
            //简历名称
            'r.name AS resume_name',
            //支付状态
            'ro.status',
            //支付方式
            'ro.payway',
            //支付时间
            'ro.pay_time',
            //添加时间
            'ro.add_time',
            //支付金额
            'ro.real_amount',
            //下单平台
            'ro.platform',
            // 流水号
            'ro.trade_no',
            //下单时间
            'ro.add_time',
            //快照
            'ro.snapshot_data',
        ])
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->asArray()
            ->all();
        //列表数据循环处理
        foreach ($list as &$item) {
            //简历ID加密
            $item['resume_id'] = UUIDHelper::encrypt(UUIDHelper::TYPE_PERSON, $item['resume_id']);
            //支付时间
            $item['pay_time'] = $item['pay_time'] && $item['pay_time'] != '0000-00-00 00:00:00' ? $item['pay_time'] : '';
            //下单时间
            $item['add_time'] = $item['add_time'] && $item['add_time'] != '0000-00-00 00:00:00' ? $item['add_time'] : '';
            //订单状态
            $item['status_name'] = isset(BaseResumeOrder::STATUS_LIST[$item['status']]) ? BaseResumeOrder::STATUS_LIST[$item['status']] : '';
            //支付方式
            $item['payway_name'] = isset(BaseResumeOrder::PAYWAY_LIST[$item['payway']]) ? BaseResumeOrder::PAYWAY_LIST[$item['payway']] : '';
            //下单平台
            $item['platform_name'] = isset(BaseResumeOrder::PLATFORM_LIST[$item['platform']]) ? BaseResumeOrder::PLATFORM_LIST[$item['platform']] : '';
            //订单快照数据
            $snapshot_data = json_decode($item['snapshot_data'], true);
            if (isset($snapshot_data['convert_data']) && isset($snapshot_data['vip_grade']) == true) {
                $item['discounts_amount'] = $snapshot_data['convert_data']['convert_price'];
            } else {
                $item['discounts_amount'] = 0;
            }
            unset($item['snapshot_data']);
        }

        //返回数据
        return [
            'list'  => $list,
            'total' => $total,
            'pages' => [
                'total' => (int)$count,
                'limit' => (int)$page_size,
                'page'  => (int)$pages['page'],
            ],
        ];
    }

    // 获取订单列表的参数部分
    public static function getResumeOrderListParams()
    {
        return [
            // 产品类型
            'queryTypeOptions' => ArrayHelper::object2LV(BaseResumeEquityPackageCategorySetting::find()
                ->select([
                    'id',
                    'name',
                ])
                ->asArray()
                ->all()),
            // 下单渠道
            'platformOptions'  => ArrayHelper::array2LV(BaseResumeOrder::PLATFORM_LIST),
            // 支付方式
            'paywayOptions'    => ArrayHelper::array2LV(BaseResumeOrder::PAYWAY_LIST),
            // 支付状态
            'statusOptions'    => ArrayHelper::array2LV(BaseResumeOrder::STATUS_LIST),
        ];
    }

    /**
     * 获取求职者订单列表导出数据
     * @param $params
     * @return array
     * @throws Exception
     */
    public static function getResumeOrderListExport($params)
    {
        //$params--驼峰转成下划线格式
        $params  = FormatConverter::convertHump($params);
        $adminId = Yii::$app->user->id;
        $app     = DownLoadTaskApplication::getInstance();
        $app->createAdmin($adminId, BaseAdminDownloadTask::TYPE_ORDER_PERSON_LIST, $params);

        return true;
        //        //构建数据DB句柄
        //        $query = self::getBuilderReusmeOrderQuery($params);
        //        //获取分页列表数据
        //        ////排序-下单时间倒叙
        //        $query->orderBy('ro.add_time DESC');
        //        $list = $query->select([
        //            //订单ID
        //            'ro.id',
        //            //订单号
        //            'ro.order_no',
        //            //流水号
        //            'ro.trade_no',
        //            //产品ID
        //            'ro.equity_package_id',
        //            //产品名称
        //            'reps.name AS equity_package_name',
        //            //产品类型
        //            'reps.equity_package_category_id',
        //            //产品类型名称
        //            'repcs.name AS equity_package_category_name',
        //            //简历ID
        //            'ro.resume_id',
        //            //简历名称
        //            'r.name AS resume_name',
        //            //支付状态
        //            'ro.status',
        //            //支付方式
        //            'ro.payway',
        //            //支付时间
        //            'ro.pay_time',
        //            //添加时间
        //            'ro.add_time',
        //            //支付金额
        //            'ro.real_amount',
        //            //下单平台
        //            'ro.platform',
        //            //下单时间
        //            'ro.add_time',
        //        ])
        //            ->asArray()
        //            ->all();
        //        //导出头部文本定义
        //        $header = [
        //            '订单号',
        //            '产品ID',
        //            '产品名称',
        //            '产品类型',
        //            '用户ID',
        //            '姓名',
        //            '支付金额',
        //            '支付状态',
        //            '支付方式',
        //            '下单时间',
        //            '支付时间',
        //            '流水号',
        //            '下单渠道',
        //        ];
        //        $data   = [];
        //        //列表数据循环处理
        //        foreach ($list as &$item) {
        //            $data[] = [
        //                //订单号
        //                $item['order_no'],
        //                //产品ID
        //                $item['equity_package_id'],
        //                //产品名称
        //                $item['equity_package_name'],
        //                //产品类型
        //                $item['equity_package_category_name'],
        //                //用户ID
        //                $item['resume_id'],
        //                //姓名
        //                $item['resume_name'],
        //                //支付金额
        //                $item['real_amount'],
        //                //支付状态
        //                isset(BaseResumeOrder::STATUS_LIST[$item['status']]) ? BaseResumeOrder::STATUS_LIST[$item['status']] : '',
        //                //支付方式
        //                isset(BaseResumeOrder::PAYWAY_LIST[$item['payway']]) ? BaseResumeOrder::PAYWAY_LIST[$item['payway']] : '',
        //                //下单时间
        //                $item['add_time'] && $item['add_time'] != '0000-00-00 00:00:00' ? $item['add_time'] : '',
        //                //支付时间
        //                $item['pay_time'] && $item['pay_time'] != '0000-00-00 00:00:00' ? $item['pay_time'] : '',
        //                //流水号
        //                $item['trade_no'],
        //                //下单渠道
        //                isset(BaseResumeOrder::PLATFORM_LIST[$item['platform']]) ? BaseResumeOrder::PLATFORM_LIST[$item['platform']] : '',
        //            ];
        //        }
        //        //释放资源
        //        unset($list);
        //        //导出
        //        $excel    = new Excel();
        //        $fileName = $excel->export($data, $header);
        //
        //        return [
        //            'excel_url' => $fileName,
        //        ];
    }

    /**
     * 获取求职者订单详情数据
     * @param $params
     * @return array
     * @throws Exception
     */
    public static function getResumeOrderDetail($orderId)
    {
        //先去看下订单是否存在
        $order_info = BaseResumeOrder::getDetail($orderId);
        //获取用户手机号码
        $order_info['resume_info']['resume_mobile'] = BaseMember::findOneVal(['id' => $order_info['resume_info']['member_id']],
            'mobile');
        //获取产品信息
        $order_info['equity_package_info'] = BaseResumeEquityPackageSetting::getDetail($order_info['equity_package_id']);
        //获取订单快照
        $order_info['snapshotInfo'] = BaseResumeOrderSnapshot::getInfo($orderId);

        return $order_info;
    }

    /**
     * 保存订单备注
     * @param $orderId
     * @param $remark
     * @return bool
     * @throws Exception
     */
    public static function setOrderRemark($orderId, $remark)
    {
        //备注不允许为空且长度不超过200
        if (mb_strlen($remark) > 200) {
            throw new Exception('长度不超过200');
        }
        //先去看下订单是否存在
        $order_info = BaseResumeOrder::findOne($orderId);
        if (!$order_info) {
            throw new Exception('订单不存在');
        }
        //更新订单备注
        $order_info->remark = $remark ?: '';
        //保存
        $order_info->save();

        return true;
    }

}