<?php

namespace admin\models;

use common\base\models\BaseCompanyGroup;
use common\base\models\BaseCompanyGroupRelation;
use common\base\models\BaseCompanyInfoAuth;
use common\base\models\BaseCompanyMemberConfig;
use common\base\models\BaseCompanyMemberMessageConfig;
use common\base\models\BaseCompanyMemberOperationLog;
use common\base\models\BaseMemberLoginForm;
use common\helpers\FileHelper;
use common\helpers\IpHelper;
use common\service\ForceLogoutService;
use Yii;
use common\base\BaseActiveRecord;
use common\base\models\BaseAdmin;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseMember;
use common\helpers\FormatConverter;
use common\helpers\TimeHelper;
use common\helpers\UUIDHelper;
use common\helpers\ValidateHelper;
use yii\base\Exception;

class CompanyAccount
{
    /**
     * 获取账号列表
     * @param $params
     * @return array
     * @throws \yii\base\Exception
     */
    public static function index($params)
    {
        //将参数格式化为下划线格式
        $params = FormatConverter::convertHump($params);
        //query句柄
        $build_query = BaseCompanyMemberInfo::find()
            ->alias('cmi')
            ->innerJoin(['m' => BaseMember::tableName()], 'cmi.member_id = m.id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'cmi.company_id = c.id')
            ->leftJoin(['a' => BaseAdmin::tableName()], 'cmi.create_id=a.id and cmi.source_type=2')
            ->leftJoin(['cmii' => BaseCompanyMemberInfo::tableName()],
                'cmi.create_id=cmii.member_id and cmi.source_type=1')
            ->leftJoin(['cgr' => BaseCompanyGroupRelation::tableName()], 'cgr.company_id=c.id')
            ->leftJoin(['cia' => BaseCompanyInfoAuth::tableName()], 'cia.company_id = c.id')
            ->where([
                'c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
                // 只展示合作单位的账号
            ]);

        //单位群组
        if ($params['group_ids']) {
            $groupIdsArr = explode(',', $params['group_ids']);
            $build_query->andFilterWhere(['cgr.group_id' => $groupIdsArr]);
        }

        //套餐类型
        $build_query->andFilterWhere([
            'c.package_type' => $params['package_type'],
        ]);

        //筛选条件
        //姓名-联系人
        $build_query->andFilterWhere([
            'like',
            'cmi.contact',
            $params['contact'],
        ]);
        //账号ID
        $build_query->andFilterWhere(['m.id' => $params['member_id']]);
        //用户名
        $build_query->andFilterWhere([
            'like',
            'm.username',
            $params['username'],
        ]);
        //单位ID-解密
        $build_query->andFilterWhere(['c.id' => UUIDHelper::decryption($params['company_id'])]);
        //单位名称
        if ($params['company_name']) {
            $build_query->andFilterWhere([
                'or',
                [
                    'like',
                    'c.full_name',
                    $params['company_name'],
                ],
                [
                    'like',
                    'cia.full_name',
                    $params['company_name'],
                ],
            ]);
        }

        //手机号
        $build_query->andFilterWhere(['m.mobile' => $params['mobile']]);
        //邮箱
        $build_query->andFilterWhere(['m.email' => $params['email']]);
        //账号类型
        if (mb_strlen($params['company_member_type']) > 0 && array_key_exists($params['company_member_type'],
                BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_LIST)) {
            $build_query->andWhere(['cmi.company_member_type' => $params['company_member_type']]);
        }
        //账号状态
        if (array_key_exists($params['status'], BaseMember::COMPANY_ACCOUNTS_STATUS_LIST)) {
            $build_query->andWhere(['m.status' => $params['status']]);
        }
        //账号权限
        if (array_key_exists($params['member_rule'], BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_LIST)) {
            $build_query->andWhere(['cmi.member_rule' => $params['member_rule']]);
        }
        //账号来源
        if (array_key_exists($params['source_type'], BaseCompanyMemberInfo::SOURCE_TYPE_LIST)) {
            $build_query->andWhere(['cmi.source_type' => $params['source_type']]);
        }
        //创建人
        //$build_query->andFilterWhere(['cmi.create_id' => $params['create_id']]);
        if ($params['create_keyword']) {
            $build_query->andWhere([
                'or',
                [
                    'like',
                    'cmii.contact',
                    $params['create_keyword'],
                ],
                [
                    'like',
                    'a.name',
                    $params['create_keyword'],
                ],
            ]);
        }
        //创建时间
        if ($params['add_time_start'] && $params['add_time_end']) {
            $build_query->andFilterWhere([
                'between',
                'm.add_time',
                TimeHelper::dayToBeginTime($params['add_time_start']),
                TimeHelper::dayToEndTime($params['add_time_end']),
            ]);
        }
        //最后登录时间
        if ($params['last_login_time_start'] && $params['last_login_time_end']) {
            $build_query->andFilterWhere([
                'between',
                'm.last_login_time',
                TimeHelper::dayToBeginTime($params['last_login_time_start']),
                TimeHelper::dayToEndTime($params['last_login_time_end']),
            ]);
        }
        //最后活跃时间
        if ($params['last_active_time_start'] && $params['last_active_time_end']) {
            $build_query->andFilterWhere([
                'between',
                'm.last_active_time',
                TimeHelper::dayToBeginTime($params['last_active_time_start']),
                TimeHelper::dayToEndTime($params['last_active_time_end']),
            ]);
        }
        //是否绑定
        if (array_key_exists($params['is_wx_bind'], BaseCompanyMemberInfo::IS_WX_BIND_LIST)) {
            $build_query->andWhere(['cmi.is_wx_bind' => $params['is_wx_bind']]);
        }
        $build_query->groupBy('cmi.id');
        $total = $build_query->count();
        $build_query->select([
            'm.id as member_id',
            'm.username',
            'm.status',
            'm.email',
            'm.add_time',
            'm.last_login_time',
            'm.last_active_time',
            'm.last_login_ip',
            'c.id as company_id',
            'c.status as company_status',
            'c.package_type as company_package_type',
            'c.full_name as company_table_name',
            // 单位表中的名称
            'cmi.id',
            'cmi.contact',
            'cmi.department',
            'cmi.member_rule',
            'cmi.company_member_type',
            'cmi.create_id',
            'cmi.source_type',
            'cmi.is_wx_bind',
            'cmii.contact as create_contact',
            'cmii.department as create_department',
            'a.name as create_name',
            'c.package_type',
            'cia.audit_status',
            'cia.full_name as auth_table_name',
            // 审核表中的名称
        ]);
        //获取分页
        $page_size = $params['page_size'] ?: \Yii::$app->params['defaultPageSize'];
        $pages     = BaseActiveRecord::setPage($total, $params['page'], $page_size);
        $list      = $build_query->orderBy('m.add_time desc')
            ->limit($pages['limit'])
            ->offset($pages['offset'])
            ->asArray()
            ->all();
        //处理数据
        $companyIds = array_column($list, 'company_id');
        foreach ($list as &$item) {
            $item['status_text']              = BaseMember::COMPANY_ACCOUNTS_STATUS_LIST[$item['status']];
            $item['company_member_type_text'] = BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_LIST[$item['company_member_type']];
            $item['source_type_text']         = BaseCompanyMemberInfo::SOURCE_TYPE_LIST[$item['source_type']];
            $item['member_rule_text']         = BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_LIST[$item['member_rule']];
            $item['package_type']             = BaseCompany::PACKAGE_TYPE_LIST[$item['package_type']] ?? '无';

            // 添加审核状态信息
            $item['company_audit_status']      = $item['audit_status'];
            $item['company_audit_status_text'] = BaseCompanyInfoAuth::AUDIT_STATUS_LIST[$item['audit_status']] ?? '未知状态';

            // 根据审核状态选择正确的单位名称
            $item['company_name'] = self::getCorrectCompanyName($item['company_status'], $item['audit_status'],
                $item['company_table_name'], $item['auth_table_name']);

            // 判断是否可编辑（未终审通过且非待审核状态）
            $item['can_edit_company_name'] = self::canEditCompanyName($item['company_status'], $item['audit_status']);
            $item['can_edit_account']      = self::canEditAccount($item['company_status'], $item['audit_status']);
            //根据source_type获取创建人信息
            if ($item['source_type'] == BaseCompanyMemberInfo::SOURCE_TYPE_SELF) {
                //自建
                $item['create_person_name'] = $item['create_contact'] ?: '';
                if ($item['create_department']) {
                    $item['create_person_name'] .= '（' . $item['create_department'] . '）';
                }
                $item['create_person_department'] = $item['create_department'] ?: '';
            } else {
                //运营
                $item['create_person_name']       = $item['create_name'] ?: '';
                $item['create_person_department'] = '';
            }
            if ($item['company_package_type'] == BaseCompany::PACKAGE_TYPE_SENIOR && $item['member_rule'] == BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_VIP) {
                $expire_time                = BaseCompanyPackageConfig::findOneVal([
                    'company_id' => $item['company_id'],
                    'status'     => BaseCompanyPackageConfig::STATUS_ACTIVE,
                ], 'expire_time');
                $item['company_vip_expire'] = $expire_time ? date('Y.m.d', strtotime($expire_time)) . '过期' : '';
            } else {
                $item['company_vip_expire'] = '';
            }
            //微信是否绑定文案
            $item['is_bind_wechat_text'] = BaseCompanyMemberInfo::IS_WX_BIND_LIST[$item['is_wx_bind']];

            //释放
            unset($item['create_contact'], $item['create_department'], $item['create_name']);
            // 登录所在地
            $item['last_login_area'] = IpHelper::getFullAreaByIntIp($item['last_login_ip']);
        }

        if ($companyIds) {
            $groupNameList = BaseCompanyGroupRelation::find()
                ->alias('cgr')
                ->select([
                    "GROUP_CONCAT(cg.group_name SEPARATOR ', ') AS group_names",
                    'cgr.company_id',
                ])
                ->leftJoin(['cg' => BaseCompanyGroup::tableName()], 'cg.id=cgr.group_id')
                ->where([
                    'in',
                    'cgr.company_id',
                    $companyIds,
                ])
                ->groupBy('cgr.company_id')
                ->asArray()
                ->all();

            $groupNameList = array_column($groupNameList, 'group_names', 'company_id');

            foreach ($list as &$item) {
                $item['group_names'] = $groupNameList[$item['company_id']] ?? '';
            }
        }

        $data = [
            'list'  => $list,
            'pages' => [
                'page_size' => (int)$page_size,
                'page'      => (int)$pages['page'],
                'total'     => (int)$total,
            ],
        ];

        return $data;
    }

    /**
     * 获取账号详情
     */
    public static function detail($id)
    {
        $info = BaseCompanyMemberInfo::find()
            ->alias('cmi')
            ->innerJoin(['m' => BaseMember::tableName()], 'cmi.member_id = m.id')
            ->innerJoin(['c' => BaseCompany::tableName()], 'cmi.company_id = c.id')
            ->leftJoin(['cmmc' => BaseCompanyMemberMessageConfig::tableName()], 'cmmc.member_id = cmi.member_id')
            ->leftJoin(['a' => BaseAdmin::tableName()], 'cmi.create_id=a.id and cmi.source_type=2')
            ->leftJoin(['cmii' => BaseCompanyMemberInfo::tableName()],
                'cmi.create_id=cmii.member_id and cmi.source_type=1')
            ->leftJoin(['cia' => BaseCompanyInfoAuth::tableName()], 'cia.company_id = c.id')
            ->where(['cmi.id' => $id])
            ->select([
                'm.id as member_id',
                'm.username',
                'm.status',
                'm.mobile',
                'm.email',
                'm.avatar',
                'm.add_time',
                'm.last_login_time',
                'm.is_chat',
                'c.id as company_id',
                'c.status as company_status',
                'c.full_name as company_table_name',
                // 单位表中的名称
                'c.package_type as company_package_type',
                'cmi.id',
                'cmi.contact',
                'cmi.department',
                'cmi.member_rule',
                'cmi.company_member_type',
                'cmi.create_id',
                'cmi.source_type',
                'cmi.is_wx_bind',
                'cmii.contact as create_contact',
                'cmii.department as create_department',
                'a.name as create_name',
                'cmmc.id as message_id',
                'cmmc.delivery_message',
                'cia.audit_status',
                'cia.full_name as auth_table_name',
                // 审核表中的名称
            ])
            ->asArray()
            ->one();
        if (!$info) {
            return [];
        }
        $info['avatar']                   = $info['avatar'] ? FileHelper::getFullUrl($info['avatar']) : '';
        $info['status_text']              = BaseMember::COMPANY_ACCOUNTS_STATUS_LIST[$info['status']];
        $info['company_member_type_text'] = BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_LIST[$info['company_member_type']];
        $info['source_type_text']         = BaseCompanyMemberInfo::SOURCE_TYPE_LIST[$info['source_type']];
        $info['member_rule_text']         = BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_LIST[$info['member_rule']];
        //根据source_type获取创建人信息
        if ($info['source_type'] == BaseCompanyMemberInfo::SOURCE_TYPE_SELF) {
            //自建
            $info['create_person_name']       = $info['create_contact'] ?: '';
            $info['create_person_department'] = $info['create_department'] ?: '';
        } else {
            //运营
            $info['create_person_name']       = $info['create_name'] ?: '';
            $info['create_person_department'] = '';
        }
        if ($info['company_package_type'] == BaseCompany::PACKAGE_TYPE_SENIOR && $info['member_rule'] == BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_VIP) {
            $expire_time                = BaseCompanyPackageConfig::findOneVal([
                'company_id' => $info['company_id'],
                'status'     => BaseCompanyPackageConfig::STATUS_ACTIVE,
            ], 'expire_time');
            $info['company_vip_expire'] = $expire_time ? date('Y.m.d', strtotime($expire_time)) . '过期' : '';
        } else {
            $info['company_vip_expire'] = '';
        }
        //增加微信未绑定固定文案
        $info['wx_bind_text'] = BaseCompanyMemberInfo::IS_WX_BIND_LIST[$info['is_wx_bind']];

        // 添加审核状态信息
        $info['company_audit_status']      = $info['audit_status'];
        $info['company_audit_status_text'] = BaseCompanyInfoAuth::AUDIT_STATUS_LIST[$info['audit_status']] ?? '未知状态';

        // 根据审核状态选择正确的单位名称
        $info['company_name'] = self::getCorrectCompanyName($info['company_status'], $info['audit_status'],
            $info['company_table_name'], $info['auth_table_name']);

        // 判断是否可编辑
        $info['can_edit_company_name'] = self::canEditCompanyName($info['company_status'], $info['audit_status']);
        $info['can_edit_account']      = self::canEditAccount($info['company_status'], $info['audit_status']);

        unset($info['create_contact'], $info['create_department'], $info['create_name']);

        return $info;
    }

    /**
     * 添加子账号
     */
    public static function add($params)
    {
        //绑定邮箱
        if (empty($params['email'])) {
            throw new Exception('绑定邮箱不能为空');
        }
        //判断选择单位ID长度是否合法
        if (empty($params['company_id'])) {
            throw new Exception('请选择单位');
        }
        //子账号权限
        if (mb_strlen($params['member_rule']) <= 0) {
            throw new Exception('请选择子账号权限');
        }
        //验证数据合法性
        self::validateCommon($params);
        //邮箱格式
        if (!empty($params['email'])) {
            if (!ValidateHelper::isEmail($params['email'])) {
                throw new Exception('邮箱格式错误');
            }
            $inEmail = BaseMember::findOne([
                'email'                 => $params['email'],
                'type'                  => BaseMember::TYPE_COMPANY,
                'email_register_status' => BaseMember::EMAIL_REGISTER_STATUS_NORMAL,
            ]);
            if ($inEmail) {
                throw new Exception('该邮箱已被使用');
            }
        }
        //手机号格式
        if (!empty($params['mobile'])) {
            if (!ValidateHelper::isMobileCN($params['mobile'])) {
                throw new Exception('联系人手机号格式错误');
            }
            $inMobile = BaseMember::findOne([
                'mobile' => $params['mobile'],
                'type'   => BaseMember::TYPE_COMPANY,
            ]);
            if ($inMobile) {
                throw new Exception('该手机号码已被使用');
            }
        }
        //获取单位信息
        $company_info = BaseCompany::findOne(['id' => $params['company_id']]);
        //获取单位主账号信息
        $member_info = BaseMember::findOne(['id' => $company_info->member_id]);
        //主账号禁用不能创建子账号
        if ($member_info->status == BaseMember::STATUS_ILLEGAL) {
            throw new Exception('该单位主账号已被禁用，无法创建子账号');
        }
        //获取该单位的配置信息
        $config_info = BaseCompanyMemberConfig::findOne(['company_id' => $params['company_id']]);
        //先判断该单位是否有余量子账号
        if ($config_info->available == 0) {
            throw new Exception('该单位子账号已用完');
        }

        if ($params['member_rule'] == BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_VIP && $config_info->vip_available == 0) {
            throw new Exception('该单位VIP授权已用完，无法创建VIP子账号');
        }
        //写入会员表member
        $member = new BaseMember();
        $member->setAttributes([
            'add_time'              => CUR_DATETIME,
            'type'                  => BaseMember::TYPE_COMPANY,
            'mobile'                => $params['mobile'] ?: '',
            'mobile_code'           => $params['mobileCode'] ?: BaseMemberLoginForm::DEFAULT_MOBILE_CODE,
            'username'              => BaseMember::createUserName(),
            'password'              => BaseMember::COMPANY_USER_PASSWORD,
            'email'                 => $params['email'],
            'email_register_status' => BaseMember::EMAIL_REGISTER_STATUS_NORMAL,
            'status'                => BaseMember::STATUS_ACTIVE,
            'source_type'           => BaseMember::SOURCE_TYPE_PC,
            'company_member_type'   => BaseMember::COMPANY_MEMBER_TYPE_SUB,
        ]);
        //写入会员表member
        if (!$member->save()) {
            throw new Exception($member->getFirstErrorsMessage());
        }
        //写入账号信息表
        $companyMemberInfo = new BaseCompanyMemberInfo();
        $companyMemberInfo->setAttributes([
            'member_id'           => $member->id,
            'company_id'          => $params['company_id'],
            'contact'             => $params['contact'],
            'department'          => $params['department'],
            'member_rule'         => $params['member_rule'],
            'company_member_type' => BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_SUB,
            'source_type'         => BaseCompanyMemberInfo::SOURCE_TYPE_OPERATION,
            'create_id'           => Yii::$app->user->id,
            'is_wx_bind'          => BaseCompanyMemberInfo::IS_WX_BIND_NO,
        ]);
        if (!$companyMemberInfo->save()) {
            throw new Exception($companyMemberInfo->getFirstErrorsMessage());
        }
        //对单位的配置数量进行+1 -1操作
        $update = [
            'available' => -1,
            'used'      => 1,
        ];
        if ($params['member_rule'] == BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_VIP) {
            $update['vip_available'] = -1;
            $update['vip_used']      = 1;
        }
        BaseCompanyMemberConfig::updateAllCounters($update, ['company_id' => $params['company_id']]);
        //日志
        $data = [
            'member_id'         => $companyMemberInfo->member_id,
            'company_id'        => $companyMemberInfo->company_id,
            'type'              => BaseCompanyMemberOperationLog::OPERATION_TYPE_CREATE_SUB,
            'operation_id'      => Yii::$app->user->id,
            'operation_port'    => BaseCompanyMemberOperationLog::OPERATION_PORT_ADMIN,
            'operation_content' => '创建子账号，获得权限：' . BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_LIST[$params['member_rule']],
        ];
        BaseCompanyMemberOperationLog::addLog($data);

        return $member->id;
    }

    /**
     * 编辑账号
     */
    public static function edit($params)
    {
        //绑定邮箱与手机号码不能同时为空
        if (empty($params['id'])) {
            throw new Exception('参数错误');
        }
        $companyMemberInfo = BaseCompanyMemberInfo::findOne($params['id']);
        if (!$companyMemberInfo) {
            throw new Exception('参数错误');
        }
        //获取账号信息
        $member = BaseMember::findOne($companyMemberInfo->member_id);
        if (!$member) {
            throw new Exception('参数错误');
        }

        // 获取单位信息
        $company = BaseCompany::findOne($companyMemberInfo->company_id);
        if (!$company) {
            throw new Exception('单位信息不存在');
        }

        // 获取最新的审核状态
        $companyInfoAuth = BaseCompanyInfoAuth::find()
            ->where(['company_id' => $company->id])
            ->orderBy('id desc')
            ->one();

        $auditStatus = $companyInfoAuth ? $companyInfoAuth->audit_status : null;

        // 检查是否可以编辑
        if (!self::canEditAccount($company->status, $auditStatus)) {
            throw new Exception('该单位当前待审核，不可修改信息');
        }

        $params['member_id'] = $companyMemberInfo->member_id;
        if ($companyMemberInfo->company_member_type == BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_SUB) {
            //绑定邮箱
            if (empty($params['email'])) {
                throw new Exception('绑定邮箱不能为空');
            }
        } else {
            //绑定邮箱与手机号码不能同时为空
            if (empty($params['email']) && empty($params['mobile'])) {
                throw new Exception('绑定邮箱与手机号码不能同时为空');
            }
        }
        //验证数据合法性
        self::validateCommon($params);
        //邮箱格式
        if (!empty($params['email'])) {
            if (!ValidateHelper::isEmail($params['email'])) {
                throw new Exception('邮箱格式错误');
            }
            if ($member->email != $params['email']) {
                $inEmail = BaseMember::find()
                    ->andWhere([
                        'email'                 => $params['email'],
                        'type'                  => BaseMember::TYPE_COMPANY,
                        'email_register_status' => BaseMember::EMAIL_REGISTER_STATUS_NORMAL,
                    ])
                    ->asArray()
                    ->one();
                if ($inEmail) {
                    throw new Exception('该邮箱已被使用');
                }
            }
        }
        //手机号格式
        if (!empty($params['mobile'])) {
            if (!ValidateHelper::isMobileCN($params['mobile'])) {
                throw new Exception('联系人手机号格式错误');
            }
            if ($member->mobile != $params['mobile']) {
                $inMobile = BaseMember::find()
                    ->andWhere([
                        'mobile' => $params['mobile'],
                        'type'   => BaseMember::TYPE_COMPANY,
                    ])
                    ->andWhere([
                        ' != ',
                        'id',
                        $params['member_id'],
                    ])
                    ->asArray()
                    ->one();

                if ($inMobile) {
                    throw new Exception('该手机号码已被使用');
                }
            }
        }
        //验证用户名必须填写且是字母与数字组成且长度6-16位
        if (empty($params['username']) || !ValidateHelper::isUsername($params['username'])) {
            throw new Exception('用户名必须填写且是字母与数字组成且长度6 - 16位');
        }

        // 验证是否需要更新登陆信息
        $needUpdateLoginInfo = false;
        $memberNewMobile = $params['mobile'] ?: '';
        $memberNewEmail = $params['email'] ?: '';

        if($member->mobile != $memberNewMobile || $member->email != $memberNewEmail){
            $needUpdateLoginInfo = true;
        }

        //写入会员表member
        $member->setAttributes([
            'mobile'                => $params['mobile'] ?: '',
            'mobile_code'           => $params['mobileCode'] ?: BaseMemberLoginForm::DEFAULT_MOBILE_CODE,
            'username'              => $params['username'],
            'email'                 => $params['email'] ?: '',
            'email_register_status' => empty($params['email']) ? BaseMember::EMAIL_REGISTER_STATUS_ILLEGAL : BaseMember::EMAIL_REGISTER_STATUS_NORMAL,
            'avatar'                => $params['avatar'] ?: '',
        ]);

        //写入会员表member
        if (!$member->save()) {
            throw new Exception($member->getFirstErrorsMessage());
        }

        if ($needUpdateLoginInfo) {
            // 密码修改成功后，强制下线所有设备
            ForceLogoutService::forceLogout(
                $member->id,
                ForceLogoutService::REASON_UPDATE_COMPANY_CONTACT,
                ForceLogoutService::OPERATOR_ADMIN,
                $member->id,
                '运营人员修改了账号信息'
            );
        }


        // 如果要修改单位名称（支持小驼峰和下划线两种格式）
        $companyName = $params['companyName'] ?? $params['company_name'] ?? '';
        if (!empty($companyName)) {
            // 获取当前正确的单位名称
            $currentCompanyName = self::getCorrectCompanyName($company->status, $auditStatus, $company->full_name,
                $companyInfoAuth ? $companyInfoAuth->full_name : '');

            if ($companyName !== $currentCompanyName) {
                // 检查是否可以修改单位名称
                if (!self::canEditCompanyName($company->status, $auditStatus)) {
                    throw new Exception('该单位已终审通过，不可修改单位名称');
                }

                // 校验单位名称是否与已终审通过的单位重复
                if (self::checkCompanyNameForFinalAudit($company->id, $companyName)) {
                    throw new Exception('单位名称已存在！');
                }

                // 根据审核状态决定更新哪个表的单位名称
                if ($company->status == BaseCompany::STATUS_ACTIVE) {
                    // 已终审通过，更新单位表
                    $company->full_name = $companyName;
                    if (!$company->save()) {
                        throw new Exception($company->getFirstErrorsMessage());
                    }
                } else {
                    // 未终审通过，更新审核表
                    if ($companyInfoAuth) {
                        $companyInfoAuth->full_name = $companyName;
                        if (!$companyInfoAuth->save()) {
                            throw new Exception($companyInfoAuth->getFirstErrorsMessage());
                        }
                    } else {
                        throw new Exception('审核信息不存在，无法修改单位名称');
                    }
                }
            }
        }

        //写入账号信息表
        $companyMemberInfo->setAttributes([
            'contact'    => $params['contact'],
            'department' => $params['department'],
        ]);
        if (!$companyMemberInfo->save()) {
            throw new Exception($companyMemberInfo->getFirstErrorsMessage());
        }

        return $member->id;
    }

    /**
     * 验证数据合法性
     */
    public static function validateCommon($params)
    {
        //验证数据合法性
        //姓名-联系人且长度小于32
        if (empty($params['contact']) || mb_strlen($params['contact']) > 32) {
            throw new Exception('联系人不能为空或者长度不能超过32个字符');
        }
        //所在部门且长度小于64
        if (empty($params['department']) || mb_strlen($params['department']) > 64) {
            throw new Exception('所在部门不能为空或者长度不能超过64个字符');
        }

        return true;
    }

    /**
     * 编辑账号初始化
     * @param $id
     * @return array
     * @throws Exception
     */
    public static function editInit($id)
    {
        if ($id <= 0) {
            throw new Exception('参数错误');
        }
        //获取单位账号信息
        $companyMemberInfo = BaseCompanyMemberInfo::findOne($id);
        if (!$companyMemberInfo) {
            throw new Exception('记录不存在');
        }
        //获取账号信息
        $member = BaseMember::findOne($companyMemberInfo->member_id);
        if (!$member) {
            throw new Exception('记录不存在');
        }
        //获取单位信息
        $company = BaseCompany::findOne($companyMemberInfo->company_id);
        if (!$company) {
            throw new Exception('记录不存在');
        }
        //获取一下单位子账号的配置信息
        $companyConfig = BaseCompanyMemberConfig::findOne(['company_id' => $company->id]);

        // 获取最新的审核状态
        $companyInfoAuth = BaseCompanyInfoAuth::find()
            ->where(['company_id' => $company->id])
            ->orderBy('id desc')
            ->one();

        $auditStatus = $companyInfoAuth ? $companyInfoAuth->audit_status : null;

        // 根据审核状态选择正确的单位名称
        $correctCompanyName = self::getCorrectCompanyName($company->status, $auditStatus, $company->full_name,
            $companyInfoAuth ? $companyInfoAuth->full_name : '');

        $result = [
            'company_name'              => $correctCompanyName,
            'contact'                   => $companyMemberInfo->contact,
            'department'                => $companyMemberInfo->department,
            'username'                  => $member->username,
            'email'                     => $member->email,
            'mobile'                    => $member->mobile,
            'avatar_show'               => FileHelper::getFullUrl($member->avatar),
            'avatar'                    => $member->avatar,
            'company_member_type'       => $companyMemberInfo->company_member_type,
            'company_member_type_text'  => BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_LIST[$companyMemberInfo->company_member_type],
            'member_rule'               => $companyMemberInfo->member_rule,
            'member_rule_text'          => BaseCompanyMemberInfo::COMPANY_MEMBER_AUTH_LIST[$companyMemberInfo->member_rule],
            'wx_bind_text'              => '未绑定',
            'vip_available'             => $companyConfig->vip_available,
            'vip_total'                 => $companyConfig->vip_total,
            'vip_used'                  => $companyConfig->vip_used,

            // 新增审核状态和编辑权限信息
            'company_status'            => $company->status,
            'company_audit_status'      => $auditStatus,
            'company_audit_status_text' => BaseCompanyInfoAuth::AUDIT_STATUS_LIST[$auditStatus] ?? '未知状态',
            'can_edit_company_name'     => self::canEditCompanyName($company->status, $auditStatus),
            'can_edit_account'          => self::canEditAccount($company->status, $auditStatus),
        ];

        return $result;
    }

    /**
     * 账号登陆日志
     * @param $params
     */
    public static function Log($params)
    {
        return BaseCompanyMemberOperationLog::getLogList($params);
    }

    /**
     * 判断是否可以编辑单位名称
     * @param int $companyStatus 单位状态
     * @param int $auditStatus   审核状态
     * @return bool
     */
    public static function canEditCompanyName($companyStatus, $auditStatus)
    {
        // 使用常量数组判断审核状态是否允许编辑名称
        return in_array($companyStatus, BaseCompany::STATUS_CAN_EDIT_LIST);
    }

    /**
     * 判断是否可以编辑账号信息
     * @param int $companyStatus 单位状态
     * @param int $auditStatus   审核状态
     * @return bool
     */
    public static function canEditAccount($companyStatus, $auditStatus)
    {
        // 如果已经终审通过,可以编辑
        if ($companyStatus == BaseCompany::STATUS_ACTIVE) {
            return true;
        }

        // 使用常量数组判断审核状态是否允许编辑名称
        return in_array($companyStatus, BaseCompany::STATUS_CAN_EDIT_LIST);
    }

    /**
     * 校验单位名称是否与已终审通过的单位重复
     * @param int    $companyId 当前单位ID
     * @param string $fullName  单位名称
     * @return bool
     */
    public static function checkCompanyNameForFinalAudit($companyId, $fullName)
    {
        // 查找与已终审通过的单位名称是否重复
        $count = BaseCompany::find()
            ->where([
                'full_name' => $fullName,
                'status'    => BaseCompany::STATUS_ACTIVE,
                // 只检查已终审通过的单位
            ])
            ->andWhere([
                '<>',
                'id',
                $companyId,
            ])
            ->count();

        return $count > 0;
    }

    /**
     * 根据审核状态获取正确的单位名称
     * @param int    $companyStatus    单位状态
     * @param int    $auditStatus      审核状态
     * @param string $companyTableName 单位表中的名称
     * @param string $authTableName    审核表中的名称
     * @return string
     */
    public static function getCorrectCompanyName($companyStatus, $auditStatus, $companyTableName, $authTableName)
    {
        // 如果已终审通过，使用单位表中的名称
        if ($companyStatus == BaseCompany::STATUS_ACTIVE) {
            return $companyTableName ?: '';
        }

        // 如果未终审通过，优先使用审核表中的名称
        if (!empty($authTableName)) {
            return $authTableName;
        }

        // 如果审核表中没有名称，使用单位表中的名称
        return $companyTableName ?: '';
    }

}

