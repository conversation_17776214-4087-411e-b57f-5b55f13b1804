<?php

namespace admin\models;

use common\base\models\BaseCompany;
use common\base\models\BaseResumeLibrary;
use common\base\models\BaseResumeLibraryCollect;
use common\base\models\BaseResumeLibraryDownloadLog;
use common\helpers\UUIDHelper;

class ResumeLibrary extends BaseResumeLibrary
{
    public static function getCollectList($params)
    {
        $query = BaseResumeLibraryCollect::find()
            ->select('b.full_name as name,a.add_time,b.id')
            ->alias('a')
            ->innerJoin(['b' => BaseCompany::tableName()], 'a.company_id=b.id');

        $query->andFilterWhere(['a.resume_id' => $params['resumeId']]);

        if ($params['companyKeyword']) {
            // 有可能是两种情况,一种是uid一种是名字,先走ui的判断
            $id = UUIDHelper::decryptionByType($params['companyKeyword'], UUIDHelper::TYPE_COMPANY);
            if ($id) {
                $query->andWhere(['b.id' => $id]);
            } else {
                $query->andWhere([
                    'like',
                    'full_name',
                    $params['companyKeyword'],
                ]);
            }
        }

        if ($params['startTime']) {
            $query->andWhere([
                '>=',
                'a.add_time',
                $params['startTime'],
            ]);
        }

        if ($params['endTime']) {
            $query->andWhere([
                '<=',
                'a.add_time',
                $params['endTime'],
            ]);
        }

        $count = $query->count();

        $pageSize = \Yii::$app->params['defaultPageSize'];

        $pages = self::setPage($count, $params['page'], $pageSize);

        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['id'] = UUIDHelper::encrypt(UUIDHelper::TYPE_COMPANY, $item['id']);
        }

        return [
            'list' => $list,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$params['page'],
            ],
        ];
    }

    public static function getDownloadList($params)
    {
        $query = BaseResumeLibraryDownloadLog::find()
            ->select('b.full_name as name,a.add_time,b.id')
            ->alias('a')
            ->innerJoin(['b' => BaseCompany::tableName()], 'a.company_id=b.id');
        $query->andFilterWhere(['a.resume_id' => $params['resumeId']]);
        if ($params['companyKeyword']) {
            // 有可能是两种情况,一种是uid一种是名字,先走ui的判断
            $id = UUIDHelper::decryptionByType($params['companyKeyword'], UUIDHelper::TYPE_COMPANY);
            if ($id) {
                $query->andWhere(['b.id' => $id]);
            } else {
                $query->andWhere([
                    'like',
                    'full_name',
                    $params['companyKeyword'],
                ]);
            }
        }

        if ($params['startTime']) {
            $query->andWhere([
                '>=',
                'a.add_time',
                $params['startTime'],
            ]);
        }

        if ($params['endTime']) {
            $query->andWhere([
                '<=',
                'a.add_time',
                $params['endTime'],
            ]);
        }

        $count = $query->count();

        $pageSize = \Yii::$app->params['defaultPageSize'];

        $pages = self::setPage($count, $params['page'], $pageSize);

        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['id'] = UUIDHelper::encrypt(UUIDHelper::TYPE_COMPANY, $item['id']);
        }

        return [
            'list' => $list,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$params['page'],
            ],
        ];
    }

}