<?php

namespace admin\models;

use common\base\models\BaseAdminMenu;

class AdminMenu extends BaseAdminMenu
{

    /**
     * 获取整个权限,给前端显示
     */
    public static function getAll()
    {
        $parent = self::find()
            ->select([
                'id',
                'name',
                'key',
            ])
            ->where(['level' => 1])
            ->asArray()
            ->all();

        $tableList = [];

        foreach ($parent as &$item) {
            $children = self::find()
                ->select([
                    'id',
                    'name',
                    'key',
                ])
                ->where(['parent_id' => $item['id']])
                ->asArray()
                ->all();
            if (!$children) {
                $tableList[] = [
                    'menu'        => $item,
                    'menuRowspan' => 1,
                    'route'       => $item,
                ];
            } else {
                $isFirst = true;
                $length  = count($children);
                foreach ($children as $child) {
                    if ($isFirst) {
                        $menuRowspan = $length;
                        $isFirst     = false;
                    } else {
                        $menuRowspan = $length > 0 ? 0 : 1;
                    }
                    $tableList[] = [
                        'menu'        => $item,
                        'menuRowspan' => $menuRowspan,
                        'route'       => $child,
                    ];
                }
            }
        }

        foreach ($tableList as &$item) {
            $item['action'] = AdminMenuAction::find()
                ->select([
                    'id',
                    'name',
                    'key',
                ])
                ->where(['admin_menu_id' => $item['route']['id']])
                ->asArray()
                ->all();
        }

        return $tableList;
    }

    /**
     * @param $data
     */
    public function addMenu($data)
    {
        $model = new self();
        if ($data['parent_id']) {
            $data['level'] = 2;
        } else {
            $data['level'] = 1;
        }
        $model->load($data);
        $model->save();
    }

    /**
     * @param $data
     */
    public function addAction($data)
    {
        $model = new AdminMenuAction();
        $model->load($data);
        $model->save();
    }

    /**
     * 获取某个管理的拥有的菜单(包含权限)
     * @param $adminId
     */
    public static function getAdminMenu($adminId)
    {
        //        return
    }

}
