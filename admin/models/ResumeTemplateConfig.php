<?php

namespace admin\models;

use common\base\BaseActiveRecord;
use common\base\models\BaseResumeTemplateConfig;
use common\helpers\FileHelper;
use common\helpers\FormatConverter;
use common\helpers\TimeHelper;
use Yii;
use yii\db\Exception;

/**
 * Class ResumeTemplateConfig
 * @package admin\models
 * 简历模板配置处理业务类
 */
class ResumeTemplateConfig
{
    /**
     * 简历模板配置列表
     * @param $params
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function index($params)
    {
        //格式化参数
        $params = FormatConverter::convertHump($params);
        //query句柄
        $query = BaseResumeTemplateConfig::find();
        //构造查询条件
        if ($params['filter_key'] > 0 && $params['filter_key'] < 4 && !empty($params['filter_value'])) {
            switch ($params['filter_key']) {
                case 1:
                    $query->andWhere(['id' => $params['filter_value']]);
                    break;
                case 2:
                    $query->andWhere([
                        'like',
                        'name',
                        $params['filter_value'],
                    ]);
                    break;
                case 3:
                    $query->andWhere([
                        'like',
                        'code',
                        $params['filter_value'],
                    ]);
                    break;
            }
        }
        //是否显示
        if (!empty($params['is_show'])) {
            $query->andWhere(['is_show' => $params['is_show']]);
        }
        //添加时间-添加开始与添加结束时间between
        if (!empty($params['add_time_start']) && !empty($params['add_time_end'])) {
            $query->andWhere([
                'between',
                'add_time',
                TimeHelper::dayToBeginTime($params['add_time_start']),
                TimeHelper::dayToEndTime($params['add_time_end']),
            ]);
        }
        //是否vip
        if (!empty($params['is_vip'])) {
            $query->andWhere(['is_vip' => $params['is_vip']]);
        }
        //是否删除
        if (!empty($params['is_delete'])) {
            $query->andWhere(['is_delete' => $params['is_delete']]);
        }
        //总数
        $total = $query->count();
        //排序
        $order = 'add_time desc,id desc';
        //pdf下载排序
        if (!empty($params['pdf_download_number_sort'])) {
            if ($params['pdf_download_number_sort'] == 1) {
                $order = 'pdf_download_number desc,add_time desc';
            } else {
                $order = 'pdf_download_number asc,add_time desc';
            }
        }
        //doc下载排序
        if (!empty($params['doc_download_number_sort'])) {
            if ($params['doc_download_number_sort'] == 1) {
                $order = 'doc_download_number desc,add_time desc';
            } else {
                $order = 'doc_download_number asc,add_time desc';
            }
        }
        //排序排序
        if (!empty($params['sort_number_sort'])) {
            if ($params['sort_number_sort'] == 1) {
                $order = 'sort_number desc,add_time desc';
            } else {
                $order = 'sort_number asc,add_time desc';
            }
        }
        //显示状态排序
        if (!empty($params['is_show_sort'])) {
            if ($params['is_show_sort'] == 1) {
                $order = 'is_show desc,add_time desc';
            } else {
                $order = 'is_show asc,add_time desc';
            }
        }
        //添加时间排序
        if (!empty($params['add_time_sort'])) {
            if ($params['add_time_sort'] == 1) {
                $order = 'add_time desc';
            } else {
                $order = 'add_time asc';
            }
        }
        //是否vip排序
        if (!empty($params['is_vip_sort'])) {
            if ($params['is_vip_sort'] == 1) {
                $order = 'is_vip desc,add_time desc';
            } else {
                $order = 'is_vip asc,add_time desc';
            }
        }
        //是否删除排序
        if (!empty($params['is_delete_sort'])) {
            if ($params['is_delete_sort'] == 1) {
                $order = 'is_delete desc,add_time desc';
            } else {
                $order = 'is_delete asc,add_time desc';
            }
        }
        //分页
        //获取分页参数
        $page_size = $params['page_size'] ?: Yii::$app->params['defaultPageSize'];
        $pages     = BaseActiveRecord::setPage($total, $params['page'], $page_size);
        //获取数据
        $data = $query->orderBy($order)
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->asArray()
            ->all();
        //循环处理数据
        foreach ($data as &$value) {
            //是否显示
            $value['is_show_text'] = BaseResumeTemplateConfig::IS_SHOW_TEXT[$value['is_show']];
            //是否vip
            $value['is_vip_text'] = BaseResumeTemplateConfig::IS_VIP_TEXT[$value['is_vip']];
            //是否删除
            $value['is_delete_text'] = BaseResumeTemplateConfig::IS_DELETE_TEXT[$value['is_delete']];
        }

        return [
            'data'  => $data,
            'pages' => [
                'total' => (int)$total,
                'limit' => (int)$page_size,
                'page'  => (int)$pages['page'],
            ],
        ];
    }

    /**
     * 简历模板配置添加
     * @param $params
     * @return bool
     * @throws Exception
     */
    public static function add($params)
    {
        //格式化参数
        $params = FormatConverter::convertHump($params);
        //验证数据合法性
        self::validate($params);
        //添加数据
        $model = new BaseResumeTemplateConfig();
        //模板名称
        $model->name = $params['template_name'];
        //模板描述
        $model->description = $params['template_description'] ?: '';
        //模板编号
        $model->code = $params['template_code'];
        //模板图片
        $model->template_image = $params['template_image'];
        //是否显示
        $model->is_show = $params['is_show'];
        //是否vip
        $model->is_vip = $params['is_vip'];
        //是否删除
        $model->is_delete = BaseResumeTemplateConfig::IS_DELETE_NO;
        //添加时间
        $model->add_time = CUR_DATETIME;
        //修改时间
        $model->update_time = CUR_DATETIME;
        //排序
        $model->sort_number = $params['sort_number'] ?: 0;
        //pdf下载次数
        $model->pdf_download_number = 0;
        //doc下载次数
        $model->doc_download_number = 0;
        //文件file ID
        $model->file_id = $params['file_id'] ?: 0;

        //保存及返回
        return $model->save();
    }

    /**
     * 简历模板配置编辑
     * @param $params
     * @return bool
     * @throws Exception
     */
    public static function edit($params)
    {
        //格式化参数
        $params = FormatConverter::convertHump($params);
        //模板记录存不存在
        $model = BaseResumeTemplateConfig::findOne($params['id']);
        if (empty($model)) {
            throw new Exception('模板记录不存在');
        }
        //验证数据合法性
        self::validate($params, true);
        //编辑数据
        //模板名称
        $model->name = $params['template_name'];
        //模板描述
        $model->description = $params['template_description'] ?: '';
        //模板编号
        $model->code = $params['template_code'];
        //模板图片
        $model->template_image = $params['template_image'];
        //是否显示
        $model->is_show = $params['is_show'];
        //是否vip
        $model->is_vip = $params['is_vip'];
        //是否删除
        //$model->is_delete = $params['is_delete'] ?: BaseResumeTemplateConfig::IS_DELETE_NO;
        //修改时间
        $model->update_time = CUR_DATETIME;
        //排序
        $model->sort_number = $params['sort_number'] ?: 0;
        //文件file ID
        $model->file_id = $params['file_id'] ?: 0;

        //保存及返回
        return $model->save();
    }

    /**
     * 验证数据合法性
     * @param $params
     * @return bool
     * @throws Exception
     */
    public static function validate($params, $isEdit = false)
    {
        //验证模板名称必填且长度做最长10
        if (empty($params['template_name']) || mb_strlen($params['template_name']) > 10) {
            throw new Exception('模板名称必填且长度最长为10');
        }
        //验证模板描述填写了那么就最长20字符
        if (!empty($params['template_description']) && mb_strlen($params['template_description']) > 20) {
            throw new Exception('模板描述最长为20字符');
        }
        //验证模板编号必填且长度最长为32，且只能是数字和字母
        if (empty($params['template_code']) || mb_strlen($params['template_code']) > 32 || !preg_match('/^[a-zA-Z0-9_]+$/',
                $params['template_code'])) {
            throw new Exception('模板编号必填且长度最长为32，且只能是数字和字母或者_');
        }
        //验证模板图片必填
        if (empty($params['template_image'])) {
            throw new Exception('模板图片必须上传');
        }
        //验证模板是否显示
        if (!in_array($params['is_show'], BaseResumeTemplateConfig::IS_SHOW_LIST)) {
            throw new Exception('模板是否显示参数错误');
        }
        //检查模板编号唯一性
        $check_query = BaseResumeTemplateConfig::find()
            ->andWhere(['code' => $params['template_code']]);
        if ($isEdit) {
            $check_query->andWhere([
                '<>',
                'id',
                $params['id'],
            ]);
        }
        $check = $check_query->one();
        if (!empty($check)) {
            throw new Exception('模板编号已存在');
        }

        return true;
    }

    /**
     * 简历模板配置编辑初始化
     * @param $id
     * @return array|\yii\db\ActiveRecord|null
     * @throws Exception
     */
    public static function editInit($id)
    {
        $info = BaseResumeTemplateConfig::getInfo($id);
        if (empty($info)) {
            throw new Exception('模板记录不存在');
        }
        //是否显示
        $info['is_show_text'] = BaseResumeTemplateConfig::IS_SHOW_TEXT[$info['is_show']];
        //是否vip
        $info['is_vip_text'] = BaseResumeTemplateConfig::IS_VIP_TEXT[$info['is_vip']];
        //是否删除
        $info['is_delete_text'] = BaseResumeTemplateConfig::IS_DELETE_TEXT[$info['is_delete']];
        //图片路劲处理
        $info['fileInfo']['path'] = FileHelper::getFullUrl($info['fileInfo']['path']);

        return $info;
    }

    /**
     * 简历模板配置删除
     * @param $id
     * @return bool
     * @throws Exception
     */
    public static function deleteStatus($id)
    {
        //模板记录存不存在
        $model = BaseResumeTemplateConfig::findOne($id);
        if (empty($model)) {
            throw new Exception('模板记录不存在');
        }
        //删除数据
        //删除状态去翻
        $model->is_delete = $model->is_delete == BaseResumeTemplateConfig::IS_DELETE_YES ? BaseResumeTemplateConfig::IS_DELETE_NO : BaseResumeTemplateConfig::IS_DELETE_YES;
        //修改时间
        $model->update_time = CUR_DATETIME;

        //保存及返回
        return $model->save();
    }

    /**
     * 简历模板配置显示状态
     * @param $id
     * @return bool
     * @throws Exception
     */
    public static function showStatus($id)
    {
        //模板记录存不存在
        $model = BaseResumeTemplateConfig::findOne($id);
        if (empty($model)) {
            throw new Exception('模板记录不存在');
        }
        //删除数据
        //删除状态去翻
        $model->is_show = $model->is_show == BaseResumeTemplateConfig::IS_SHOW_YES ? BaseResumeTemplateConfig::IS_SHOW_NO : BaseResumeTemplateConfig::IS_SHOW_YES;
        //修改时间
        $model->update_time = CUR_DATETIME;

        //保存及返回
        return $model->save();
    }
}