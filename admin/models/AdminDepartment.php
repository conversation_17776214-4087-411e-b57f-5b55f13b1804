<?php

namespace admin\models;

use common\base\models\BaseAdminDepartment;

class AdminDepartment extends BaseAdminDepartment
{

    public static function getList()
    {
        return self::find()
            ->select('id,name')
            ->asArray()
            ->all();
    }

    public static function getPosition()
    {
        $list = self::getList();

        foreach ($list as &$item) {
            $item['list'] = AdminPosition::find()
                ->select([
                    'id',
                    'name',
                ])
                ->where(['department_id' => $item['id']])
                ->asArray()
                ->all();
        }

        return $list;
    }

}
