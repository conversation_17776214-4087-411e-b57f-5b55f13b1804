<?php

namespace admin\models;

use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseDictionary;
use common\base\models\BaseJobSubscribe;

class Person
{
    /**
     * 获取职位订阅的详情信息
     * @param $resumeId
     * @return array|\yii\db\ActiveRecord|null
     * @throws \Exception
     */
    public static function getSubscribeInfo($resumeId)
    {
        $info = BaseJobSubscribe::getInfo($resumeId);
        if (!empty($info)) {
            //编辑的情况
            //获取意向职位的文本
            $info['job_category_text'] = BaseCategoryJob::getMultipleJobCategory(explode(',',
                $info['job_category_ids']));
            //获取地区文本
            $info['area_text'] = BaseArea::getTextByIdList(explode(',', $info['area_ids']));
            //获取学历要求文本
            $info['education_text'] = BaseDictionary::getEducationTextByCodeList(explode(',', $info['education_ids']));

            //订阅渠道
            $subscribe_type = [];
            if ($info['is_send_email'] == 1) {
                $subscribe_type[] = BaseJobSubscribe::SEND_EMAIL_TEXT;
            }
            if ($info['is_send_wechat'] == 1) {
                $subscribe_type[] = BaseJobSubscribe::SEND_WECHAT_TEXT;
            }
            $info['subscribe_type'] = implode(',', $subscribe_type);

            return $info;
        }

        return [];
    }
}