<?php

namespace admin\models;

use common\base\models\BaseHomeColumn;
use common\base\models\BaseTopic;
use common\base\models\BaseNewsHandleLog;
use common\helpers\FileHelper;
use common\helpers\TimeHelper;
use Yii;
use yii\base\Exception;

class Topic extends BaseTopic
{
    /**
     * 新增或者编辑都在这里
     * @throws Exception
     */
    public function create($data)
    {
        try {
            if ($data['id']) {
                //todo 编辑话题
                $topicModel = self::findOne($data['id']);
                if (!$topicModel) {
                    $this->ThrowException('话题不存在');
                }
                $data['status'] = $topicModel->status == 1 ? $topicModel->status : $data['status'];
            } else {
                //todo 新增话题
                $topicModel           = new self;
                $topicModel->add_time = CUR_DATETIME;
            }

            //todo 话题表数据
            $topicModel->status         = $data['status'];
            $topicModel->title          = $data['title'];
            $topicModel->sub_title      = $data['sub_title'];
            $topicModel->target_url     = $data['target_url'];
            $topicModel->cover_url      = $data['cover_url'];
            $topicModel->news_list_json = json_encode($data['news_list_json']);
            $topicModel->save();
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 获取话题详情
     */
    public static function getTopicDetail($Id): array
    {
        $list       = BaseTopic::getTopicDetail($Id);
        $statusName = BaseTopic::STATUS_NAME;
        if (sizeof($list) > 0) {
            $list['news_list_json'] = json_decode($list['news_list_json'], true);
            $list['status_title']   = $statusName[$list['status']];
        }else{
            $list=[];
        }

        return $list;
    }

    /**
     * 获取话题列表
     */
    public static function getTopicList($keywords): array
    {
        $select = [
            'id',
            'add_time',
            'status',
            'title',
            'sub_title',
            'target_url',
            'cover_url',
            'news_list_json',
        ];

        $query = BaseTopic::find()
            ->select($select);

        $query->andFilterCompare('concat(title,id )', $keywords['title'], 'like');
        $query->andFilterCompare('status', BaseTopic::STATUS_DELETE, '<>');

        if ($keywords['add_time_start']) {
            $query->andWhere([
                '>=',
                'add_time',
                TimeHelper::dayToBeginTime($keywords['add_time_start']),
            ]);
        }
        if ($keywords['add_time_end']) {
            $query->andWhere([
                '<=',
                'add_time',
                TimeHelper::dayToEndTime($keywords['add_time_end']),
            ]);
        }

        // todo 查询资讯列表
        $count    = $query->count();
        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->asArray()
            ->all();

        $statusName = BaseTopic::STATUS_NAME;
        foreach ($list as $k => $item) {
            $list[$k]['news_list_json'] = json_decode($item['news_list_json'], true);
            $list[$k]['status_title']   = $statusName[$item['status']];
        }

        return [
            'list' => $list,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
        ];
    }

    /**
     * 获取资讯审核状态列表
     */
    public static function getStatusList(): array
    {
        $statusName = BaseTopic::STATUS_NAME;

        $list = [];
        foreach ($statusName as $k => $item) {
            $list[] = [
                'k' => $k,
                'v' => $item,
            ];
        }

        return $list;
    }

    /**
     * 审核话题
     * @throws Exception
     */
    public static function auditTopic($keywords)
    {
        try {
            //todo  校验当前和目标模型类型
            $Ids      = explode(',', $keywords['ids']);
            $newsMess = BaseTopic::find()
                ->select([
                    'status',
                    'id',
                ])
                ->where([
                    'in',
                    'id',
                    $Ids,
                ])
                ->andWhere([
                    '<>',
                    'status',
                    BaseTopic::STATUS_DELETE,
                ])
                ->asArray()
                ->all();

            $adoptIds = [];
            foreach ($newsMess as $item) {
                if ($item['status'] != BaseTopic::STATUS_ONLINE) {
                    $adoptIds[] = $item['id'];
                }
            }

            if (sizeof($adoptIds) < 1) {
                return true;
            }

            $data = [
                'status' => $keywords['status'],
            ];

            $condition = [
                'in',
                'id',
                $adoptIds,
            ];

            BaseTopic::updateAll($data, $condition);
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 删除资讯
     * @throws Exception
     */
    public static function deleteTopic($keywords)
    {
        try {
            //todo  校验当前和目标模型类型
            $Ids  = explode(',', $keywords['ids']);
            $data = [
                'status' => BaseTopic::STATUS_DELETE,
            ];

            $condition = [
                'in',
                'id',
                $Ids,
            ];

            BaseTopic::updateAll($data, $condition);
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}
