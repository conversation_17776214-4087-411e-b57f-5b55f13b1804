<?php

namespace admin\models;

use common\base\models\BaseCompanyCooperationApplyHandle;
use yii\base\Exception;

class CompanyCooperationApplyHandle extends BaseCompanyCooperationApplyHandle
{

    public static function getContent($adminId, $type, $remark = '')
    {
        $adminName = Admin::find()
                         ->select('name')
                         ->where(['id' => $adminId])
                         ->asArray()
                         ->one()['name'];
        switch ($type) {
            case self::TYPE_ADD_REMARK:
                $content = '“' . $adminName . '”添加标注：' . $remark;
                break;
            case self::TYPE_SET_HANDLE_YES:
                $content = '“' . $adminName . '”设为已处理';
                break;
            case self::TYPE_SET_HANDLE_NO:
                $content = '“' . $adminName . '”设为未处理';
                break;
        }

        return $content;
    }

    /**
     * 添加一条
     * @param        $id
     * @param        $adminId
     * @param        $type
     * @param string $content
     */
    public function create($id, $adminId, $type, string $content = '')
    {
        $this->isNewRecord                  = true;
        $this->admin_id                     = $adminId;
        $this->type                         = $type;
        $this->content                      = self::getContent($adminId, $type, $content);
        $this->company_cooperation_apply_id = $id;
        if (!$this->save()) {
            throw new Exception($this->getFirstErrorsMessage());
        }
    }
}