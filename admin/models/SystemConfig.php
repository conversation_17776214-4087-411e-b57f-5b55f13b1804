<?php

namespace admin\models;

use common\base\models\BaseSystemConfig;
use Yii;

class SystemConfig extends BaseSystemConfig
{

    public static function getList($params)
    {
        $query = self::find()
            ->select('id,name, value, platform_type, is_secret,remark')
            ->where(['is_secret' => self::IS_SECRET_NO]);

        $query->andFilterCompare('platform_type', $params['platformType']);
        $query->andFilterCompare('is_business', $params['isBusiness']);

        if ($params['name']) {
            // or name value remark
            $query->andWhere([
                'or',
                [
                    'like',
                    'name',
                    $params['name'],
                ],
                [
                    'like',
                    'value',
                    $params['name'],
                ],
                [
                    'like',
                    'remark',
                    $params['name'],
                ],
            ]);
        }

        $count    = $query->count();
        $pageSize = $params['pageSize'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $params['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['platform']    = self::PLATFORM_TYPE_LIST[$item['platform_type']];
            $item['isSecretTxt'] = self::IS_SECRET_LIST[$item['is_secret']];
        }

        return [
            'list' => $list,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$params['page'],
            ],
        ];
    }

    public static function create($params)
    {
        $model = $params['id'] ? self::findOne($params['id']) : new self();

        if (!$params['id']) {
            // 新增
            $model->name          = $params['name'];
            $model->platform_type = $params['platformType'] ?: self::PLATFORM_TYPE_COMMON;
            $model->is_secret     = $params['isSecret'] ?: self::IS_SECRET_NO;
        }

        $model->value  = $params['value'];
        $model->remark = $params['remark'] ?: '';
        if (!$model->save()) {
            throw new \Exception($model->getFirstErrorsMessage());
        }
    }

    public static function getValueBuKey($key)
    {
        return self::find()
            ->select('value')
            ->where(['name' => $key])
            ->scalar();
    }

    /**
     * 获取简历库相关配置
     */
    public static function getResumeLibrary()
    {
        $keyList = self::RESUME_LIBRARY_LIST;

        $data = [];
        foreach ($keyList as $item) {
            $data[$item] = self::getValue($item);
        }

        return $data;
    }

    /**
     *
     * 设置简历库的值
     * @param $params
     *
     */
    public static function setResumeLibrary($params)
    {
        $keyList = self::RESUME_LIBRARY_LIST;
        foreach ($params as $k => $v) {
            // 值必须是正整数
            if (!is_numeric($v) || $v < 0) {
                throw new \Exception('参数错误');
            }
            if (!in_array($k, $keyList)) {
                throw new \Exception('参数错误');
            }

            self::setValue($k, $v);
        }
    }
}
