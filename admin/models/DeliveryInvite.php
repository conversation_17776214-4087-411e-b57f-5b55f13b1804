<?php

namespace admin\models;

use common\base\BaseActiveRecord;
use common\base\models\BaseAdmin;
use common\base\models\BaseAdminDownloadTask;
use common\base\models\BaseAdminJobInvite;
use common\base\models\BaseAdminJobInviteConfig;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseOffSiteJobApply;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeIntention;
use common\base\models\BaseResumeIntentionAreaRelation;
use common\base\models\BaseResumeLibraryInviteLog;
use common\base\models\BaseResumeSetting;
use common\base\models\BaseResumeWork;
use common\base\models\BaseShieldCompany;
use common\helpers\TimeHelper;
use common\helpers\UUIDHelper;
use common\service\downloadTask\DownLoadTaskApplication;
use common\service\jobInvite\AdminJobInviteListService;
use common\service\jobInvite\CompanyJobInviteListService;
use Yii;
use yii\base\Exception;

/**
 * 投递邀约业务类
 */
class DeliveryInvite
{
    /**
     * 检测简历信息
     * @param $params
     * @return array[]
     * @throws Exception
     */
    public static function check($params)
    {
        //对所有数据解析
        //分情况讨论
        /// 两种情况 1手动邀约  2智能邀约
        /// //智能邀约 需要先进行分析 拿到匹配的简历ID
        if ($params['inviteDeliveryWay'] == BaseAdminJobInviteConfig::INVITE_WAY_AUTO) {
            //自动的时候计算一下当前规则需要邀请哪些简历
            $query = BaseResume::find()
                ->alias('r')
                ->select(['r.id'])
                ->innerJoin(['re' => BaseResumeEducation::tableName()], 'r.last_education_id=re.id')
                ->innerJoin(['m' => BaseMember::tableName()], 'r.member_id=m.id')
                ->groupBy('r.id')
                ->orderBy('m.last_active_time desc');
            //人才过滤箱项
            if ($params['personMatchType']) {
                $person_match_type_arr = explode(',', $params['personMatchType']);
                // 学历
                if (in_array(BaseAdminJobInviteConfig::MATCH_TYPE_EDUCATION,
                        $person_match_type_arr) && $params['educationType']) {
                    $allEducationIds = [];
                    foreach ($params['educationType'] as $education) {
                        $allEducationIds = array_merge(BaseResumeEducation::getEducationValueBysKey($education),  $allEducationIds);
                    }
                    $query->andFilterWhere(['re.education_id' => $allEducationIds]);
                }
                // 专业
                if (in_array(BaseAdminJobInviteConfig::MATCH_TYPE_MAJOR,
                        $person_match_type_arr) && $params['majorId']) {
                    // 得到的专业id改为三级 https://zentao.jugaocai.com/index.php?m=story&f=view&storyID=961&version=0&param=0&storyType=story
                    $major_ids = explode(',', $params['majorId']);
                    $query->andFilterWhere(['re.major_id' => $major_ids]);

                    ///获取当前选择对的专业的所有三级
                    //                    $major_ids  = explode(',', $params['majorId']);
                    //                    $major_data = BaseMajor::getLevelThreeList($major_ids);
                    //                    $major_arr  = array_column($major_data, 'id');
                    //                    $query->andFilterWhere(['re.major_id' => $major_ids]);
                }
                //意向职位类型
                if (in_array(BaseAdminJobInviteConfig::MATCH_TYPE_CATEGORY,
                        $person_match_type_arr) && $params['jobCategoryId'] > 0) {
                    $query->leftJoin(['ri' => BaseResumeIntention::tableName()], 'r.id=ri.resume_id and ri.status=1');
                    $query->andFilterWhere(['ri.job_category_id' => $params['jobCategoryId']]);
                }
                //意向城市
                if (in_array(BaseAdminJobInviteConfig::MATCH_TYPE_CITY,
                        $person_match_type_arr) && $params['cityId'] > 0) {
                    $query->leftJoin(['ria' => BaseResumeIntentionAreaRelation::tableName()], 'r.id=ria.resume_id');
                    $query->andFilterWhere(['ria.area_id' => $params['cityId']]);
                }
                //工作经验
                if (in_array(BaseAdminJobInviteConfig::MATCH_TYPE_EXPERIENCE,
                        $person_match_type_arr) && $params['experienceType'] > 0) {
                    $workYearInfo = BaseDictionary::WORK_YEARS_LIST[$params['experienceType']];
                    $query->andFilterWhere([
                        'between',
                        'r.work_experience',
                        $workYearInfo['min'],
                        $workYearInfo['max'],
                    ]);
                }
                //职称
                if (in_array(BaseAdminJobInviteConfig::MATCH_TYPE_TITLE,
                        $person_match_type_arr) && $params['titleType'] > 0) {
                    $query->andFilterWhere(['r.title_id' => $params['titleType']]);
                }
                //政治面貌
                if (in_array(BaseAdminJobInviteConfig::MATCH_TYPE_POLITICAL,
                        $person_match_type_arr) && $params['politicalType'] > 0) {
                    $query->andFilterWhere(['r.political_status_id' => $params['politicalType']]);
                }
                //海外经验
                if (in_array(BaseAdminJobInviteConfig::MATCH_TYPE_ABROAD,
                        $person_match_type_arr) && $params['abroadType'] > 0) {
                    $query->leftJoin(['rw' => BaseResumeWork::tableName()], 'r.id=rw.resume_id');
                    $query->andFilterWhere(['rw.is_abroad' => $params['abroadType']]);
                }
            }
            //邀约人数
            if ($params['inviteNumber'] > 0) {
                $query->limit($params['inviteNumber']);
            }
            //邀约活跃时间
            if ($params['personActiveDayNumber'] > 0) {
                $time = date('Y-m-d H:i:s', strtotime('-' . $params['personActiveDayNumber'] . ' day'));
                $query->andWhere([
                    '>',
                    'm.last_active_time',
                    $time,
                ]);
            }
            //获取符合条件的简历数据
            $data = $query->asArray()
                ->all();
            //获取简历ID
            $resume_ids = array_column($data, 'id');
        } else {
            $encrypt_explode = explode('、', $params['invitePersonResumeIds']);
            $resume_ids      = [];
            foreach ($encrypt_explode as $encrypt_resume_id) {
                array_push($resume_ids, UUIDHelper::decryption($encrypt_resume_id));
            }
        }
        if (count($resume_ids) <= 0) {
            throw new Exception('当前推荐配置没有可推荐的简历资源，请修改配置！');
        }
        //这里就开始做简历ID的校验 --组装数据
        $verify_list = self::verify($resume_ids, $params);

        return [
            'verify_list' => $verify_list,
        ];
    }

    /**
     * 验证邀请的简历
     * @param $resume_ids
     * @param $params
     * @return array
     */
    public static function verify($resume_ids, $params)
    {
        ///1、先做所有大前提校验（3个条件）
        /// ---- 人才是否存在
        /// ---- 简历未开放
        /// ---- 屏蔽该单位
        ///2、可选条件校验（四个，即人才过滤项）
        /// ---- 30天内已邀约过该职位
        /// ---- 30天内已投递过该职位
        /// ---- 简历完善度不足65%
        /// ---- 30天内已被邀约过
        /// ---- $item=[
        ///         'resume_id'=>'',
        ///         'name'=>'',
        ///         'verify_notice'=>[],
        ///         'is_notice'=>false
        ///    ]
        // 获取职位信息
        $job_info               = BaseJob::findOne($params['jobId']);
        $person_search_type_arr = $params['personSearchType'] ? explode(',', $params['personSearchType']) : [];
        $list                   = [];
        foreach ($resume_ids as $key => $resume_id) {
            $item              = [
                'resume_id'     => UUIDHelper::encrypt(UUIDHelper::TYPE_PERSON, $resume_id),
                'name'          => '',
                'resume_desc'   => '',
                'verify_notice' => '',
                'is_notice'     => false,
                'is_check'      => false,
                'is_disabled'   => true,
            ];
            $verify_notice_arr = [];

            //1、人才是否存在
            //获取简历信息
            $query       = BaseResume::find();
            $resume_info = $query->alias('r')
                ->select([
                    'r.*',
                    're.school',
                    're.education_id',
                    'major.name as major_name',
                ])
                ->where(['r.id' => $resume_id])
                ->leftJoin(['re' => BaseResumeEducation::tableName()], 'r.last_education_id = re.id')
                ->leftJoin(['major' => BaseMajor::tableName()], 'major.id = re.major_id_level_3')
                ->asArray()
                ->one();

            if (!$resume_info) {
                $item['name'] = '----';
                array_push($verify_notice_arr,
                    BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME[BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_ISSET]);
                $item['is_notice']     = true;
                $item['is_disabled']   = false;
                $item['verify_notice'] = implode(';', $verify_notice_arr);
                array_unshift($list, $item);
                //简历不存在跳过下面的验证
                continue;
            } else {
                $item['name'] = $resume_info['name'];
                $resumeDesc   = [
                    $resume_info['age'] ? $resume_info['age'] . "岁" : '',
                    BaseResumeEducation::EDUCATION_TYPE_LIST[$resume_info['education_id']] ?? '',
                    $resume_info['school'] ?? '',
                    $resume_info['major_name'] ?? '',
                ];
                $resumeDesc   = array_filter($resumeDesc, function ($value) {
                    return $value !== null && $value !== 0 && $value !== '';
                });

                $item['resume_desc'] = implode('/', $resumeDesc);
            }

            //2、简历未开放
            //获取简历配置信息
            $resume_config_info = BaseResumeSetting::findOne(['resume_id' => $resume_id]);
            if ($resume_config_info->is_hide_resume == BaseResumeSetting::IS_HIDE_RESUME_YES) {
                array_push($verify_notice_arr,
                    BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME[BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_IS_OPEN]);
                $item['is_disabled'] = false;
            }

            //3、屏蔽该单位
            //获取简历屏蔽的单位
            $shield_company_ids = BaseShieldCompany::find()
                ->select('company_id')
                ->andWhere([
                    'status'    => BaseShieldCompany::STATUS_ACTIVE,
                    'resume_id' => $resume_id,
                ])
                ->column();
            if (count($shield_company_ids) > 0 && in_array($job_info->company_id, $shield_company_ids)) {
                array_push($verify_notice_arr,
                    BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME[BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_IS_HIDE]);
                $item['is_disabled'] = false;
            }

            //---------- 人才过滤项开始
            if (count($person_search_type_arr) > 0) {
                //4、30天内已邀约过该职位
                //获取距离最近的一次该职位邀约
                // https://zentao.jugaocai.com/index.php?m=file&f=read&t=png&fileID=6246 需要过滤该人才，只包含1，2，3，4项目

                if (in_array(BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_INVITE_JOB_30,
                    $person_search_type_arr)) {
                    $admin_invite_job_info   = BaseAdminJobInvite::getInviteJobInfo30($params['jobId'], $resume_id);
                    $library_invite_job_info = BaseResumeLibraryInviteLog::getInviteJobInfo30($params['jobId'],
                        $resume_id);
                    if ($admin_invite_job_info) {
                        continue;
                        //                        $invite_job_admin_info  = BaseAdmin::findOne($admin_invite_job_info['admin_id']);
                        //                        $invite_job_admin_name  = $invite_job_admin_info ? $invite_job_admin_info->name : '-';
                        //                        $invite_job_text_prefix = BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME[BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_INVITE_JOB_30];
                        //                        $invite_job_text        = $invite_job_text_prefix . '，' . BaseAdminJobInviteConfig::verifyTextReplace(BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_INVITE_JOB_30,
                        //                                $invite_job_admin_name, $admin_invite_job_info['add_time']);
                        //                        array_push($verify_notice_arr, $invite_job_text);
                    } elseif ($library_invite_job_info) {
                        continue;
                        //                        $invite_job_company_info = BaseCompany::findOne($library_invite_job_info['company_id']);
                        //                        $invite_job_company_name = $invite_job_company_info ? $invite_job_company_info->full_name : '-';
                        //                        $invite_job_text_prefix  = BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME[BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_INVITE_JOB_30];
                        //                        $invite_job_text         = $invite_job_text_prefix . '，' . BaseAdminJobInviteConfig::verifyTextReplace(BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_INVITE_JOB_30,
                        //                                $invite_job_company_name, $library_invite_job_info['add_time']);
                        //                        array_push($verify_notice_arr, $invite_job_text);
                    }
                }

                //5、30天内已投递过该职位
                //获取30天距离最近的一次投递
                $delivery_info = BaseJobApplyRecord::getApplyInfo30($params['jobId'], $resume_id);
                if ($delivery_info && in_array(BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_DELIVERY_30,
                        $person_search_type_arr)) {
                    continue;
                    //                    $invite_text_prefix = BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME[BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_DELIVERY_30];
                    //                    $invite_text        = $invite_text_prefix . BaseAdminJobInviteConfig::verifyTextReplace(BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_DELIVERY_30);
                    //                    array_push($verify_notice_arr, $invite_text);
                }

                //6、简历完善度不足65%
                if ($resume_info['complete'] < BaseAdminJobInviteConfig::RESUME_COMPLETE_VERIFY_65 && in_array(BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_COMPLETE_65,
                        $person_search_type_arr)) {
                    continue;
                    //                    array_push($verify_notice_arr,
                    //                        BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME[BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_COMPLETE_65]);
                }

                //7、30天内已被邀约过
                //获取30天距离最近的一次邀约
                if (in_array(BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_INVITE_30, $person_search_type_arr)) {
                    $admin_invite_info   = BaseAdminJobInvite::getInviteInfo30($resume_id);
                    $library_invite_info = BaseResumeLibraryInviteLog::getInviteInfo30($resume_id);
                    if ($admin_invite_info) {
                        continue;
                        //                        $invite_admin_info  = BaseAdmin::findOne($admin_invite_info['admin_id']);
                        //                        $invite_admin_name  = $invite_admin_info ? $invite_admin_info->name : '-';
                        //                        $invite_text_prefix = BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME[BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_INVITE_30];
                        //                        $invite_text        = $invite_text_prefix . '，' . BaseAdminJobInviteConfig::verifyTextReplace(BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_INVITE_30,
                        //                                $invite_admin_name, $admin_invite_info['add_time']);
                        //                        array_push($verify_notice_arr, $invite_text);
                    } elseif ($library_invite_info) {
                        continue;
                        //                        $invite_company_info = BaseCompany::findOne($library_invite_info['company_id']);
                        //                        $invite_company_name = $invite_company_info ? $invite_company_info->full_name : '-';
                        //                        $invite_text_prefix  = BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME[BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_INVITE_30];
                        //                        $invite_text         = $invite_text_prefix . '，' . BaseAdminJobInviteConfig::verifyTextReplace(BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_INVITE_30,
                        //                                $invite_company_name, $library_invite_info['add_time']);
                        //                        array_push($verify_notice_arr, $invite_text);
                    }
                }
            }
            //---------- 人才过滤项结束

            //验证完毕
            if (count($verify_notice_arr) > 0) {
                $item['verify_notice'] = implode('；', $verify_notice_arr);
                $item['is_notice']     = true;
                array_unshift($list, $item);
            } else {
                $item['is_check'] = true;
                array_push($list, $item);
            }
        }

        return $list;
    }

    /**
     * 添加邀约配置
     * @param $params
     * @return bool
     */
    public static function add($params)
    {
        $model                    = new BaseAdminJobInviteConfig();
        $model->add_time          = CUR_DATETIME;
        $model->update_time       = CUR_DATETIME;
        $model->way_type          = $params['inviteDeliveryWay'] ?: 0;
        $model->text_type         = $params['inviteSelect'] ?: 0;
        $model->text_content      = $params['inviteSelectText'] ?: '';
        $model->invite_resume_set = $params['invitePersonSelectResumeIds'] ?: '';
        $model->job_id            = $params['jobId'] ?: 0;
        $model->invite_time       = $params['inviteTime'] ?: CUR_DATETIME;
        $model->admin_id          = Yii::$app->user->id;
        $model->status_call       = BaseAdminJobInviteConfig::STATUS_CALL_EXEC_WAITING;
        if ($params['personSearchType']) {
            $person_search_type_arr = explode(',', $params['personSearchType']);
            if (in_array(BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_INVITE_JOB_30, $person_search_type_arr)) {
                $model->invite_job_type = BaseAdminJobInviteConfig::INVITE_JOB_TYPE_30;
            }

            if (in_array(BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_DELIVERY_30, $person_search_type_arr)) {
                $model->apply_type = BaseAdminJobInviteConfig::APPLY_TYPE_30;
            }

            if (in_array(BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_COMPLETE_65, $person_search_type_arr)) {
                $model->resume_complete_type = BaseAdminJobInviteConfig::RESUME_COMPLETE_TYPE_YES;
            }

            if (in_array(BaseAdminJobInviteConfig::SEARCH_VERIFY_TYPE_RESUME_INVITE_30, $person_search_type_arr)) {
                $model->invite_type = BaseAdminJobInviteConfig::INVITE_TYPE_30;
            }
        }
        if ($params['inviteDeliveryWay'] == BaseAdminJobInviteConfig::INVITE_WAY_AUTO) {
            $model->match_type    = $params['personMatchType'] ?: '';
            $model->invite_number = $params['inviteNumber'] ?: 0;
            $model->active_day    = $params['personActiveDayNumber'] ?: 0;
            $model->remark        = $params['remark'] ?: '';
            $match_content        = [];
            if ($params['personMatchType']) {
                $person_match_type_arr = explode(',', $params['personMatchType']);
                // 学历
                if (in_array(BaseAdminJobInviteConfig::MATCH_TYPE_EDUCATION,
                        $person_match_type_arr) && $params['educationType']) {
                    $match_content['educationType'] = $params['educationType'];
                }
                // 专业
                if (in_array(BaseAdminJobInviteConfig::MATCH_TYPE_MAJOR,
                        $person_match_type_arr) && $params['majorId']) {
                    $match_content['majorId'] = $params['majorId'];
                }
                //意向职位类型
                if (in_array(BaseAdminJobInviteConfig::MATCH_TYPE_CATEGORY,
                        $person_match_type_arr) && $params['jobCategoryId'] > 0) {
                    $match_content['jobCategoryId'] = $params['jobCategoryId'];
                }
                //意向城市
                if (in_array(BaseAdminJobInviteConfig::MATCH_TYPE_CITY,
                        $person_match_type_arr) && $params['cityId'] > 0) {
                    $match_content['provinceId'] = $params['provinceId'];
                    $match_content['cityId']     = $params['cityId'];
                }
                //工作经验
                if (in_array(BaseAdminJobInviteConfig::MATCH_TYPE_EXPERIENCE,
                        $person_match_type_arr) && $params['experienceType'] > 0) {
                    $match_content['experienceType'] = $params['experienceType'];
                }
                //职称
                if (in_array(BaseAdminJobInviteConfig::MATCH_TYPE_TITLE,
                        $person_match_type_arr) && $params['titleType'] > 0) {
                    $match_content['titleType'] = $params['titleType'];
                }
                //政治面貌
                if (in_array(BaseAdminJobInviteConfig::MATCH_TYPE_POLITICAL,
                        $person_match_type_arr) && $params['politicalType'] > 0) {
                    $match_content['politicalType'] = $params['politicalType'];
                }
                //海外经验
                if (in_array(BaseAdminJobInviteConfig::MATCH_TYPE_ABROAD,
                        $person_match_type_arr) && $params['abroadType'] > 0) {
                    $match_content['abroadType'] = $params['abroadType'];
                }
            }
            $model->match_content = json_encode($match_content);
        }

        if ($model->save()) {
            return true;
        } else {
            //throw new Exception($model->getFirstErrorsMessage());
            return false;
        }
    }

    /**
     * 获取邀约投递列表
     * @param $params
     * @return array
     * @throws Exception
     */
    public static function getList($params)
    {
        if (!isset($params['portType']) || !in_array($params['portType'], [
                1,
                2,
            ])) {
            throw new Exception('参数错误，端口类型不存在');
        }
        if ($params['portType'] == 1) {
            return self::getListAdminInvite($params);
        } else {
            return self::getListCompanyInvite($params);
        }
    }

    /**
     * 获取运营平台邀约列表
     * @param $params
     * @return array
     */
    public static function getListAdminInvite($params)
    {
        return AdminJobInviteListService::getInstance()
            ->run($params);
    }

    /**
     * 获取单位端邀约列表
     * @param $params
     * @return array
     */
    public static function getListCompanyInvite($params)
    {
        return CompanyJobInviteListService::getInstance()
            ->getList($params);
    }

    /**
     * 导出邀约投递列表
     * @param $params
     * @throws Exception
     */
    public static function getListExport($params)
    {
        if (!isset($params['portType']) || !in_array($params['portType'], [
                1,
                2,
            ])) {
            throw new Exception('参数错误，端口类型不存在');
        }

        $adminId = Yii::$app->user->id;
        $app     = DownLoadTaskApplication::getInstance();
        $app->createAdmin($adminId, BaseAdminDownloadTask::TYPE_DELIVERY_INVITE_LIST, $params);

        return true;

        //        if ($params['portType'] == 1) {
        //            $list = self::getListAdminInvite($params);
        //        } else {
        //            $list = self::getListCompanyInvite($params);
        //        }
        //        $headers = [
        //            '人才ID',
        //            '人才姓名',
        //            '人才基本信息',
        //            '邀约投递职位',
        //            '邀约时间',
        //            '邀约人',
        //            '邀约方式',
        //            '是否投递',
        //            '投递时间',
        //            '应聘状态',
        //        ];
        //
        //        $data = [];
        //        foreach ($list as $item) {
        //            //先处理邀约方式--单位端没有所以注意判断
        //            if (isset($item['way_type'])) {
        //                $way_type_text = BaseAdminJobInviteConfig::INVITE_WAY_TYPE_TEXT[$item['way_type']];
        //            } else {
        //                $way_type_text = BaseAdminJobInviteConfig::INVITE_WAY_TYPE_TEXT[BaseAdminJobInviteConfig::INVITE_WAY_MANUAL];
        //            }
        //            //判断是否投递
        //            if ($item['apply_tid'] > 0) {
        //                $delivery_text = '是';
        //            } else {
        //                $delivery_text = '否';
        //            }
        //            //应聘状态
        //            if ($item['apply_status'] > 0) {
        //                $apply_status_text = BaseJobApply::PERSON_STATUS_LIST[$item['apply_status']];
        //            } elseif ($item['apply_site_status'] > 0) {
        //                $apply_status_text = BaseOffSiteJobApply::PERSON_STATUS_LIST[$item['apply_site_status']];
        //            } else {
        //                $apply_status_text = '';
        //            }
        //            //人才基本信息
        //            $gender_text     = BaseResume::GENDER_LIST[$item['gender']];
        //            $education_text  = BaseDictionary::getEducationName($item['education_id']);
        //            $major_text      = BaseMajor::getMajorName($item['major_id']);
        //            $resume_info_arr = [
        //                $item['age'] . '岁',
        //                $gender_text,
        //                $education_text,
        //                $major_text,
        //                $item['complete'] . '%',
        //            ];
        //            $resume_info     = implode('/', $resume_info_arr);
        //            $data[]          = [
        //                UUIDHelper::encrypt(UUIDHelper::TYPE_PERSON, $item['resume_id']),
        //                $item['resume_name'],
        //                $resume_info,
        //                $item['job_name'] . '(' . UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $item['job_id']) . ')',
        //                $item['invite_add_time'],
        //                $item['invite_username'],
        //                $way_type_text,
        //                $delivery_text,
        //                $item['apply_add_time'] ?: '',
        //                $apply_status_text,
        //            ];
        //        }
        //
        //        //释放
        //        unset($list);
        //        $excel    = new Excel();
        //        $fileName = $excel->export($data, $headers);
        //
        //        return [
        //            'download_url' => $fileName,
        //        ];
    }
}