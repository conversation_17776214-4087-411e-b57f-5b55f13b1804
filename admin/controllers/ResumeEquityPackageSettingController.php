<?php
/**
 * create user：gaocai
 * create time：2023/11/21 11:31
 */
namespace admin\controllers;

use admin\models\ResumeEquityPackageSetting;
use common\base\models\BaseResumeEquityPackageCategorySetting;
use common\base\models\BaseResumeEquityPackageSetting;
use common\base\models\BaseResumeEquitySetting;
use common\helpers\ArrayHelper;
use Yii;
use yii\base\Exception;

class ResumeEquityPackageSettingController extends BaseAdminController
{
    /**
     * 权益套餐配置列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionIndex()
    {
        try {
            $params = Yii::$app->request->get();
            $data   = ResumeEquityPackageSetting::index($params);

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 权益套餐配置添加
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAdd()
    {
        try {
            $params = Yii::$app->request->post();
            $res    = ResumeEquityPackageSetting::add($params);
            if ($res) {
                return $this->success($res, '添加成功');
            } else {
                return $this->fail('添加失败');
            }
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 权益套餐配置编辑
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEdit()
    {
        try {
            $params = Yii::$app->request->post();
            $res    = ResumeEquityPackageSetting::edit($params);
            if ($res) {
                return $this->success($res, '编辑成功');
            } else {
                return $this->fail('编辑失败');
            }
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 权益套餐配置编辑初始化
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditInit()
    {
        try {
            $id  = Yii::$app->request->get('id');
            $res = ResumeEquityPackageSetting::editInit($id);
            if ($res) {
                return $this->success($res);
            } else {
                return $this->fail('编辑初始化失败');
            }
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 权益套餐配置显示状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionStatus()
    {
        try {
            $params = Yii::$app->request->post();
            $res    = ResumeEquityPackageSetting::status($params);
            if ($res) {
                return $this->success($res, '操作成功');
            } else {
                return $this->fail('操作失败');
            }
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 权益套餐配置filter
     */
    public function actionFilter()
    {
        return $this->success([
            'equity_package_type_List'     => ArrayHelper::obj2Arr(BaseResumeEquityPackageSetting::EQUITY_PACKAGE_TYPE_LIST_TEXT),
            'buy_type_List'                => ArrayHelper::obj2Arr(BaseResumeEquityPackageSetting::TYPE_BUY_LIST),
            'status_List'                  => ArrayHelper::obj2Arr(BaseResumeEquityPackageSetting::STATUS_LIST),
            'equity_list'                  => ArrayHelper::obj2Arr(array_column(BaseResumeEquitySetting::getList(),
                'name', 'id')),
            'equity_package_category_list' => ArrayHelper::obj2Arr(array_column(BaseResumeEquityPackageCategorySetting::getList(),
                'name', 'id')),
        ]);
    }
}