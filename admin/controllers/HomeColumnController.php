<?php

namespace admin\controllers;

use admin\models\Area;
use admin\models\CategoryJob;
use admin\models\HomeColumn;
use admin\models\Major;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomeColumnDictionaryRelationship;
use common\helpers\ArrayHelper;
use Yii;

class HomeColumnController extends BaseAdminController
{

    /**
     * 获取参数
     */
    public function actionGetListParams()
    {
        return $this->success([
            'isHideList'           => ArrayHelper::obj2Arr(HomeColumn::IS_HIDE_LIST),
            'contentTypeList'      => ArrayHelper::obj2Arr(HomeColumn::CONTENT_TYPE_LIST),
            'templateTypeList'     => ArrayHelper::obj2Arr(HomeColumn::TEMPLATE_TYPE_LIST),
            'detailTypeList'       => ArrayHelper::obj2Arr(HomeColumn::DETAIL_TYPE_LIST),
            'list'                 => HomeColumn::getLevel1List(),
            'majorList'            => ArrayHelper::arr2KV(Major::find()
                ->select([
                    'id',
                    'name',
                ])
                ->where([
                    'level'  => 2,
                    'status' => 1,
                ])
                ->asArray()
                ->all(), 'id', 'name'),
            'areaList'             => Area::getAreaList(),
            'categoryList'         => CategoryJob::getAllCategoryJobList(),
            'operateAttributeList' => ArrayHelper::obj2Arr(BaseHomeColumn::OPERATE_ATTRIBUTE_LIST),
        ]);
    }

    /**
     * 获取整个列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetList()
    {
        return $this->success(HomeColumn::getList(Yii::$app->request->get()));
    }

    public function actionGetNewsList()
    {
        return $this->success(HomeColumn::getNewsList());
    }

    public function actionGetDictionaryList()
    {
        // 获取字典相关属性
        $id = Yii::$app->request->get('id');

        $dictionaryList = HomeColumn::getDictionaryList($id);

        return $this->success($dictionaryList);
    }

    /**
     * 新增编辑
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAdd()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $model   = new HomeColumn();
            $adminId = Yii::$app->user->id;
            $model->create(Yii::$app->request->post(), $adminId);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    public function actionSetDictionary()
    {
        $columnId   = Yii::$app->request->post('id');
        $majorId    = Yii::$app->request->post('majorId');
        $areaId     = Yii::$app->request->post('areaId');
        $categoryId = Yii::$app->request->post('categoryId');

        if (!$columnId) {
            return $this->fail('请选择栏目');
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            BaseHomeColumnDictionaryRelationship::setMajor($columnId, $majorId);
            BaseHomeColumnDictionaryRelationship::setArea($columnId, $areaId);
            BaseHomeColumnDictionaryRelationship::setCategory($columnId, $categoryId);

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }
}