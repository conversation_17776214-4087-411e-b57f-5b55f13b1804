<?php

namespace admin\controllers;

use Yii;
use yii\base\Exception;
use admin\models\FriendLinkConfig;

class FriendLinkConfigController extends BaseAdminController
{
    /**
     * 获取友情链接列表
     */
    public function actionIndex()
    {
        try {
            $params = Yii::$app->request->get();
            $data   = FriendLinkConfig::index($params);

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 友情链接添加
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAdd()
    {
        try {
            $params = Yii::$app->request->post();
            $res    = FriendLinkConfig::add($params);
            if ($res) {
                return $this->success($res, '添加成功');
            } else {
                return $this->fail('添加失败');
            }
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 友情链接编辑
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEdit()
    {
        try {
            $params = Yii::$app->request->post();
            $res    = FriendLinkConfig::edit($params);
            if ($res) {
                return $this->success($res, '编辑成功');
            } else {
                return $this->fail('编辑失败');
            }
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 友情链接编辑初始化
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEditInit()
    {
        try {
            $id  = Yii::$app->request->get('id');
            $res = FriendLinkConfig::editInit($id);
            if ($res) {
                return $this->success($res);
            } else {
                return $this->fail('编辑初始化失败');
            }
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 友情链接状态
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionStatus()
    {
        try {
            $id     = Yii::$app->request->post('id');
            $status = Yii::$app->request->post('status');
            $res    = FriendLinkConfig::status($id, $status);
            if ($res) {
                return $this->success($res, '操作成功');
            } else {
                return $this->fail('操作失败');
            }
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}