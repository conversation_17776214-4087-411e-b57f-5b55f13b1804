<?php

namespace admin\controllers;

use admin\models\HomeColumn;
use admin\models\Topic;
use common\helpers\FormatConverter;
use Yii;
use yii\base\Exception;
use yii\console\Response;

class TopicController extends BaseAdminController
{

    /**
     * 发布/编辑话题
     * @throws Exception
     */
    public function actionAdd()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $request = Yii::$app->request->post();

            $checkData = [
                'title',
                'subTitle',
                'coverUrl',
                'newsListJson',
            ];
            foreach ($checkData as $list) {
                if (!isset($request[$list])) {
                    throw new Exception('参数' . $list . '不能为空');
                }
            }
            $model = new Topic();
            $model->create(FormatConverter::convertHump($request));
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            throw new Exception($e->getMessage());
        }
    }

    /**
     * 获取话题详情
     * @return Response|\yii\web\Response
     */
    public function actionGetTopicDetail()
    {
        $request = Yii::$app->request->get();
        try {
            if (!isset($request['id'])) {
                throw new Exception('资讯id不能为空');
            }
            $model = new Topic();
            $list  = $model->getTopicDetail($request['id']);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取话题列表
     * @return Response|\yii\web\Response
     */
    public function actionGetTopicList()
    {
        $request = Yii::$app->request->get();
        try {
            $model = new Topic();
            $list  = $model->getTopicList(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取审核状态表
     * @return Response|\yii\web\Response
     */
    public function actionGetStatusList()
    {
        try {
            $model = new Topic();
            $list  = $model->getStatusList();

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 审核话题
     * @return Response|\yii\web\Response
     */
    public function actionAuditTopic()
    {
        $request = Yii::$app->request->post();
        try {
            $checkData = [
                'ids',
                'status',
            ];
            foreach ($checkData as $list) {
                if (!isset($request[$list])) {
                    throw new Exception('参数不能为空');
                }
            }
            $model = new Topic();
            $model->auditTopic(FormatConverter::convertHump($request));

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除话题
     * @return Response|\yii\web\Response
     */
    public function actionDeleteTopic()
    {
        $request = Yii::$app->request->post();
        try {
            $checkData = [
                'ids',
            ];
            foreach ($checkData as $list) {
                if (!isset($request[$list])) {
                    throw new Exception('参数不能为空');
                }
            }
            $model = new Topic();
            $model->deleteTopic(FormatConverter::convertHump($request));

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}