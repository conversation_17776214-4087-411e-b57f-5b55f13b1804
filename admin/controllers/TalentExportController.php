<?php

namespace admin\controllers;

use admin\models\TalentExport;
use common\base\models\BaseAdminDownloadTask;
use common\service\downloadTask\DownLoadTaskApplication;
use Yii;
use yii\base\Exception;

/**
 * 人才数据导出控制器
 */
class TalentExportController extends BaseAdminController
{
    /**
     * 获取筛选条件配置
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetFilterConfig()
    {
        try {
            $config = TalentExport::getFilterConfig();

            return $this->success($config);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 预览数据（分页显示）
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionPreview()
    {
        try {
            $params = Yii::$app->request->get();
            $data   = TalentExport::getPreviewData($params);

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 导出数据
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionExport()
    {
        try {
            $params  = Yii::$app->request->post();
            $adminId = Yii::$app->user->id;

            // 创建下载任务
            $app = DownLoadTaskApplication::getInstance();
            $app->createAdmin($adminId, BaseAdminDownloadTask::TYPE_TALENT_EXPORT, $params);

            return $this->success('数据开始导出，成功下载后会在企业微信通知，请后续留意');
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}
