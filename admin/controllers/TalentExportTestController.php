<?php

namespace admin\controllers;

use admin\models\TalentExport;
use Yii;

/**
 * 人才导出测试控制器 - 用于测试SQL查询
 */
class TalentExportTestController extends BaseAdminController
{
    /**
     * 测试SQL查询
     */
    public function actionTestQuery()
    {
        try {
            // 测试参数 - 模拟前端传参
            $params = [
                'startDate' => '2025-06-12',
                'endDate' => '2025-07-09',
                'hasEnglish' => 1,
                'chineseMoreThan4' => 1,
                'hasMultipleNumbers' => 1,
                'specialKeywords' => ['穆罕默德', '哈桑', '莎米拉'],
                'targetCountries' => [3879, 3881, 3882],
                'page' => 1,
                'pageSize' => 10
            ];

            // 测试查询
            $data = TalentExport::getPreviewData($params);

            return $this->success([
                'message' => 'SQL查询测试成功',
                'count' => count($data['list']),
                'total' => $data['pages']['total'],
                'params' => $params,
                'sample' => array_slice($data['list'], 0, 3) // 只返回前3条作为示例
            ]);

        } catch (\Exception $e) {
            return $this->fail('SQL查询测试失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 测试配置获取
     */
    public function actionTestConfig()
    {
        try {
            $config = TalentExport::getFilterConfig();
            
            return $this->success([
                'message' => '配置获取测试成功',
                'targetCountriesCount' => count($config['targetCountries']),
                'specialKeywordsCount' => count($config['specialKeywords']),
                'sampleCountries' => array_slice($config['targetCountries'], 0, 5),
                'sampleKeywords' => array_slice($config['specialKeywords'], 0, 5)
            ]);
            
        } catch (\Exception $e) {
            return $this->fail('配置获取测试失败: ' . $e->getMessage());
        }
    }
}
