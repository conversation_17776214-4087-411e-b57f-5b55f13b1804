<?php

namespace admin\controllers;

use admin\models\Announcement;
use admin\models\Article;
use admin\models\ArticleAttribute;
use admin\models\CategoryJob;
use admin\models\Company;
use admin\models\Dictionary;
use admin\models\HomeColumn;
use admin\models\Job;
use admin\models\JobTemp;
use admin\models\Major;
use admin\models\UploadForm;
use common\base\models\BaseAdminDownloadTask;
use common\base\models\BaseAnnouncementLog;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseJob;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseJobTemp;
use common\components\MessageException;
use common\helpers\ArrayHelper;
use common\helpers\FormatConverter;
use common\libs\ColumnAuto\AnnouncementAutoClassify;
use common\libs\JobBatchImport;
use common\service\announcement\AddJobTempService;
use common\service\announcement\AuditHandleService;
use common\service\announcement\AuditService;
use common\service\announcement\BaseService;
use common\service\announcement\BatchDeleteService;
use common\service\announcement\ChangeAttributeService;
use common\service\announcement\DeleteJobService;
use common\service\announcement\DeleteService;
use common\service\announcement\HiddenJobService;
use common\service\announcement\HiddenService;
use common\service\announcement\OfflineJobService;
use common\service\announcement\OfflineService;
use common\service\announcement\OnlineJobService;
use common\service\announcement\OnlineService;
use common\service\announcement\RemoveService;
use common\service\announcement\ShowJobService;
use common\service\announcement\ShowService;
use common\service\CommonService;
use common\service\downloadTask\DownLoadTaskApplication;
use common\service\downloadTask\ExecuteAdminService;
use common\service\v2\announcement\AuditDetailService;
use common\service\v2\announcement\AuditStatusService;
use common\service\v2\announcement\DetailService;
use common\service\v2\announcement\EditInitService;
use common\service\v2\announcement\EditService;
use common\service\v2\announcement\IdentityService;
use common\service\v2\announcement\AddService;
use common\service\v2\announcement\IndexService;
use common\service\v2\announcement\RefreshService;
use common\service\v2\job\EditInitService as JobEditInitService;
use common\service\v2\job\TempAddBatchImportService;
use common\service\v2\job\TempAddService;
use common\service\v2\job\TempCopyService;
use common\service\v2\job\TempDeleteService;
use common\service\v2\job\TempEditInitService;
use common\service\v2\job\TempEditService;
use common\service\v2\job\CopyService;
use common\service\v2\job\DeleteService as JobDeleteService;
use Yii;
use yii\base\Exception;
use yii\console\Response;

class AnnouncementController extends BaseAdminController
{
    /**
     * 添加公告下的临时职位
     */
    public function actionTempJobAdd()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $data = (new TempAddService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run();
            $transaction->commit();

            return $this->success($data);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 编辑公告下的职位初始化
     * 1、编辑正式的职位产生新的临时职位
     * 2、编辑临时职位则修改临时职位
     */
    public function actionJobEditInit()
    {
        ini_set('memory_limit', '1024M');
        try {
            $params = Yii::$app->request->get();
            if (!isset($params['id']) || !isset($params['isTemp']) || !isset($params['jobId'])) {
                throw new Exception('参数错误');
            }
            if ($params['id'] > 0 && $params['isTemp'] == BaseJobTemp::IS_TEMP_YES) {
                //临时职位初始化
                $data = (new TempEditInitService())->setPlatform(CommonService::PLATFORM_ADMIN)
                    ->run();
            } else {
                if ($params['jobId'] <= 0) {
                    throw new Exception('参数错误');
                }
                if (BaseJob::findOne($params['jobId'])->announcement_id == 0) {
                    throw new Exception('无权访问');
                }
                //正式职位初始化
                $data = (new JobEditInitService())->setPlatform(CommonService::PLATFORM_ADMIN)
                    ->run();
            }

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 编辑公告下的职位
     */
    public function actionTempJobEdit()
    {
        ini_set('memory_limit', '1024M');
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $data = (new TempEditService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run();
            $transaction->commit();

            return $this->success($data);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告下复制职位
     * @return Response|\yii\web\Response
     */
    public function actionTempJobCopy()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $data = (new TempCopyService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run();
            $transaction->commit();

            return $this->success($data);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告下职位删除
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionJobDelete()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $params = Yii::$app->request->post();
            if (!isset($params['id']) || !isset($params['isTemp']) || !isset($params['jobId'])) {
                throw new Exception('参数错误');
            }
            if ($params['id'] > 0 && $params['isTemp'] == BaseJobTemp::IS_TEMP_YES && $params['jobId'] == 0) {
                //临时职位初始化
                (new TempDeleteService())->setPlatform(CommonService::PLATFORM_ADMIN)
                    ->run();
            } else {
                if ($params['jobId'] <= 0) {
                    throw new Exception('参数错误');
                }
                $announcementId = BaseJob::findOne($params['jobId'])->announcement_id;
                $countJob       = BaseJob::find()
                    ->where([
                        'announcement_id' => $announcementId,
                        'is_show'         => BaseJob::IS_SHOW_YES,
                        'status'          => [
                            BaseJob::STATUS_WAIT,
                            BaseJob::STATUS_ONLINE,
                            BaseJob::STATUS_OFFLINE,
                        ],
                    ])
                    ->count();
                if ($countJob <= 1) {
                    throw new Exception('至少保留一条有效职位！');
                }
                //正式职位初始化
                (new JobDeleteService())->setPlatform(CommonService::PLATFORM_ADMIN)
                    ->delete($params);
                if ($params['id'] > 0) {
                    (new TempDeleteService())->setPlatform(CommonService::PLATFORM_ADMIN)
                        ->run();
                }
            }
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位批量导入
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionJobTempAddBatchImport()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $data = (new TempAddBatchImportService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run();
            $transaction->commit();

            return $this->success($data, '成功批量导入数据');
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告识别富文本的内容
     */
    public function actionIdentity()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $data = (new IdentityService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run();
            $transaction->commit();

            return $this->success($data);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告添加
     * @return Response|\yii\web\Response
     */
    public function actionAdd()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $data = (new AddService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run();
            $transaction->commit();

            return $this->success($data);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告编辑初始化
     * @return Response|\yii\web\Response
     */
    public function actionEditInit()
    {
        try {
            return $this->success((new EditInitService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run());
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告编辑
     * @return Response|\yii\web\Response
     */
    public function actionEdit()
    {
        // 关闭内存限制
        ini_set('memory_limit', '1024M');
        // 关闭超时限制
        set_time_limit(0);
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $data = (new EditService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run();
            $transaction->commit();

            return $this->success($data);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告详情
     * @return Response|\yii\web\Response
     */
    public function actionDetail()
    {
        try {
            return $this->success((new DetailService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run());
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告审核详情
     * @return Response|\yii\web\Response
     */
    public function actionAuditDetail()
    {
        //增加内存开销
        ini_set('memory_limit', '1024M');
        try {
            return $this->success((new AuditDetailService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run());
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位审核通过/拒绝
     * @return Response|\yii\web\Response
     */
    public function actionAuditStatus()
    {
        ini_set('memory_limit', '1024M');
        $transaction = Yii::$app->db->beginTransaction();
        try {
            (new AuditStatusService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run();
            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 合作公告列表
     */
    public function actionCooperationIndex()
    {
        try {
            $params = \Yii::$app->request->get();

            if ($params['export'] == 1) {
                $params['exportIsCooperation'] = 1;

                //导出===去到下载中心
                $app = DownLoadTaskApplication::getInstance();
                $app->createAdmin(Yii::$app->user->id, BaseAdminDownloadTask::TYPE_ANNOUNCEMENT_LIST, $params);

                return $this->success('数据开始导出,成功下载后会在企业微信通知,请后续留意');
            }

            return $this->success((new IndexService(IndexService::CHANNEL_COOPERATION))->setPlatform(CommonService::PLATFORM_ADMIN)
                ->cooperationRun($params));
        } catch (\yii\db\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 非合作公告列表
     */
    public function actionUnCooperationIndex()
    {
        try {
            $params = \Yii::$app->request->get();
            if ($params['export'] == 1) {
                $params['exportIsCooperation'] = 2;
                //导出===去到下载中心
                $app = DownLoadTaskApplication::getInstance();
                $app->createAdmin(Yii::$app->user->id, BaseAdminDownloadTask::TYPE_ANNOUNCEMENT_LIST, $params);

                return $this->success('数据开始导出,成功下载后会在企业微信通知,请后续留意');
            }

            return $this->success((new IndexService(IndexService::CHANNEL_UNCOOPERATION))->setPlatform(CommonService::PLATFORM_ADMIN)
                ->unCooperationRun($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 非合作公告审核列表
     */
    public function actionUnCooperationAuditIndex()
    {
        try {
            $params = \Yii::$app->request->get();

            return $this->success((new IndexService(IndexService::CHANNEL_UNCOOPERATION_AUDIT))->setPlatform(CommonService::PLATFORM_ADMIN)
                ->unCooperationAuditRun($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 合作公告审核列表
     */
    public function actionCooperationAuditIndex()
    {
        try {
            $params = \Yii::$app->request->get();

            return $this->success((new IndexService(IndexService::CHANNEL_UNCOOPERATION_AUDIT))->setPlatform(CommonService::PLATFORM_ADMIN)
                ->cooperationAuditRun($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告操作-刷新
     */
    public function actionRefresh()
    {
        $params      = Yii::$app->request->post();
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $service = new RefreshService();
            $service->setPlatform(CommonService::PLATFORM_ADMIN)
                ->refresh($params);

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告操作-刷新
     */
    public function actionRefreshBatch()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $params = Yii::$app->request->post();

            $service = new RefreshService();
            $res     = $service->setPlatform(CommonService::PLATFORM_ADMIN)
                ->refreshBatch($params);
            $transaction->commit();

            return $this->success([], $res['failMessage'], $res['failMessage'] ? 201 : 200);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告操作-删除
     */
    public function actionDelete()
    {
        $params      = Yii::$app->request->post();
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $service = new \common\service\v2\announcement\DeleteService();
            $service->setPlatform(CommonService::PLATFORM_ADMIN)
                ->delete($params);

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告操作-删除
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDeleteBatch()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $params = Yii::$app->request->post();

            $service = new \common\service\v2\announcement\DeleteService();
            $res     = $service->setPlatform(CommonService::PLATFORM_ADMIN)
                ->deleteBatch($params);
            $transaction->commit();

            return $this->success([], $res['failMessage'], $res['failMessage'] ? 201 : 200);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告操作-再发布
     */
    public function actionRepublish()
    {
        $params      = Yii::$app->request->post();
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $service = new \common\service\v2\announcement\RepublishService();
            $res     = $service->setPlatform(CommonService::PLATFORM_ADMIN)
                ->repubilsh($params);

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告操作-批量再发布
     */
    public function actionRepublishBatch()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $params = Yii::$app->request->post();

            $service = new \common\service\v2\announcement\RepublishService();
            $res     = $service->setPlatform(CommonService::PLATFORM_ADMIN)
                ->repubilshBatch($params);
            $transaction->commit();

            return $this->success([], $res['failMessage'], $res['failMessage'] ? 201 : 200);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告操作-下线
     */
    public function actionOffline()
    {
        $params      = Yii::$app->request->post();
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $service = new \common\service\v2\announcement\OfflineService();
            $service->setPlatform(CommonService::PLATFORM_ADMIN)
                ->offline($params);

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告操作-批量下线
     */
    public function actionOfflineBatch()
    {
        $params      = Yii::$app->request->post();
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $service = new \common\service\v2\announcement\OfflineService();
            $res     = $service->setPlatform(CommonService::PLATFORM_ADMIN)
                ->offlineBatch($params);
            $transaction->commit();

            return $this->success([], $res['failMessage'], $res['failMessage'] ? 201 : 200);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    public function actionShow()
    {
        $params      = Yii::$app->request->post();
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $service = new \common\service\v2\announcement\ShowService();
            $service->setPlatform(CommonService::PLATFORM_ADMIN)
                ->show($params);

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告操作-批量显示
     */
    public function actionShowBatch()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $params = Yii::$app->request->post();

            $service = new \common\service\v2\announcement\ShowService();
            $res     = $service->setPlatform(CommonService::PLATFORM_ADMIN)
                ->showBatch($params);
            $transaction->commit();

            return $this->success([], $res['failMessage'], $res['failMessage'] ? 201 : 200);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    public function actionHide()
    {
        $params      = Yii::$app->request->post();
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $service = new \common\service\v2\announcement\HideService();
            $service->setPlatform(CommonService::PLATFORM_ADMIN)
                ->hide($params);

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告操作-批量显示
     */
    public function actionHideBatch()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $params = Yii::$app->request->post();

            $service = new \common\service\v2\announcement\HideService();
            $res     = $service->setPlatform(CommonService::PLATFORM_ADMIN)
                ->hideBatch($params);
            $transaction->commit();

            return $this->success([], $res['failMessage'], $res['failMessage'] ? 201 : 200);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量编辑属性
     * @return Response|\yii\web\Response
     */
    public function actionChangeAttribute()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $params = Yii::$app->request->post();
            if (empty($params['announcementId'])) {
                throw new Exception('参数缺失');
            }

            $service = new \common\service\v2\announcement\ChangeAttributeService();
            $res     = $service->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run($params);
            $transaction->commit();

            return $this->success([], $res['failMessage'], $res['failMessage'] ? 201 : 200);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告修改单位主页排序值
     */
    public function actionChangeHomeSort()
    {
        $id = Yii::$app->request->post('id');

        $announcement = Announcement::findOne($id);
        if (!$announcement) {
            return $this->fail('公告不存在');
        }

        try {
            $announcement->home_sort = Yii::$app->request->post('homeSort');
            if (!$announcement->save()) {
                throw new Exception($announcement->getFirstErrorsMessage());
            }

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告文档属性数据
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionAttributeData()
    {
        $params = Yii::$app->request->get();

        try {
            if (!$params['articleId']) {
                throw new Exception('参数缺失');
            }

            return $this->success(ArticleAttribute::getAttributeData($params['articleId']));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }


    //======== ======= ======= =上面是新增接口= ======= ======= ======= =======
    //======== ======= ======= =上面是新增接口= ======= ======= ======= =======
    //======== ======= ======= =上面是新增接口= ======= ======= ======= =======
    //======== ======= ======= =上面是新增接口= ======= ======= ======= =======
    //======== ======= ======= =上面是新增接口= ======= ======= ======= =======
    //======== ======= ======= =上面是新增接口= ======= ======= ======= =======
    //======== ======= ======= =上面是新增接口= ======= ======= ======= =======
    //======== ======= ======= =上面是新增接口= ======= ======= ======= =======
    //======== ======= ======= =上面是新增接口= ======= ======= ======= =======
    //======== ======= ======= ======= ======= ======= ======= =======
    //======== ======= ======= ======= ======= ======= ======= =======
    //======== ======= ======= ======= ======= ======= ======= =======
    //======== ======= ======= ======= ======= ======= ======= =======
    //======== ======= ======= ======= ======= ======= ======= =======
    //======== ======= ======= ======= ======= ======= ======= =======
    //======== ======= ======= ======= ======= ======= ======= =======
    //======== ======= ======= ======= ======= ======= ======= =======
    //======== ======= ======= ======= ======= ======= ======= =======
    //======== ======= ======= ======= ======= ======= ======= =======
    //======== ======= ======= ======= ======= ======= ======= =======
    //======== ======= ======= ======= ======= ======= ======= =======
    //======== ======= ======= ======= ======= ======= ======= =======
    //======== ======= ======= ======= ======= ======= ======= =======
    //======== ======= ======= ======= ======= ======= ======= =======
    //======== ======= ======= ======= ======= ======= ======= =======
    //======== ======= ======= ======= ======= ======= ======= =======
    //======== ======= ======= ======= ======= ======= ======= =======
    /**
     * 获取公告发布参数
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetAddParams()
    {
        $columnList = HomeColumn::getAllList();

        return $this->success([
            // 栏目列表
            'columnList'                => $columnList,
            // 副栏目
            'subColumnList'             => $columnList,
            // 合作单位类型
            'companyCooperationList'    => ArrayHelper::obj2Arr(Company::COOPERATIVE_UNIT_LIST),
            // 文档属性
            'documentList'              => ArrayHelper::obj2Arr(ArticleAttribute::ATTRIBUTE_ANNOUNCEMENT_LIST),
            // 非海外属性文档
            'notOverseasAttributeList'  => ArrayHelper::obj2Arr(ArticleAttribute::ATTRIBUTE_ANNOUNCEMENT_NOT_HAW_WAI_LIST),
            //海外属性文档
            'overseasAttributeList'     => ArrayHelper::obj2Arr(ArticleAttribute::ANNOUNCEMENT_ATTRIBUTE_ABROAD_LIST),
            // 应聘方式
            'applyList'                 => ArrayHelper::obj2Arr(Dictionary::getSignUpList()),
            // 特色标签
            'tagList'                   => ArrayHelper::obj2Arr(Article::ATTRIBUTE_TAG_LIST),
            // 推荐位
            'recommendList'             => ArrayHelper::obj2Arr(Article::ATTRIBUTE_RECOMMEND_LIST),
            // 页面模版
            'templateList'              => ArrayHelper::obj2Arr(Announcement::TEMPLATE_LIST),
            // 背景图类型
            'backgroundImgFileTypeList' => ArrayHelper::obj2Arr(Announcement::BACKGROUND_IMG_FILE_TYPE_LIST),
        ]);
    }

    /**
     * 获取公告列表检索参数
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetSearchParams()
    {
        return $this->success([
            // 栏目列表
            'columnList'               => HomeColumn::getAllList(),
            // 审核状态
            'auditStatusList'          => ArrayHelper::obj2Arr(Announcement::STATUS_AUDIT_LISTS),
            // 职位类型
            'jobTypeList'              => ArrayHelper::objMoreArr(CategoryJob::getCompanyCategoryJobList()),
            // 招聘状态
            'statusRecruitList'        => ArrayHelper::obj2Arr(Article::STATUS_LIST),
            // 需求专业
            'majorList'                => ArrayHelper::objMoreArr(Major::getAllMajorList()),
            // 学历要求
            'educationTypeList'        => ArrayHelper::obj2Arr(Dictionary::getEducationList()),
            // 公告属性
            'attributeDocument'        => ArrayHelper::obj2Arr(ArticleAttribute::ATTRIBUTE_ANNOUNCEMENT_LIST),
            //海外公告属性
            'overseasAttributeList'    => ArrayHelper::obj2Arr(ArticleAttribute::ANNOUNCEMENT_ATTRIBUTE_ABROAD_LIST),
            // 非海外属性文档
            'notOverseasAttributeList' => ArrayHelper::obj2Arr(ArticleAttribute::ATTRIBUTE_ANNOUNCEMENT_NOT_HAW_WAI_LIST),
            // 显示状态
            'showStatusList'           => ArrayHelper::obj2Arr(Article::IS_SHOW_LIST),
            // 属性
            'attributeList'            => ArrayHelper::kV2LV([
                [
                    'v'        => '公告属性',
                    'k'        => 'notOverseasAttributeList',
                    'children' => ArrayHelper::obj2Arr(ArticleAttribute::ATTRIBUTE_ANNOUNCEMENT_NOT_HAW_WAI_LIST),
                ],
                [
                    'v'        => '高才海外相关属性',
                    'k'        => 'attributeDocument',
                    'children' => ArrayHelper::obj2Arr(ArticleAttribute::ANNOUNCEMENT_ATTRIBUTE_ABROAD_LIST),
                ],
            ]),
        ]);
    }

    /**
     * 获取类型单位
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionSearchCompanyList()
    {
        $params = Yii::$app->request->get();

        try {
            if (!$params['type']) {
                throw new Exception('合作类型不能为空');
            }

            return $this->success(Announcement::searchCompanyList($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取所有单位
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetAllCompanyList()
    {
        $params = Yii::$app->request->get();

        return $this->success(Announcement::getAllCompanyList($params));
    }

    /**
     * 识别富文本的内容
     */
    public function actionIdentityEditor()
    {
        $content = Yii::$app->request->post('content');
        try {
            $transaction = Yii::$app->db->beginTransaction();
            $model       = new Announcement();
            $rs          = $model->identityEditor($content);
            $transaction->commit();

            return $this->success($rs);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 检查公告标题唯一性
     * @return Response|\yii\web\Response
     */
    public function actionCheckTitleOnly()
    {
        $params = Yii::$app->request->get();
        if (!$params['title']) {
            return $this->fail('标题不能为空');
        }

        return $this->success(Article::checkTitleOnly($params['title'], $params['id']));
    }

    //    /**
    //     * 公告保存/提交
    //     * @return \yii\console\Response|\yii\web\Response
    //     * @throws \Exception
    //     */
    //    public function actionSubmitAdd()
    //    {
    //        //开辟零时内存
    //        ini_set('memory_limit', '2048M');
    //
    //        $data = Yii::$app->request->post();
    //
    //        $adminId     = Yii::$app->user->id;
    //        $transaction = Yii::$app->db->beginTransaction();
    //
    //        try {
    //            $addService = new AddService();
    //
    //            if ($data['announcementId']) {
    //                // 编辑发布
    //                if ($data['submitType'] == $addService::SAVE_TYPE_AUDIT) {
    //                    $addService->setOperator($adminId, AddService::OPERATOR_TYPE_ADMIN)
    //                        ->setStaging()
    //                        ->setEditData($data)
    //                        ->run();
    //                } else {
    //                    $addService->setOperator($adminId, AddService::OPERATOR_TYPE_ADMIN)
    //                        ->setAudit()
    //                        ->setEditData($data)
    //                        ->run();
    //                }
    //            } else {
    //                // 保存发布
    //                $addService->setOperator($adminId, AddService::OPERATOR_TYPE_ADMIN)
    //                    ->setStaging($data['submitType'])
    //                    ->setAddData($data)
    //                    ->run();
    //            }
    //
    //            $transaction->commit();
    //            if ($data['submitType'] == $addService::SAVE_TYPE_STAGING) {
    //                return $this->success(['id' => $addService->announcementId ?: 0], '保存成功');
    //            } else {
    //                return $this->success();
    //            }
    //        } catch (\Exception $e) {
    //            $transaction->rollBack();
    //
    //            return $this->fail($e->getMessage());
    //        }
    //    }

    /**
     * 获取公告列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetList()
    {
        $params = Yii::$app->request->get();

        $params['admin'] = \Yii::$app->user->id;

        return $this->success(Announcement::getList($params));
    }

    /**
     * 获取公告列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetSimpleList()
    {
        $params = Yii::$app->request->get();

        $params['admin'] = \Yii::$app->user->id;

        return $this->success(Announcement::getsSimpleList($params));
    }


    //===================公告发布时的临时职位信息与操作 start===================

    /**
     * 临时职位-信息列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionTemporaryJobList()
    {
        $params = Yii::$app->request->get();
        if (!$params['jobTempId']) {
            return $this->fail();
        }

        return $this->success(JobTemp::getJobTempList($params));
    }

    /**
     * 选择职位模版填充编辑数据
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionJobEditData()
    {
        $id = Yii::$app->request->get('id');
        if (!$id) {
            return $this->fail();
        }

        return $this->success(Job::getJobEditData($id));
    }

    /**
     * 获取职位名称列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionJobNameList()
    {
        $params = Yii::$app->request->get();

        return $this->success(Job::getJobNameList($params));
    }

    /**
     * 上传临时Excel文件
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionUploadExcel()
    {
        $model = new UploadForm();
        $model->setUploadType('file');
        $transaction = \Yii::$app->db->beginTransaction();

        try {
            $path = 'job_temp_excel';
            $data = $model->temporaryUploadExcel('file', $path);

            $transaction->commit();

            return $this->success([
                'url'     => $data['path'],
                'fullUrl' => \Yii::$app->params['homeUrl'] . '/' . $data['path'],
            ]);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 职位批量导入
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionJobTemporaryBatchImport()
    {
        $request = Yii::$app->request->post();

        $filePath = $request['filePath'];
        if (!$filePath || $request['companyId'] <= 0) {
            return $this->fail('参数缺失');
        }
        $transaction = \Yii::$app->db->beginTransaction();

        try {
            $data = JobTemp::jobTemporaryBatchImport($request, JobBatchImport::PLATFORM_ADMIN);

            $transaction->commit();

            return $this->success($data, '成功批量导入数据');
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    //===================公告发布时的临时职位信息与操作 end===================
    //
    //    /**
    //     * 公告详情
    //     * @return \yii\console\Response|\yii\web\Response
    //     */
    //    public function actionDetail()
    //    {
    //        $params = Yii::$app->request->get();
    //        if (!$params['id'] || !$params['type']) {
    //            return $this->fail('参数缺失');
    //        }
    //
    //        try {
    //            return $this->success(Announcement::getDetail($params));
    //        } catch (\Exception $e) {
    //            return $this->fail($e->getMessage());
    //        }
    //    }
    //
    //    /**
    //     * 审核详情
    //     * @return \yii\console\Response|\yii\web\Response
    //     */
    //    public function actionAuditDetail()
    //    {
    //        $params = Yii::$app->request->get();
    //        if (!$params['id']) {
    //            return $this->fail();
    //        }
    //
    //        try {
    //            return $this->success(Announcement::getAuditDetail($params));
    //        } catch (\Exception $e) {
    //            return $this->fail($e->getMessage());
    //        }
    //    }

    /**
     * 职位审核详情列表
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionJobAuditDetailList()
    {
        $params = Yii::$app->request->get();
        if (!$params['id']) {
            return $this->fail('公告id参数缺失');
        }

        return $this->success(Job::jobAuditDetailList($params));
    }

    /**
     * 公告操作-审核(通过、拒绝、审核拒绝并编辑)
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAuditHandle()
    {
        $params  = Yii::$app->request->post();
        $adminId = Yii::$app->user->id;

        if (!$params['id']) {
            return $this->fail('参数缺失');
        }
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $auditHandleService = new AuditHandleService();
            if ($params['auditStatus'] == $auditHandleService::TYPE_AUDIT_PASS) {
                $auditHandleService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setAuditPass()
                    ->setData($params)
                    ->run();
            } elseif ($params['auditStatus'] == $auditHandleService::TYPE_AUDIT_REFUSE) {
                $auditHandleService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setAuditRefuse()
                    ->setData($params)
                    ->run();
            } else {
                throw new Exception('审核类型非法');
            }

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告职位列表（非合作单位）
     */
    public function actionJobList()
    {
        $request = Yii::$app->request->get();

        try {
            $list = Job::getAnnouncementJobListAdmin(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告职位列表（简版）
     */
    public function actionSimpleJobList()
    {
        $request = Yii::$app->request->get();

        try {
            $list = Job::getAnnouncementSimpleJobList(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 审核列表（非合作单位）
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionNoncooperationAuditList()
    {
        $params = Yii::$app->request->get();

        try {
            return $this->success(Announcement::getNoncooperationAuditList($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    //    /**
    //     * 批量复制
    //     * @return Response|\yii\web\Response
    //     */
    //    public function actionBatchCopy()
    //    {
    //        $transaction = Yii::$app->db->beginTransaction();
    //
    //        try {
    //            $request   = Yii::$app->request->post();
    //            $checkData = [
    //                'ids',
    //                'homeColumnId',
    //            ];
    //            foreach ($checkData as $list) {
    //                if (!isset($request[$list])) {
    //                    throw new Exception('参数不能为空');
    //                }
    //            }
    //            $ids     = explode(',', $request['ids']);
    //            $adminId = Yii::$app->user->id;
    //
    //            foreach ($ids as $id) {
    //                for ($i = 0; $i < $request['amount']; $i++) {
    //                    $copyService = new CopyService();
    //                    $params      = [
    //                        'announcementId' => $id,
    //                        'homeColumnId'   => $request['homeColumnId'],
    //                    ];
    //                    $copyService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
    //                        ->setData($params)
    //                        ->run();
    //                }
    //            }
    //
    //            $list = Announcement::getTemplateAmount($ids, $request['homeColumnId']);
    //            $transaction->commit();
    //
    //            return $this->success($list);
    //        } catch (\Exception $e) {
    //            $transaction->rollBack();
    //
    //            return $this->fail($e->getMessage());
    //        }
    //    }
    //
    //    /**
    //     * 批量移动
    //     * @return Response|\yii\web\Response
    //     */
    //    public function actionBatchMove()
    //    {
    //        $transaction = Yii::$app->db->beginTransaction();
    //
    //        try {
    //            $request   = Yii::$app->request->post();
    //            $checkData = [
    //                'ids',
    //                'homeColumnId',
    //            ];
    //            foreach ($checkData as $list) {
    //                if (!isset($request[$list])) {
    //                    throw new Exception('参数不能为空');
    //                }
    //            }
    //            $ids     = explode(',', $request['ids']);
    //            $adminId = Yii::$app->user->id;
    //
    //            foreach ($ids as $id) {
    //                $copyService = new RemoveService();
    //                $params      = [
    //                    'announcementId' => $id,
    //                    'homeColumnId'   => $request['homeColumnId'],
    //                ];
    //                $copyService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
    //                    ->setData($params)
    //                    ->run();
    //            }
    //
    //            $list = Announcement::getTemplateAmount($ids, $request['homeColumnId']);
    //            $transaction->commit();
    //
    //            return $this->success($list);
    //        } catch (\Exception $e) {
    //            $transaction->rollBack();
    //
    //            return $this->fail($e->getMessage());
    //        }
    //    }
    //
    //    /**
    //     * 批量审核
    //     * @return Response|\yii\web\Response
    //     */
    //    public function actionBatchAudit()
    //    {
    //        $transaction = Yii::$app->db->beginTransaction();
    //        try {
    //            $request   = Yii::$app->request->post();
    //            $checkData = [
    //                'ids',
    //                'status',
    //            ];
    //            foreach ($checkData as $list) {
    //                if (!isset($request[$list])) {
    //                    throw new Exception('参数不能为空');
    //                }
    //            }
    //            $ids     = explode(',', $request['ids']);
    //            $adminId = Yii::$app->user->id;
    //
    //            foreach ($ids as $id) {
    //                $auditService = new AuditService();
    //                $params       = [
    //                    'announcementId' => $id,
    //                    'status'         => $request['status'],
    //                    'opinion'        => $request['opinion'],
    //                ];
    //
    //                $auditService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
    //                    ->setData($params)
    //                    ->run();
    //            }
    //            $transaction->commit();
    //
    //            return $this->success();
    //        } catch (\Exception $e) {
    //            $transaction->rollBack();
    //
    //            return $this->fail($e->getMessage());
    //        }
    //    }

    /**
     * 审核列表（合作单位）
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionAuditList()
    {
        $params = Yii::$app->request->get();

        try {
            return $this->success(Announcement::getAuditList($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 站外投递列表(非合作单位)
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionOffJobApplyList()
    {
        $params = Yii::$app->request->get();

        return $this->success(Job::offJobApplyList(FormatConverter::convertHump($params)));
    }

    /**
     * 公告操作日志
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetAnnouncementHandleLog()
    {
        $params = Yii::$app->request->get();

        try {
            if (!$params['id']) {
                throw new Exception('公告id不能为空');
            }

            return $this->success(Announcement::getAnnouncementHandleLog($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 业务--站内投递(收到的简历变更站内投递)
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetAnnouncementJobApplyList()
    {
        $params = Yii::$app->request->get();

        try {
            if (!$params['id']) {
                throw new Exception('公告id不能为空');
            }

            return $this->success(Announcement::getAnnouncementJobApplyList(FormatConverter::convertHump($params)));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 业务--站外投递
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetOutsideApplyList()
    {
        $params = Yii::$app->request->get();

        try {
            if (!$params['id']) {
                throw new Exception('公告id不能为空');
            }

            return $this->success(Announcement::getOutsideApplyList(FormatConverter::convertHump($params)));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 业务--面试邀约
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetAnnouncementInterviewList()
    {
        $params = Yii::$app->request->get();

        try {
            if (!$params['id']) {
                throw new Exception('公告id不能为空');
            }

            return $this->success(Announcement::getAnnouncementInterviewList(FormatConverter::convertHump($params)));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 业务--下载的简历
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetResumeDownloadLog()
    {
        $params = Yii::$app->request->get();

        try {
            if (!$params['id']) {
                throw new Exception('公告id不能为空');
            }

            return $this->success(Announcement::getResumeDownloadLog(FormatConverter::convertHump($params)));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 业务--公告操作日志下操作类型
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetHandleTypeList()
    {
        try {
            return $this->success(Announcement::getHandleTypeList());
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告操作-文档属性排序时间刷新
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionRefreshSortTime()
    {
        // 获取POST请求中的参数
        $params = Yii::$app->request->post();

        // 开始事务
        $transaction = Yii::$app->db->beginTransaction();
        try {
            // 判断参数是否缺失
            if (!$params['articleId'] && !$params['attributeId']) {
                throw new Exception('参数缺失');
            }

            // 提交事务
            $transaction->commit();

            // 调用ArticleAttribute类的refreshSortTime方法，刷新文档属性，并返回成功信息
            return $this->success(ArticleAttribute::refreshSortTime($params['articleId'], $params['attributeId']),
                '文档属性刷新成功');
        } catch (Exception $e) {
            // 回滚事务
            $transaction->rollBack();

            // 返回失败信息
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量删除
     * @return Response|\yii\web\Response
     */
    public function actionBatchDelete()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $request   = Yii::$app->request->post();
            $checkData = [
                'ids',
            ];
            foreach ($checkData as $list) {
                if (!isset($request[$list])) {
                    throw new Exception('参数不能为空');
                }
            }
            $ids     = explode(',', $request['ids']);
            $adminId = Yii::$app->user->id;

            foreach ($ids as $id) {
                $deleteService = new BatchDeleteService();
                $params        = [
                    'id' => $id,
                ];
                $deleteService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setData($params)
                    ->run();
            }
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 回收站列表
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionRecycleList()
    {
        $params = Yii::$app->request->get();

        return $this->success(Announcement::getRecycleList($params));
    }

    /**
     * 回收站操作-还原
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionRecycleReduction()
    {
        $params = Yii::$app->request->post();
        if (!$params['id']) {
            return $this->fail();
        }

        $adminId     = Yii::$app->user->id;
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $deleteService = new DeleteService();
            $deleteService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                ->setReduction()
                ->setReductionData($params)
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 回收站操作-批量还原
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionRecycleBatchReduction()
    {
        $params = Yii::$app->request->post();
        if (!$params['ids']) {
            return $this->fail();
        }

        $adminId     = Yii::$app->user->id;
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $ids = explode(',', $params['ids']);

            $deleteService = new DeleteService();
            foreach ($ids as $id) {
                $params = [
                    'id' => $id,
                ];
                $deleteService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setReduction()
                    ->setReductionData($params)
                    ->run();
            }
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 回收站职位列表
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionRecycleJobList()
    {
        $params = Yii::$app->request->get();

        return $this->success(Job::getRecycleJobList($params));
    }

    /**
     * 回收站职位操作-还原
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionRecycleJobReduction()
    {
        $params = Yii::$app->request->post();
        if (!$params['id']) {
            return $this->fail();
        }

        $adminId     = Yii::$app->user->id;
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $deleteService = new DeleteJobService();
            $deleteService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                ->setReduction()
                ->setReductionData($params)
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 回收站职位操作-批量还原
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionRecycleJobBatchReduction()
    {
        $params = Yii::$app->request->post();
        if (!$params['ids']) {
            return $this->fail();
        }

        $adminId     = Yii::$app->user->id;
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $ids = explode(',', $params['ids']);

            $deleteService = new DeleteJobService();
            foreach ($ids as $id) {
                $params = [
                    'id' => $id,
                ];
                $deleteService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setReduction()
                    ->setReductionData($params)
                    ->run();
            }
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 栏目自动归类
     * @return Response|\yii\web\Response
     */
    public function actionAutoClassify()
    {
        $id = Yii::$app->request->post('id');

        $announcement = Announcement::findOne($id);
        if (!$announcement) {
            return $this->fail('公告不存在');
        }
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $model = new AnnouncementAutoClassify($id);
            $model->run();
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }
}