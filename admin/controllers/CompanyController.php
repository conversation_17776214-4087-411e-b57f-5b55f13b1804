<?php

namespace admin\controllers;

use admin\models\Area;
use admin\models\Company;
use admin\models\CompanyChildUnit;
use admin\models\CompanyFeatureTag;
use admin\models\CompanyGroup;
use admin\models\CompanyInfoAuth;
use admin\models\CompanyInterview;
use admin\models\Dictionary;
use admin\models\JobApply;
use admin\models\Member;
use admin\models\ServiceConfiguration;
use admin\models\Trade;
use admin\models\UploadForm;
use admin\models\WelfareLabel;
use common\base\models\BaseAdmin;
use common\base\models\BaseAdminDownloadTask;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyGroup;
use common\base\models\BaseCompanyMemberConfig;
use common\base\models\BaseCompanyMemberOperationLog;
use common\base\models\BaseCompanyPackageChangeLog;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseJobApply;
use common\base\models\BaseMember;
use common\components\MessageException;
use common\helpers\ArrayHelper;
use common\helpers\DebugHelper;
use common\libs\Excel;
use common\libs\WxWork;
use common\service\downloadTask\DownLoadTaskApplication;
use Exception;
use Yii;
use yii\console\Response;

class CompanyController extends BaseAdminController
{
    /**
     * 单位属性数据获取
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetCompanyAttrData()
    {
        return $this->success([
            // 合作单位类型
            'companyCooperationList' => ArrayHelper::obj2Arr(Company::COOPERATIVE_UNIT_LIST),
            // 单位类型
            'companyTypeList'        => ArrayHelper::obj2Arr(Dictionary::getCompanyTypeList()),
            // 单位性质
            'companyNatureList'      => ArrayHelper::obj2Arr(Company::getNatureList()),
            // 地区列表
            'companyAreaList'        => Area::getAreaList(),
            // 行业列表
            'companyIndustryList'    => Trade::getTradeList(),
            // 入网来源
            'companySourceTypeList'  => ArrayHelper::obj2Arr(Company::TYPE_SOURCE_LIST),
            // 套餐列表
            'companyPackageList'     => ArrayHelper::obj2Arr(BaseCompany::FOR_ADMIN_PACKAGE_TYPE_LIST),
            // 单位帐号状态
            'companyAccountsList'    => ArrayHelper::obj2Arr(Member::COMPANY_ACCOUNTS_STATUS_LIST),
            // 单位审核状态
            'companyAuditStatusList' => ArrayHelper::obj2Arr(CompanyInfoAuth::AUDIT_STATUS_LIST),
            // 面试管理-面试状态
            'interviewStatus'        => ArrayHelper::obj2Arr(JobApply::STATUS_INTERVIEW_SEARCH),
            //单位隐藏状态
            'hideStatusList'         => ArrayHelper::obj2Arr(Company::HIDE_STATUS_LIST),
            //投递类型
            'deliveryType'           => ArrayHelper::obj2Arr(BaseCompany::DELIVERY_TYPE_NAME),
            //账号性质
            'accountNature'          => ArrayHelper::obj2Arr(BaseCompany::ACCOUNT_NATURE_NAME),
            //单位群组
            'companyGroup'           => CompanyGroup::filter(),
            // 特色标签列表
            'featuredTagList'        => CompanyFeatureTag::getFeaturedTagList(),
        ]);
    }

    /**
     * 单位信息查询
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSearchList()
    {
        $request = Yii::$app->request->get();
        try {
            if ($request['export'] == 1) {// && $request['all']
                $adminId = Yii::$app->user->id;
                $app     = DownLoadTaskApplication::getInstance();
                $app->createAdmin($adminId, BaseAdminDownloadTask::TYPE_COMPANY_LIST, $request);
                // BaseAdminDownloadTask::createCompanyList($adminId);
                // Company::exportAllList($adminId);
                // // 10分钟后的时间
                // $time = date('Y-m-d H:i:s', time() + 600);

                //
                return $this->success('数据开始导出,成功下载后会在企业微信通知,请后续留意');
            }

            $list = Company::getSearchCompanyList($request);

            return $this->success($list);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 单位详情查看
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDetail()
    {
        $request = Yii::$app->request->get();
        try {
            $list = Company::getCompanyDetail($request);

            return $this->success($list);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 单位详情修改单位性质
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDetailEditAccountNature()
    {
        try {
            return $this->success(Company::editAccountNature());
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * todo：单位消耗明细列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionConsumeList()
    {
        return $this->success();
    }

    /**
     * 单位信息编辑
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEdit()
    {
        $companyId = \Yii::$app->request->get('id');
        $memberId  = Company::findOneVal(['id' => $companyId], 'member_id');

        return $this->success([
            // 基本信息
            'companyInfo'      => Company::getCompanyFullInfo($companyId),
            // 单位性质
            'natureList'       => ArrayHelper::obj2Arr(Company::getNatureList()),
            // 单位规模
            'scaleList'        => ArrayHelper::obj2Arr(Company::getScaleList()),
            // 所属行业
            'industryList'     => Trade::getTradeList(),
            // 单位福利
            'welfareLabelList' => WelfareLabel::getCompanyWelfareLabelList($memberId),
            // 单位类型
            'typeList'         => ArrayHelper::obj2Arr(Dictionary::getCompanyTypeList()),
            // 单位标签
            'tagList'          => ArrayHelper::obj2Arr(Dictionary::getCompanyLabelList()),
        ]);
    }

    /**
     * 单位信息保存
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionInfoSave()
    {
        $request = \Yii::$app->request->post();

        $transaction = \Yii::$app->db->beginTransaction();
        try {
            Company::saveCompanyFullInfo($request);

            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 单位二级院校删除
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionChildUnitDelete()
    {
        $id = \Yii::$app->request->post('id');
        if (!$id) {
            return $this->fail();
        }

        return $this->success(CompanyChildUnit::setChildUnitDelete($id));
    }

    //==============================免费单位==============================

    /**
     * 免费单位查询
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionFreeSearchList()
    {
        $request = Yii::$app->request->get();
        try {
            if ($request['export'] == 1 && $request['all']) {
                $adminId = Yii::$app->user->id;
                $app     = DownLoadTaskApplication::getInstance();
                $app->createAdmin($adminId, BaseAdminDownloadTask::TYPE_FREE_COMPANY_LIST);
                // BaseAdminDownloadTask::createCompanyList($adminId);
                // Company::exportAllList($adminId);
                // // 10分钟后的时间
                // $time = date('Y-m-d H:i:s', time() + 600);

                //
                return $this->success('数据开始导出,成功下载后会在企业微信通知,请后续留意');
            }

            $list = Company::getFreeSearchList($request);

            return $this->success($list);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 免费单位编辑
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionFreeEdit()
    {
        $companyId = \Yii::$app->request->get('id');
        $memberId  = Company::findOneVal(['id' => $companyId], 'member_id');

        return $this->success([
            // 基本信息
            'companyInfo'      => Company::getCompanyFullInfo($companyId),
            // 单位性质
            'natureList'       => ArrayHelper::obj2Arr(Company::getNatureList()),
            // 单位规模
            'scaleList'        => ArrayHelper::obj2Arr(Company::getScaleList()),
            // 所属行业
            'industryList'     => Trade::getTradeList(),
            // 单位福利
            'welfareLabelList' => WelfareLabel::getCompanyWelfareLabelList($memberId),
            // 单位类型
            'typeList'         => ArrayHelper::obj2Arr(Dictionary::getCompanyTypeList()),
            // 单位标签
            'tagList'          => ArrayHelper::obj2Arr(Dictionary::getCompanyLabelList()),
        ]);
    }

    /**
     * 免费单位保存
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionFreeInfoSave()
    {
        $request = \Yii::$app->request->post();

        $transaction = \Yii::$app->db->beginTransaction();
        try {
            Company::saveFreeCompanyFullInfo($request);

            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 免费单位详情查看
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionFreeDetail()
    {
        $request = Yii::$app->request->get();
        try {
            $list = Company::getFreeCompanyDetail($request);

            return $this->success($list);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
    //==============================免费单位 END==============================

    /**
     * 上传临时Excel文件
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionUploadExcel()
    {
        $model = new UploadForm();
        $model->setUploadType('file');

        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $transaction->commit();
            $path = 'company_excel';
            $data = $model->temporaryUploadExcel('file', $path);

            return $this->success([
                'url'     => $data['path'],
                'fullUrl' => \Yii::$app->params['homeUrl'] . '/' . $data['path'],
            ]);
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 面试管理列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionInterviewList()
    {
        $request = Yii::$app->request->get();
        try {
            $list = CompanyInterview::getInterviewList($request);

            return $this->success($list);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 面试管理查看详情
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionInterviewDetail()
    {
        $request = Yii::$app->request->get();
        try {
            $list = CompanyInterview::getInterviewDetail($request);

            return $this->success($list);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 上线单位未配置会员
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCompanyAllocation()
    {
        try {
            BaseCompanyPackageConfig::companyAllocation();

            return $this->success();
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取简历来源列表
     * @return Response|\yii\web\Response
     * @throws Exception
     */
    public function actionGetSourceList()
    {
        try {
            $list   = BaseJobApply::SOURCE_LIST;
            $result = [];
            foreach ($list as $k => $item) {
                $temp['k'] = $k;
                $temp['v'] = $item;
                $result[]  = $temp;
            }

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 更新单位的隐藏状态
     * @return Response|\yii\web\Response
     */
    public function actionUpdateHideStatus()
    {
        $id = Yii::$app->request->post('id');
        if (!$id) {
            return $this->fail();
        }
        $transaction = Yii::$app->db->beginTransaction();
        try {
            Company::updateHideStatus($id);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取单位当下套餐内容--单位名称，套餐内容，待审核公告&职位
     * @return Response|\yii\web\Response
     */
    public function actionGetCompanyMomentInfo()
    {
        try {
            $memberId = Yii::$app->request->get('memberId');
            if (!$memberId) {
                return $this->fail();
            }

            return $this->success(ServiceConfiguration::getCompanyMomentInfo($memberId));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 停止单位当前服务
     * 当前时间点
     */
    public function actionExpirationCompanyPackageNow()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $memberId = Yii::$app->request->post('memberId');
            if (!$memberId) {
                return $this->fail();
            }
            ServiceConfiguration::expirationCompanyNow($memberId);

            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取单位的子账号配置
     */
    public function actionGetCompanySubAccountConfig()
    {
        try {
            $company_id = Yii::$app->request->get('id');
            if (!$company_id) {
                return $this->fail();
            }
            $config_info = BaseCompanyMemberConfig::find()
                ->where(['company_id' => $company_id])
                ->asArray()
                ->one();

            //获取单位信息
            $company_info                     = BaseCompany::find()
                ->select([
                    'package_type',
                    'full_name',
                ])
                ->where(['id' => $company_id])
                ->asArray()
                ->one();
            $company_info['package_type_txt'] = BaseCompany::PACKAGE_TYPE_LIST[$company_info['package_type']] ?? '';

            return $this->success([
                'config_info'  => $config_info ?? [],
                'company_info' => $company_info ?? [],
            ]);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 保存单位的子账号配置
     */
    public function actionSaveCompanySubAccountConfig()
    {
        try {
            //单位子账号配置ID
            $id = Yii::$app->request->post('id');
            if (!$id) {
                throw new Exception('参数错误');
            }
            $admin_id = Yii::$app->user->id;
            //获取单位子账号配置
            $config_info = BaseCompanyMemberConfig::findOne($id);
            //获取子账号总数以及VIP授权数量配置参数
            $total     = Yii::$app->request->post('totalNumber');
            $vip_total = Yii::$app->request->post('vipTotalNumber');
            //验证是否合法
            if ($total < $vip_total) {
                throw new Exception('VIP授权数量不能大于子账号总数');
            }
            //子账号总数不能配置比当前以创建的子账号数量
            if ($total < $config_info->used) {
                throw new Exception('子账号总数不能小于当前已创建的子账号数量');
            }
            //VIP授权数量不能配置比当前以创建的VIP授权数量
            if ($vip_total < $config_info->vip_used) {
                throw new Exception('VIP授权数量不能小于当前已创建的VIP授权数量');
            }
            $before_total     = $config_info->total;
            $before_vip_total = $config_info->vip_total;
            //保存配置
            $config_info->total         = $total;
            $config_info->vip_total     = $vip_total;
            $config_info->available     = $total - $config_info->used;
            $config_info->vip_available = $vip_total - $config_info->vip_used;
            $res                        = $config_info->save();
            if (!$res) {
                throw new Exception('保存失败');
            } else {
                //获取单位信息
                $company_info = BaseCompany::findOne($config_info->company_id);
                //获取单位账号信息
                $member_info = BaseMember::findOne($company_info->member_id);
                //写入日志
                $common_item           = [
                    'member_id'      => $company_info->member_id,
                    'company_id'     => $company_info->id,
                    'type'           => BaseCompanyMemberOperationLog::OPERATION_TYPE_SUB_CONFIG,
                    'operation_id'   => $admin_id,
                    'operation_port' => BaseCompanyMemberOperationLog::OPERATION_PORT_ADMIN,
                ];
                $package_common_item   = [
                    'member_id'    => $company_info->member_id,
                    'member_name'  => $member_info->username ?: '',
                    'company_id'   => $company_info->id ?: '',
                    'company_name' => $company_info->full_name ?: '',
                    'handler_type' => BaseCompanyPackageChangeLog::HANDLER_TYPE_PLATFORM,
                    'handler_id'   => $admin_id,
                    'handler'      => BaseAdmin::findOneVal(['id' => $admin_id], 'name') ?: '',
                    'remark'       => '系统操作子账号配置',
                    'handle_type'  => BaseCompanyPackageChangeLog::HANDLE_TYPE_CONFIGURE_SUB,
                ];
                $package_sub_item      = [
                    'type'          => BaseCompanyPackageChangeLog::TYPE_SUB_ACCOUNT,
                    'handle_before' => strval($before_total),
                    'handle_after'  => strval($total),
                    'content'       => BaseCompanyPackageChangeLog::TYPE_NAME[BaseCompanyPackageChangeLog::TYPE_SUB_ACCOUNT],
                ];
                $package_vip_item      = [
                    'type'          => BaseCompanyPackageChangeLog::TYPE_VIP_AUTH,
                    'handle_before' => strval($before_vip_total),
                    'handle_after'  => strval($vip_total),
                    'content'       => BaseCompanyPackageChangeLog::TYPE_NAME[BaseCompanyPackageChangeLog::TYPE_VIP_AUTH],
                ];
                $sub_operation_content = '{sp1}子账号总量，变更前：{sp2}，变更后：{sp3}';
                $vip_operation_content = '{vp1}VIP权限授权总量，变更前：{vp2}，变更后：{vp3}';
                //判断子账号总数是否变更
                $total_diff = $total - $before_total;
                if ($total_diff != 0) {
                    $package_sub_item['change_amount'] = abs($total_diff);
                    if ($total_diff > 0) {
                        $sp1                          = '增加';
                        $package_sub_item['identify'] = BaseCompanyPackageChangeLog::IDENTIFY_ADD;
                    } elseif ($total_diff < 0) {
                        $sp1                          = '减少';
                        $package_sub_item['identify'] = BaseCompanyPackageChangeLog::IDENTIFY_REDUCE;
                    }
                    $sp2                   = $before_total;
                    $sp3                   = $total;
                    $sub_operation_content = str_replace([
                        '{sp1}',
                        '{sp2}',
                        '{sp3}',
                    ], [
                        $sp1,
                        $sp2,
                        $sp3,
                    ], $sub_operation_content);
                    $main_data_sub_item    = array_merge($common_item, [
                        'operation_content' => $sub_operation_content,
                    ]);
                    $package_data_sub_item = array_merge($package_common_item, $package_sub_item);
                    BaseCompanyMemberOperationLog::addLog($main_data_sub_item);
                    BaseCompanyPackageChangeLog::createCompanyPackageChangeLog($package_data_sub_item);
                }
                //判断VIP授权总数是否变更
                $vip_total_diff = $vip_total - $before_vip_total;
                if ($vip_total_diff != 0) {
                    $package_vip_item['change_amount'] = abs($vip_total_diff);
                    if ($vip_total_diff > 0) {
                        $vp1                          = '增加';
                        $package_vip_item['identify'] = BaseCompanyPackageChangeLog::IDENTIFY_ADD;
                    } elseif ($vip_total_diff < 0) {
                        $vp1                          = '减少';
                        $package_vip_item['identify'] = BaseCompanyPackageChangeLog::IDENTIFY_REDUCE;
                    }
                    $vp2                   = $before_vip_total;
                    $vp3                   = $vip_total;
                    $vip_operation_content = str_replace([
                        '{vp1}',
                        '{vp2}',
                        '{vp3}',
                    ], [
                        $vp1,
                        $vp2,
                        $vp3,
                    ], $vip_operation_content);
                    $main_data_vip_item    = array_merge($common_item, [
                        'operation_content' => $vip_operation_content,
                    ]);
                    $package_data_vip_item = array_merge($package_common_item, $package_vip_item);
                    BaseCompanyMemberOperationLog::addLog($main_data_vip_item);
                    BaseCompanyPackageChangeLog::createCompanyPackageChangeLog($package_data_vip_item);
                }

                return $this->success();
            }
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 编辑群组
     */
    public function actionEditGroup()
    {
        $request   = \Yii::$app->request->post();
        $companyId = $request['companyId'];
        $groupIds  = $request['groupIds'];
        if (empty($companyId)) {
            throw new Exception('参数错误');
        }
        if (empty($groupIds)) {
            throw new Exception('必须保留一个群组');
        }
        $transaction = Yii::$app->db->beginTransaction();
        try {
            BaseCompany::editGroup($companyId, $groupIds);
            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 上传临时Excel文件-批量修改单位群组文件上传
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionBatchEditGroup()
    {
        (WxWork::getInstance())->message(Yii::$app->user->id, '正在导入请等待......');
        // 关闭内存限制
        ini_set('memory_limit', '2048M');
        // 关闭超时限制
        set_time_limit(0);
        $model = new UploadForm();
        $model->setUploadType('file');
        $path = 'company_group_excel';
        $data = $model->temporaryUploadExcel('file', $path);

        //导入文件了 读取文件数据
        $filePath = Yii::getAlias('@frontendPc') . '/web/' . $data['path'];
        $excel    = new Excel();
        $data     = $excel->import($filePath, [], 2);
        $count    = count($data);
        //            if ($count == 0 || $count > 200) {
        if ($count == 0) {
            //                throw new Exception('文件数据为空或者超出200单位群组导入');
            throw new MessageException('文件数据为空');
        }
        $company = [];
        foreach ($data as $key => $value) {
            if (empty($value[0]) || empty($value[1])) {
                (WxWork::getInstance())->message(Yii::$app->user->id, '请检查第' . ($key + 1) . '行出错，数据为空！');
                throw new MessageException('请检查第' . ($key + 1) . '行出错，数据为空！');
            }
            //检查 单位名称的单位是否存在
            $companyInfo = BaseCompany::find()
                ->select('id')
                ->where(['full_name' => $value[0]])
                ->one();
            if (!$companyInfo) {
                (WxWork::getInstance())->message(Yii::$app->user->id,
                    '请检查第' . ($key + 1) . '行出错，单位名称查询的单位信息不存在！');
                throw new MessageException('请检查第' . ($key + 1) . '行出错，单位名称查询的单位信息不存在！');
            }
            $groupNames = explode(',', $value[1]);
            $groupIds   = BaseCompanyGroup::find()
                ->where(['group_name' => $groupNames])
                ->select('id')
                ->column();
            if (count($groupIds) != count($groupNames)) {
                (WxWork::getInstance())->message(Yii::$app->user->id, '请检查第' . ($key + 1) . '行出错，群组名称错误！');
                throw new MessageException('请检查第' . ($key + 1) . '行出错，群组名称错误！');
            }
            $company[$companyInfo['id']] = $groupIds;
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            foreach ($company as $key => $value) {
                BaseCompany::editGroup($key, implode(',', $value));
            }

            $transaction->commit();

            (WxWork::getInstance())->message(Yii::$app->user->id, '恭喜你，导入成功');

            return $this->success(true, '批量修改单位群组成功！');
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 编辑单位特色标签
     * @return Response|\yii\web\Response
     */
    public function actionEditCompanyFeaturedTag()
    {
        $params = Yii::$app->request->post();
        if (empty($params['companyId'])) {
            return $this->fail('参数错误');
        }
        try {
            Company::editCompanyFeaturedTag($params['companyId'], $params['featuredTagIds']);

            return $this->success();
        } catch (\yii\base\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 新增特色标签
     * @return Response|\yii\web\Response
     */
    public function actionAddFeaturedTag()
    {
        $params = Yii::$app->request->post();
        if (empty($params['tag']) || mb_strlen(trim($params['tag'])) > 20) {
            return $this->fail('参数错误');
        }
        try {
            Company::addFeaturedTag($params['tag'], \Yii::$app->user->id);

            return $this->success();
        } catch (\yii\base\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}