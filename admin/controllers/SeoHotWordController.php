<?php
/**
 * create user：shannon
 * create time：2024/2/29 15:15
 */
namespace admin\controllers;

use admin\models\SeoHotWord;
use common\base\models\BaseAdminDownloadTask;
use common\base\models\BaseSeoHotWordConfig;
use common\service\downloadTask\DownLoadTaskApplication;
use Yii;
use yii\base\Exception;

class SeoHotWordController extends BaseAdminController
{
    /**
     * 获取热词列表
     */
    public function actionList()
    {
        try {
            $params = Yii::$app->request->get();

            //返回热词列表
            return $this->success(SeoHotWord::getList($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 热词导出
     */
    public function actionExport()
    {
        try {
            $params  = Yii::$app->request->get();
            $adminId = Yii::$app->user->id;
            $app     = DownLoadTaskApplication::getInstance();
            $app->createAdmin($adminId, BaseAdminDownloadTask::TYPE_SEO_HOT_WORD, $params);

            return $this->success('数据开始导出,成功下载后会在企业微信通知,请后续留意');
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 添加热词
     */
    public function actionAdd()
    {
        try {
            //获取输入的关键词参数
            $keyword = Yii::$app->request->post('keyword');

            $result = SeoHotWord::add($keyword);

            return $this->success($result['res'], $result['msg']);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 编辑热词
     */
    public function actionEdit()
    {
        try {
            $params = Yii::$app->request->post();
            $result = SeoHotWord::edit($params);

            if ($result) {
                return $this->success($result, '修改成功');
            } else {
                return $this->fail('修改失败');
            }
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除热词
     */
    public function actionDelete()
    {
        try {
            $id     = Yii::$app->request->post('id');
            $result = SeoHotWord::delete($id);

            if ($result) {
                return $this->success($result, '删除成功');
            } else {
                return $this->fail('删除失败');
            }
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}
