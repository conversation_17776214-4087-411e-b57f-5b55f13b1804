<?php

namespace admin\controllers;

use admin\models\DevTools;
use Yii;

/**
 * 开发工具控制器
 * 用于开发和调试过程中查询用户相关信息
 */
class DevToolsController extends BaseAdminController
{
    /**
     * 精确查询用户相关信息
     * 
     * @return \yii\web\Response
     */
    public function actionGetUserRelatedInfo()
    {
        try {
            $searchType = $this->request('searchType');
            $searchValue = $this->request('searchValue');

            if (!$searchType || !$searchValue) {
                return $this->fail('查询类型和查询值不能为空');
            }

            // 验证查询类型
            $allowedTypes = [
                'id', 'phone', 'uid', 'email', 'username',
                'companyId', 'companyAccountId', 'companyUid'
            ];
            
            if (!in_array($searchType, $allowedTypes)) {
                return $this->fail('不支持的查询类型');
            }

            $result = DevTools::getUserRelatedInfo($searchType, $searchValue);

            // 无论是否有数据都返回成功，没有数据时返回空数组
            if (empty($result)) {
                $result = [];
            }

            return $this->success($result);

        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 智能查询用户相关信息
     *
     * @return \yii\web\Response
     */
    public function actionIntelligentSearch()
    {
        try {
            $keyword = $this->request('keyword');

            if (!$keyword) {
                return $this->fail('查询关键词不能为空');
            }

            // 执行智能搜索，返回所有匹配的结果
            $results = DevTools::intelligentSearch($keyword);

            // 构建返回数据，无论是否有结果都返回成功
            $responseData = [
                'keyword' => $keyword,
                'totalCount' => count($results),
                'results' => $results // 没有结果时这里是空数组
            ];

            return $this->success($responseData);

        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 无需登录就可以操作的控制器
     * 开发工具需要管理员权限，不在此列表中
     */
    public function ignoreLogin()
    {
        return [];
    }

    /**
     * 权限控制
     * 开发工具需要超级管理员权限
     */
    public function permissions()
    {
        return [
            'dev-tools/get-user-related-info' => 'devToolsQuery',
            'dev-tools/intelligent-search' => 'devToolsQuery',
        ];
    }
}
