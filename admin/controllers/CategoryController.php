<?php

namespace admin\controllers;

use admin\models\CategoryJob;
use Yii;

class CategoryController extends BaseAdminController
{

    /**
     * 获取职位分类
     */
    public function actionGetJobList()
    {
        return $this->success(CategoryJob::getList());
    }

    /**
     * 添加职位分类
     */
    public function actionAddJob()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $model   = new CategoryJob();
            $adminId = Yii::$app->user->id;
            $model->create(Yii::$app->request->post(), $adminId);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    public function actionAreaList()
    {
    }
}