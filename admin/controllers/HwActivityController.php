<?php

namespace admin\controllers;

use admin\models\HwActivity;
use common\base\models\BaseAdminPositionMenu;
use common\base\models\BaseHwActivity;
use common\base\models\BaseHwActivityCompany;
use common\base\models\BaseHwActivityCompanyHot;
use common\base\models\BaseHwActivityFeatureTagRelation;
use common\base\models\BaseHwActivityPromotion;
use common\base\models\BaseHwSpecialActivity;
use common\helpers\ArrayHelper;
use common\service\hwActivity\saveService;
use Yii;
use yii\base\Exception;

class HwActivityController extends BaseAdminController
{
    /**
     * 编辑、新增活动
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSaveInfo()
    {
        $data        = Yii::$app->request->post();
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $service = new saveService();
            $data    = $service->setData($data)
                ->run();

            $transaction->commit();

            return $this->success($data);
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取编辑信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetEditInfo()
    {
        $id = Yii::$app->request->get('id');
        if (!$id) {
            return $this->fail();
        }
        try {
            $info = HwActivity::getInfo($id);

            return $this->success($info);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionGetList()
    {
        $searchData = Yii::$app->request->get();

        try {
            $list = HwActivity::search($searchData);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 设置排序
     * @return void|\yii\console\Response|\yii\web\Response
     */
    public function actionSetSort()
    {
        $activityId  = Yii::$app->request->post('id');
        $sort        = Yii::$app->request->post('sort');
        $transaction = Yii::$app->db->beginTransaction();
        try {
            if (!BaseAdminPositionMenu::getAuth('sortAndGrounding')) {
                return $this->fail('无权操作');
            }
            if (!$activityId || mb_strlen($sort) == 0) {
                return $this->fail('缺少必填参数');
            }

            $model = BaseHwActivity::findOne($activityId);
            if (!$model) {
                return $this->fail('活动不存在');
            }
            BaseHwActivity::setSort($activityId, $sort);
            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除推广位置
     * @return void|\yii\console\Response|\yii\web\Response
     */
    public function actionDeletePromotion()
    {
        $promotionId = Yii::$app->request->post('promotionId');

        try {
            if (!$promotionId) {
                return $this->fail('参数错误');
            }

            $model = BaseHwActivityPromotion::findOne($promotionId);
            if (!$model) {
                return $this->fail('推广位置不存在');
            }
            if ($model->delete()) {
                return $this->success();
            } else {
                return $this->fail('取消推广位失败');
            }
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 设置推广位置排序
     * @return void|\yii\console\Response|\yii\web\Response
     */
    public function actionSetPromotionSort()
    {
        $promotionId   = Yii::$app->request->post('promotionId');
        $promotionSort = Yii::$app->request->post('promotionSort');

        try {
            if (!$promotionId || mb_strlen($promotionSort) == 0) {
                return $this->fail('参数错误');
            }

            $model = BaseHwActivityPromotion::findOne($promotionId);
            if (!$model) {
                return $this->fail('推广位置不存在');
            }
            BaseHwActivityPromotion::setSort($promotionId, $promotionSort);

            return $this->success();
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取未关联单位列表
     * Associated company
     */
    public function actionGetAssociatedCompanyList()
    {
        $params = Yii::$app->request->get();
        try {
            if (!$params['activityId']) {
                return $this->fail('参数错误');
            }

            $model = BaseHwActivity::findOne($params['activityId']);
            if (!$model) {
                return $this->fail('活动不存在');
            }
            //验证活动类型-非招聘会类型不允许
            if (in_array($model->series_type, BaseHwActivity::ZHAOPINHUI_UN_TYPE)) {
                return $this->fail('当前活动的活动类型不支持当前操作');
            }

            return $this->success(HwActivity::getAssociatedCompanyList($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 添加关联单位
     * Associated company
     */
    public function actionAddAssociatedCompany()
    {
        $activityId = Yii::$app->request->post('activityId');
        $companyIds = Yii::$app->request->post('companyId');
        try {
            if (!$activityId || !$companyIds) {
                return $this->fail('参数错误');
            }
            //验证活动类型-非招聘会类型不允许
            $activityInfo = BaseHwActivity::findOne($activityId);
            if (in_array($activityInfo->series_type, BaseHwActivity::ZHAOPINHUI_UN_TYPE)) {
                return $this->fail('当前活动的活动类型不支持当前操作');
            }
            if (HwActivity::addAssociatedCompany($activityId, $companyIds)) {
                return $this->success();
            } else {
                return $this->fail('关联失败');
            }
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取未关联公告列表
     * Associated company
     */
    public function actionGetAssociatedAnnouncementList()
    {
        $params = Yii::$app->request->get();
        try {
            if (!$params['activityId']) {
                return $this->fail('参数错误');
            }

            $model = BaseHwActivity::findOne($params['activityId']);
            if (!$model) {
                return $this->fail('活动不存在');
            }
            //验证活动类型-非招聘会类型不允许
            if (in_array($model->series_type, BaseHwActivity::ZHAOPINHUI_UN_TYPE)) {
                return $this->fail('当前活动的活动类型不支持当前操作');
            }

            return $this->success(HwActivity::getAssociatedAnnouncementList($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 添加关联公告
     * Associated company
     */
    public function actionAddAssociatedAnnouncement()
    {
        $activityId      = Yii::$app->request->post('activityId');
        $announcementIds = Yii::$app->request->post('announcementId');
        try {
            if (!$activityId || !$announcementIds) {
                return $this->fail('参数错误');
            }
            //验证活动类型-非招聘会类型不允许
            $activityInfo = BaseHwActivity::findOne($activityId);
            if (in_array($activityInfo->series_type, BaseHwActivity::ZHAOPINHUI_UN_TYPE)) {
                return $this->fail('当前活动的活动类型不支持当前操作');
            }

            return $this->success(HwActivity::addAssociatedAnnouncement($activityId, $announcementIds));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 取消关联单位
     */
    public function actionCancelAssociatedCompany()
    {
        try {
            $activityId = Yii::$app->request->post('activityId');
            $companyId  = Yii::$app->request->post('companyId');
            if (!$activityId || !$companyId) {
                return $this->fail('参数错误');
            }
            //验证活动类型-非招聘会类型不允许
            $activityInfo = BaseHwActivity::findOne($activityId);
            if (in_array($activityInfo->series_type, BaseHwActivity::ZHAOPINHUI_UN_TYPE)) {
                return $this->fail('当前活动的活动类型不支持当前操作');
            }
            HwActivity::cancelAssociatedCompany($activityId, $companyId);

            return $this->success();
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取关联的列表
     */
    public function actionGetAssociatedList()
    {
        try {
            $params = Yii::$app->request->get();

            if (!$params['activityId']) {
                return $this->fail('参数错误');
            }

            //验证活动类型-非招聘会类型不允许
            $activityInfo = BaseHwActivity::findOne($params['activityId']);
            if (in_array($activityInfo->series_type, BaseHwActivity::ZHAOPINHUI_UN_TYPE)) {
                return $this->fail('当前活动的活动类型不支持当前操作');
            }

            return $this->success(HwActivity::getAssociatedList($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 设置关联单位的排序
     */
    public function actionSetAssociatedSort()
    {
        $id   = Yii::$app->request->post('id');
        $sort = Yii::$app->request->post('sort');

        try {
            if (!$id || mb_strlen($sort) == 0) {
                return $this->fail('缺少必填参数');
            }

            $model = BaseHwActivityCompany::findOne($id);
            if (!$model) {
                return $this->fail('关联单位不存在');
            }
            BaseHwActivityCompany::setAssociatedSort($id, $sort);

            return $this->success();
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 设置关联单位的置顶
     */
    public function actionSetAssociatedTop()
    {
        $id    = Yii::$app->request->post('id');
        $isTop = Yii::$app->request->post('isTop');//1置顶2否

        try {
            if (!$id || !in_array($isTop, [
                    BaseHwActivityCompany::IS_TOP_NO,
                    BaseHwActivityCompany::IS_TOP_YES,
                ])) {
                return $this->fail('参数错误');
            }

            $model = BaseHwActivityCompany::findOne($id);
            if (!$model) {
                return $this->fail('关联单位不存在');
            }
            BaseHwActivityCompany::setAssociatedTop($id, $isTop);

            return $this->success();
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 切换活动上下架状态
     * @return void|\yii\console\Response|\yii\web\Response
     */
    public function actionChangeGroundingStatus()
    {
        $activityId  = Yii::$app->request->post('id');
        $transaction = Yii::$app->db->beginTransaction();

        try {
            if (!BaseAdminPositionMenu::getAuth('sortAndGrounding')) {
                return $this->fail('无权操作');
            }
            if (!$activityId) {
                return $this->fail('缺少必填参数');
            }

            $model = BaseHwActivity::findOne($activityId);
            if (!$model) {
                return $this->fail('活动不存在');
            }
            BaseHwActivity::changeGroundingStatus($activityId);
            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除活动
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelActivity()
    {
        $activityId  = Yii::$app->request->post('id');
        $transaction = Yii::$app->db->beginTransaction();

        try {
            if (!$activityId) {
                return $this->fail('缺少必填参数');
            }

            $model = BaseHwActivity::findOne($activityId);
            if (!$model) {
                return $this->fail('活动不存在');
            }
            if ($model->status == BaseHwActivity::STATUS_DELETE) {
                return $this->success();
            }
            BaseHwActivity::delActivity($activityId);
            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取搜索参数列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetSearchParamsList()
    {
        //获取活动系列、类型列表
        $seriesList = BaseHwActivity::getSeriesAdminAndTypeList();
        //活动状态列表
        $statusList = ArrayHelper::obj2Arr(BaseHwActivity::ACTIVITY_STATUS_TEXT_LIST);
        //上架状态列表
        $groundingStatus = ArrayHelper::obj2Arr(BaseHwActivity::GROUNDING_STATUS_TEXT_LIST);
        //推广位置
        $promotionPositionList = ArrayHelper::obj2Arr(BaseHwActivityPromotion::PROMOTION_POSITION_TEXT_LIST);
        //活动标签
        $tagsList = ArrayHelper::obj2Arr(BaseHwActivity::TAGS_TEXT_LIST);

        return $this->success([
            'tagsList'              => $tagsList,
            'seriesList'            => $seriesList,
            'statusList'            => $statusList,
            'groundingStatus'       => $groundingStatus,
            'promotionPositionList' => $promotionPositionList,
            'toHoldType'            => ArrayHelper::obj2Arr(BaseHwActivity::TO_HOLD_TYPE_LIST),
        ]);
    }

    /**
     * 获取系列、类型列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetSeriesTypeList()
    {
        return $this->success(BaseHwActivity:: getSeriesAndTypeList());
    }

    /**
     * 获取添加、编辑活动选项
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetAddParamsList()
    {
        $promotionPositionList = [];
        foreach (BaseHwActivityPromotion::PROMOTION_POSITION_LIST as $key => $value) {
            $promotionPositionList[$key] = ArrayHelper::obj2Arr($value);
        }

        return $this->success([
            'featureTagParams'        => ArrayHelper::obj2Arr(BaseHwActivityFeatureTagRelation:: FEATURE_TAG_TEXT_LIST),
            'subTypeParams'           => ArrayHelper::obj2Arr(BaseHwActivity:: SUB_TYPE_TEXT_LIST),
            'specialLinkParams'       => ArrayHelper::obj2Arr(BaseHwActivity:: ACTIVITY_LINK_LIST),
            'toHoldType'              => ArrayHelper::obj2Arr(BaseHwActivity:: TO_HOLD_TYPE_LIST),
            'promotionPositionList'   => $promotionPositionList,
            'applyLinkPersonTypeList' => ArrayHelper::obj2Arr(BaseHwSpecialActivity::APPLY_LINK_PERSON_TYPE_TEXT_LIST),
            'companyHotLinkTypeList'  => ArrayHelper::obj2Arr(BaseHwActivityCompanyHot::LINK_TYPE_LIST),
            'templateList'            => ArrayHelper::obj2Arr(BaseHwActivity::TEMPLATE_TYPE_LIST),
        ]);
    }

    /**
     * 获取显示图片类型列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetShowImgType()
    {
        $list = [];
        //        foreach (BaseHwActivityPromotion::SHOW_IMG_TYPE_TEXT_LIST as $key => $value) {
        //            $list[] = [
        //                'i' => $key,
        //                'l' => ArrayHelper::obj2Arr($value),
        //            ];
        //        }
        foreach (BaseHwActivityPromotion::SHOW_IMG_TYPE_TEXT_LIST as $key => $value) {
            $list[$key] = ArrayHelper::obj2Arr($value);
        }

        return $this->success($list);
    }

    /**
     * 获取活动详情链接是公告详情时候自动识别单位信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetAnnouncementDetailCompanyInfo()
    {
        try {
            $link = Yii::$app->request->get('detailUrl');

            return $this->success(HwActivity::getAnnouncementDetailCompanyInfo($link));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取专场查询条件数据
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetSpecialActivityParams()
    {
        return $this->success(HwActivity::getSpecialActivityParams());
    }

    /**
     * 获取专场列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetSpecialActivityList()
    {
        $params = \Yii::$app->request->get();

        return $this->success(HwActivity::getSpecialActivityList($params));
    }

    /**
     * 获取专场详情
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetSpecialActivityDetail()
    {
        $params = \Yii::$app->request->get();

        return $this->success(ArrayHelper::intToString(HwActivity::getSpecialActivityDetail($params['id'] ?? 0)));
    }

    /**
     * 获取专场可新增的海外活动
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetSpecialActivityCanAddActivity()
    {
        $params = \Yii::$app->request->get();

        return $this->success(HwActivity::getSpecialActivityCanAddActivity($params));
    }

    /**
     * 编辑/新增专场详情
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionUpdateSpecialActivityDetail()
    {
        $params            = \Yii::$app->request->post();
        $params['adminId'] = \Yii::$app->user->id;

        HwActivity::checkUpdateSpecialParams($params);

        return $this->success(HwActivity::updateSpecialActivityDetail($params));
    }

    /**
     * 查询公告关联的海外活动列表
     */
    public function actionSearchAnnouncementHwActivity()
    {
        $params = \Yii::$app->request->get();

        return $this->success(HwActivity::searchAnnouncementHwActivity($params));
    }
}