<?php

namespace admin\controllers;

use admin\models\Member;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyMemberOperationLog;
use common\base\models\BaseMember;
use common\helpers\ArrayHelper;
use Yii;

class MemberController extends BaseAdminController
{
    /**
     * 日志查询
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSearchLogList()
    {
        return $this->success(Member::getLogList(Yii::$app->request->get()));
    }

    /**
     * 日志查询筛选器
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSearchLogFilter()
    {
        //获取memberId
        $memberId   = Yii::$app->request->get('id');
        $memberInfo = BaseMember::findOne($memberId);
        if ($memberInfo->type == BaseMember::TYPE_COMPANY) {
            //获取单位信息
            $company_info = BaseCompany::findOne(['member_id' => $memberId]);
            if (!$company_info) {
                return $this->fail('单位信息不存在');
            }
            //获取单位所有账号信息
            $company_member_info = BaseCompanyMemberInfo::find()
                ->select([
                    'id',
                    'contact',
                    'department',
                ])
                ->where(['company_id' => $company_info->id])
                ->asArray()
                ->all();

            $contact_select = [];
            foreach ($company_member_info as $value) {
                $item = [
                    'contact'            => $value['contact'],
                    'contact_department' => $value['contact'],
                ];
                if ($value['department']) {
                    $item['contact_department'] = $value['contact'] . '(' . $value['department'] . ')';
                }
                $contact_select[] = $item;
            }

            $search['contact_select'] = ArrayHelper::obj2Arr(array_column($contact_select, 'contact',
                'contact_department'));
        } else {
            $search = [];
        }

        return $this->success($search);
    }

    /**
     * 获取单位账号操作日志
     * @return \yii\console\Response|\yii\web\Response
     * @throws \yii\base\Exception
     */
    public function actionOperationLogList()
    {
        return $this->success(BaseCompanyMemberOperationLog::getCompanyLogList(Yii::$app->request->get()));
    }

    /**
     * 禁用账号
     */
    public function actionLockAccount()
    {
        $id = Yii::$app->request->post('id');
        if (!$id) {
            return $this->fail();
        }
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $adminId = Yii::$app->user->id;
            $model   = new Member();
            $model->lockAccount($id, $adminId);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 启动账号
     */
    public function actionUnlockAccount()
    {
        $id = Yii::$app->request->post('id');
        if (!$id) {
            return $this->fail();
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $adminId = Yii::$app->user->id;
            $model   = new Member();
            $model->unlockAccount($id, $adminId);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 注册单位查询
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionRegisterSearchList()
    {
        $request = Yii::$app->request->get();
        try {
            $list = Member::getRegisterSearchList($request);

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

}