<?php

namespace admin\controllers;

use common\service\jobInvite\InvitationScreenService;
use Yii;
use yii\base\Exception;

/**
 * 邀约数据大屏控制器
 * 提供邀约数据统计和分析接口
 */
class InvitationScreenController extends BaseAdminController
{
    /**
     * 获取KPI概览数据
     * GET /invitation-screen/kpi-overview
     *
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionKpiOverview()
    {
        try {
            $service = new InvitationScreenService();
            $data    = $service->getKpiOverview();

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取趋势分析数据
     * GET /invitation-screen/trend-analysis
     *
     * 请求参数:
     * - startDate: 开始日期 (YYYY-MM-DD) 可选
     * - endDate: 结束日期 (YYYY-MM-DD) 可选
     *
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionTrendAnalysis()
    {
        try {
            $params    = Yii::$app->request->get();
            $startDate = $params['startDate'] ?? null;
            $endDate   = $params['endDate'] ?? null;

            // 验证日期格式
            if ($startDate && !$this->validateDate($startDate)) {
                return $this->fail('开始日期格式错误，请使用YYYY-MM-DD格式');
            }

            if ($endDate && !$this->validateDate($endDate)) {
                return $this->fail('结束日期格式错误，请使用YYYY-MM-DD格式');
            }

            // 验证日期范围
            if ($startDate && $endDate && $startDate > $endDate) {
                return $this->fail('开始日期不能大于结束日期');
            }

            // 限制查询范围不超过90天
            if ($startDate && $endDate) {
                $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (24 * 3600);
                if ($daysDiff > 90) {
                    return $this->fail('查询时间范围不能超过90天');
                }
            }

            $service = new InvitationScreenService();
            $data    = $service->getTrendAnalysis($startDate, $endDate);

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取单位/账号排行
     * GET /invitation-screen/company-ranking
     *
     * 请求参数:
     * - startDate: 开始日期 (YYYY-MM-DD) 可选
     * - endDate: 结束日期 (YYYY-MM-DD) 可选
     *
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCompanyRanking()
    {
        try {
            $params    = Yii::$app->request->get();
            $startDate = $params['startDate'] ?? null;
            $endDate   = $params['endDate'] ?? null;

            // 验证日期格式和范围
            $validation = $this->validateDateParams($startDate, $endDate);
            if ($validation !== true) {
                return $this->fail($validation);
            }

            $service = new InvitationScreenService();
            $data    = $service->getCompanyRanking($startDate, $endDate);

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取求职者邀约次数排行
     * GET /invitation-screen/candidate-ranking
     *
     * 请求参数:
     * - startDate: 开始日期 (YYYY-MM-DD) 可选
     * - endDate: 结束日期 (YYYY-MM-DD) 可选
     *
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCandidateRanking()
    {
        try {
            $params    = Yii::$app->request->get();
            $startDate = $params['startDate'] ?? null;
            $endDate   = $params['endDate'] ?? null;

            // 验证日期格式和范围
            $validation = $this->validateDateParams($startDate, $endDate);
            if ($validation !== true) {
                return $this->fail($validation);
            }

            $service = new InvitationScreenService();
            $data    = $service->getCandidateRanking($startDate, $endDate);

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取单位对求职者邀约排行
     * GET /invitation-screen/company-to-candidate-ranking
     *
     * 请求参数:
     * - startDate: 开始日期 (YYYY-MM-DD) 可选
     * - endDate: 结束日期 (YYYY-MM-DD) 可选
     *
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCompanyToCandidateRanking()
    {
        try {
            $params    = Yii::$app->request->get();
            $startDate = $params['startDate'] ?? null;
            $endDate   = $params['endDate'] ?? null;

            // 验证日期格式和范围
            $validation = $this->validateDateParams($startDate, $endDate);
            if ($validation !== true) {
                return $this->fail($validation);
            }

            $service = new InvitationScreenService();
            $data    = $service->getCompanyToCandidateRanking($startDate, $endDate);

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 验证日期格式
     * @param string $date
     * @return bool
     */
    private function validateDate($date)
    {
        $d = \DateTime::createFromFormat('Y-m-d', $date);

        return $d && $d->format('Y-m-d') === $date;
    }

    /**
     * 验证日期参数
     * @param string|null $startDate
     * @param string|null $endDate
     * @return bool|string true表示验证通过，字符串表示错误信息
     */
    private function validateDateParams($startDate, $endDate)
    {
        // 验证日期格式
        if ($startDate && !$this->validateDate($startDate)) {
            return '开始日期格式错误，请使用YYYY-MM-DD格式';
        }

        if ($endDate && !$this->validateDate($endDate)) {
            return '结束日期格式错误，请使用YYYY-MM-DD格式';
        }

        // 验证日期范围
        if ($startDate && $endDate && $startDate > $endDate) {
            return '开始日期不能大于结束日期';
        }

        // 限制查询范围不超过90天
        if ($startDate && $endDate) {
            $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (24 * 3600);
            if ($daysDiff > 90) {
                return '查询时间范围不能超过90天';
            }
        }

        return true;
    }

    /**
     * 无需登录就可以访问的接口
     * 管理后台的邀约数据大屏需要登录权限
     *
     * @return array
     */
    public function ignoreLogin()
    {
        return [// 管理后台接口都需要登录，这里为空
        ];
    }
}
