<?php

namespace tests\unit\service;

use common\service\memberCancel\ResumeCancelSearchService;
use PHPUnit\Framework\TestCase;

/**
 * 求职者注销搜索服务小驼峰参数测试
 */
class ResumeCancelSearchServiceCamelCaseTest extends TestCase
{
    /**
     * 测试小驼峰参数格式
     */
    public function testCamelCaseParameters()
    {
        // 测试小驼峰格式的参数
        $params = [
            'cancelLogId' => 123,
            'mobile' => '138',
            'name' => '张三',
            'email' => '<EMAIL>',
            'status' => 1,
            'cancelReasonType' => 1,
            'smsStatus' => 1,
            'applyTimeStart' => '2024-01-01',
            'applyTimeEnd' => '2024-01-31',
            'cooldownEndTimeStart' => '2024-01-01',
            'cooldownEndTimeEnd' => '2024-01-31',
            'completeTimeStart' => '2024-01-01',
            'completeTimeEnd' => '2024-01-31',
            'adminId' => 1,
            'ip' => '***********',
            'sortField' => 'applyTime',
            'sortOrder' => 'DESC',
            'page' => 1,
            'pageSize' => 10
        ];
        
        // 调用方法，不应该抛出异常
        $result = ResumeCancelSearchService::getList($params);
        
        // 验证返回结构
        $this->assertArrayHasKey('list', $result);
        $this->assertArrayHasKey('pages', $result);
        $this->assertIsArray($result['list']);
        $this->assertIsArray($result['pages']);
    }

    /**
     * 测试统计方法的小驼峰参数
     */
    public function testStatisticsCamelCaseParameters()
    {
        $params = [
            'startDate' => '2024-01-01',
            'endDate' => '2024-01-31'
        ];
        
        $result = ResumeCancelSearchService::getStatistics($params);
        
        // 验证返回结构
        $this->assertArrayHasKey('overview', $result);
        $this->assertArrayHasKey('statusStats', $result);
        $this->assertArrayHasKey('reasonStats', $result);
        $this->assertArrayHasKey('smsStats', $result);
    }

    /**
     * 测试排序字段的小驼峰格式
     */
    public function testSortFieldCamelCase()
    {
        $sortFields = [
            'applyTime',
            'cooldownEndTime', 
            'completeTime',
            'status',
            'cancelReasonType',
            'smsStatus'
        ];
        
        foreach ($sortFields as $sortField) {
            $params = [
                'sortField' => $sortField,
                'sortOrder' => 'DESC',
                'page' => 1,
                'pageSize' => 5
            ];
            
            $result = ResumeCancelSearchService::getList($params);
            
            // 验证能正常返回结果
            $this->assertArrayHasKey('list', $result);
            $this->assertArrayHasKey('pages', $result);
        }
    }

    /**
     * 测试空参数情况
     */
    public function testEmptyParameters()
    {
        $params = [];
        
        $result = ResumeCancelSearchService::getList($params);
        
        // 验证返回结构
        $this->assertArrayHasKey('list', $result);
        $this->assertArrayHasKey('pages', $result);
        $this->assertIsArray($result['list']);
        $this->assertIsArray($result['pages']);
    }

    /**
     * 测试部分参数情况
     */
    public function testPartialParameters()
    {
        $params = [
            'mobile' => '138',
            'status' => 1,
            'page' => 1
        ];
        
        $result = ResumeCancelSearchService::getList($params);
        
        // 验证返回结构
        $this->assertArrayHasKey('list', $result);
        $this->assertArrayHasKey('pages', $result);
    }

    /**
     * 测试筛选选项方法
     */
    public function testFilterOptions()
    {
        $result = ResumeCancelSearchService::getFilterOptions();
        
        // 验证返回结构
        $this->assertArrayHasKey('statusOptions', $result);
        $this->assertArrayHasKey('cancelReasonOptions', $result);
        $this->assertArrayHasKey('smsStatusOptions', $result);
        
        // 验证选项格式
        foreach ($result['statusOptions'] as $option) {
            $this->assertArrayHasKey('value', $option);
            $this->assertArrayHasKey('label', $option);
        }
    }
}
